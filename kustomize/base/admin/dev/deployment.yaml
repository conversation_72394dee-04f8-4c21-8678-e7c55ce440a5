apiVersion: apps/v1
kind: Deployment
metadata:
  name: acqwired-admin
  labels:
    app: admin
    version: v1
spec:
  replicas: 1
  revisionHistoryLimit: 50
  selector:
    matchLabels:
      app: admin
      version: v1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
  template:
    metadata:
      labels:
        app: admin
        version: v1
    spec:
      serviceAccountName: acqwired-ksa
      containers:
        - name: acqwired-admin
          imagePullPolicy: Always
          image: gcr.io/acqwired-dev/admin:latest
          ports:
            - containerPort: 3006 # the app running in this deployment (pod) listens this port
              protocol: TCP
              name: admin-web # name of the port we need to use in the service to route traffic to this deployment/pod
          readinessProbe:
            httpGet:
              path: /
              port: admin-web
              scheme: HTTP
            failureThreshold: 3
            initialDelaySeconds: 3
            periodSeconds: 3
            successThreshold: 2
          livenessProbe:
            httpGet:
              path: /
              port: admin-web
              scheme: HTTP
            initialDelaySeconds: 3
            timeoutSeconds: 3
            failureThreshold: 3
          resources:
            requests:
              cpu: 250m
              memory: 250Mi
            limits:
              cpu: 500m
              memory: 500Mi
        - name: cloud-sql-proxy
          image: gcr.io/cloud-sql-connectors/cloud-sql-proxy:latest
          imagePullPolicy: IfNotPresent
          env:
            - name: CONNECTION
              value: 'acqwired-dev:us-central1:acqwired-dev'
            - name: PORT
              value: '5432'
          args:
            - '--private-ip'
            - '--port=5432'
            - 'acqwired-dev:us-central1:acqwired-dev'
          securityContext:
            runAsNonRoot: true
          resources:
            limits:
              cpu: 500m
              memory: 1024M
            requests:
              cpu: 100m
              memory: 512M
