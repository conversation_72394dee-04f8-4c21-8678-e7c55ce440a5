const { ListSourceType } = require('@prisma/client');

const intentions = [
  {
    id: 'similar-companies',
    name: 'Similar companies',
    description: 'Find companies similar to your target',
    sources: [
      {
        id: 'sourcescrub-similar',
        name: 'SourceScrub',
        description: 'Find similar companies with SourceScrub',
        sourceType: ListSourceType.sourceScrub,
        icon: 'sourcescrub'
      },
      {
        id: 'ocean-similar',
        name: 'Ocean.io',
        description: 'Find similar companies with Ocean.io',
        sourceType: ListSourceType.oceanIo,
        icon: 'ocean'
      }
    ]
  },
  {
    id: 'ai-tools',
    name: 'AI tools',
    description: 'Use AI to build your list',
    sources: [
      {
        id: 'list-building-agent',
        name: 'List building agent',
        description: 'Find companies with latest AI agent',
        sourceType: ListSourceType.aiAgent,
        parameters: {
          defaultModel: 'gpt-4.1',
          excludeModels: ['gpt-4.1-mini']
        },
        icon: 'robot'
      }
    ]
  },
  {
    id: 'conference-industry',
    name: 'Conference & industry lists',
    description: 'Import companies from conferences and industry lists',
    sources: [
      {
        id: 'sourcescrub-conference',
        name: 'SourceScrub - Source Search',
        description: 'Find Conference & industry lists with SourceScrub',
        sourceType: ListSourceType.sourceScrub,
        icon: 'sourcescrub'
      }
    ]
  },
  {
    id: 'my-data',
    name: 'My data',
    description: 'Import your own data',
    sources: [
      {
        id: 'csv-upload',
        name: 'CSV upload',
        description: 'Upload your data',
        sourceType: ListSourceType.resource,
        icon: 'file'
      },
      {
        id: 'acqwired-database',
        name: 'Acqwired Database',
        description: 'Filter set of companies from salesforce',
        sourceType: ListSourceType.companyFilter,
        icon: 'filter'
      }
    ]
  },
  {
    id: 'location-based',
    name: 'Location based',
    description: 'Find companies by location',
    sources: [
      {
        id: 'google-maps',
        name: 'Google maps',
        description: 'Find Companies by location with Google Maps',
        sourceType: ListSourceType.googleMaps,
        icon: 'map'
      },
      {
        id: 'sourcescrub-location',
        name: 'SourceScrub',
        description: 'Find Companies by location with SourceScrub',
        sourceType: ListSourceType.sourceScrub,
        icon: 'sourcescrub'
      }
    ]
  }
];

module.exports = intentions;
