{"verbose": false, "watch": ["src/**/*.ts", "src/**/*.js", "src/**/*.json", "src/*.json", "src/*.ts", "src/*.js", "./*.ts", "./*.js", "./*.json"], "ignore": ["logs/*", "node_modules/*", "dist/*", "prisma/*", "src/tests/*"], "exec": "node --inspect=0.0.0.0:9228 -r ts-node/register ./src/index.ts", "pollingInterval": 100, "delay": 100, "legacyWatch": true, "events": {"restart": "echo 'Nodemon restart event triggered!'"}, "ext": "ts,js,json"}