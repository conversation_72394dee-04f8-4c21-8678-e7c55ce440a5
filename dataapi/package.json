{"version": "1.0.0", "author": "<PERSON><PERSON><PERSON>", "main": "src/index.ts", "scripts": {"dev": "nodemon --config /dataapi/nodemon.json", "build": "tsc", "start": "ts-node ./src/index.ts", "test": "mocha", "test:watch": "mocha --watch", "test:coverage": "nyc mocha", "lint": "./node_modules/.bin/eslint ./src --ignore-pattern '*.config.js'"}, "dependencies": {"@microsoft/microsoft-graph-client": "^3.0.7", "@prisma/client": "6.2.1", "@sendgrid/mail": "^7.7.0", "@types/amqplib": "^0.10.1", "@types/cors": "^2.8.17", "amqplib": "^0.10.3", "bcryptjs": "^2.4.3", "bluebird": "^3.7.2", "cors": "^2.8.5", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "csvtojson": "^2.0.10", "dayjs": "^1.11.10", "device-detector-js": "^3.0.1", "dotenv": "^16.3.1", "express": "^4.18.1", "firebase-admin": "12.7.0", "google-auth-library": "^9.10.0", "handlebars": "^4.7.8", "html-to-text": "^9.0.5", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "multer": "^1.4.5-lts.1", "papaparse": "^5.4.1", "pino": "^8.6.1", "pino-http": "^8.2.0", "prisma": "6.2.1", "resolve": "^1.22.8", "tldts": "^6.0.14", "uuid": "^11.0.3", "openai": "5.0.1"}, "devDependencies": {"@types/chai": "^4.3.1", "@types/chai-as-promised": "^8.0.1", "@types/express": "^4.17.21", "@types/html-to-text": "^9.0.4", "@types/json2csv": "^5.0.7", "@types/lodash": "^4.14.202", "@types/mocha": "^9.1.1", "@types/multer": "^1.4.11", "@types/node": "^20.11.16", "@types/sinon": "^10.0.12", "@types/uuid": "8.3.2", "@typescript-eslint/eslint-plugin": " 8.16.0", "@typescript-eslint/parser": "8.16.0", "chai": "^4.3.6", "eslint": "9.16.0", "eslint-define-config": "2.1.0", "mocha": "^10.0.0", "nodemon": "^3.0.3", "nyc": "^17.1.0", "prettier": "3.4.2", "sinon": "^14.0.0", "ts-mocha": "^11.1.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.7.2", "typescript-eslint": "8.18.1"}, "eslintConfig": "eslint.config.mjs"}