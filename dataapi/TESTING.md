# DataAPI Testing Guide

This guide provides comprehensive information about testing the DataAPI service, including setup, execution, and troubleshooting.

## Overview

The DataAPI uses integration tests that connect to a real test database to ensure comprehensive testing of database operations, business logic, and API endpoints. The testing system is designed to provide fast, reliable, and isolated test execution.

## Test Database Architecture

### Database Structure

The testing system uses three databases:

1. **Main Database (`acqwired`)**

   - Your development database
   - Contains your working data and schema
   - Used as the source for creating test snapshots

2. **Template Database (`acqwired_template`)**

   - A snapshot/template of the main database
   - Created from the main database using `make create-test-snapshot`
   - Used as a fast source for resetting the test database

3. **Test Database (`acqwired_test`)**
   - Isolated test environment
   - Reset before each test suite runs
   - Contains the same schema and seed data as the main database

### Why This Architecture?

- **Speed**: Creating a database from a template is much faster than running migrations and seeds
- **Isolation**: Each test runs against a clean, predictable database state
- **Reliability**: Tests don't interfere with each other or your development data
- **Consistency**: All tests run against the same baseline data

## Getting Started

### Prerequisites

1. **Docker Environment**: Ensure your local development environment is running:

   ```bash
   make start
   ```

2. **Database Setup**: Your main database should be properly migrated and seeded:
   ```bash
   cd postgresql
   ./node_modules/.bin/prisma migrate deploy
   ./node_modules/.bin/prisma db seed
   ```

### Initial Setup

**Create Test Database Snapshot** (Required before first test run):

```bash
make create-test-snapshot
```

This command:

- Terminates all connections to the main database
- Creates a template database (`acqwired_template`) as a copy of the main database
- Prepares the system for fast test database resets

⚠️ **Important**: Run this command whenever you:

- Set up the project for the first time
- Make significant changes to your database schema
- Update seed data that tests depend on
- Notice tests failing due to missing or outdated data

## Running Tests

### Basic Test Execution

```bash
# Run all dataapi tests
make dataapi-test

# Alternative: Run from dataapi directory
cd dataapi
NODE_ENV=test npm test
```

### Test Output

The test runner provides detailed output including:

- Database connection and reset status
- Individual test results with timing
- Error details for failing tests
- Test coverage summary

Example output:

```
Restoring test database from template...
Test database reset complete

✔ ScoringModel Router (85 tests)
✔ ListCompanyIdea Service (16 tests)
✔ ScoringModel Service (42 tests)

85 passing (4s)
```

## Test Structure

### Test Organization

```
dataapi/src/tests/
├── helpers/           # Test utilities and data helpers
│   ├── DBHelpers.ts   # Database test utilities
│   ├── DBManager.ts   # Database reset management
│   └── *.ts          # Test data factories
├── routers/          # API endpoint tests
│   └── *.test.ts
└── services/         # Business logic tests
    └── *.test.ts
```

### Test Categories

1. **Router Tests**: Test HTTP endpoints, request/response handling, authentication
2. **Service Tests**: Test business logic, data validation, complex operations
3. **Integration Tests**: Test complete workflows across multiple components

## Database Reset Mechanism

### How It Works

1. **Before Each Test Suite**:

   - Current test database connections are terminated
   - Test database is dropped and recreated from template
   - Fresh database connection is established

2. **During Tests**:

   - Tests create and manipulate data as needed
   - Each test suite is isolated from others

3. **After Each Test Suite**:
   - Database is reset again for the next suite
   - Connections are properly closed

### Reset Process Details

The reset process is handled by:

- `DBManager.resetDB()` - Orchestrates the reset process
- `postgresql/scripts/pg-test-helper.sh` - Handles database operations
- `make reset-testdb` - Makefile target for manual resets

## Configuration

### Environment Variables

Tests use these environment variables (from `dataapi/.env`):

```bash
NODE_ENV=test                    # Enables test mode
TEST_DATABASE_URL="postgresql://acqwired:acqwired@localhost:5432/acqwired_test?schema=public"
DATABASE_URL="**********************************************/acqwired?schema=public"
```

### Test Configuration Files

- `.mocharc.json` - Mocha test runner configuration
- `package.json` - Test scripts and dependencies
- `tsconfig.json` - TypeScript configuration for tests

## Troubleshooting

### Common Issues

#### 1. "Template database doesn't exist"

**Error**: Tests fail with template database not found

**Solution**:

```bash
make create-test-snapshot
```

#### 2. "Connection refused" or Database Connection Errors

**Possible Causes**:

- Docker containers not running
- Database not accessible from test environment

**Solutions**:

```bash
# Ensure containers are running
make start

# Check container status
docker ps

# Check database connectivity
docker exec -it postgresql psql -U acqwired -d acqwired_test
```

#### 3. Tests Failing Due to Missing Data

**Cause**: Template database is outdated

**Solution**:

```bash
# Recreate template with current data
make create-test-snapshot
```

#### 4. "Database is being accessed by other users"

**Cause**: Active connections preventing database operations

**Solution**: The system automatically handles this, but if issues persist:

```bash
# Manually reset test database
make reset-testdb
```

### Manual Database Operations

#### Reset Test Database Manually

```bash
make reset-testdb
```

#### Recreate Template Database

```bash
make create-test-snapshot
```

#### Connect to Test Database

```bash
docker exec -it postgresql psql -U acqwired -d acqwired_test
```

## Best Practices

### Writing Tests

1. **Use Test Helpers**: Leverage existing test utilities in `helpers/`
2. **Clean Data**: Each test should create its own test data
3. **Isolation**: Don't rely on data from other tests
4. **Descriptive Names**: Use clear, descriptive test names

### Maintaining Tests

1. **Update Snapshots**: Recreate test snapshots after schema changes
2. **Monitor Performance**: Keep test execution time reasonable
3. **Regular Cleanup**: Remove obsolete tests and test data

### Development Workflow

1. Make code changes
2. Run tests: `make dataapi-test`
3. If schema changed: `make create-test-snapshot`
4. Commit changes including test updates

## Performance Considerations

- **Template Creation**: Takes 10-30 seconds, done infrequently
- **Database Reset**: Takes 1-3 seconds per test suite
- **Test Execution**: Varies by test complexity
- **Total Runtime**: Typically 3-5 seconds for full test suite

## Integration with CI/CD

The test system is designed to work in CI/CD environments:

- Automated template creation
- Reliable database state management
- Comprehensive error reporting
- Exit codes for build systems

For production deployments, ensure the CI environment has:

- Access to PostgreSQL
- Proper environment variables
- Sufficient permissions for database operations

## Advanced Topics

### Custom Test Data

The testing system provides utilities for creating test data:

```typescript
// Example from ListCompanyIdea tests
import { createTestSource, createTestIdea, createTestMappings } from '../helpers/ListCompanyIdeaData';

// Create test source
const source = await createTestSource(testListCompanyId, intentionId);

// Create test idea
const idea = await createTestIdea(context, 'Test Company', 'www.example.com');

// Create mappings
await createTestMappings([idea.id], source.id, testListCompanyId);
```

### Mocking External Services

Tests use Sinon.js for mocking external dependencies:

```typescript
import sinon from 'sinon';
import NotificationService from '../../services/Notification';

// Create mock service
const notificationServiceStub = sinon.createStubInstance(NotificationService);
(notificationServiceStub.updateData as sinon.SinonStub) = sinon.stub().resolves(true);

// Use in service
const service = new ScoringModelService({
  auth,
  notificationService: notificationServiceStub as unknown as NotificationService,
  writeResultsToSocket: true
});
```

### Database Schema Validation

Tests automatically validate that:

- Database schema matches Prisma schema
- Required seed data exists
- Foreign key relationships are intact
- Indexes are properly created

## Monitoring and Debugging

### Test Logging

Tests use structured logging for debugging:

```bash
# View test logs with debug level
NODE_ENV=test LOG_LEVEL=debug npm test
```

### Database State Inspection

During test development, you can inspect the test database:

```bash
# Connect to test database
docker exec -it postgresql psql -U acqwired -d acqwired_test

# List tables
\dt

# Check specific data
SELECT * FROM "Organization" LIMIT 5;
```

### Performance Profiling

Monitor test performance:

```bash
# Run tests with timing information
NODE_ENV=test npm test -- --reporter spec
```

## Maintenance Schedule

### Regular Tasks

- **Weekly**: Review test execution times and optimize slow tests
- **Monthly**: Update test snapshots if schema has evolved
- **Per Release**: Validate all tests pass and update documentation

### Schema Migration Impact

When database schema changes:

1. Update Prisma schema
2. Run migrations on development database
3. Recreate test snapshot: `make create-test-snapshot`
4. Update affected tests
5. Verify all tests pass

## Support and Resources

### Getting Help

- Check this documentation first
- Review test logs for specific error messages
- Inspect database state during test failures
- Consult team members familiar with the testing system

### Useful Commands Reference

```bash
# Setup and maintenance
make create-test-snapshot    # Create/update test database template
make reset-testdb           # Reset test database manually
make dataapi-test          # Run all tests

# Development
cd dataapi && NODE_ENV=test npm test                    # Run tests from dataapi directory
cd dataapi && NODE_ENV=test npm test -- --grep "pattern" # Run specific tests
cd dataapi && NODE_ENV=test LOG_LEVEL=debug npm test    # Run with debug logging

# Database inspection
docker exec -it postgresql psql -U acqwired -d acqwired_test        # Connect to test DB
docker exec -it postgresql psql -U acqwired -d acqwired_template    # Connect to template DB
docker exec -it postgresql psql -U acqwired -d acqwired             # Connect to main DB
```
