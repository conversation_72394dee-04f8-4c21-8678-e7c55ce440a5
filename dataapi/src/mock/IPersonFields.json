[{"id": 119385, "name": "Job Titles", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 119386, "name": "Industry", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 119388, "name": "Location", "list_id": null, "enrichment_source": "affinity-data", "value_type": 5, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 119389, "name": "Source of Introduction", "list_id": null, "enrichment_source": "none", "value_type": 0, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 119387, "name": "Phone Number", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 119390, "name": "LinkedIn URL", "list_id": null, "enrichment_source": "affinity-data", "value_type": 6, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 3044454, "name": "Current Job Title", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308389, "name": "LinkedIn Profile Headline", "list_id": null, "enrichment_source": "affinity-data", "value_type": 6, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308388, "name": "Keywords", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 4308387, "name": "Current Organization Headcount", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308386, "name": "Education", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 4308385, "name": "Years of Experience", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308384, "name": "Current Job Functions", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 4308383, "name": "Current Job Seniorities", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 4308382, "name": "Current Job Start Date", "list_id": null, "enrichment_source": "affinity-data", "value_type": 4, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 2909538, "name": "Sector", "list_id": null, "enrichment_source": "none", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": [{"id": 6046487, "text": "Asset & Wealth Management", "rank": 0, "color": 0}, {"id": 6046488, "text": "Banking", "rank": 0, "color": 0}, {"id": 6046489, "text": "Capital Markets", "rank": 0, "color": 0}, {"id": 6046497, "text": "CFO-Suite Tools", "rank": 0, "color": 0}, {"id": ********, "text": "Consumer", "rank": 0, "color": 0}, {"id": 6046501, "text": "Cybersecurity", "rank": 0, "color": 0}, {"id": 6046502, "text": "ESG", "rank": 0, "color": 0}, {"id": ********, "text": "Generalist (Business Tech and Services)", "rank": 0, "color": 0}, {"id": ********, "text": "Generalist (Fintech / Financial Services)", "rank": 0, "color": 0}, {"id": 6046503, "text": "GRC", "rank": 0, "color": 0}, {"id": 6046517, "text": "HealthTech & Services", "rank": 0, "color": 0}, {"id": 6046518, "text": "Human Capital Management", "rank": 0, "color": 0}, {"id": 6046491, "text": "InsureTech & Services", "rank": 0, "color": 0}, {"id": 6046519, "text": "Marketing & Sales & Ad Technology", "rank": 0, "color": 0}, {"id": 6046493, "text": "Payments (B2B & B2C)", "rank": 0, "color": 0}, {"id": ********, "text": "Supply Chain & Logistics Software", "rank": 0, "color": 0}, {"id": 11373091, "text": "Workflow and Process Automation", "rank": 0, "color": 0}]}, {"id": 3723731, "name": "Function", "list_id": null, "enrichment_source": "none", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": [{"id": 10142384, "text": "Board", "rank": 0, "color": 0}, {"id": 10142387, "text": "Executive", "rank": 0, "color": 0}, {"id": 10142386, "text": "Finance", "rank": 0, "color": 0}, {"id": 10142385, "text": "Legal", "rank": 0, "color": 0}, {"id": 10142383, "text": "Operations", "rank": 0, "color": 0}, {"id": 10142382, "text": "Product", "rank": 0, "color": 0}, {"id": 10142381, "text": "Sales & Marketing", "rank": 0, "color": 0}, {"id": 10142388, "text": "Strategy / Biz Dev", "rank": 0, "color": 0}]}, {"id": 4537443, "name": "Distributed Swag", "list_id": null, "enrichment_source": "none", "value_type": 6, "allows_multiple": false, "track_changes": false, "dropdown_options": []}]