[{"id": 160480, "name": "Deal Start Date", "list_id": null, "enrichment_source": "none", "value_type": 4, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 318512, "name": "Financing Status", "list_id": null, "enrichment_source": "none", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": [{"id": 1233664, "text": "Accelerator/Incubator Backed", "rank": 0, "color": 0}, {"id": 1233686, "text": "<PERSON>-<PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 1233662, "text": "Corporate Backed Or Acquired", "rank": 0, "color": 0}, {"id": 1233674, "text": "Corporation", "rank": 0, "color": 0}, {"id": 1252774, "text": "Formerly Accelerator/Incubator Backed", "rank": 0, "color": 0}, {"id": 1252748, "text": "Formerly <PERSON> Backed", "rank": 0, "color": 0}, {"id": 1233656, "text": "Formerly PE-Backed", "rank": 0, "color": 0}, {"id": 1233648, "text": "Formerly VC-backed", "rank": 0, "color": 0}, {"id": 1233692, "text": "Pending Transaction (<PERSON>)", "rank": 0, "color": 0}, {"id": 1233701, "text": "Pending Transaction (M&A)", "rank": 0, "color": 0}, {"id": 1265465, "text": "Pending Transaction (VC)", "rank": 0, "color": 0}, {"id": 1233666, "text": "Private Equity-Backed", "rank": 0, "color": 0}, {"id": 1233653, "text": "Venture Capital-Backed", "rank": 0, "color": 0}]}, {"id": 318513, "name": "Ownership Status", "list_id": null, "enrichment_source": "none", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": [{"id": 1233649, "text": "Acquired/Merged", "rank": 0, "color": 0}, {"id": 1233676, "text": "Acquired/Merged (Operating Subsidiary)", "rank": 0, "color": 0}, {"id": 1286956, "text": "Edu Institution", "rank": 0, "color": 0}, {"id": 1286954, "text": "Gov Agency", "rank": 0, "color": 0}, {"id": 1265508, "text": "In IPO Registration", "rank": 0, "color": 0}, {"id": 2315703, "text": "Investment Company", "rank": 0, "color": 0}, {"id": 1286955, "text": "Joint Venture", "rank": 0, "color": 0}, {"id": 1233964, "text": "Non-Profit", "rank": 0, "color": 0}, {"id": 1233956, "text": "Other (Retired)", "rank": 0, "color": 0}, {"id": 1233699, "text": "Out Of Business", "rank": 0, "color": 0}, {"id": 1233952, "text": "Private", "rank": 0, "color": 0}, {"id": 1233955, "text": "Private Equity", "rank": 0, "color": 0}, {"id": 1233654, "text": "Privately <PERSON> (backing)", "rank": 0, "color": 0}, {"id": 1233675, "text": "Privately Held (no Backing)", "rank": 0, "color": 0}, {"id": 1233957, "text": "Private Sub", "rank": 0, "color": 0}, {"id": 1233954, "text": "Public", "rank": 0, "color": 0}, {"id": 1233657, "text": "Publicly Held", "rank": 0, "color": 0}, {"id": 1233959, "text": "Public Sub", "rank": 0, "color": 0}, {"id": 1233953, "text": "Seed", "rank": 0, "color": 0}, {"id": 1233951, "text": "Venture Capital", "rank": 0, "color": 0}]}, {"id": 447758, "name": "LREP Connection", "list_id": null, "enrichment_source": "none", "value_type": 6, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 160467, "name": "Secondary Raise Amount ($mm)", "list_id": null, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 160477, "name": "Organization Type", "list_id": null, "enrichment_source": "none", "value_type": 7, "allows_multiple": false, "track_changes": true, "dropdown_options": [{"id": 140171, "text": "Company", "rank": 1, "color": 1}, {"id": 140172, "text": "VC / PE", "rank": 2, "color": 1}, {"id": 140173, "text": "Banker", "rank": 3, "color": 1}, {"id": 140174, "text": "<PERSON><PERSON>", "rank": 4, "color": 1}, {"id": 140175, "text": "Lawyer", "rank": 5, "color": 1}, {"id": 140176, "text": "Portfolio Company", "rank": 6, "color": 1}, {"id": 140177, "text": "Other", "rank": 7, "color": 1}]}, {"id": 159650, "name": "VC Referral List Suitable?", "list_id": null, "enrichment_source": "none", "value_type": 7, "allows_multiple": false, "track_changes": true, "dropdown_options": [{"id": 139463, "text": "Yes - Raising Now", "rank": 1, "color": 3}, {"id": 139464, "text": "Yes - Not Raising, but Cool Company", "rank": 2, "color": 3}, {"id": 139486, "text": "No", "rank": 3, "color": 1}]}, {"id": 160481, "name": "Term Sheet Signed Date", "list_id": null, "enrichment_source": "none", "value_type": 4, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 160482, "name": "Bid Submitted Date", "list_id": null, "enrichment_source": "none", "value_type": 4, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 160474, "name": "Growth (%)", "list_id": null, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 160470, "name": "Fiscal Year", "list_id": null, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 160479, "name": "Killed Date", "list_id": null, "enrichment_source": "none", "value_type": 4, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 160466, "name": "Primary Raise Amount ($mm)", "list_id": null, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 160547, "name": "Date Created", "list_id": null, "enrichment_source": "none", "value_type": 4, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 160514, "name": "High Priority", "list_id": null, "enrichment_source": "none", "value_type": 7, "allows_multiple": false, "track_changes": true, "dropdown_options": [{"id": 140205, "text": "Yes", "rank": 1, "color": 3}, {"id": 140206, "text": "No", "rank": 2, "color": 1}]}, {"id": 447756, "name": "FTE", "list_id": null, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 447757, "name": "Key People", "list_id": null, "enrichment_source": "none", "value_type": 6, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 466623, "name": "Metro", "list_id": null, "enrichment_source": "none", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": [{"id": 1735028, "text": "Atlanta, GA", "rank": 0, "color": 0}, {"id": 1735029, "text": "Austin, TX", "rank": 0, "color": 0}, {"id": 1783332, "text": "Birmingham, AL", "rank": 0, "color": 0}, {"id": 1735030, "text": "Boston, MA", "rank": 0, "color": 0}, {"id": 1735031, "text": "Calgary, CA", "rank": 0, "color": 0}, {"id": 1735032, "text": "Chicago, IL", "rank": 0, "color": 0}, {"id": 1735033, "text": "Cincinnati, OH", "rank": 0, "color": 0}, {"id": 1735034, "text": "Cleveland, OH", "rank": 0, "color": 0}, {"id": 1735035, "text": "Dallas, TX", "rank": 0, "color": 0}, {"id": 1735036, "text": "DC", "rank": 0, "color": 0}, {"id": 1735037, "text": "Denver, CO", "rank": 0, "color": 0}, {"id": 1735038, "text": "Des Moines, IO", "rank": 0, "color": 0}, {"id": 1735039, "text": "Detroit, MI", "rank": 0, "color": 0}, {"id": 1735040, "text": "Dublin, IE", "rank": 0, "color": 0}, {"id": 1735041, "text": "Hartford, CT", "rank": 0, "color": 0}, {"id": 1735042, "text": "Holland", "rank": 0, "color": 0}, {"id": 1735043, "text": "Houston, TX", "rank": 0, "color": 0}, {"id": 1735044, "text": "Israel", "rank": 0, "color": 0}, {"id": 1735045, "text": "Jacksonville, FL", "rank": 0, "color": 0}, {"id": 1735046, "text": "Kansas City, MO", "rank": 0, "color": 0}, {"id": 1735047, "text": "LA, CA", "rank": 0, "color": 0}, {"id": 1735048, "text": "London, UK", "rank": 0, "color": 0}, {"id": 1735049, "text": "Miami, FL", "rank": 0, "color": 0}, {"id": 1735050, "text": "Minneapolis, MN", "rank": 0, "color": 0}, {"id": 1735051, "text": "Missoula, MT", "rank": 0, "color": 0}, {"id": 1735052, "text": "Montreal, CA", "rank": 0, "color": 0}, {"id": 1735053, "text": "Mumbai, IN", "rank": 0, "color": 0}, {"id": 1735054, "text": "New Hampshire", "rank": 0, "color": 0}, {"id": 1735055, "text": "New Zealand", "rank": 0, "color": 0}, {"id": 1735056, "text": "NYC, NY", "rank": 0, "color": 0}, {"id": 1735057, "text": "Omaha, NB", "rank": 0, "color": 0}, {"id": 1735058, "text": "Ottowa, CA", "rank": 0, "color": 0}, {"id": 1735059, "text": "Paris, FR", "rank": 0, "color": 0}, {"id": 1735060, "text": "Parsippany, NJ", "rank": 0, "color": 0}, {"id": 1735061, "text": "Philadelphia, PA", "rank": 0, "color": 0}, {"id": 1735062, "text": "Phoenix, AZ", "rank": 0, "color": 0}, {"id": 1735063, "text": "Portland, OR", "rank": 0, "color": 0}, {"id": 1735064, "text": "Quebec City, CA", "rank": 0, "color": 0}, {"id": 1735065, "text": "Richmond, VA", "rank": 0, "color": 0}, {"id": 1735066, "text": "San Diego, CA", "rank": 0, "color": 0}, {"id": 1735067, "text": "San Luis Obispo, CA", "rank": 0, "color": 0}, {"id": 1735068, "text": "SF, CA", "rank": 0, "color": 0}, {"id": 1735069, "text": "SLC, UT", "rank": 0, "color": 0}, {"id": 1735070, "text": "Switzerland", "rank": 0, "color": 0}, {"id": 1735071, "text": "Toronto, CA", "rank": 0, "color": 0}, {"id": 1735072, "text": "Vancouver, CA", "rank": 0, "color": 0}]}, {"id": 119375, "name": "Investment Stage", "list_id": null, "enrichment_source": "crunchbase", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 119380, "name": "Year Founded (CB)", "list_id": null, "enrichment_source": "crunchbase", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 119381, "name": "Total Funding Amount (USD)", "list_id": null, "enrichment_source": "crunchbase", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 119382, "name": "Location (CB)", "list_id": null, "enrichment_source": "crunchbase", "value_type": 5, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 119383, "name": "Last Funding Date", "list_id": null, "enrichment_source": "crunchbase", "value_type": 4, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 119384, "name": "Source of Introduction", "list_id": null, "enrichment_source": "none", "value_type": 0, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 407529, "name": "Last Funding Amount (USD)", "list_id": null, "enrichment_source": "crunchbase", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 877075, "name": "Active Work 2019-2020", "list_id": null, "enrichment_source": "none", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": [{"id": 3002662, "text": "Yes", "rank": 0, "color": 0}]}, {"id": 802769, "name": "Description", "list_id": null, "enrichment_source": "affinity-data", "value_type": 6, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 802771, "name": "Location", "list_id": null, "enrichment_source": "affinity-data", "value_type": 5, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 802772, "name": "Number of Employees", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 802773, "name": "Year Founded", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 802770, "name": "Industry", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 830202, "name": "Total Funding Amount (USD)", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 830200, "name": "Last Funding Amount (USD)", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 830201, "name": "Last Funding Date", "list_id": null, "enrichment_source": "affinity-data", "value_type": 4, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 830198, "name": "Investment Stage", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 830199, "name": "Investors", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 1087370, "name": "LinkedIn", "list_id": null, "enrichment_source": "none", "value_type": 6, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 1087365, "name": "9 Mo Growth Rate (SS)", "list_id": null, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 1087359, "name": "Growth Intent (SS)", "list_id": null, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 119378, "name": "Description (CB)", "list_id": null, "enrichment_source": "crunchbase", "value_type": 6, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 119377, "name": "<PERSON><PERSON><PERSON> (CB)", "list_id": null, "enrichment_source": "crunchbase", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 119376, "name": "Industry (CB)", "list_id": null, "enrichment_source": "crunchbase", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 119379, "name": "Number of Employees (CB)", "list_id": null, "enrichment_source": "crunchbase", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 1087366, "name": "6 Mo Growth Rate (SS)", "list_id": null, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 1087367, "name": "3 Mo Growth Rate (SS)", "list_id": null, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 1087369, "name": "Total Amount Invested (SS)", "list_id": null, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 1087375, "name": "Investors (SS)", "list_id": null, "enrichment_source": "none", "value_type": 6, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 422198, "name": "Strong Prospect (To be deleted)", "list_id": null, "enrichment_source": "none", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": [{"id": 1550952, "text": "No", "rank": 0, "color": 0}, {"id": 1550951, "text": "Yes", "rank": 0, "color": 0}]}, {"id": 4117103, "name": "Tier A Senior Sponsor", "list_id": null, "enrichment_source": "none", "value_type": 0, "allows_multiple": true, "track_changes": true, "dropdown_options": []}, {"id": 657627, "name": "Sub-sector", "list_id": null, "enrichment_source": "none", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": [{"id": 2395890, "text": "B2B Payments", "rank": 0, "color": 0}, {"id": 5726364, "text": "Capital Markets", "rank": 0, "color": 0}, {"id": 2395817, "text": "<PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 8226720, "text": "GRC - Compliance & Licensing", "rank": 0, "color": 0}, {"id": 8226726, "text": "GRC - Contractor Compliance", "rank": 0, "color": 0}, {"id": 8226724, "text": "GRC - EHSQ", "rank": 0, "color": 0}, {"id": 8226725, "text": "GRC - ESG", "rank": 0, "color": 0}, {"id": 8226719, "text": "GRC - Financial Crimes", "rank": 0, "color": 0}, {"id": 8226728, "text": "GRC - General / All-in-one", "rank": 0, "color": 0}, {"id": 8226721, "text": "GRC - ID / Fraud", "rank": 0, "color": 0}, {"id": 8226722, "text": "GRC - Regulatory Content & Reporting", "rank": 0, "color": 0}, {"id": 8226727, "text": "GRC - TPRM", "rank": 0, "color": 0}, {"id": 2395913, "text": "Infrastructure", "rank": 0, "color": 0}, {"id": 2395820, "text": "Integrated Payments", "rank": 0, "color": 0}, {"id": 2395818, "text": "Open Banking", "rank": 0, "color": 0}, {"id": 2395888, "text": "Processing", "rank": 0, "color": 0}, {"id": 2395891, "text": "Security", "rank": 0, "color": 0}]}, {"id": 1087368, "name": "FTE Growth % (SS)", "list_id": null, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 1087364, "name": "Raise Date (SS)", "list_id": null, "enrichment_source": "none", "value_type": 4, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 1087362, "name": "Latest FTE", "list_id": null, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 1087363, "name": "Most Recent Raise (SS)", "list_id": null, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 2847489, "name": "Ownership Types", "list_id": null, "enrichment_source": "dealroom", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 2847494, "name": "Business Models", "list_id": null, "enrichment_source": "dealroom", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 2847496, "name": "Total Funding Amount (EUR)", "list_id": null, "enrichment_source": "dealroom", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 2847483, "name": "Last Month Twitter Followers", "list_id": null, "enrichment_source": "dealroom", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 2847499, "name": "Industry", "list_id": null, "enrichment_source": "dealroom", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 2847487, "name": "Technologies", "list_id": null, "enrichment_source": "dealroom", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 2847492, "name": "Corporate Industries", "list_id": null, "enrichment_source": "dealroom", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 2847497, "name": "Year Founded", "list_id": null, "enrichment_source": "dealroom", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 2847498, "name": "Description", "list_id": null, "enrichment_source": "dealroom", "value_type": 6, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 2847484, "name": "Last Month Change in Twitter Followers", "list_id": null, "enrichment_source": "dealroom", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 2847488, "name": "Service Industries", "list_id": null, "enrichment_source": "dealroom", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 2847482, "name": "Dealroom.co URL", "list_id": null, "enrichment_source": "dealroom", "value_type": 6, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 2847491, "name": "Number of Employees", "list_id": null, "enrichment_source": "dealroom", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 2847486, "name": "Total Tweets", "list_id": null, "enrichment_source": "dealroom", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 2847485, "name": "Last Month Twitter Favorites", "list_id": null, "enrichment_source": "dealroom", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 2847490, "name": "Income Streams", "list_id": null, "enrichment_source": "dealroom", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 2847493, "name": "Client Focus", "list_id": null, "enrichment_source": "dealroom", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 2847495, "name": "Last Funding Amount (EUR)", "list_id": null, "enrichment_source": "dealroom", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 2847500, "name": "Location", "list_id": null, "enrichment_source": "dealroom", "value_type": 5, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 159649, "name": "Keep Escalate", "list_id": null, "enrichment_source": "none", "value_type": 7, "allows_multiple": false, "track_changes": true, "dropdown_options": [{"id": 139460, "text": "Yes", "rank": 1, "color": 1}, {"id": 139510, "text": "No", "rank": 2, "color": 0}]}, {"id": 3658679, "name": "LinkedIn URL", "list_id": null, "enrichment_source": "affinity-data", "value_type": 6, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 160468, "name": "Partner - TO BE DELETED", "list_id": null, "enrichment_source": "none", "value_type": 0, "allows_multiple": true, "track_changes": true, "dropdown_options": []}, {"id": 160471, "name": "Quarterback - TO BE DELETED", "list_id": null, "enrichment_source": "none", "value_type": 0, "allows_multiple": true, "track_changes": true, "dropdown_options": []}, {"id": 160465, "name": "Associate - TO BE DELETED", "list_id": null, "enrichment_source": "none", "value_type": 0, "allows_multiple": true, "track_changes": true, "dropdown_options": []}, {"id": 4308390, "name": "LinkedIn Profile (Founders/CEOs)", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 4308391, "name": "Employees: Growth YoY (%)", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308392, "name": "Employees: Growth QoQ (%)", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308397, "name": "Employee Departures: Last 3 Months (#)", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308398, "name": "Employee Hires: Last 3 Months (Leadership)", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 4308399, "name": "Employee Departures: Last 3 Months (Leadership)", "list_id": null, "enrichment_source": "affinity-data", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": []}, {"id": 4308400, "name": "Employees: 24 Months Ago", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308401, "name": "Employees: 12 Months Ago", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308402, "name": "Employees: 6 Months Ago", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308403, "name": "Employees: 3 Months Ago", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308404, "name": "Employees: 1 Month Ago", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308405, "name": "Employees (Current)", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308393, "name": "Employees: Growth MoM (%)", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308394, "name": "Employee Hires: Last 3 Months (%)", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308395, "name": "Employee Hires: Last 3 Months (#)", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 4308396, "name": "Employee Departures: Last 3 Months (%)", "list_id": null, "enrichment_source": "affinity-data", "value_type": 3, "allows_multiple": false, "track_changes": false, "dropdown_options": []}]