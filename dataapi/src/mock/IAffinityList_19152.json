{"id": 19152, "type": 1, "name": "Long Ridge Master", "public": true, "owner_id": 14023074, "creator_id": 14105928, "list_size": 21702, "fields": [{"id": 158784, "name": "Pipeline Stage", "list_id": 19152, "enrichment_source": "none", "value_type": 7, "allows_multiple": false, "track_changes": true, "dropdown_options": [{"id": 235449, "text": "0) Unengaged", "rank": 1, "color": 2}, {"id": 235452, "text": "1) Engaged", "rank": 2, "color": 2}, {"id": 9851011, "text": "2) Quality Interaction (Meeting / Call)", "rank": 3, "color": 2}, {"id": 235453, "text": "3) Actionable Deal Opportunity", "rank": 4, "color": 1}, {"id": 235456, "text": "4) Presented to IC", "rank": 5, "color": 1}, {"id": 235455, "text": "5) Submitted LOI", "rank": 6, "color": 5}, {"id": 235454, "text": "6) Signed LOI", "rank": 7, "color": 5}, {"id": 235457, "text": "7) Portfolio Company", "rank": 8, "color": 3}, {"id": 235450, "text": "9) <PERSON><PERSON>", "rank": 9, "color": 6}, {"id": 235451, "text": "8) Circle-Back", "rank": 10, "color": 2}]}, {"id": 158786, "name": "LREP Point of Introduction", "list_id": 19152, "enrichment_source": "none", "value_type": 0, "allows_multiple": true, "track_changes": true, "dropdown_options": []}, {"id": 158785, "name": "Delete", "list_id": 19152, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 4310133, "name": "Personal Pipeline", "list_id": 19152, "enrichment_source": "none", "value_type": 0, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 214895, "name": "Sector Thesis", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": [{"id": 5327517, "text": "Accessibility Tech", "rank": 0, "color": 0}, {"id": 5327510, "text": "Accounting & Close", "rank": 0, "color": 0}, {"id": 5327521, "text": "AI / ML Model Governance", "rank": 0, "color": 0}, {"id": 5327500, "text": "API Management Software", "rank": 0, "color": 0}, {"id": 5327509, "text": "AR, AP, Billings, & Collections", "rank": 0, "color": 0}, {"id": 5327572, "text": "B2B Banking Technology", "rank": 0, "color": 0}, {"id": 5327580, "text": "B2B Insurance", "rank": 0, "color": 0}, {"id": 5327584, "text": "B2B Payments", "rank": 0, "color": 0}, {"id": 5327579, "text": "B2C Insurance", "rank": 0, "color": 0}, {"id": 245800, "text": "Baby Boomer Economy", "rank": 0, "color": 0}, {"id": 2843863, "text": "Business Intelligence", "rank": 0, "color": 0}, {"id": 5327533, "text": "Capital Markets Tech", "rank": 0, "color": 0}, {"id": 5327583, "text": "Card Issuance", "rank": 0, "color": 0}, {"id": 244963, "text": "CFO Suite", "rank": 0, "color": 0}, {"id": 5327528, "text": "Collections & Bankruptcy Solutions", "rank": 0, "color": 0}, {"id": 5327506, "text": "Competitive Intelligence", "rank": 0, "color": 0}, {"id": 5327577, "text": "Consumer Asset / Wealth Management", "rank": 0, "color": 0}, {"id": 5327574, "text": "Consumer Banking Technology", "rank": 0, "color": 0}, {"id": 5327531, "text": "Consumer Lending", "rank": 0, "color": 0}, {"id": 5327504, "text": "Corporate Training", "rank": 0, "color": 0}, {"id": 5327529, "text": "Counter Cyclical Economy", "rank": 0, "color": 0}, {"id": 5327573, "text": "Credit Union / SMB Software", "rank": 0, "color": 0}, {"id": 5164127, "text": "Customer Success & Service", "rank": 0, "color": 0}, {"id": 5327514, "text": "Data Security", "rank": 0, "color": 0}, {"id": 5327532, "text": "Digitization of Valuations", "rank": 0, "color": 0}, {"id": 5327502, "text": "Employee Performance & Engagement Tools", "rank": 0, "color": 0}, {"id": 5327576, "text": "Enterprise Software for Alternatives", "rank": 0, "color": 0}, {"id": 5327525, "text": "FertilityTech/FemTech", "rank": 0, "color": 0}, {"id": 5327488, "text": "Financial Services Sales Enablement", "rank": 0, "color": 0}, {"id": 5327497, "text": "Food Safety Compliance", "rank": 0, "color": 0}, {"id": 5327508, "text": "FP&A", "rank": 0, "color": 0}, {"id": 5327505, "text": "General HCM (non-financial)", "rank": 0, "color": 0}, {"id": 5327484, "text": "General Marketing Technology", "rank": 0, "color": 0}, {"id": 5327522, "text": "Government Contractor Compliance", "rank": 0, "color": 0}, {"id": 3574459, "text": "GovTech", "rank": 0, "color": 0}, {"id": 245802, "text": "GRC", "rank": 0, "color": 0}, {"id": 5327519, "text": "GRC - EHS", "rank": 0, "color": 0}, {"id": 5327518, "text": "GRC - Financial Crime", "rank": 0, "color": 0}, {"id": 244962, "text": "HC Payments", "rank": 0, "color": 0}, {"id": 5327523, "text": "Healthcare IT", "rank": 0, "color": 0}, {"id": 5327582, "text": "Healthcare Payments & RCM", "rank": 0, "color": 0}, {"id": 5327524, "text": "Healthcare Workflows & Credentialing", "rank": 0, "color": 0}, {"id": 3574458, "text": "InsurTech", "rank": 0, "color": 0}, {"id": 5327586, "text": "International Payments", "rank": 0, "color": 0}, {"id": 5327499, "text": "IT Automation & BPO", "rank": 0, "color": 0}, {"id": 5327501, "text": "IT Service Management", "rank": 0, "color": 0}, {"id": 5327495, "text": "Last Mile Delivery", "rank": 0, "color": 0}, {"id": 5327530, "text": "Lending Software", "rank": 0, "color": 0}, {"id": 5327585, "text": "Loyalty", "rank": 0, "color": 0}, {"id": 5327513, "text": "Managed Services", "rank": 0, "color": 0}, {"id": 5327526, "text": "Mortgage & CRE Software", "rank": 0, "color": 0}, {"id": 5327503, "text": "Payroll, Comp, and Benefits", "rank": 0, "color": 0}, {"id": 5327515, "text": "Penetration Testing", "rank": 0, "color": 0}, {"id": 5327520, "text": "Privacy Tech", "rank": 0, "color": 0}, {"id": 5327498, "text": "Procurement & Spend Management", "rank": 0, "color": 0}, {"id": 245801, "text": "Real Estate", "rank": 0, "color": 0}, {"id": 5327486, "text": "Real Estate Energy Monitoring", "rank": 0, "color": 0}, {"id": 5327511, "text": "Recurring Revenue Economy", "rank": 0, "color": 0}, {"id": 5327516, "text": "Recurring Tech-Enabled BPO", "rank": 0, "color": 0}, {"id": 5327578, "text": "Retirement Software", "rank": 0, "color": 0}, {"id": 5327483, "text": "Sales Enablement / Revenue Ops", "rank": 0, "color": 0}, {"id": 1109770, "text": "Supply Chain", "rank": 0, "color": 0}, {"id": 5327496, "text": "Supply Chain Compliance", "rank": 0, "color": 0}, {"id": 5327485, "text": "Supply Chain ESG", "rank": 0, "color": 0}, {"id": 5327512, "text": "Trade Finance / Digitization", "rank": 0, "color": 0}, {"id": 5327581, "text": "Value Added Payments", "rank": 0, "color": 0}, {"id": 5327527, "text": "Verticalized Real Estate SaaS", "rank": 0, "color": 0}, {"id": 5327507, "text": "Verticalized RPA / Workflow Automation", "rank": 0, "color": 0}, {"id": 5327494, "text": "Warehouse / Order Management", "rank": 0, "color": 0}, {"id": 5327487, "text": "Wealth / Asset Management ESG", "rank": 0, "color": 0}, {"id": 5327575, "text": "WealthTech Themes", "rank": 0, "color": 0}]}, {"id": 591336, "name": "NDA Signed Date", "list_id": 19152, "enrichment_source": "none", "value_type": 4, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 591337, "name": "NDA Signed By", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": [{"id": 2258824, "text": "<PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 2258825, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 2258826, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 2258827, "text": "<PERSON>", "rank": 0, "color": 0}]}, {"id": 3612056, "name": "Company Tier", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": [{"id": 11905496, "text": "C", "rank": 0, "color": 0}, {"id": 10623530, "text": "Tier A", "rank": 0, "color": 0}, {"id": 9852088, "text": "Tier B", "rank": 0, "color": 0}, {"id": 9894845, "text": "Tier C", "rank": 0, "color": 0}]}, {"id": 174153, "name": "Deal Mtg Status", "list_id": 19152, "enrichment_source": "none", "value_type": 7, "allows_multiple": false, "track_changes": true, "dropdown_options": [{"id": 151805, "text": "Update (No Materials)", "rank": 1, "color": 3}, {"id": 151806, "text": "Discussion (With Materials)", "rank": 2, "color": 5}, {"id": 151807, "text": "No Discussion", "rank": 3, "color": 6}]}, {"id": 160472, "name": "EBITDA ($mm)", "list_id": 19152, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 797358, "name": "Majority / Minority", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": [{"id": 2970257, "text": "Majority", "rank": 0, "color": 0}, {"id": 2970258, "text": "Minority", "rank": 0, "color": 0}]}, {"id": 160473, "name": "Revenue ($mm)", "list_id": 19152, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 160475, "name": "Valuation ($mm)", "list_id": 19152, "enrichment_source": "none", "value_type": 3, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 3611911, "name": "Process Dynamics", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": [{"id": 9851006, "text": "Auction", "rank": 0, "color": 0}, {"id": 9851004, "text": "Proprietary", "rank": 0, "color": 0}, {"id": 9851005, "text": "Semi-Proprietary", "rank": 0, "color": 0}]}, {"id": 3612064, "name": "Killed <PERSON>", "list_id": 19152, "enrichment_source": "none", "value_type": 6, "allows_multiple": false, "track_changes": false, "dropdown_options": []}, {"id": 319632, "name": "Introduction Source", "list_id": 19152, "enrichment_source": "none", "value_type": 1, "allows_multiple": false, "track_changes": true, "dropdown_options": []}, {"id": 159637, "name": "Introduction Type", "list_id": 19152, "enrichment_source": "none", "value_type": 7, "allows_multiple": false, "track_changes": true, "dropdown_options": [{"id": 139443, "text": "Direct Outreach", "rank": 1, "color": 2}, {"id": 140619, "text": "Banker Introduction", "rank": 2, "color": 2}, {"id": 139445, "text": "LREP Network Introduction", "rank": 3, "color": 2}]}, {"id": 4260515, "name": "HM Priority", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": [{"id": ********, "text": "No", "rank": 0, "color": 0}, {"id": ********, "text": "Yes", "rank": 0, "color": 0}]}, {"id": 3314149, "name": "To Refer?", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": [{"id": 7879826, "text": "No", "rank": 0, "color": 0}, {"id": 7879827, "text": "Yes", "rank": 0, "color": 0}]}, {"id": 3314153, "name": "Stage? (Growth/PE/VC)", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": [{"id": 7879831, "text": "Growth", "rank": 0, "color": 0}, {"id": 7961546, "text": "N/A", "rank": 0, "color": 0}, {"id": 7879830, "text": "PE", "rank": 0, "color": 0}, {"id": 7879829, "text": "VC", "rank": 0, "color": 0}]}, {"id": 797357, "name": "Check Size", "list_id": 19152, "enrichment_source": "none", "value_type": 3, "allows_multiple": true, "track_changes": true, "dropdown_options": []}, {"id": 354539, "name": "Category", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": [{"id": 2990413, "text": "Business Technology", "rank": 0, "color": 0}, {"id": 2990412, "text": "Financial Technology", "rank": 0, "color": 0}]}, {"id": 2252815, "name": "Sector", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": [{"id": 5326999, "text": "Asset & Wealth Management", "rank": 0, "color": 0}, {"id": 5327000, "text": "Banking Technology", "rank": 0, "color": 0}, {"id": 5327007, "text": "Business Services", "rank": 0, "color": 0}, {"id": 5327001, "text": "Capital Markets Technology", "rank": 0, "color": 0}, {"id": 5327008, "text": "CFO-Suite Software", "rank": 0, "color": 0}, {"id": 5327006, "text": "Cybersecurity", "rank": 0, "color": 0}, {"id": 5327009, "text": "DevOps & Infrastructure", "rank": 0, "color": 0}, {"id": 7085819, "text": "EdTech", "rank": 0, "color": 0}, {"id": 5327014, "text": "ESG", "rank": 0, "color": 0}, {"id": 7085820, "text": "GovTech", "rank": 0, "color": 0}, {"id": 5327005, "text": "GRC Software", "rank": 0, "color": 0}, {"id": 5327010, "text": "HCM Software", "rank": 0, "color": 0}, {"id": 5327004, "text": "HealthTech & Services", "rank": 0, "color": 0}, {"id": 5326998, "text": "InsureTech & Services", "rank": 0, "color": 0}, {"id": 5327013, "text": "Marketing & Sales & Ad Technology", "rank": 0, "color": 0}, {"id": 9852084, "text": "Other Horizontal Software", "rank": 0, "color": 0}, {"id": 5327015, "text": "Other Vertical Software", "rank": 0, "color": 0}, {"id": 5326997, "text": "Payments (B2B & B2C)", "rank": 0, "color": 0}, {"id": 5327003, "text": "Real Estate Technology", "rank": 0, "color": 0}, {"id": 13055186, "text": "security", "rank": 0, "color": 0}, {"id": 5327002, "text": "Specialty Finance", "rank": 0, "color": 0}, {"id": 5327011, "text": "Supply Chain & Logistics Software", "rank": 0, "color": 0}]}, {"id": 3651836, "name": "Senior Team (QB(s)/Partner(s))", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": [{"id": 10003223, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 10003221, "text": "<PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 10003225, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 10003224, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 13815834, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 10003222, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 10003219, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 10003220, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 14053207, "text": "<PERSON>", "rank": 0, "color": 0}]}, {"id": 3652527, "name": "Associate", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": [{"id": 11031243, "text": "<PERSON><PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 10011286, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 11178341, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 13815827, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 10011279, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 13815828, "text": "NA", "rank": 0, "color": 0}, {"id": 10011285, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 10011281, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 10011284, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 10011282, "text": "<PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 11178340, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 10011283, "text": "<PERSON>", "rank": 0, "color": 0}]}, {"id": 3652241, "name": "Add-On / Platform?", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": true, "track_changes": false, "dropdown_options": [{"id": 10021822, "text": "Add-On", "rank": 0, "color": 0}, {"id": 10021823, "text": "Platform", "rank": 0, "color": 0}]}, {"id": 3611910, "name": "LREP Tracking Coverage", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": [{"id": 9852244, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 10178603, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9852279, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9852302, "text": "<PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 9850988, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9850987, "text": "<PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 9852254, "text": "<PERSON><PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 11049754, "text": "<PERSON><PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 9852280, "text": "<PERSON><PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 9850995, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 12633531, "text": "<PERSON>, <PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 9852250, "text": "<PERSON><PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 11125348, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 10011644, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9852255, "text": "<PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 12633532, "text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 12633533, "text": "<PERSON><PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 12633534, "text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 12633535, "text": "<PERSON><PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 9852285, "text": "<PERSON><PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 9850989, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 12633536, "text": "<PERSON>, <PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 12633537, "text": "<PERSON>, <PERSON><PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 12633538, "text": "<PERSON>, <PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 12633539, "text": "<PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 12633540, "text": "<PERSON>, <PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 12633541, "text": "<PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 9850990, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 12633542, "text": "<PERSON>, <PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 12633543, "text": "<PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 12633544, "text": "<PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 14403711, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9852300, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 12633545, "text": "<PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 9852276, "text": "<PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 12445690, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9850984, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 12633546, "text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 12633547, "text": "<PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 12633548, "text": "<PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 9852299, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9850994, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 12633549, "text": "<PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 9850985, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9850986, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9852286, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 12548889, "text": "Option 0", "rank": 0, "color": 0}, {"id": 9850992, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9850997, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 12633550, "text": "<PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 12633551, "text": "<PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 12633552, "text": "<PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 9852251, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9850993, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9850991, "text": "<PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 12633553, "text": "<PERSON><PERSON>, Bishoy <PERSON>", "rank": 0, "color": 0}, {"id": 12633554, "text": "<PERSON><PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 12633555, "text": "<PERSON><PERSON>, <PERSON>", "rank": 0, "color": 0}, {"id": 11125349, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9852301, "text": "<PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 9852277, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 9852252, "text": "<PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 9850996, "text": "<PERSON>", "rank": 0, "color": 0}, {"id": 12633556, "text": "<PERSON>, <PERSON><PERSON>", "rank": 0, "color": 0}, {"id": 12633557, "text": "<PERSON>, <PERSON>", "rank": 0, "color": 0}]}, {"id": 4353842, "name": "Functional", "list_id": 19152, "enrichment_source": "none", "value_type": 2, "allows_multiple": false, "track_changes": false, "dropdown_options": [{"id": 12892131, "text": "No", "rank": 0, "color": 0}, {"id": 12892130, "text": "Yes", "rank": 0, "color": 0}]}]}