import { ListCompanyIdea, ListSource } from '@prisma/client';
import { TableSortModel } from './Common';
import { IStoredScoringResult } from './ScoringResult';

export interface IListCompanyIdeaFilters extends TableSortModel {
  text?: string;
  inShortlist?: ('true' | 'false')[];
  recordStatus?: ICompanyIdeaStatus[];
  listSourceIds: string[];
  listCompanyId?: string;
  createdBy?: string[];
  createdAt?: string[];
  pageIndex: number;
}

export interface IListCompanyIdeaResponse {
  listCompanyIdeas: IListCompanyIdeas[];
  totalRecords: number;
  totalPages: number;
  hasRecords: boolean;
}

export interface IListCompanyIdeas extends ListCompanyIdea {
  inShortlist: boolean;
  sources: ListSource[];
  status: ICompanyIdeaStatus;
  enrichmentResults: IStoredEnrichmentResult[];
  scoringResults: IStoredScoringResult[];
}

export interface IStoredEnrichmentResult {
  id: string;
  enrichmentId: string;
  value: object | string | number | boolean | null;
  createdAt: Date;
  status?: IStoredEntityFieldStatus;
  statusMessage?: string;
  details?: object;
}

export enum IStoredEntityFieldStatus {
  Ready = 'ready',
  Preprocessing = 'preprocessing',
  Processing = 'processing',
  Completed = 'completed',
  Error = 'error'
}

export enum ICompanyIdeaStatus {
  Idea = 'idea',
  InAcqwired = 'inAcqwired',
  Invalid = 'invalid'
}

export interface IFilterSampleData {
  schemaFieldId: string;
  id: string;
  value: string;
  companyId: string;
  schemaField: {
    label: string;
  };
}

export interface IFilterParameter {
  filter: string;
  value: string;
  entityField: string;
}

export interface IEntityField {
  id: string;
  key: string;
  label: string;
  isMandatory?: boolean;
}

export interface ICompanyFilter {
  name?: any;
  website?: any;
  fields?: {
    some?: {
      schemaFieldId: string;
      [key: string]: any;
    };
    none?: {
      schemaFieldId: string;
      [key: string]: any;
    };
  };
}
