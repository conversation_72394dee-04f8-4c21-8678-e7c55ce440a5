import { DataSourceType } from '@prisma/client';
import { ICompany, IEntityMapping, IOwner, IParticipant } from './Entity';
import { IEntityGetResults, IFilterCriteria, ITableList, TableSortModel, Uuid } from './Common';
import { ISimpleField } from './Field';
import { IActivity } from './Activity';

interface IContactGetResults extends IEntityGetResults {
  firstName: string;
  lastName: string;
  email: string;
  entityMapping:
    | {
        company: {
          id: string;
          name: string;
          website: string;
          fields: IField[] | null;
        };
      }[]
    | null;
  enrichmentResults: {
    id: string;
    enrichmentId: string;
    value: string;
    createdAt: Date;
  }[];
  fieldMetadata?:
    | {
        type: string;
        dataType: string;
        schemaFieldId: string;
      }[]
    | null;
}

interface IContactEntityMapping extends IEntityMapping {
  companyId: Uuid;
  label: string;
}

interface IMapping {
  id: Uuid;
  name: string;
  website: string;
  companyId: Uuid;
  isPrimary: boolean;
}

export interface IGetFilteredContactList {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  sourceType: DataSourceType;
  updatedAt: Date;
  fields: ISimpleField[];
  entityMapping: IContactEntityMapping[];
  enrichmentResults: {
    id: string;
    enrichmentId: string;
    value: string;
    createdAt: Date;
  }[];
  owner: IOwner;
  activities: IActivity[];
  mappings: IMapping[];
  participants: IParticipant[];
}

export interface IGetFilteredContactResult extends ITableList {
  contactList: IGetFilteredContactList[];
}

interface IContactMapping {
  company: ICompany;
  isKey: boolean | null;
  isPrimary: boolean | null;
}

interface IContactFilters extends TableSortModel {
  text?: string;
  lastActivityDate?: string[];
  lastModifiedDate?: string[];
  valueStats?: number[];
  owners?: string[];
  filters?: IFilterCriteria[];
  pageIndex: number;
}

interface IContactDetailsParams {
  sourceType: DataSourceType;
  firstName: string;
  lastName: string;
  email: string;
  updatedAt: Date;
  ownerId: string | null;
  deletedAt: Date | null;
  deletedById: string | null;
}

interface ICreateContactParams {
  contact: {
    id?: Uuid | null;
    firstName: string;
    lastName: string;
    email: string;
  };
  assignCompanyId?: Uuid | null;
  contactCompanyMappingId?: Uuid | null;
}

interface IContactList extends ITableList {
  executions?: { id: string; entityId: string; status: string }[];
  contactList: IContactGetResults[] | null;
  contactFields?: ISchemaFieldInfo[];
}
interface IField {
  key: string;
  label: string;
  dataType: string;
  schemaField: ISchemaFieldInfo;
}

export interface ISchemaFieldInfo {
  label: string;
  dataType: string;
  key: string;
}

interface IGetEmailFieldsResult {
  id: string;
  label: string;
}

interface ISetEmailFieldResult {
  id: string;
  value: string;
}

interface ISetEmailFieldParams {
  contactId: string;
  schemaFieldId: string;
  value: string;
  label: string;
}

interface ISetDefaultEmailParams {
  contactId: string;
  fieldKey?: string;
}

export type { ICreateContactParams, IContactDetailsParams, ISetEmailFieldParams };
export type { IContactGetResults, IContactMapping, IGetEmailFieldsResult, ISetEmailFieldResult, ISetDefaultEmailParams };
export type { IContactList, IContactFilters };
