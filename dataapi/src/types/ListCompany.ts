import { ListCompanyStatus, ListSourceType } from '@prisma/client';
import { TableSortModel } from './Common';

export interface IListCompanyFilters extends TableSortModel {
  listCompanyId: string;
  text?: string;
  status?: ListCompanyStatus[];
  createdBy?: string[];
  createdAt?: string[];
  pageIndex: number;
}

export interface IListCompany {
  id: string;
  name: string;
  status: ListCompanyStatus;
  createdAt: Date;
  updatedAt: Date | null;
  sources: { id: string; sourceType: ListSourceType }[];
}

export interface IListCompanyResponse {
  listCompanies: IListCompany[];
  totalRecords: number;
  totalPages: number;
  hasRecords: boolean;
}
