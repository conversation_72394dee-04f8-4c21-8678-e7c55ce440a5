import { ActivitySource, ActivityStatus, ActivityType, CrmType, EmailStatus, User } from '@prisma/client';
import { Uuid } from './Common';

export enum ActivityResponseStatus {
  Upcoming = 'Upcoming',
  Recent = 'Recent'
}

interface IActivity {
  id: string;
  companyId: string;
  contactId: string;
  type: string;
  to: string;
  others: string[];
  by: string;
  source: ActivitySource;
  followUp?: string;
  title?: string;
  content?: string;
  dueDate: Date;
  status: ActivityStatus;
  group: ActivityResponseStatus | string;
  updatedAt?: Date;
  sourceId?: Uuid;
  errorMessage?: string;
  subject?: string;
}

interface IActivityContact {
  id: string;
  firstName: string;
  lastName: string;
}

interface IActivityCompany {
  id: string;
  name: string;
}

interface IParticipant {
  contact?: IActivityContact;
  company?: IActivityCompany;
}

interface IAcqwiredActivity {
  title?: string;
  content?: string;
  dueDate: Date;
  status: ActivityStatus;
  createdAt: Date;
  updatedAt?: Date;
  createdById: string;
}

interface ICrmActivity {
  id: string;
  orgId: string;
  crmId: string;
  activityId: string;
  title?: string;
  content?: string;
  dueDate: Date;
  crmType: CrmType;
  status: ActivityStatus;
}

interface IDbActivity {
  id: string;
  type: ActivityType;
  source: ActivitySource;
  deletedAt?: Date;
  deletedBy?: User;
  participants: IParticipant[];
  acqwiredActivity?: IAcqwiredActivity;
  crmActivity?: ICrmActivity;
  channelEmail?: IChannelEmail;
}

interface IActivityParticipantResponse {
  id: Uuid;
  companyId: string;
  contactId: string;
  activity: IDbActivity;
}

interface IChannelEmail {
  id: Uuid;
  from: string;
  to: string;
  subject: string;
  body: string;
  status: EmailStatus;
  errorMessage?: string;
  cc: string[];
  scheduledAt?: Date;
  createdAt: Date;
  executionId?: string;
  contact: IActivityContact;
  execution?: IExecution;
}

interface IExecution {
  id: Uuid;
  type: string;
  typeId: string;
}

type PlayNamesMap = Record<string, string>;

export const activityTitles: { [key in ActivityType]: string } = {
  [ActivityType.email]: 'Email',
  [ActivityType.text]: 'Note',
  [ActivityType.call]: 'Phone Call',
  [ActivityType.chat]: 'SMS-Message',
  [ActivityType.f2f]: 'F2F Meeting',
  [ActivityType.video]: 'Video Call',
  [ActivityType.other]: 'Unknown'
};

export interface ICreateActivityPayload {
  type: ActivityType;
  companyId: string;
  contactId: string;
  date: string;
  content: string;
  source: ActivitySource;
  status: ActivityStatus;
}

interface IUpdateActivityPayload {
  id: Uuid;
  content: string;
  date: string;
  companyId: string;
  contactId: string;
  type: ActivityType;
}

interface IAcqwiredActivityDetails {
  content: string | null;
  dueDate: Date;
  status: ActivityStatus;
}

interface IActivityDetails {
  type: string;
  acqwiredActivity: IAcqwiredActivityDetails | null;
}

type IActivityParticipant = {
  contact: IActivityContact | null;
  company: IActivityCompany | null;
  activity: IActivityDetails | null;
};

export enum ActivityErrorDetail {
  ACTIVITY_NOT_FOUND = 'ACTIVITY_NOT_FOUND',
  ACTIVITY_COMPLETED = 'ACTIVITY_COMPLETED',
  INVALID_DATE = 'INVALID_DATE',
  PARTICIPANT_NOT_FOUND = 'PARTICIPANT_NOT_FOUND',
  DELETE_ACTIVITY_FAILED = 'DELETE_ACTIVITY_FAILED'
}

export type { IDbActivity, IActivity, PlayNamesMap, IActivityParticipantResponse, IUpdateActivityPayload, IActivityParticipant };
