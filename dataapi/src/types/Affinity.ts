interface Tenant {
  id: number;
  name: string;
  subdomain: string;
}

interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
}

interface Grant {
  type: string;
  scope: string;
  createdAt: string;
}

export interface IWhoAmIResponse {
  tenant: Tenant;
  user: User;
  grant: Grant;
}

export interface IDropdownOption {
  id: number;
  text: string;
  rank: number;
  color: number;
}

export interface ICustomField {
  id: number;
  name: string;
  list_id: number | null;
  enrichment_source: string;
  value_type: number;
  allows_multiple: boolean;
  track_changes: boolean;
  dropdown_options: IDropdownOption[];
}

export interface IOrganization {
  id: number;
  name: string;
  domain: string;
  domains: string[];
  global: boolean;
  person_ids?: number[];
  listIds?: string[];
  listEntryIds?: number[];
  fetchRelated?: boolean;
  interaction_dates?: IInteractionDates;
  interactions?: IInteractions;
}

export interface IOrganizationsResponse {
  organizations: IOrganization[];
  next_page_token?: string;
}

export interface IPerson {
  id: number;
  type: number;
  first_name: string;
  last_name: string;
  primary_email: string;
  emails: string[];
  organization_ids?: number[];
  opportunity_ids?: number[];
  current_organization_ids?: number[];
  list_entries?: any;
  interaction_dates?: IInteractionDates;
  interactions?: IInteractions;
  listIds?: string[];
  listEntryIds?: number[];
  fetchRelated?: boolean;
}

export interface IPersonsResponse {
  persons: IPerson[];
  next_page_token?: string;
}

export interface IInteractionDates {
  first_email_date?: string;
  last_email_date?: string;
  last_event_date?: string;
  last_chat_message_date?: string;
  last_interaction_date?: string;
  next_event_date?: string;
  first_event_date?: string;
}

export interface IInteractions {
  first_email?: IInteraction;
  last_email?: IInteraction;
  last_event?: IInteraction;
  last_chat_message?: IInteraction;
  last_interaction?: IInteraction;
  next_event?: IInteraction;
  first_event?: IInteraction;
}

export interface IInteraction {
  date: string;
  person_ids: number[];
}

export interface IFieldValueResponse {
  id: number;
  field_id: number;
  list_entry_id: number | null;
  entity_id: number;
  created_at: string;
  updated_at: string | null;
  value: string | number | object | any[] | null | IDropdownOption;
}

export interface IAffinityList {
  id: number;
  type: number;
  name: string;
  public: boolean;
  owner_id: number;
  list_size: number;
  creator_id?: number;
  fields?: ICustomField[];
}

export interface IListEntry {
  id: number;
  list_id: number;
  creator_id: number;
  entity_id: number;
  created_at: string;
  entity: IPerson | IOrganization;
}

export interface IRateLimit {
  limit: number;
  remaining: number;
  reset: number;
  used: number;
}

export interface IRateLimitInfo {
  apiKeyPerMinute: IRateLimit;
  orgMonthly: IRateLimit;
}

// Base structure for an Interaction
export interface IInteraction {
  id: number;
  date: string;
  type: InteractionType;
  persons: IInteractionPerson[];
  noteObjects?: INote[]; // Full note objects if needed
  notes?: number[]; // Note IDs
}

// Email-specific interaction structure
export interface IEmailInteraction extends IInteraction {
  subject: string;
  from: IInteractionPerson;
  to: IInteractionPerson[];
  cc?: IInteractionPerson[];
  direction?: number; // 0 = outgoing, 1 = incoming
}

// Meeting or Call interaction structure
export interface IMeetingOrCallInteraction extends IInteraction {
  title: string;
  start_time: string;
  end_time?: string | null;
  attendees: string[];
}

// Chat message interaction structure
export interface IChatInteraction extends IInteraction {
  direction: number; // 0 = outgoing, 1 = incoming
}

// Person involved in the interaction
export interface IInteractionPerson {
  id: number;
  type: number; // 0 = contact, 1 = company
  first_name: string;
  last_name: string;
  primary_email: string;
  emails: string[];
}

// Types of interactions
export enum InteractionType {
  Meeting = 0,
  Call = 1,
  ChatMessage = 2,
  Email = 3
}

// Paginated interaction response from Affinity API
export interface IInteractionResponse {
  emails?: IEmailInteraction[];
  events?: IMeetingOrCallInteraction[];
  chat_messages?: IChatInteraction[];
  next_page_token?: string;
}

export interface INote {
  id: number;
  creator_id: number;
  person_ids: number[];
  associated_person_ids: number[];
  interaction_person_ids: number[];
  interaction_id: number;
  interaction_type: number;
  is_meeting: boolean;
  mentioned_person_ids: number[];
  organization_ids: number[];
  opportunity_ids: number[];
  parent_id: number | null;
  content: string | null | object;
  type: number;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}
