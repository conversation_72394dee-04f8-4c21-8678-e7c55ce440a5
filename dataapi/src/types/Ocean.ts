export interface IOceanCompanySuggestion {
    domain: string;
    logo?: string;
    name?: string;
}

export interface IOceanSourcePayload {
    domains: string[];
    maxResultCount: number;
}

export interface IOceanCompanySearchResult {
    companies: IOceanCompanyResult[];
    searchAfter: string;
    detail: string;
    total: number;
}

export interface IOceanCompanyResult {
    company: Company;
    score: number;
}

export interface Company {
    domain: string;
    countries?: string[];
    primaryCountry?: string;
    companySize?: string;
    industryCategories?: string[];
    industries?: string[];
    keywords?: string[];
    revenue?: string;
    yearFounded?: number;
    description?: string;
    emails?: string[];
    phones?: Phone[];
    logo?: string;
    technologies?: string[];
    technologyCategories?: string[];
    webTraffic?: WebTraffic;
    medias?: { [key: string]: Media };
    name?: string;
    legalName?: string;
    locations?: Location[];
    departmentSizes?: DepartmentSize[];
    rootUrl?: string;
    faxes?: Phone[];
    impressum?: Impressum;
    mobileApps?: MobileApp[];
    ecommerce?: boolean;
}

interface Phone {
    number: string;
    country: string;
    primary?: boolean;
}

interface WebTraffic {
    visits: number;
    pageViews: number;
    pagesPerVisit: number;
    bounceRate: number;
}

interface Media {
    url: string;
    handle: string;
    name?: string;
}

interface Location {
    primary?: boolean;
    latitude?: number;
    longitude?: number;
    country?: string;
    locality?: string;
    region?: string;
    postalCode?: string;
    streetAddress?: string;
    regionCode?: string;
}

interface DepartmentSize {
    department: string;
    size: number;
}

interface Impressum {
    company?: string;
    address?: string;
    email?: string;
    phone?: string;
    fax?: string;
    vat?: string;
    url?: string;
    people?: { name: string }[];
}

interface MobileApp {
    link: string;
    name: string;
}

export interface IOceanFilteredCompany {
    name: string;
    domain: string;
    website: string;
    logo: string;
    score: number;
}

export interface IOceanCompanyPreview {
    companies: IOceanFilteredCompany[];
    total: number;
}
