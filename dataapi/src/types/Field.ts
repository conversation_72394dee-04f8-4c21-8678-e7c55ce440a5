import { EntityType, FieldDataType } from '@prisma/client';

export enum FieldConfigReconciliationRule {
  UseLatest = 'use_latest',
  KeepExisting = 'keep_existing'
}

export enum FieldConfigAutoRunFrequency {
  Never = 'never',
  Daily = 'daily',
  Weekly = 'weekly',
  Monthly = 'monthly',
  Quarterly = 'quarterly',
  Yearly = 'yearly'
}

export interface IStoredFieldConfig {
  reconciliation: FieldConfigReconciliationRule;
  runOnFieldCall: {
    enabled: boolean;
    runFor: {
      allRows: boolean;
      autoRun: FieldConfigAutoRunFrequency;
    };
  };
}

export enum FieldType {
  Acqwired = 'acqwired',
  Crm = 'crm'
}

export interface ISimpleField {
  id: string;
  type: FieldType;
  entityType: EntityType;
  dataType: FieldDataType;
  key: string;
  label: string;
  value?: string;
}
