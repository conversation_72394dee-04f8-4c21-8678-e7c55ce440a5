import { ExecutionType } from '.prisma/client';
import { EntityType, ExecutionStatus, QueueTaskType } from '@prisma/client';
import { Uuid } from './Common';

interface ICreateExecutionParams {
  orgId: string;
  type: ExecutionType;
  typeId: string;
  userId: string;
  entityType: EntityType;
  entityId: string;
  scheduledAt: Date;
  executionId?: Uuid;
}

interface ICreateQueueTaskParams {
  executionId: string;
  type: QueueTaskType;
  taskData?: object;
  scheduledAt: Date;
  status?: ExecutionStatus;
}

interface IQueueTask {
  id: string;
  type: QueueTaskType;
  status: ExecutionStatus;
  scheduledAt: Date | null;
}

interface IExecution {
  id: string;
  type: ExecutionType;
  typeId: string;
  entityType: EntityType;
  entityId: string;
  status: ExecutionStatus;
  createdAt: Date;
  scheduledAt: Date | null;
  executedAt: Date | null;
  queueTasks: IQueueTask[];
}

export type { ICreateExecutionParams, ICreateQueueTaskParams };
export type { IExecution };
