export interface ICSVOptions {
  acceptMissingData?: boolean;
  acceptRowLengthMismatch?: boolean;
  separator?: string;
}

export interface ICSVObject {
  [key: string]: string;
}

export interface ICompanyDataForCsv {
  name: string;
  website: string;

  [key: string]: string;
}

export interface IContactDataForCsv {
  first_name: string;
  last_name: string;
  email: string;

  [key: string]: string;
}

export type IEntityDataForCsv = ICompanyDataForCsv | IContactDataForCsv;
