import { AcqwiredActivity, ActivitySource, ActivityType, ChannelEmail, CrmActivity, FieldDataType, Play } from '@prisma/client';
import { Uuid } from './Common';

interface IBaseEntity {
  id: string;
  fields?: { [key: string]: string };
}

interface IContact extends IBaseEntity {
  firstName: string;
  lastName: string;
  email: string;
  company?: ICompany | null;
  entityMapping?: { companyId: string }[];
}

interface ICompany extends IBaseEntity {
  name: string;
  website: string;
  contact?: IContact | null;
  entityMapping?: { contactId: string }[];
}

interface IContactEntityMapping {
  isPrimary: boolean | null;
  company: ICompany;
}

interface ICompanyEntityMapping {
  isKey: boolean | null;
  contact: IContact;
}

interface IEntityResponseFields {
  schemaField: { key: string };
  value: string;
}

interface IEntityResponse {
  id: string;
  fields: IEntityResponseFields[];
}

interface ICompanyEntityResponse extends IEntityResponse {
  name: string;
  website: string;
  entityMapping: ICompanyEntityMapping[];
}

interface IContactEntityResponse extends IEntityResponse {
  firstName: string;
  lastName: string;
  email: string;
  entityMapping: IContactEntityMapping[];
}

interface IField {
  id?: string;
  schemaFieldId?: string;
  value: string;
  schemaField: ISchemaField;
}

interface ISchemaField {
  id?: string;
  key: string;
  label: string;
  dataType: FieldDataType;
  isMandatory?: boolean;
  isInternal?: boolean;
}

interface IOwner {
  id: string;
  firstName: string;
  lastName: string;
}

type IEntity = ICompany | IContact;

interface ICreateMappingResponse {
  isPrimary: boolean | null;
  isKey: boolean | null;
  contactId: string;
  companyId: string;
}

interface ISyncField {
  crmFieldId: string;
  fieldKey: string;
  value: any;
  label: string;
  schemaFieldId: string;
  locked: boolean | null;
  type: string;
  isMandatory: boolean | null;
}

interface ISyncData {
  id: string;
  fields: ISyncField[];
}

interface ISyncFullData {
  mainData: ISyncData[];
  relatedData: ISyncData[];
}

interface ICreateCompanyMappingResponse extends ICreateMappingResponse {
  name: string;
  website: string;
}

interface EmailData {
  id: string;
  orgId: string;
  contactId: string | null;
  companyId: string | null;
  type: string;
  status: string;
  errorMessage?: string | null;
  cc: string[];
  subject: string;
  body: string;
  createdById: string | null;
  createdAt: Date;
  updatedAt: Date | null;
  deletedAt: Date | null;
  cancelledAt: Date | null;
  scheduledAt: Date | null;
  executionId: string | null;
  contact?: {
    email: string;
  } | null;
  Execution?: IEmailExecution;
  play?: Play;
}

interface IEmailExecution {
  typeId: string;
}

interface ICreateContactMappingResponse extends ICreateMappingResponse {}

interface IContactCompanyEntity {
  company: {
    id?: Uuid | null;
    name: string;
    website: string;
  };
  contact: {
    id?: Uuid | null;
    firstName: string;
    lastName: string;
    email: string;
  };
  contactCompanyMappingId?: Uuid | null;
}

export interface IEntitySearchResult {
  id: string;
  type: string;
  name: string;
  info: string;
}

interface IEntityField {
  id: string;
  entity: string;
  label: string;
  dataType: FieldDataType;
  key: string;
  isInternal: boolean;
  isAttachment: boolean;
  isMandatory: boolean;
  companyFields?: IEntityFieldData[];
  contactFields?: IEntityFieldData[];
  enrichments?: IEnrichmentId[];
}

interface IEntityFieldData {
  id: string;
  value: string;
}

interface IEnrichmentId {
  id: string;
}

interface checkAiGenerated {
  executionId: string;
  channelEmailId: string;
  contactId: string;
}

interface IResponseStatus {
  success: boolean;
  message: string;
}

export interface IFilterFields {
  label: string;
  dataType: string;
  key: string;
}

export enum FilterLabelEnum {
  LastActivity = 'Last Activity',
  LastModified = 'Last Modified',
  PlayStats = 'Plays Run',
  Owner = 'Owner'
}

export enum FilterDataTypeEnum {
  Date = 'date',
  Number = 'number',
  Uuid = 'uuid'
}

export enum FilterKeyEnum {
  createdAt = 'createdAt',
  updatedAt = 'updatedAt',
  Stats = 'stats',
  Owners = 'ownerId'
}

export enum ErrorMessages {
  NOT_FOUND = 'NOT_FOUND',
  INVALID_COMPANY = 'INVALID_COMPANY',
  INVALID_CONTACT = 'INVALID_CONTACT',
  INVALID_CONTACT_COMPANY = 'INVALID_CONTACT_COMPANY'
}

interface IValidEntityBody {
  contactId?: Uuid;
  companyId?: Uuid;
  playId: string;
}

interface IEntityMapping {
  id: Uuid;
  type: number;
  value: string;
  isKey: boolean;
  isPrimary: boolean;
}

interface IParticipant {
  activity: {
    id: Uuid;
    type: ActivityType;
    source: ActivitySource;
    participants: {
      contact: {
        id: Uuid;
        firstName: string;
        lastName: string;
      } | null;
      company: {
        id: Uuid;
        name: string;
      } | null;
    }[];
    acqwiredActivity: AcqwiredActivity | null;
    crmActivity: CrmActivity | null;
    channelEmail: ChannelEmail | null;
  };
}

export type { IEntity, IContact, ICompany };
export type { ICompanyEntityResponse, IContactEntityResponse, IEntityResponseFields };
export type { IContactCompanyEntity, ICompanyEntityMapping, IContactEntityMapping, ICreateContactMappingResponse, ICreateCompanyMappingResponse, ICreateMappingResponse, ISyncField, ISyncData, ISyncFullData };
export type { IField, ISchemaField, IOwner, EmailData };
export type { IEntityField, checkAiGenerated, IResponseStatus, IValidEntityBody, IParticipant, IEntityMapping };
