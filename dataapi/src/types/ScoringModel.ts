import { ScoringOperator, FieldDataType, ScoringFieldSource } from '@prisma/client';

export interface ICreateScoringModelParams {
  name: string;
  description?: string;
  listCompanyId: string;
  ruleSets: IRuleSet[];
}

export interface IListScoringModelsParams {
  listCompanyId: string;
}

export interface IScoringFieldData {
  id: string;
  createdAt: Date;
  updatedAt: Date | null;
  sourceType: ScoringFieldSource;
  fieldType: FieldDataType;
  fieldPath: string;
  enrichmentId: string | null;
}

export interface IRuleSet {
  id: string;
  createdAt: Date;
  updatedAt: Date | null;
  label: string;
  weight: number;
  defaultScore: number;
  scoringModelId: string;
  fieldDataId: string;
  fieldData: IScoringFieldData;
  rules: {
    id: string;
    createdAt: Date;
    updatedAt: Date | null;
    value: any;
    ruleSetId: string;
    operator: ScoringOperator;
    score: number;
  }[];
}

export interface IFieldData {
  id?: string;
  sourceType: ScoringFieldSource;
  fieldType: FieldDataType;
  fieldPath: string;
  enrichmentId?: string;
}

export interface IRule {
  id?: string;
  operator: ScoringOperator;
  value: {
    primary: string;
    secondary?: string;
  };
  score: number;
}

export const MANDATORY_FIELDS = ['companyName', 'website'] as const;

export interface IScoringModel {
  id: string;
  name: string;
  description: string | null;
  listCompanyId: string;
  createdById: string;
  updatedById: string | null;
  createdAt: Date;
  updatedAt: Date | null;
  deletedAt: Date | null;
  ruleSets?: IRuleSet[];
}
