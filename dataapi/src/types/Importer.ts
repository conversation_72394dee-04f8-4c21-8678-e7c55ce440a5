import { DataSourceType } from '@prisma/client';

export interface ICompanyMandatoryData {
  name: string;
  website: string;
  orgId: string;
  ownerId: string;
  sourceType: DataSourceType;
  createdAt: Date;
  createdById: string;
}

export interface ICompanyFieldData {
  schemaFieldId: string;
  sourceType: DataSourceType;
  createdAt: Date;
  value: string;
}

export interface ICompanyDataToWrite {
  company: ICompanyMandatoryData,
  fields: ICompanyFieldData[]
}

export interface IContactMandatoryData {
  firstName: string;
  lastName: string;
  email: string;
  orgId: string;
  ownerId: string;
  sourceType: DataSourceType;
  createdAt: Date;
  createdById: string;
}

export interface IContactFieldData {
  schemaFieldId: string;
  sourceType: DataSourceType;
  createdAt: Date;
  value: string;
}

export interface IContactDataToWrite {
  contact: IContactMandatoryData,
  fields: IContactFieldData[]
}

export interface IImporterResponse {
  success: number;
  error: number;
  successRate: number;
}

export type IEntityDataToWrite = ICompanyDataToWrite | IContactDataToWrite;
export type IEntityFieldData = ICompanyFieldData | IContactFieldData;
