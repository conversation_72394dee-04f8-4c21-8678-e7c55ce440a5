export interface IExaSearchPayload {
  query: string;
  maxResultCount: number;
}

export interface IExaCompany {
  id: string;
  title: string;
  url: string;
  score: number;
  favicon: string;
}

export interface IExaSearchResponse {
  autopromptString: string;
  results: IExaCompany[];
}

export interface IExaFilteredCompany {
  name: string;
  domain: string;
  website: string;
  logo: string;
  score: number;
}
