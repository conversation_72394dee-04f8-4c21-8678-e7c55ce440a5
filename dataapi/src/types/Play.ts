import { <PERSON>T<PERSON>, LlmProvider, Queue<PERSON>askType, Prisma, EntityType, PlayStatus } from '@prisma/client';
import { JsonValue } from '@prisma/client/runtime/library';

import { IAuth, TableSortModel, Uuid } from './Common';
import { IExecution } from './Execution';
import { CompanyService, ContactService, NotificationService, TemplateService, TemplateVariableService } from '../services';

interface ITemplate {
  id: string;
  llmEngine: LlmProvider;
  title: string | null;
  template: string;
  subject?: string;
  fieldMapping?: {
    schemaField: {
      id: string;
      entity: string;
      key: string;
      label: string;
    };
  }[];
}

interface IPlay {
  id: string;
  name: string;
  description?: string | null;
  templates?: ITemplate[] | null;
}
interface IPlaybookUsers {
  id: string;
  name: string;
}
interface IPlaybook {
  id: string;
  playName: string;
  playType: string;
  description: string;
  template: string;
  enrichmentsIncluded: number;
  useCount?: number;
  acqwiredProvidedPlays: boolean;
  createdBy: string | null;
  createdAt: string | null;
}

interface IPlaybookResponse {
  playbooks: IPlaybook[];
  totalRecords: number;
  totalPages: number;
  hasRecords: boolean;
}

interface IPlaybookFilters extends TableSortModel {
  text?: string;
  createdBy?: Array<{ id: string; acqwiredProvidedPlays: boolean }>;
  createdAt?: string[];
  playType?: string;
  pageIndex: number;
}

interface ListPlaybookParams {
  orgId: string;
  playIds: string[];
  pageSize: number;
  whereClause: Prisma.PlayWhereInput[];
}

interface IPlayExecutionParams {
  playId: string;
  entityType: EntityType;
  targetIds: string[];
  executionId?: Uuid;
  channelEmailId?: Uuid;
  entityId: Uuid;
}

interface IPlayServiceConstructor {
  auth: IAuth;
  companyService?: CompanyService;
  contactService?: ContactService;
  templateService?: TemplateService;
  templateVariablesService?: TemplateVariableService;
  notificationService?: NotificationService;
  writeResultsToSocket?: boolean;
}

interface ISinglePlay {
  id: string;
  name: string;
  description: string | null;
  status: PlayStatus;
  templates?: ITemplate[] | null;
  tasks: {
    id: string;
    data: JsonValue;
    queueTaskType: QueueTaskType;
    enrichment: {
      id: string;
      data: JsonValue;
    } | null;
  }[];
}

interface IPlayExecution extends IPlay {
  execution: IExecution;
}

export interface IPlaySaveRequest {
  id: string | null;
  name: string;
  title: string;
  instructions: string;
  attachments: string[];
  status?: string;
  llmProvider?: LlmProvider;
}

interface IDynamicPlayTask {
  variable: string;
  dependencies: string[];
  playId: string;
  enrichmentId?: string;
  priority: number;
  taskType: TaskType;
  queueTaskType: QueueTaskType;
  data: {
    [key: string]: string;
  };
}

interface ITaskStep {
  priority: number;
  type: TaskType;
  enrichmentKey?: {
    entity: EntityType;
    key: string;
  };
  queueTaskType: QueueTaskType;
}

interface IPlayInitialData {
  id: string;
  configId: string;
  name: string;
  description?: string;
  data?: {
    taskSteps: ITaskStep[];
  };
}

export type { ITemplate };
export type { IPlay };
export type { IPlayExecutionParams };
export type { ISinglePlay };
export type { IPlayServiceConstructor };
export type { IPlayExecution };
export type { IDynamicPlayTask };
export type { IPlaybook };
export type { IPlaybookResponse };
export type { IPlaybookFilters };
export type { ListPlaybookParams };
export type { IPlaybookUsers };
export type { IPlayInitialData };
