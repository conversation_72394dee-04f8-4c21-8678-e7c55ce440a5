export interface ISourceScrubAuthResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
}

export interface ISourceScrubSourcesPayload {
  sourceIds: string[];
}

export interface ISourceScrubPayload {
  domains: string[];
  maxResultCount: number;
}

export interface ISourceScrubFilterPayload {
  filters: SourceScrubFilters;
  maxResultCount: number;
}

export interface ISourceScrubSource {
  id: string;
  officialTitle: string;
  sourceType: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  status: string;
  companyCount: number;
  modifiedDate: string;
}

export interface ISourceScrubCompaniesResponse {
  total: number;
  items: ISourceScrubCompany[];
}

export interface ISourceScrubCompany {
  id: string;
  name: string;
  website: string;
  domain: string;
  description?: string;
  foundingYear?: string;
  location?: string;
  phoneNumber?: string;
  linkedIn?: string;
  modifiedDate?: string;
  baseCompanyId?: string;
}

export interface ISourceScrubFilteredCompany {
  name: string;
  domain: string;
  website: string;
  logo: string;
  score: number;
}

export interface ISourceScrubSourcesResponse {
  total: number;
  items: ISourceScrubSource[];
}

export interface SourceScrubLocationFilter {
  type: 'Area' | 'Country' | 'State' | 'City' | 'PostalCode';
  area?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
  geoLocation?: {
    latitude: number;
    longitude: number;
  };
  distance?: {
    value: number;
    unit: 'Miles' | 'Kilometers';
  };
}

export interface SourceScrubDateRange {
  from: string;
  to: string;
}

export type SourceScrubModifiedDateRange = 'LastOneDay' | 'LastWeek' | 'LastThirtyDays' | 'LastSixMonths' | 'LastYear';

export type SourceScrubStatus = 'Complete' | 'In Progress' | 'Queued';

export interface SourceScrubSourceFilters {
  sourceTypes?: string[];
  locations?: SourceScrubLocationFilter[];
  industries?: string[];
  startDateRange?: SourceScrubDateRange;
  endDateRange?: SourceScrubDateRange;
  modifiedDateTimeRange?: SourceScrubModifiedDateRange;
  statuses?: SourceScrubStatus[];
}

export interface ISourceScrubSourceRequest {
  searchText?: string;
  filters: SourceScrubSourceFilters;
}

export interface SourceScrubFilters {
  locations?: SourceScrubLocationFilter[];
  industries?: string[];
}
