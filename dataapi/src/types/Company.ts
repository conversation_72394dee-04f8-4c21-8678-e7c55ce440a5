import { DataSourceType, FieldDataType } from '@prisma/client';
import { IContact, IEntityMapping, IField, IOwner, IParticipant } from './Entity';
import { IEntityGetResults, IFilterCriteria, ITableList, TableSortModel, Uuid } from './Common';
import { ISimpleField } from './Field';
import { IActivity } from './Activity';

interface ICompanyFilters extends TableSortModel {
  text?: string;
  lastActivityDate?: string[];
  lastModifiedDate?: string[];
  lists?: string[];
  valueStats?: number[];
  owners?: string[];
  filters?: IFilterCriteria[];
  pageIndex: number;
}

interface ICompanyMapping {
  contact: IContact;
  isKey: boolean | null;
  isPrimary: boolean | null;
}

interface IFlattenedField {
  id?: string;
  fkey?: string;
  key: string;
  label: string;
  value: string;
  dataType?: FieldDataType;
  isDefault?: boolean;
  enrichmentId?: string;
}

interface IFlattenedMapping {
  id?: string;
  label: string;
  value: string;
  isKey: boolean;
  isPrimary: boolean;
  firstName?: string;
  lastName?: string;
  email?: string;
}

interface ICompanyEntityMapping extends IEntityMapping {
  contactId: Uuid;
}

interface IMapping {
  id: Uuid;
  firstName: string;
  lastName: string;
  email: string;
  contactId: Uuid;
  isKey: boolean;
}

export interface IGetFilteredCompanyList {
  id: string;
  name: string;
  website: string;
  sourceType: DataSourceType;
  updatedAt: Date;
  fields: ISimpleField[];
  entityMapping: ICompanyEntityMapping[];
  enrichmentResults: {
    id: string;
    enrichmentId: string;
    value: string;
    createdAt: Date;
  }[];
  owner: IOwner;
  activities: IActivity[];
  mappings: IMapping[];
  participants: IParticipant[];
}

export interface IGetFilteredCompanyResult extends ITableList {
  entityList: IGetFilteredCompanyList[];
}

interface ICompanyGetResults extends IEntityGetResults {
  name: string;
  website: string;
  sourceType: DataSourceType;
  entityMapping:
    | {
        contact?: {
          id: string;
          firstName: string;
          lastName: string;
          email: string;
          fields: IField[] | null;
        };
      }[]
    | null;
  enrichmentResults?: {
    id: string;
    enrichmentId: string;
    value: string;
    createdAt: Date;
  }[];
}

interface ICompanyCreateWithContactId {
  company: {
    id?: Uuid | null;
    name: string;
    website: string;
  };
  assignContactId?: Uuid | null;
  contactCompanyMappingId?: Uuid | null;
}

interface ICompanyDetailsParams {
  sourceType: DataSourceType;
  name: string;
  website: string;
  domain: string;
  updatedAt: Date;
  ownerId: string | null;
  deletedAt: Date | null;
  deletedById: string | null;
}

interface IRemoveCompanyProps {
  id: string;
  filter: ICompanyFilters;
}

interface IFilterResults {
  ids: string[];
  count: number;
}

export type { ICompanyFilters, ICompanyGetResults, IField, IFlattenedField, IFilterResults };
export type { ICompanyMapping, IFlattenedMapping };
export type { ICompanyCreateWithContactId, ICompanyDetailsParams };
export type { IRemoveCompanyProps };
