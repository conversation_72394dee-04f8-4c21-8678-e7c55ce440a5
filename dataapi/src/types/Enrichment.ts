import { EnrichmentConfig, EnrichmentProvider, EnrichmentType, EntityType } from '@prisma/client';
import { TableSortModel } from './Common';
import { JsonValue } from '@prisma/client/runtime/library';

export interface IEnrichmentListItem {
  id: string;
  name: string;
  entityType: EntityType;
  isPrimary: boolean;
  createdAt: Date;
  updatedAt: Date | null;
  config: EnrichmentConfig;
  entityField: { id: string } | null;
  llmModelId: string | null;
  listCompanyId: string | null;
  listColumns: JsonValue | null;
  createdBy: {
    id: string;
    firstName: string | null;
    lastName: string | null;
  };
}

export interface IPagination {
  totalRecords: number;
  totalPages: number;
  hasRecords: boolean;
}

export interface IEnrichmentListResponse {
  records: IEnrichmentListItem[];
  pagination: IPagination | null;
}

export interface IEnrichment {
  id: string;
  config: EnrichmentConfig;
}

export interface IEnrichmentFilters extends TableSortModel {
  text?: string;
  provider?: EnrichmentProvider;
  createdBy?: string[];
  createdAt?: string[];
  pageIndex: number;
}

export interface IEnrichmentConfig {
  id: string;
  name: string;
  description: string;
  enrichmentType: EnrichmentType;
  entityType: EntityType;
}
