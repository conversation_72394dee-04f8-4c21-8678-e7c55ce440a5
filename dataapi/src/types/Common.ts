import { Request } from 'express';
import { ParamsDictionary } from 'express-serve-static-core';
import { IOwner } from './Entity';
import { ChannelEmail, MembershipType, EntityType } from '@prisma/client';
import { ICrmService } from '../lib/crm/ICrmService';
import { ISimpleField } from './Field';

type Uuid = `${string}-${string}-${string}-${string}-${string}`;

interface FlattenedObject {
  [key: string]: string;
}

interface IAcqRequest extends Request {
  auth?: IAuth | null;
  params: ParamsDictionary;
}

interface IAcqCrmRequest extends Request {
  auth?: IAuth | null;
  crmService?: ICrmService | null;
  entityType?: EntityType | null;
  params: ParamsDictionary;
}

interface IAuth {
  uid: string;
  firstName: string;
  lastName: string;
  lid: string;
  oid: string;
  tokenType: string;
  email: string;
  scope: string;
  iat: number;
  exp: number;
  aud: string;
  iss: string;
}

export interface ITableList {
  totalRecords?: number; // Provides the total records available in given table filter
  totalPages?: number; // Provides the total pages available in given table filter
  hasRecords: boolean; // Provides whether the table has any records available for given organization. Needed for Frontend.
}

export interface IEntityGetResults {
  id: string;
  owner: IOwner | null;
  playRunCount?: number;
  channelEmails: ChannelEmail[];
  fields: ISimpleField[] | null;
  updatedAt: Date | null;
  lastActivity?: Date | null;
}

export interface TableSortModel {
  sortKey?: string;
  sortOrder?: SortOrder;
}

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc'
}

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user'
}

interface InviteOrgRequest {
  contact: {
    email: string;
    firstName: string;
    lastName: string;
  };

  firmType: string;
  firmName: string;
  aiSummary: string;
  website: string;
  address: SysAddress;

  membership: {
    type: string;
    maxUsers: number;
    initialCredit?: number;
  };

  integrations: {
    provider: string;
    activeUntil: Date;
    keyName: string;
    key: string;
    aiCredits: number;
    type: string;
  }[];

  phone: {
    phone: string;
    contactName: string;
    purpose?: string;
  }[];
}

interface SysCity {
  cityName: string;
  phoneCode?: number;
  state: SysState;
}

interface SysState {
  state: string;
  code?: number;
  country: SysCountry;
}

interface SysCountry {
  country: string;
  phoneCode?: number;
  isoCode: string;
}

interface SysAddress {
  title: string;
  addressLine1: string;
  addressLine2?: string;
  zipCode: number;
  city: SysCity;
  purpose: string;
}

interface IOrganization {
  id: string;
}

interface InviteOrgResponse {
  isInvited: boolean;
  orgId: string;
  userId: string;
}

interface IContactInfo {
  type: string;
  purpose: string;
  value: string;
  label: string;
}

interface IIntegration {
  provider: string;
  expiresAt: Date;
  name: string;
  token: string;
  refreshedAt: Date;
  data: { aiCredits: number };
  active: boolean;
  remainingCredit: number;
  type: string;
}

interface IOrganizationResult {
  id: string;
  name: string;
  website: string;
  status: string;
  aiSummary: string;
  type?: string;
  membership?: {
    type: MembershipType;
    maxUser: number;
    startedAt: Date;
    endsAt: Date | null;
  } | null;
  addresses?: {
    id: string;
    title: string;
    zipCode: number;
    purpose: string;
    addressLine1: string;
    addressLine2?: string | null;
    city: {
      cityName: string;
      phoneCode: string;
      state: {
        code: string;
        state: string;
        country: {
          isoCode: string;
          country: string;
        };
      };
    };
  }[];
  orgContacts?: {
    id: string;
    purpose: string;
    label: string;
    value: string;
    type: string;
  }[];
  userRoles?: {
    id: string;
    userId: string;
    roleId: string;
    createdAt: Date;
    updatedAt?: Date | null;
    userRole: {
      role: string;
    };
    user: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      active: boolean;
      invitationAccepted: boolean;
      updatedAt: Date;
      deletedById: string | null;
      deletedAt: Date | null;
    };
  }[];
  user?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    active: boolean;
    invitationAccepted: boolean;
    updatedAt: Date;
    deletedById: string | null;
    deletedAt: Date | null;
  }[];
}

interface IUserRole {
  id: string;
  role: string;
}

interface IFilterCriteria {
  key: string;
  items: IFilterCondition[];
}

enum TableFilterConditions {
  Equals = 'equals',
  Contains = 'contains',
  In = 'in',
  StartsWith = 'starts_with',
  EndsWith = 'ends_with',
  GreaterThan = 'greater_than',
  LessThan = 'less_than',
  GreaterThanOrEqual = 'greater_than_or_equal',
  LessThanOrEqual = 'less_than_or_equal',
  Between = 'between',
  NotEquals = 'not_equals',
  NotContains = 'not_contains',
  NotStartsWith = 'not_starts_with',
  NotEndsWith = 'not_ends_with',
  NotBetween = 'not_between'
}

interface IFilterCondition {
  condition: TableFilterConditions;
  data: string;
}

export type { Uuid, IAcqRequest, IAcqCrmRequest, IAuth, FlattenedObject, InviteOrgRequest, SysCity, SysState, SysCountry, SysAddress, IOrganization, IOrganizationResult, InviteOrgResponse, IContactInfo, IIntegration, IUserRole, IFilterCriteria, IFilterCondition };

export { TableFilterConditions };
