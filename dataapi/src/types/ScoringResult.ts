import { Decimal } from "@prisma/client/runtime/library";

export interface IScoringRuleResult {
  ruleId: string;
  operator: string;
  value: object;
  score: number;
  status: 'matched' | 'unmatched';
}

export interface IScoringRuleSetResult {
  id: string;
  name: string;
  weight: number;
  normalizedWeight: number;
  normalizedScore: number;
  maxPossibleScore: number;
  contribution: number;
  matchedRules: IScoringRuleResult[];
  unmatchedRules: IScoringRuleResult[];
  calculation: {
    sumMatchedScores: number;
    maxPossibleScore: number;
    normalizedScore: number;
    normalizationExplanation: string;
  };
}

export interface IScoringCalculationSummary {
  totalWeightedScore: number;
  weightNormalizationFactor: number;
  ruleSetContributions: Array<{
    name: string;
    weight: number;
    normalizedWeight: number;
    normalizedScore: number;
    contribution: number;
  }>;
}

export interface IScoringDetails {
  ruleSets: IScoringRuleSetResult[];
  calculationSummary: IScoringCalculationSummary;
}

export interface IStoredScoringResult {
  id: string;
  scoringModelId: string;
  entityId: string;
  executionId: string;
  details: IScoringDetails;
  totalScore: Decimal;
  createdAt: Date;
}
