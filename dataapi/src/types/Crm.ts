import { EntityType, FieldDataType, CrmFieldType, ActivityType, ActivityStatus, CrmEntityType } from '@prisma/client';
import { SyncStatus } from '../constants';
import { IAuth } from './Common';
import { CompanyService, ContactService } from '../services';
import { ISyncData } from './Entity';
import ActivityService from '../services/Activity';
import { IInteraction } from './Affinity';

export interface ISalesforceAuth {
  accessToken: string;
  instanceUrl: string;
  endPoints: {
    query: string;
    t_query: string;
    describeAccount: string;
    describeContact: string;
  };
}

export interface IAffinityAuth {
  accessToken: string;
}

export interface IMappingField {
  crmFieldId: string;
  fieldKey: string;
  value: string;
  label?: string;
  schemaFieldId: string;
  locked?: boolean;
  type: FieldDataType;
  isMandatory: boolean;
}

export interface ISyncSettings {
  syncContacts: boolean;
  syncAccounts: boolean;
  userSync: boolean;
  inProgress?: boolean;
}

export interface IFilterItem {
  filterId?: string;
  condition: string;
  field: string;
  input: string | number;
}

export interface ICrmFields {
  uniqueId: string;
  label: string;
  key: string;
  type: FieldDataType;
  source: CrmFieldType;
  sourceId?: string;
  sourceName?: string;
}

export interface ICrmDefaultFields {
  uniqueId: string;
  key: string;
  label: string;
  type: FieldDataType;
  locked?: boolean;
  isMandatory?: boolean;
}

export interface ICrmFieldMapping {
  id?: string;
  uniqueId: string;
  entityType: EntityType;
  label?: string;
  type: FieldDataType;
  isMandatory: boolean;
  schemaFieldId?: string;
  locked: boolean;
  source: CrmFieldType;
  sourceId?: string | null;
  key: string;
}

export enum CRMType {
  SALESFORCE = 'Salesforce',
  AFFINITY = 'Affinity'
}

export interface ICrmBasicEntity {
  id: string;
  FirstName?: string;
  LastName?: string;
  Email?: string;
  Name: string;
  Website?: string;
}

export interface ICrmUser {
  exists: boolean;
  firstName?: string;
  lastName?: string;
  email?: string;
  id?: string;
}

export interface ICrmFilter {
  id?: string;
  parameters: IFilterItem[];
  fetchRelated?: boolean;
  filterType?: string;
}

export interface ISyncResult {
  status: SyncStatus;
  msg?: string;
}

export interface ISalesforceServiceParam {
  sfAuth: ISalesforceAuth | null;
  auth: IAuth;
  companyService: CompanyService;
  contactService: ContactService;
  activityService: ActivityService;
}

export interface IAffinityServiceParam {
  affAuth: IAffinityAuth | null;
  auth: IAuth;
  companyService: CompanyService;
  contactService: ContactService;
  activityService: ActivityService;
}

export interface IUserCrmMap {
  email: string;
  crmUserId: string;
}

export interface ISyncRecordResult {
  mainData: ISyncData[];
  relatedData: ISyncData[];
  activities: IActivity[];
}

export interface IContactMandatoryFields {
  crmCompanyIds: string[];
  firstName: string | null;
  lastName: string | null;
  email: string | null;
}

export interface ICompanyMandatoryFields {
  name: string | null;
  website: string | null;
}

export interface IRecordSyncStatus {
  entity: CrmEntity;
  entityId: string;
  synced: boolean;
  error?: string;
}

export interface ICrmListEntity {
  id: string;
  name: string;
  entityType: EntityType;
  ownerId: string;
}

export interface ICrmListMapping extends ICrmListEntity {
  fetchRelated?: boolean;
  listMappingId?: string;
  hasMapping?: boolean;
}

export interface IActivity {
  id: string;
  entityType: CrmEntityType;
  type: ActivityType;
  status: ActivityStatus;
  title: string | null;
  content: string | null;
  dueDate: Date | null;
  location?: string;
  participant?: IActivityParticipant[];
  startDateTime?: Date | null;
  endDateTime?: Date | null;
  isAllDayEvent?: boolean;
  crmActivity: ISalesforceTask | ISalesforceEvent | IInteraction | null;
}

export interface IActivityParticipant {
  contactId?: string | null;
  companyId?: string | null;
}

export interface ISalesforceTask {
  Id: string;
  Subject: string;
  ActivityDate: string | null;
  Status: string;
  Description: string | null;
  WhoId: string | null; // Contact ID
  WhatId: string | null; // Company ID
  TaskSubtype: string | null;
}

export interface ISalesforceEvent {
  Id: string;
  Subject: string;
  ActivityDate: string | null;
  WhoId: string | null; // Contact ID
  WhatId: string | null; // Company ID
  Description: string | null;
  Location: string | null;
  StartDateTime: string | null;
  EndDateTime: string | null;
  IsAllDayEvent: boolean;
  EventSubtype: string | null;
}

export enum CrmEntity {
  CONTACT = "CONTACT",
  ACCOUNT = "ACCOUNT",
  TASK = "TASK",
  EVENT = "EVENT",
  INTERACTION = "INTERACTION",
}
