import { Response, NextFunction } from 'express';
import CrmServiceFactory from '../lib/crm/CrmServiceFactory';
import { ICrmService } from '../lib/crm/ICrmService';
import APIError from '../lib/APIError';
import { IAcqCrmRequest } from '../types/Common';
import { logger } from '../logger';
import config from '../config';
import { CrmType, EntityType } from '@prisma/client';

export default () => {
  return (req: IAcqCrmRequest, res: Response, next: NextFunction) => {
    const { auth }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new APIError('Unauthorized'));
    }

    const crmType = req.headers['x-crm-type'] as CrmType;
    if (!crmType) {
      return res.status(400).json(new APIError('Missing X-CRM-Type header'));
    }

    if (req.query.entityType) {
      req.entityType = req.query.entityType as EntityType;
    }

    const isAllowed = config.publicCrmEndpoints.includes(req.url) || config.publicCrmEndpoints.find((e) => e !== '/' && req.url.startsWith(e));

    logger.debug({ msg: 'Getting CRM service', crmType, isAllowed, url: req.url });

    CrmServiceFactory.getCrmService(crmType, auth, !isAllowed)
      .then((crmService: ICrmService) => {
        req.crmService = crmService;
        next();
      })
      .catch((err: any) => {
        logger.info({ msg: 'Unable to get CRM service', error: err.message });
        const errorCode = err.code ? err.code : 500;
        res.status(errorCode).json(
          new APIError('Unable to get CRM service', {
            details: err.toString(),
            code: errorCode
          })
        );
      });
  };
};
