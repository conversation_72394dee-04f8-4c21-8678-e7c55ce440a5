import { logger } from '../logger';
import { TokenService } from '../services/index';
import { APIError } from '../lib';
import { extractReqToken } from '../lib/utils';

export default () => {
  return (req, res, next) => {
    try {
      const token = extractReqToken(req);
      const appSource = TokenService.detectAppSource(req);
      return TokenService.verifyAccessToken(token, appSource)
        .then(async (isValid) => {
          if (!isValid) {
            throw new APIError('Unauthorized request', {
              name: 'AuthTokenError',
              details: 'Your token is not valid or expired. Please signin again',
              code: 401
            });
          }
          req.auth = await TokenService.decodeAccessToken(token);
          return TokenService.updateLastUsed(req.auth.lid)
            .then(() => {
              next();
              return true;
            })
            .catch((err) => {
              logger.error({ msg: 'DB error updating last used token, but continue anyway.', details: err });
              next();
              return true;
            });
        })
        .catch((err) => {
          res.status(err.code || 401).json(
            new APIError('Unauthorized request', {
              name: 'AuthTokenError',
              details: err,
              code: 401
            })
          );
        });
    } catch (err) {
      res.status(err.code || 401).json(
        new APIError('Unauthorized request', {
          name: 'AuthTokenError',
          details: err,
          code: 401
        })
      );
    }
  };
};
