import dotenv from 'dotenv';

if (dotenv.config({ path: `./.env` }).error) {
  throw new Error(`.env file not found in the root folder!`);
}

export default {
  env: process.env.NODE_ENV,
  envName: process.env.ENV_NAME,
  port: parseInt(process.env.API_PORT || '0', 10),
  logLevel: process.env.LOG_LEVEL || 'warn',
  logdir: process.env.LOG_DIR,
  systemEmailSender: String(process.env.SYSTEM_EMAIL_SENDER).trim(),
  scheduleOffsetSeconds: parseInt(process.env.SCHEDULE_OFFSET_SECONDS || '300'),
  internal: {
    consoleDomain: String(process.env.CONSOLE_DOMAIN).trim(),
    frontendDomain: String(process.env.FRONTEND_DOMAIN).trim(),
    assetDomain: String(process.env.ASSET_DOMAIN).trim()
  },
  salesforce: {
    auth: {
      clientId: process.env.SALESFORCE_CLIENT_ID?.trim(),
      clientSecret: process.env.SALESFORCE_CLIENT_SECRET?.trim(),
      authority: process.env.SALESFORCE_AUTHORITY?.trim(),
      redirectUri: process.env.SALESFORCE_REDIRECTURI?.trim(),
      settingsUri: process.env.SALESFORCE_SETTINGSURI?.trim()
    },
    version: process.env.SALESFORCE_API_VERSION?.trim()
  },
  affinity: {
    instanceUrl: process.env.AFFINITY_API_URL?.trim(),
    endPoints: {
      whoami: '/auth/whoami',
      orgFields: '/organizations/fields',
      personFields: '/persons/fields',
      organizations: '/organizations',
      persons: '/persons',
      fieldValues: '/field-values',
      lists: '/lists',
      rateLimit: '/rate-limit',
      interactions: '/interactions'
    }
  },
  office365: {
    auth: {
      clientId: process.env.OFFICE_365_CLIENT_ID,
      clientSecret: process.env.OFFICE_365_CLIENT_SECRET,
      authority: process.env.OFFICE_365_AUTHORITY,
      redirectUri: process.env.OFFICE_365_REDIRECTURI
    },
    endPoints: {
      getContacts: '/me/contacts',
      getEvents: '/me/calendar/events',
      getAvailability: '/me/calendar/getschedule'
    }
  },
  publicCrmEndpoints: ['/validate', '/settings', '/api-key', '/submit-key'],
  jwtAudience: '<EMAIL>',
  jwtIssuer: 'console.acqwired.com',
  jwtAlgo: 'HS256',
  jwtExpAccess: '1d',
  jwtExpRefresh: '30d',
  jwtWelcomeExpiration: '1h',

  superadminOrgID: process.env.SUPERADMIN_ORG_ID,
  source: 'acqwired',
  email: {
    subjects: {
      welcome: 'Welcome to Acqwired',
      forgotPassword: 'Forgot Password',
      resetPassword: 'Reset Password',
      invitation: 'Invitation to Acqwired'
    },
    data: {
      consoleDomain: String(process.env.CONSOLE_DOMAIN).trim(),
      frontendDomain: String(process.env.FRONTEND_DOMAIN).trim(),
      assetDomain: String(process.env.ASSET_DOMAIN).trim(),
      linkDomain: String(process.env.LINK_DOMAIN).trim(),
      source: 'acqwired',
      invitationLink: String(process.env.CONSOLE_DOMAIN).trim() + '/accept-invitation'
    }
  },
  queue: {
    enrichment: {
      name: 'enrichment'
    },
    play: {
      name: 'play'
    }
  },
  firebase: {
    databaseUrl: String(process.env.FIREBASE_DATABASE_URL).trim(),
    serviceAccount: {
      type: String(process.env.FIREBASE_SA_TYPE).trim(),
      project_id: String(process.env.FIREBASE_SA_PROJECT_ID).trim(),
      private_key_id: String(process.env.FIREBASE_SA_PRIVATE_KEY_ID).trim(),
      private_key: String(process.env.FIREBASE_SA_PRIVATE_KEY).trim(),
      client_email: String(process.env.FIREBASE_SA_CLIENT_EMAIL).trim(),
      client_id: String(process.env.FIREBASE_SA_CLIENT_ID).trim(),
      auth_uri: String(process.env.FIREBASE_SA_AUTH_URI).trim(),
      token_uri: String(process.env.FIREBASE_SA_TOKEN_URI).trim(),
      auth_provider_x509_cert_url: String(process.env.FIREBASE_SA_AUTH_PROVIDER_X509_CERT_URL).trim(),
      client_x509_cert_url: String(process.env.FIREBASE_SA_CLIENT_X509_CERT_URL).trim(),
      universe_domain: String(process.env.FIREBASE_SA_UNIVERSE_DOMAIN).trim()
    }
  },
  google: {
    maps: {
      apiKey: String(process.env.GOOGLE_MAPS_API_KEY).trim()
    }
  },
  sourceScrub: {
    apiUrl: process.env.SOURCE_SCRUB_API_URL || 'https://api.sourcescrub.com',
    identityUrl: process.env.SOURCE_SCRUB_IDENTITY_URL || 'https://identity.sourcescrub.com/connect/token',
    clientKey: process.env.SOURCE_SCRUB_CLIENT_KEY || '',
    username: process.env.SOURCE_SCRUB_USERNAME || '',
    password: process.env.SOURCE_SCRUB_PASSWORD || ''
  },
  ocean: {
    OCEAN_API_URL: String(process.env.OCEAN_API_URL).trim(),
    apiToken: String(process.env.OCEAN_API_TOKEN).trim()
  },
  exa: { 
    apiUrl: String(process.env.EXA_API_URL).trim(),
    apiKey: String(process.env.EXA_API_KEY).trim()
  }
};
