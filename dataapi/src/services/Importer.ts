import { IAuth } from '../types/Common';
import { logger } from '../logger';
import { ServerError, UnprocessableContentError } from '../lib/errors';
import { DataSourceType, EntityType } from '@prisma/client';
import EntityService from './Entity';
import { ICSVObject } from '../types/CSV';
import { ICompanyDataToWrite, ICompanyFieldData, ICompanyMandatoryData, IContactDataToWrite, IContactFieldData, IContactMandatoryData, IEntityDataToWrite, IImporterResponse } from '../types/Importer';

interface IImporterServiceParams {
  auth: IAuth;
  entityType: EntityType;
  entityService: EntityService;
}

interface IField {
  id: string;
  key: string;
}

class ImporterService {
  auth: IAuth;
  headers: string[] = [];
  fields: IField[] = [];
  entityService: EntityService;
  entityType: EntityType;

  constructor({ auth, entityType, entityService }: IImporterServiceParams) {
    this.auth = auth;
    this.entityService = entityService;
    this.entityType = entityType;
  }

  async prepareData(data: ICSVObject[], identifier: string): Promise<IEntityDataToWrite[]> {
    if (this.entityType === EntityType.company) {
      return this.prepareCompanyData(data as ICSVObject[], identifier);
    }
    if (this.entityType === EntityType.contact) {
      return this.prepareContactData(data as ICSVObject[], identifier);
    }

    throw new UnprocessableContentError('Invalid entity type');
  }

  async createEntities(entities: IEntityDataToWrite[], crop: boolean): Promise<IImporterResponse> {
    if (this.entityType === EntityType.company) {
      return this.createCompanies(entities as ICompanyDataToWrite[], crop);
    }
    if (this.entityType === EntityType.contact) {
      return this.createContacts(entities as IContactDataToWrite[], true);
    }

    throw new UnprocessableContentError('Invalid entity type');
  }

  async prepareCompanyData(companies: ICSVObject[], identifier: string): Promise<ICompanyDataToWrite[]> {
    if (companies.length === 0) {
      throw new ServerError('No companies to import');
    }
    await this.entityService.createExecutionIdentifierIfNeeded(identifier, EntityType.company);

    const schemaFields: IField[] = await this.entityService.findEnrichmentFieldsByKeys(Object.keys(companies[0]), EntityType.company);

    return companies
      .map((company: ICSVObject): ICompanyDataToWrite | null => {
        if (!company.website) {
          return null;
        }
        if (!company.name) {
          company.name = company.website;
        }
        if (!company.website.startsWith('http')) {
          company.website = `https://${company.website}`;
        }

        const mandatoryCompanyData: ICompanyMandatoryData = {
          name: company.name,
          website: company.website,
          orgId: this.auth.oid,
          ownerId: this.auth.uid,
          sourceType: DataSourceType.acqwired,
          createdAt: new Date(),
          createdById: this.auth.uid
        };
        const fieldsToBind: ICompanyFieldData[] = schemaFields.map((field: IField) => {
          return {
            schemaFieldId: field.id,
            sourceType: DataSourceType.acqwired,
            createdAt: new Date(),
            value: company[field.key] || ''
          };
        });

        return {
          company: mandatoryCompanyData,
          fields: fieldsToBind
        };
      })
      .filter((company: ICompanyDataToWrite | null) => !!company) as ICompanyDataToWrite[];
  }

  async createCompanies(companies: ICompanyDataToWrite[], crop: boolean): Promise<IImporterResponse> {
    if (companies.length === 0) {
      throw new ServerError('No companies to import');
    }

    let insertCounter: number = 0;
    let successCounter: number = 0;
    let errorCounter: number = 0;
    const startTime: number = new Date().getTime();

    for (const company of companies) {
      if (crop) {
        company.company.name = company.company.name.slice(0, 64);
      }
      insertCounter++;
      const progress = Math.round((100 * insertCounter) / companies.length);
      if (insertCounter % 1000 === 1) {
        const diff: number = new Date().getTime() - startTime;
        const eta: number = Math.round((diff * (companies.length - insertCounter)) / insertCounter / 1000);
        logger.debug({
          msg: `Creating company #${insertCounter}`,
          progress: `${progress}%, eta: ${Math.floor(eta / 60)}:${(eta % 60).toString().padStart(2, '0')}`,
          success: successCounter,
          error: errorCounter
        });
      }
      await this.entityService
        .createCompanyWithFields(company)
        .then((result: boolean): void => {
          if (result) {
            successCounter++;
          } else {
            errorCounter++;
          }
        })
        .catch((_error): void => {
          // logger.error({ msg: 'Error creating company', error });
          errorCounter++;
        });
    }

    return {
      success: successCounter,
      error: errorCounter,
      successRate: Math.floor((10000 * successCounter) / (successCounter + errorCounter)) / 100
    };
  }

  async prepareContactData(contacts: ICSVObject[], _identifier: string): Promise<IContactDataToWrite[]> {
    if (contacts.length === 0) {
      throw new ServerError('No contacts to import');
    }

    const schemaFields: IField[] = await this.entityService.findEnrichmentFieldsByKeys(Object.keys(contacts[0]), EntityType.contact);

    return contacts
      .map((contact: ICSVObject): IContactDataToWrite | null => {
        if (!contact.firstName || !contact.lastName || !contact.email) {
          return null;
        }

        const mandatoryContactData: IContactMandatoryData = {
          firstName: contact.first_name,
          lastName: contact.last_name,
          email: contact.email,
          orgId: this.auth.oid,
          ownerId: this.auth.uid,
          sourceType: DataSourceType.acqwired,
          createdAt: new Date(),
          createdById: this.auth.uid
        };
        const fieldsToBind: IContactFieldData[] = schemaFields.map((field: IField) => {
          return {
            schemaFieldId: field.id,
            sourceType: DataSourceType.acqwired,
            createdAt: new Date(),
            value: contact[field.key] || ''
          };
        });

        return {
          contact: mandatoryContactData,
          fields: fieldsToBind
        };
      })
      .filter((contact: IContactDataToWrite | null) => !!contact) as IContactDataToWrite[];
  }

  async createContacts(contacts: IContactDataToWrite[], _crop: boolean): Promise<IImporterResponse> {
    if (contacts.length === 0) {
      throw new ServerError('No contacts to import');
    }

    let successCounter: number = 0;
    let errorCounter: number = 0;

    for (const contact of contacts) {
      await this.entityService
        .createContactWithFields(contact)
        .then((result: boolean): void => {
          if (result) {
            successCounter++;
          } else {
            errorCounter++;
          }
        })
        .catch((error): void => {
          logger.error({ msg: 'Error creating contact', error });
          errorCounter++;
        });
    }

    return {
      success: successCounter,
      error: errorCounter,
      successRate: Math.floor((10000 * successCounter) / (successCounter + errorCounter)) / 100
    };
  }
}

export { ImporterService };
