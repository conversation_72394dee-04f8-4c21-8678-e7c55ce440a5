import { db } from '../database';
import BaseService from './BaseService';
import { IAuth } from '../types/Common';
import { PlayNamesMap, activityTitles, ActivityResponseStatus, IActivity, ICreateActivityPayload, IUpdateActivityPayload, IActivityParticipantResponse, IActivityParticipant, ActivityErrorDetail } from '../types/Activity';
import { ActivityParticipant, ActivityStatus, EmailStatus, EntityType } from '@prisma/client';
import { logger } from '../logger';
import { ActivitySource, CrmEntityType, CrmType } from '@prisma/client';
import { IActivity as ICrmActivity, IActivityParticipant as ICrmActivityParticipant } from '../types/Crm';
import { DB_TRANSACTION_SETTING } from '../constants';
import { IResponseStatus } from '../types/Entity';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import dayjs from 'dayjs';
import { BadRequestError, NotFoundError, UnprocessableContentError } from '../lib/errors';
dayjs.extend(utc);
dayjs.extend(timezone);

export default class ActivityService extends BaseService {
  constructor({ auth }: { auth: IAuth }) {
    super({ auth });
  }

  async saveActivity(activity: ICrmActivity, oid: string, crmType: CrmType): Promise<boolean> {
    try {
      // 1. Validate mandatory fields
      if (!activity.id || !activity.type || !activity.status || !activity.entityType) {
        logger.error({ msg: 'Missing mandatory fields in activity', activity });
        throw new Error('Missing mandatory fields in activity');
      }

      // 2. Validate and map participants
      const participants = await this.validateAndMapParticipants(activity.participant, oid, crmType);
      if (participants.length === 0) {
        logger.warn({ msg: 'No valid participants found for activity', activity });
        throw new Error('No valid participants found for activity');
      }

      // 3. Use a transaction with DB_TRANSACTION_SETTING
      await db.$transaction(async (trans) => {
        // 4. Find the CrmActivity in the database using crmId
        let crmActivity = await this.findCrmActivity(activity.id, oid, crmType, activity.entityType, trans);

        // 4.1 If not found, create a new CrmActivity along with its entry in Activity table
        if (!crmActivity) {
          crmActivity = await this.createCrmActivity(activity, oid, crmType, trans);
        } else {
          // 5. If found, update the fields in Activity, CrmActivity tables
          await this.updateActivity(activity, crmActivity.activityId, trans);
          await this.updateCrmActivity(activity, crmActivity.id, trans); // Update CrmActivity
        }

        // 6. Update the ActivityParticipant table
        await this.updateActivityParticipants(crmActivity.activityId, participants, trans);

        logger.debug({ msg: `Activity ${crmActivity.activityId} saved/updated successfully with ${participants.length} participants.` });
      }, DB_TRANSACTION_SETTING);

      return true; // Return success
    } catch (error) {
      logger.error({ msg: 'Error saving activity', error: (error as Error).message });
      return false;
    }
  }

  // Helper to validate and map participants
  private async validateAndMapParticipants(participants: ICrmActivityParticipant[] | undefined, oid: string, crmType: CrmType): Promise<ICrmActivityParticipant[]> {
    if (!participants || participants.length === 0) return [];

    const validatedParticipants: ICrmActivityParticipant[] = [];

    for (const participant of participants) {
      let systemContactId: string | null = null;
      let systemCompanyId: string | null = null;

      // Find the system contactId from CrmContact
      if (participant.contactId) {
        const contact = await db.crmContact.findUnique({
          where: {
            crmContactConnection: {
              crmId: participant.contactId,
              orgId: oid,
              crmType: crmType
            }
          }
        });
        if (contact) {
          systemContactId = contact.contactId;
        }
      }

      // Find the system companyId from CrmCompany
      if (participant.companyId) {
        const company = await db.crmCompany.findUnique({
          where: {
            crmCompanyConnection: {
              crmId: participant.companyId,
              orgId: oid,
              crmType: crmType
            }
          }
        });
        if (company) {
          systemCompanyId = company.companyId;
        }
      }

      // Add to validated participants list if at least one valid companyId/contactId is found
      if (systemContactId || systemCompanyId) {
        validatedParticipants.push({
          contactId: systemContactId,
          companyId: systemCompanyId
        });
      }
    }

    return validatedParticipants;
  }

  // Helper to find CrmActivity using CRM id
  private async findCrmActivity(crmId: string, oid: string, crmType: CrmType, entityType: CrmEntityType, trans) {
    return trans.crmActivity.findUnique({
      where: {
        crmActivityConnection: {
          crmId: crmId,
          orgId: oid,
          crmType: crmType,
          entityType
        }
      }
    });
  }

  // Helper to create CrmActivity and link it to an Activity
  private async createCrmActivity(activity: ICrmActivity, oid: string, crmType: CrmType, trans) {
    return trans.crmActivity.create({
      data: {
        crmId: activity.id,
        orgId: oid,
        crmType: crmType,
        entityType: activity.entityType,
        title: activity.title,
        status: activity.status,
        content: activity.content,
        dueDate: activity.dueDate,
        startDate: activity.startDateTime,
        endDate: activity.endDateTime,
        activity: {
          create: {
            orgId: oid,
            type: activity.type,
            source: ActivitySource.crm
          }
        },
        field: activity // Store full activity object as JSON
      }
    });
  }

  // Helper to update an existing Activity
  private async updateActivity(activity: ICrmActivity, activityId: string, trans) {
    return trans.activity.update({
      where: { id: activityId },
      data: {
        type: activity.type,
        source: ActivitySource.crm,
        deletedAt: null, // Mark as not deleted if found,
        deletedById: null // Reset deletedBy if found
      }
    });
  }

  // Helper to update CrmActivity with the latest data
  private async updateCrmActivity(activity: ICrmActivity, crmActivityId: string, trans) {
    return trans.crmActivity.update({
      where: { id: crmActivityId },
      data: {
        title: activity.title,
        content: activity.content,
        status: activity.status,
        dueDate: activity.dueDate,
        startDate: activity.startDateTime,
        endDate: activity.endDateTime,
        field: activity // Update full activity object as JSON
      }
    });
  }

  // Helper to update ActivityParticipant table
  private async updateActivityParticipants(activityId: string, participants: ICrmActivityParticipant[], trans) {
    // Delete existing participants for the activity
    await trans.activityParticipant.deleteMany({
      where: { activityId: activityId }
    });

    // Add new participants
    for (const participant of participants) {
      await trans.activityParticipant.create({
        data: {
          activityId: activityId,
          contactId: participant.contactId || null,
          companyId: participant.companyId || null
        }
      });
    }
  }

  async formatActivityResponse(activityParticipant: IActivityParticipantResponse[], id: string, entityType: EntityType): Promise<IActivity[]> {

    const playIdSet = new Set<string>();

    activityParticipant.forEach((participant) => {
      const { channelEmail } = participant.activity;

      if (channelEmail && (channelEmail.status === EmailStatus.cancelled || channelEmail.status === EmailStatus.draft)) {
        return;
      }
      if (channelEmail && channelEmail.execution) {
        playIdSet.add(channelEmail.execution.typeId);
      }
    });

    const playNamesMap = this.getPlayNames(Array.from(playIdSet));

    const response = activityParticipant.map((participant) => {
      const { channelEmail, acqwiredActivity, crmActivity, participants } = participant.activity;

      if (channelEmail && (channelEmail.status === EmailStatus.cancelled || channelEmail.status === EmailStatus.draft)) {
        return undefined;
      }

      if ((crmActivity && crmActivity.status === ActivityStatus.cancelled) || (acqwiredActivity && acqwiredActivity.status === ActivityStatus.cancelled)) {
        return undefined;
      }

      const playName = channelEmail && channelEmail.execution ? playNamesMap[channelEmail.execution.typeId] : '';

      let to: string = '';
      const others: string[] = [];
      participants.forEach((participant) => {
        if (entityType === EntityType.contact && participant.contact) {
          if (participant.contact.id === id) {
            to = `${participant.contact.firstName} ${participant.contact.lastName}`;
          } else {
            others.push(`${participant.contact.firstName} ${participant.contact.lastName}`);
          }
        } else if (entityType === EntityType.company && participant.company) {
          if (participant.contact && participant.company.id === id) {
            to = `${participant.contact.firstName} ${participant.contact.lastName}`;
          } else if (participant.contact) {
            others.push(`${participant.contact.firstName} ${participant.contact.lastName}`);
          }
          if (!to) {
            to = participant.company.name;
          }
        }
      });

      let formattedActivity: Partial<IActivity> = {
        id: participant.id,
        companyId: participant.companyId,
        contactId: participant.contactId,
        type: participant.activity.type,
        source: participant.activity.source,
        to,
        others,
        by: `${this.auth.firstName} ${this.auth.lastName}`,
        followUp: playName,
      };

      const activitySpecificData =
        acqwiredActivity ? {
          title: acqwiredActivity.title || activityTitles[participant.activity.type],
          content: acqwiredActivity.content,
          dueDate: acqwiredActivity.dueDate,
          status: acqwiredActivity.status,
        } : crmActivity ? {
          title: crmActivity.title || activityTitles[participant.activity.type],
          content: crmActivity.content,
          dueDate: crmActivity.dueDate,
          status: crmActivity.status
        } : channelEmail ? {
          title: activityTitles[participant.activity.type],
          sourceId: channelEmail.id,
          errorMessage: channelEmail.status === EmailStatus.error ? channelEmail.errorMessage : '',
          content: channelEmail.body,
          subject: channelEmail.subject,
          status: channelEmail.status,
          dueDate: channelEmail.status === EmailStatus.scheduled ? channelEmail.scheduledAt as Date : channelEmail.createdAt,
        } : undefined;

      formattedActivity = { ...formattedActivity, ...activitySpecificData } as IActivity;

      let isScheduled: boolean = false;
      if (activitySpecificData) {
        isScheduled = activitySpecificData.status === ActivityStatus.scheduled;
      }

      const activityDate = new Date(activitySpecificData!.dueDate || '');

      if (isScheduled && activityDate.getTime() >= Date.now()) {
        formattedActivity.group = ActivityResponseStatus.Upcoming;
      } else if (dayjs(activityDate).isSame(dayjs(), 'month')) {
        formattedActivity.group = ActivityResponseStatus.Recent;
      } else {
        const monthYear = dayjs(activityDate).format('MMMM YYYY');
        formattedActivity.group = monthYear;
      }

      return formattedActivity;
    })
      .filter((f) => !!f);

    return response as IActivity[];
  }

  async getPlayNames(ids: string[]): Promise<PlayNamesMap> {
    if (ids && ids.length > 0) {
      const playData = await db.play.findMany({
        where: {
          id: {
            in: ids
          }
        },
        select: {
          id: true,
          name: true
        }
      });

      return playData.reduce((acc, play) => {
        acc[play.id] = play.name;
        return acc;
      }, {} as PlayNamesMap);
    }

    return {};
  }

  async createActivity(body: ICreateActivityPayload): Promise<IResponseStatus> {

    const validation = await this.validateActivityPayload(body, true);
    if (!validation.success) {
      return validation;
    };

    return db.$transaction(async (trans): Promise<IResponseStatus> => {
      return trans.activity
        .create({
          data: {
            orgId: this.auth.oid,
            source: body.source,
            type: body.type,
            acqwiredActivity: {
              create: {
                content: body.content,
                status: body.status,
                dueDate: new Date(body.date),
                createdById: this.auth.uid
              }
            },
            participants: {
              create: [
                {
                  contactId: body.contactId,
                  companyId: body.companyId
                }
              ]
            }
          }
        })
        .then(() => {
          return { success: true, message: '' };
        });
    });
  }

  async updateParticipantAndActivity(body: IUpdateActivityPayload): Promise<IResponseStatus> {

    const validation = await this.validateActivityPayload(body, false);
    if (!validation.success) {
      return validation;
    };

    return db.$transaction(async (trans): Promise<IResponseStatus> => {

      return trans.activityParticipant.findFirst({
        where: { id: body.id },
        select: {
          activityId: true,
          activity: {
            select: {
              acqwiredActivity: {
                select: { status: true },
              },
            },
          },
        }
      }).then(async (res) => {
        if (!res) {
          throw new NotFoundError(ActivityErrorDetail.ACTIVITY_NOT_FOUND);
        }
        if (res.activity.acqwiredActivity && res.activity.acqwiredActivity.status === ActivityStatus.completed) {
          throw new UnprocessableContentError(ActivityErrorDetail.ACTIVITY_COMPLETED);
        }
        const updatedParticipant: ActivityParticipant = await trans.activityParticipant.update({
          where: { id: body.id },
          data: {
            contactId: body.contactId,
            companyId: body.companyId,
          }
        });

        await trans.activity.update({
          where: { id: updatedParticipant.activityId },
          data: {
            orgId: this.auth.oid,
            type: body.type,
            acqwiredActivity: {
              update: {
                content: body.content,
                status: ActivityStatus.scheduled,
                dueDate: new Date(body.date),
                createdById: this.auth.uid,
              },
            },
          },
        });

        return { success: true, message: '' };
      });

    });
  }

  async validateActivityPayload(body: ICreateActivityPayload | IUpdateActivityPayload, isNew: boolean = true): Promise<{ success: boolean; message: string }> {
    const requiredFields = [
      { field: 'type', label: 'Activity' },
      { field: 'companyId', label: 'Company' },
      { field: 'contactId', label: 'Contact' },
      { field: 'date', label: 'Date' },
      { field: 'content', label: 'Content' },
    ];

    if (isNew) {
      requiredFields.unshift({ field: 'source', label: 'Source' });
      requiredFields.unshift({ field: 'status', label: 'Status' });
    }
    if (!isNew) {
      requiredFields.unshift({ field: 'id', label: 'Id' });
    }

    const missingFields = requiredFields
      .filter(({ field }) => !body[field])
      .map(({ label }) => label);

    if (missingFields.length > 0) {
      return { success: false, message: `Please provide valid ${missingFields.join(', ')}.` };
    }

    const now = dayjs();
    if (dayjs(body.date).isBefore(now, 'day')) {
      throw new BadRequestError(ActivityErrorDetail.INVALID_DATE);
    }

    if (dayjs(body.date).isSame(now, 'day')) {
      body.date = now.format('YYYY-MM-DDTHH:mm:ss.SSS');
    } else {
      body.date = dayjs(body.date).format('YYYY-MM-DDTHH:mm:ss.SSS');
    }

    return { success: true, message: '' };
  }

  async getParticipant(id: string): Promise<IActivityParticipant | null> {

    return db.activityParticipant.findFirst({
      where: { id },
      select: {
        contact: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        company: {
          select: {
            id: true,
            name: true,
            website: true
          },
        },
        activity: {
          select: {
            type: true,
            acqwiredActivity: {
              select: {
                content: true,
                dueDate: true,
                status: true
              }
            }
          }
        }
      }
    });
  }

  async deleteParticipant(id: string): Promise<IResponseStatus> {

    return db.activityParticipant.findFirst({
      where: { id },
      select: { activityId: true }
    }).then(participant => {
      if (!participant) {
        throw new NotFoundError(ActivityErrorDetail.PARTICIPANT_NOT_FOUND);
      }

      return db.acqwiredActivity.findFirst({
        where: { activityId: participant.activityId },
        select: { status: true }
      }).then((activity) => {
        if (!activity) {
          throw new NotFoundError(ActivityErrorDetail.ACTIVITY_NOT_FOUND);
        }

        if (activity.status === ActivityStatus.completed) {
          throw new UnprocessableContentError(ActivityErrorDetail.ACTIVITY_COMPLETED);
        }

        return db.activity.update({
          where: { id: participant.activityId },
          data: { deletedAt: new Date() }
        }).then(() => {
          return { success: true, message: '' };
        }).catch(err => { return { success: false, message: err }; });
      });
    }).catch(() => { throw new BadRequestError(ActivityErrorDetail.DELETE_ACTIVITY_FAILED); });
  }
}
