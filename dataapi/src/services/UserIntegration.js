import { logger } from '../logger';
import config from '../config';
import { db } from '../database';
import { IntegrationProvider } from '@prisma/client';

export default class UserIntegration {
  auth = null;

  constructor(auth) {
    this.auth = auth;

    if (!auth.oid) {
      logger.error({ error: 'Invalid organization Id', orgId: auth.oid });
      throw new Error('Invalid service call! Check your permissions!');
    }
  }

  async extractO365Integration(data) {
    const { code, state } = data;

    const codeVerifier = state.toString('base64').replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');

    const postHeaders = new Headers();
    postHeaders.append('Content-Type', 'application/x-www-form-urlencoded');

    const postData = new URLSearchParams();
    postData.append('client_id', config.office365.auth.clientId);
    postData.append('scope', 'offline_access openid Mail.Read Mail.Send');
    postData.append('redirect_uri', config.office365.auth.redirectUri);
    postData.append('grant_type', 'authorization_code');
    postData.append('client_secret', config.office365.auth.clientSecret);
    postData.append('code', code);
    postData.append('code_verifier', codeVerifier);

    return fetch(config.office365.auth.authority + '/oauth2/v2.0/token', {
      method: 'POST',
      headers: postHeaders,
      body: postData,
      redirect: 'follow'
    })
      .then((res) => res.json())
      .then(async (response) => {
        const { error, error_description, refresh_token, access_token } = response;
        if (error || !refresh_token || !access_token) {
          logger.error({ msg: 'Office365 returned an error!', error, error_description });
          logger.error({ response });
          throw new Error(`Office365 returned an error ${error} ${error_description}`);
        }
        if (!refresh_token || !access_token) {
          throw new Error('No token found in office365 response. Please check and authorize properly.');
        }

        const splitedToken = access_token.split('.');
        if (splitedToken.length < 2) {
          throw new Error('Invalid office365 Account. Personal Accounts are not support. Please authorize via your O365 Organization account.');
        }
        const account = JSON.parse(Buffer.from(splitedToken[1], 'base64'));
        const expires = new Date();
        expires.setHours(expires.getHours() + 60 * 24);

        return {
          expiresAt: expires,
          provider: IntegrationProvider.office365,
          name: account['unique_name'],
          metadata: {
            type: 'email',
            name: account?.name,
            ipaddr: account?.ipaddr,
            aTokenExpiresAt: new Date(account.exp * 1000),
            rTokenExpiresAt: expires
          },
          token: {
            accessToken: access_token,
            refreshToken: refresh_token
          }
        };
      })
      .catch((err) => {
        throw new Error(err);
      });
  }

  async createIntegration({ expiresAt, provider, name, metadata, token }) {
    logger.info({ msg: 'Creating new integration', name, provider, metadata });
    const { uid: userId, oid: orgId } = this.auth;

    const create = {
      expiresAt,
      name,
      metadata,
      token,
      refreshedAt: new Date(),
      updatedAt: new Date(),
      provider,
      user: {
        connect: {
          id: userId
        }
      },
      organization: {
        connect: {
          id: orgId
        }
      }
    };

    const update = {
      expiresAt,
      name,
      metadata,
      token,
      refreshedAt: new Date(),
      updatedAt: new Date()
    };

    const where = {
      uniqueOrgUserIntegration: { orgId, userId, provider }
    };

    return db.UserIntegration.upsert({ where, update, create });
  }

  async getIntegrations() {
    const { uid: userId } = this.auth;
    logger.info({ msg: 'Reading integrations', userId });

    return db.UserIntegration.findMany({
      where: { userId },
      select: {
        id: true,
        name: true,
        provider: true,
        createdAt: true,
        expiresAt: true,
        refreshedAt: true,
        user: {
          select: {
            firstName: true,
            lastName: true
          }
        }
      }
    });
  }

  async removeIntegration(integrationId) {
    logger.info({ msg: 'Removing integration', integrationId });
    const user = await db.User.findFirst({
      where: {
        id: this.auth.uid
      },
      select: {
        integrations: {
          where: { id: integrationId },
          select: { token: true }
        }
      }
    });
    if (!user) {
      throw new Error('Cannot find user');
    }

    if (user.integrations.length === 0) {
      throw new Error('Cannot find integration');
    }

    const postHeaders = new Headers();
    postHeaders.append('Content-Type', 'application/x-www-form-urlencoded');

    const postData = new URLSearchParams();
    postData.append('client_id', config.office365.auth.clientId);
    postData.append('client_secret', config.office365.auth.clientSecret);
    postData.append('token', user.integrations[0].token.accessToken);
    postData.append('token_type_hint', 'access_token');

    return fetch(config.office365.auth.authority + '/oauth2/v2.0/token', {
      method: 'POST',
      headers: postHeaders,
      body: postData,
      redirect: 'follow'
    })
      .then(async () => {
        await db.UserIntegration.delete({ where: { id: integrationId } });
        return true;
      })
      .catch((_err) => false);
  }
}
