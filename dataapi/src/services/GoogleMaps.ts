import config from '../config';
import { logger } from '../logger';

export interface GoogleMapsServiceSearchTextInput {
  textQuery: string;
  locationBias: {
    lat: number;
    lng: number;
  };
  maxResultCount: number;
  radius: number;
  fields?: string[];
}

export interface Place {
  location: {
    latitude: number;
    longitude: number;
  };
  websiteUri?: string;
  displayName: {
    text: string;
    languageCode: string;
  };
}

const MAX_REQUEST_IN_BATCH = 5;
const MAX_PAGE_SIZE = 20;
const MAX_DOCUMENTS_ALLOWED_TO_RETURN = 100;

export default class GoogleMapsService {
  // https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places/searchNearby
  static async searchText({ textQuery, locationBias, maxResultCount, radius, fields }: GoogleMapsServiceSearchTextInput): Promise<Place[]> {
    const effectiveMaxResultCount = Math.min(MAX_DOCUMENTS_ALLOWED_TO_RETURN, maxResultCount ?? MAX_PAGE_SIZE);

    const foundPlaces: Place[] = [];
    const seenPlaces = new Set<string>();
    let requestCount = 0;
    let nextPageToken: string | undefined = undefined;

    const returnFields = fields ?? ['places.displayName', 'places.websiteUri'];
    // nextPageToken should be sent via X-Goog-FieldMask
    returnFields.push('nextPageToken');

    do {
      const requestBody: any = {
        textQuery,
        pageSize: Math.min(effectiveMaxResultCount - foundPlaces.length, MAX_PAGE_SIZE),
        locationBias: {
          circle: {
            center: {
              latitude: locationBias.lat,
              longitude: locationBias.lng
            },
            radius: radius * 1609 // Convert miles to meters
          }
        }
      };

      // Include nextPageToken for subsequent requests
      if (nextPageToken) {
        requestBody.pageToken = nextPageToken;
      }

      const batch = await fetch('https://places.googleapis.com/v1/places:searchText', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
          'X-Goog-FieldMask': returnFields.join(','),
          'X-Goog-Api-Key': config.google.maps.apiKey,
          Referer: config.internal.consoleDomain
        }
      })
        .then((res) => res.json())
        .catch((err) => {
          logger.error({ msg: 'Failed to fetch places', error: err });
          return null;
        });

      if (batch?.places) {
        for (const place of batch.places) {
          // Generate a unique key based on available properties
          const uniqueKey = place.location ? `${place.location.latitude ?? 'unknown'},${place.location.longitude ?? 'unknown'}` : `${place.displayName?.text ?? 'unknown'},${place.websiteUri ?? 'unknown'}`;

          if (!seenPlaces.has(uniqueKey)) {
            seenPlaces.add(uniqueKey);
            foundPlaces.push(place);
          }
        }
      }

      // Update nextPageToken for the next iteration
      nextPageToken = batch?.nextPageToken;

      // Delay between requests if nextPageToken is present
      if (nextPageToken) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      requestCount++;
    } while (nextPageToken && foundPlaces.length < effectiveMaxResultCount && requestCount < MAX_REQUEST_IN_BATCH);

    return foundPlaces;
  }
}
