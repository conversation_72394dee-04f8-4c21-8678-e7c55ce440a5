import config from '../config';
import { IExaCompany, IExaSearchResponse } from '../types/Exa';
import { logger } from '../logger';

export default class ExaService {
  /**
   * Get companies associated with a specific query
   * @param query - The query to search for
   * @param limit - Optional limit for the number of results per page (default is 100)
   * @returns A promise resolving to an array of companies
   */
  static async getCompanies({ query, limit = 100 }: { query: string; limit?: number }): Promise<IExaCompany[]> {
    const baseUrl = `${config.exa.apiUrl}/search`;

    try {
      logger.info({
        msg: 'Fetching Exa companies for query',
        query,
        limit
      });

      const url = new URL(baseUrl);
      const response = await fetch(url.toString(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': config.exa.apiKey
        },
        body: JSON.stringify({
          query,
          numResults: limit,
          category: 'company',
          text: true
        })
      });

      // Handle potential errors
      if (!response.ok) {
        if (response.status === 429) {
          throw new Error('Monthly API request limit reached');
        } else if (response.status === 404) {
          throw new Error(`Query ${query} not found`);
        } else {
          throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
        }
      }

      const data = (await response.json()) as IExaSearchResponse;

      logger.info({
        msg: 'Successfully fetched all companies from Exa for query',
        query,
        count: data.results.length
      });

      return data.results;
    } catch (error) {
      logger.error({
        msg: 'Failed to fetch companies from Exa for query',
        query,
        error
      });
      throw error;
    }
  }
}
