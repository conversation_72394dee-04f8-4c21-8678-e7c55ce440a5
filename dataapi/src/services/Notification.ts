import { IAuth } from '../types/Common';
import { logger } from '../logger';
import { UnauthorizedError } from '../lib/errors';
import { NotificationProvider } from '../lib/notification/Notification';

export default class NotificationService {
  auth: IAuth;
  provider: NotificationProvider;

  constructor({ auth, provider }) {
    this.auth = auth;

    if (!auth.oid) {
      logger.error({ error: 'Invalid organization Id' });
      throw new UnauthorizedError('Invalid service call! Check your permissions!');
    }
    this.provider = provider;
  }

  async createCustomToken(): Promise<any> {
    return this.provider.createCustomToken();
  }

  async writeData(db: string, data: object) {
    return this.provider.writeData(db, data);
  }

  async updateData(db: string, data: object) {
    return this.provider.updateData(db, data);
  }
}
