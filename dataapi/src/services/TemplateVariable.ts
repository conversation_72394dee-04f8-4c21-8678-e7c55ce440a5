import { IAuth } from '../types/Common';
import { logger } from '../logger';
import { Patterns } from '../utils';
import { BadRequestError, EntityFieldNotFound } from '../lib/errors';
import { db } from '../database';
import { EmailStatus, EntityType, VariableType } from '@prisma/client';
import { convert } from 'html-to-text';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import advancedFormat from 'dayjs/plugin/advancedFormat';

const variableConfiguration = {
  entities: ['contact', 'company'],
  mapping: {
    user: {
      first_name: 'first_name',
      last_name: 'last_name',
      name: 'name',
      calendar_availability: 'calendar_availability',
      meeting_length: 'meeting_length',
      timezone: 'timezone'
    },
    organization: {
      name: 'name',
      website: 'website',
      about_us: 'about_us',
      website_content: 'website_content'
    },
    system: {
      current_date: 'current_date'
    },
    contact_activity: {
      recent_email: 'recent_email'
    }
  }
};

export default class TemplateVariables {
  auth: IAuth;
  executers: object;

  constructor({ auth }: { auth: IAuth }) {
    this.auth = auth;
    this.executers = {
      user: new UserVariables({ auth }),
      organization: new OrganizationVariables({ auth }),
      system: new SystemVariables({ auth }),
      contact_activity: new ContactActivityVariables({ auth }),
      company: new CompanyVariables({ auth }),
      contact: new ContactVariables({ auth })
    };
  }

  async getAllPseudoVariables() {
    return db.templateVariables.findMany({
      where: {
        type: VariableType.pseudo
      }
    });
  }

  async get(key: string, input?: object): Promise<string> {
    // @todo: If the key is in uuid format, get the key by uuid from db, and process the rest with this key. (instead of creating a separate getById method)

    if (!this.checkIfKeyValid(key)) {
      throw new BadRequestError('Invalid key', { key });
    }
    const [namespace, method] = key.split('.');
    if (!this.checkIfNamespaceValid(namespace)) {
      throw new BadRequestError('Invalid namespace', { namespace });
    }

    if (variableConfiguration.entities.includes(namespace)) {
      if (!input) {
        throw new BadRequestError('Entity parameters required', { namespace, method });
      }
      return this.getEntityVariable(key, input);
    }

    if (!this.checkIfMethodValid(namespace, method)) {
      throw new BadRequestError('Invalid method', { namespace, method });
    }
    return this.getPseudoVariable(key, input);
  }

  validate(key: string): boolean {
    if (!this.checkIfKeyValid(key)) {
      return false;
    }

    const [namespace, method] = key.split('.');
    if (!this.checkIfNamespaceValid(namespace)) {
      return false;
    }

    if (variableConfiguration.entities.includes(namespace)) {
      return true;
    }

    if (!this.checkIfMethodValid(namespace, method)) {
      return false;
    }

    return true;
  }

  getType(key: string): string {
    const [namespace] = key.split('.');
    if (['user', 'organization', 'system', 'contact_activity', 'idea'].includes(namespace)) {
      return 'pseudo';
    }
    if (['contact', 'company'].includes(namespace)) {
      return 'entity';
    }

    throw new BadRequestError('Invalid namespace', { namespace });
  }

  private async getPseudoVariable(key: string, input?: object): Promise<string> {
    const [namespace, method] = key.split('.');

    if (!this.executers[namespace][method]) {
      throw new BadRequestError('Invalid variable executer', { namespace, method });
    }

    if (this.executers[namespace][method].length === 0) {
      return await this.executers[namespace][method]();
    }
    if (this.executers[namespace][method].length === 1 && input) {
      return await this.executers[namespace][method](input);
    }

    throw new BadRequestError('Invalid variable executer parameters', { namespace, method, input });
  }

  private async getEntityVariable(key: string, input: object): Promise<string> {
    const [namespace, method] = key.split('.');

    return this.executers[namespace].field(method, input);
  }

  private checkIfNamespaceValid(namespace: string): boolean {
    return namespace in variableConfiguration.mapping || variableConfiguration.entities.includes(namespace);
  }

  private checkIfMethodValid(namespace: string, method: string): boolean {
    return method in variableConfiguration.mapping[namespace];
  }

  private checkIfKeyValid(key: string): boolean {
    if (!Patterns.VariableKey.test(key)) {
      logger.error({ msg: 'Invalid key', key });
      return false;
    }

    return true;
  }
}

class TemplateVariableBase {
  auth: IAuth;

  constructor(auth: IAuth) {
    this.auth = auth;
  }
}

class SystemVariables extends TemplateVariableBase {
  constructor({ auth }: { auth: IAuth }) {
    super(auth);
  }

  async current_date(): Promise<string> {
    return db.userCalendar
      .findFirst({
        where: { userId: this.auth.uid },
        select: { timezone: true }
      })
      .then((result: { timezone: number } | null) => {
        const offset: number = result?.timezone || 0;
        dayjs.extend(utc);
        dayjs.extend(timezone);
        dayjs.extend(advancedFormat);
        dayjs(new Date()).utcOffset(offset);
        // Text formatted version of Date/Time as “Monday, June 5th, 2023”.
        return dayjs().format('dddd, MMMM Do, YYYY');
      });
  }
}

class UserVariables extends TemplateVariableBase {
  constructor({ auth }: { auth: IAuth }) {
    super(auth);
  }

  async first_name(): Promise<string> {
    return this.auth.firstName;
  }

  async last_name(): Promise<string> {
    return this.auth.lastName;
  }

  async name(): Promise<string> {
    return `${await this.first_name()} ${await this.last_name()}`;
  }

  async timezone(): Promise<string> {
    return db.userCalendar
      .findFirst({
        where: { userId: this.auth.uid },
        select: { timezone: true }
      })
      .then((result: { timezone: number } | null) => {
        if (result === null) {
          return '';
        }
        const tz = result.timezone;
        if (tz > 0) {
          return `UTC+${tz}`;
        }
        if (tz === 0) {
          return 'UTC';
        }
        return `UTC${tz}`;
      });
  }

  async calendar_availability(): Promise<string> {
    // @todo: To be implemented later with the calendar service
    return '';
  }

  async meeting_length(): Promise<string> {
    return db.userCalendar
      .findFirst({
        where: { userId: this.auth.uid },
        select: { meetingLength: true }
      })
      .then((result: { meetingLength: number } | null) => result?.meetingLength.toString() || '');
  }
}

class OrganizationVariables extends TemplateVariableBase {
  constructor({ auth }: { auth: IAuth }) {
    super(auth);
  }

  async name(): Promise<string> {
    return db.organization
      .findUnique({
        where: { id: this.auth.oid },
        select: { name: true }
      })
      .then((result: { name: string } | null) => result?.name || '');
  }

  async website(): Promise<string> {
    return db.organization
      .findUnique({
        where: { id: this.auth.oid },
        select: { website: true }
      })
      .then((result: { website: string } | null) => result?.website || '');
  }

  async about_us(): Promise<string> {
    return db.userSetting
      .findFirst({
        where: {
          userId: this.auth.uid,
          orgId: this.auth.oid,
          key: 'ORG_SUMMARY'
        },
        select: { value: true }
      })
      .then((result: { value: string } | null) => result?.value || '');
  }
}

interface IContactActivityRecentEmailInput {
  contactId: string;
}

class ContactActivityVariables extends TemplateVariableBase {
  htmlToTextOptions: object = {
    leadingLineBreaks: 0,
    trailingLineBreaks: 0,
    wordwrap: 130,
    longWordSplit: { forceWrapOnLimit: true },
    selectors: [
      { selector: 'img', format: 'skip' },
      { selector: 'a', format: 'inline', ignoreHref: true },
      { selector: 'button', format: 'inline' },
      { selector: 'a.button', format: 'inline' },
      { selector: 'ul', format: 'block' },
      { selector: 'ol', format: 'block' },
      { selector: 'br', format: 'skip' },
      { selector: 'li', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1, uppercase: true } },
      { selector: 'h1', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 1, uppercase: true } },
      { selector: 'h2', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 1, uppercase: true } },
      { selector: 'h3', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 1, uppercase: true } },
      { selector: 'h4', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 1, uppercase: true } },
      { selector: 'h5', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 1, uppercase: true } },
      { selector: 'h6', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 1, uppercase: true } }
    ]
  };

  constructor({ auth }: { auth: IAuth }) {
    super(auth);
  }

  async recent_email(input: IContactActivityRecentEmailInput): Promise<string> {
    return db.channelEmail
      .findFirst({
        where: { contactId: input.contactId, orgId: this.auth.oid, status: EmailStatus.sent },
        select: { body: true }
      })
      .then((result: { body: string } | null) => {
        if (!result) {
          return '';
        }

        return convert(result.body, this.htmlToTextOptions).replace(/(\S{50,})/g, (match: string): string => {
          return match.length <= 50 ? match : '';
        });
      });
  }
}

interface ICompanyVariablesInput {
  companyId: string;
}

interface IContactVariablesInput {
  contactId: string;
}

class CompanyVariables extends TemplateVariableBase {
  constructor({ auth }: { auth: IAuth }) {
    super(auth);
  }

  async field(key: string, input: ICompanyVariablesInput): Promise<string> {
    return db.entityField
      .findFirst({
        where: { entity: EntityType.company, key, orgId: this.auth.oid },
        select: { id: true }
      })
      .then(async (result: { id: string } | null) => {
        if (!result) {
          throw new EntityFieldNotFound(`Entity field not found ${key}`, {
            param: key
          });
        }

        return db.companyField
          .findFirst({
            where: { companyId: input.companyId, schemaFieldId: result.id },
            select: { value: true }
          })
          .then((result: { value: string } | null) => result?.value || '');
      });
  }
}

class ContactVariables extends TemplateVariableBase {
  constructor({ auth }: { auth: IAuth }) {
    super(auth);
  }

  async field(key: string, input: IContactVariablesInput): Promise<string> {
    return db.entityField
      .findFirst({
        where: { entity: EntityType.contact, key, orgId: this.auth.oid },
        select: { id: true }
      })
      .then(async (result: { id: string } | null) => {
        if (!result) {
          throw new EntityFieldNotFound(`Entity field not found ${key}`, {
            param: key
          });
        }

        return db.contactField
          .findFirst({
            where: { contactId: input.contactId, schemaFieldId: result.id },
            select: { value: true }
          })
          .then((result: { value: string } | null) => result?.value || '');
      });
  }
}
