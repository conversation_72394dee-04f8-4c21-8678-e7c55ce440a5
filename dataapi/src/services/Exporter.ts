import { IAuth } from '../types/Common';
import { EntityType } from '@prisma/client';
import { ICompanyDataForCsv, IContactDataForCsv } from '../types/CSV';
import EntityService from './Entity';
import { ICompanyDataForExport, ICompanyFieldDataForExport, IContactDataForExport, IContactFieldDataForExport } from '../types/Exporter';
import { logger } from '../logger';

interface IExporterServiceParams {
  auth: IAuth;
  fields: string[];
  entityType: EntityType;
  entityService: EntityService;
}

interface IField {
  id: string;
  key: string;
}

class ExporterService {
  auth: IAuth;
  headers: string[] = [];
  requestedFields: string[] = [];
  entityFields: IField[] = [];
  entityService: EntityService;
  entityType: EntityType;

  constructor({ auth, fields, entityType, entityService }: IExporterServiceParams) {
    this.auth = auth;
    this.requestedFields = fields;
    this.entityService = entityService;
    this.entityType = entityType;
  }

  private async prepareCompanyData(): Promise<ICompanyDataForCsv[]> {
    const fields: IField[] = await this.entityService.findEnrichmentFieldsByKeys(this.requestedFields, EntityType.company);
    logger.debug({ msg: 'Preparing company data for export', fields: fields });
    return this.entityService.getCompaniesForExport().then((companies: ICompanyDataForExport[]) => {
      logger.debug({ msg: 'Example company data for export', company: companies[0] });
      return companies.map((company: ICompanyDataForExport) => {
        const companyData: ICompanyDataForCsv = {
          name: company.name,
          website: company.website
        };

        fields.forEach((field: IField): void => {
          const companyField: ICompanyFieldDataForExport | undefined = company.fields.find((f: ICompanyFieldDataForExport): boolean => f.schemaFieldId === field.id);
          companyData[field.key] = companyField?.value || '';
        });

        return companyData;
      });
    });
  }

  private async prepareContactData(): Promise<IContactDataForCsv[]> {
    const fields: IField[] = await this.entityService.findEnrichmentFieldsByKeys(this.requestedFields, EntityType.contact);
    logger.debug({ msg: 'Preparing contact data for export', fields: fields });
    return this.entityService.getContactsForExport().then((contacts: IContactDataForExport[]) => {
      return contacts.map((contact: IContactDataForExport) => {
        const contactData: IContactDataForCsv = {
          first_name: contact.firstName,
          last_name: contact.lastName,
          email: contact.email
        };

        fields.forEach((field: IField): void => {
          const contactField: IContactFieldDataForExport | undefined = contact.fields.find((f: IContactFieldDataForExport): boolean => f.schemaFieldId === field.id);
          if (contactField) {
            contactData[field.key] = contactField.value;
          }
        });

        return contactData;
      });
    });
  }

  async prepareEntityData(): Promise<ICompanyDataForCsv[] | IContactDataForCsv[]> {
    logger.debug({ msg: 'Preparing entity data for export', fields: this.requestedFields });
    if (this.entityType === EntityType.company) {
      return this.prepareCompanyData();
    }
    if (this.entityType === EntityType.contact) {
      return this.prepareContactData();
    }

    throw new Error('Invalid entity type');
  }
}

export { ExporterService };
