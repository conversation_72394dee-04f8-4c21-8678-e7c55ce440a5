import { db } from '../database';
import { logger } from '../logger';
import { validate as uuidValidate } from 'uuid';
import { IAuth } from '../types/Common';
import { checkAiGenerated, IContactCompanyEntity, ICreateMappingResponse, IEntitySearchResult, IResponseStatus, ISyncField, IFilterFields, FilterLabelEnum, FilterDataTypeEnum, FilterKeyEnum, IValidEntityBody, ErrorMessages } from '../types/Entity';

import { DB_TRANSACTION_SETTING, defaultFieldConfiguration, FieldKeys } from '../constants';
import { Company, CompanyContactMapping, Contact, CrmType, DataSourceType, EntityType, ExecutionStatus, FieldDataType, VariableType } from '@prisma/client';
import { ICreateContactParams } from '../types/Contact';
import { Patterns } from '../utils';
import { BadRequestError, ConflictError, NotFoundError, UnprocessableContentError } from '../lib/errors';
import { ICompanyCreateWithContactId } from '../types/Company';
import BaseService from './BaseService';
import { ICompanyDataToWrite, IContactDataToWrite } from '../types/Importer';
import { ICompanyDataForExport, IContactDataForExport } from '../types/Exporter';
import { JsonValue } from '@prisma/client/runtime/library';
import { formatKey, getDomainFromUrl, validateFieldKey } from '../lib/utils';
import { IdeaFiltersMapping } from '../utils/IdeaFiltersMapping';
import { IListCompanyIdeaFilters } from '../types/ListCompanyIdea';

type TUpdateCompanyWithContactMapping = {
  contactId: string;
  companyId: string;
  isKey?: boolean;
  isPrimary?: boolean;
};

type TUpdateContactWithCompanyMapping = TUpdateCompanyWithContactMapping;

interface ICreateFieldArgs {
  label: string;
  entityType: EntityType;
  dataType: FieldDataType;
}

interface IUpdateFieldDataArgs {
  entityId: string;
  fieldId: string;
  value: string;
}

export default class EntityService extends BaseService {
  constructor({ auth }: { auth: IAuth }) {
    super({ auth });
    if (!this.auth.oid) {
      throw new BadRequestError('Organization ID is required to access Entity service.');
    }
  }

  async createField({ label, entityType, dataType }: ICreateFieldArgs): Promise<boolean> {
    return this.fieldExists({ label, entityType }).then(async (exists) => {
      if (exists) {
        throw new ConflictError('This field name is already in use. Please enter a unique name.');
      }

      const key = formatKey(label);
      return db.entityField
        .create({
          data: {
            key,
            label,
            orgId: this.auth.oid,
            entity: entityType as EntityType,
            dataType: dataType as FieldDataType,
            isInternal: false,
            isAttachment: false,
            isMandatory: false,
            config: defaultFieldConfiguration
          },
          select: { id: true }
        })
        .then((field: { id: string }): boolean => !!field)
        .catch((error: Error) => {
          logger.debug({ msg: 'Error creating entity field', errorMessage: error.message, error });
          throw new UnprocessableContentError('Unable to create entity field');
        });
    });
  }

  async updateFieldData({ entityType, entityId, fieldId, value }: IUpdateFieldDataArgs & { entityType: EntityType }): Promise<boolean> {
    if (entityType === EntityType.company) {
      return this.updateCompanyFieldData({ entityId, fieldId, value });
    }
    if (entityType === EntityType.contact) {
      return this.updateContactFieldData({ entityId, fieldId, value });
    }

    throw new UnprocessableContentError('Invalid entity type');
  }

  async updateCompanyFieldData({ entityId, fieldId, value }: IUpdateFieldDataArgs) {
    return db.companyField
      .upsert({
        where: {
          companyFieldMapping: {
            companyId: entityId,
            schemaFieldId: fieldId
          }
        },
        create: {
          company: { connect: { id: entityId } },
          schemaField: { connect: { id: fieldId } },
          sourceType: DataSourceType.acqwired,
          createdAt: new Date(),
          value
        },
        update: {
          value,
          updatedAt: new Date()
        },
        select: {
          id: true
        }
      })
      .then((result: { id: string } | null): boolean => !!result)
      .catch((error: Error): boolean => {
        logger.debug({ msg: 'Error updating company field', errorMessage: error.message, error, entityId, fieldId, value });
        return false;
      });
  }

  async updateContactFieldData({ entityId, fieldId, value }: IUpdateFieldDataArgs) {
    return db.contactField
      .upsert({
        where: {
          contactFieldMapping: {
            contactId: entityId,
            schemaFieldId: fieldId
          }
        },
        create: {
          contact: { connect: { id: entityId } },
          schemaField: { connect: { id: fieldId } },
          sourceType: DataSourceType.acqwired,
          createdAt: new Date(),
          value
        },
        update: {
          value,
          updatedAt: new Date()
        },
        select: {
          id: true
        }
      })
      .then((result: { id: string } | null): boolean => !!result)
      .catch((error: Error): boolean => {
        logger.debug({ msg: 'Error updating contact field', errorMessage: error.message, error, entityId, fieldId, value });
        return false;
      });
  }

  async fieldExists({ label, entityType }: { label: string; entityType: EntityType }) {
    const key = formatKey(label);
    return db.entityField
      .findFirst({
        where: {
          entity: entityType,
          orgId: this.auth.oid,
          OR: [{ key }, { label }]
        },
        select: { id: true }
      })
      .then((field: { id: string } | null): boolean => !!field);
  }

  async getAllFields() {
    const pseudoFields = await db.templateVariables
      .findMany({
        where: { type: VariableType.pseudo }
      })
      .then((fields) =>
        fields.map((f) => ({
          id: f.id,
          entityType: null,
          dataType: FieldDataType.string,
          namespace: f.namespace,
          key: f.key,
          label: f.description,
          isInternal: false,
          isAttachment: false,
          isMandatory: false,
          isPseudo: true,
          enrichments: [],
          assignedEnrichments: []
        }))
      );
    const entityFields = await db.entityField
      .findMany({
        where: { orgId: this.auth.oid, deletedAt: null },
        select: {
          id: true,
          entity: true,
          dataType: true,
          key: true,
          label: true,
          isInternal: true,
          isAttachment: true,
          isMandatory: true,
          config: true,
          enrichments: {
            select: {
              id: true,
              name: true,
              isPrimary: true,
              config: {
                select: {
                  name: true
                }
              },
              results: {
                select: {
                  createdAt: true
                },
                orderBy: {
                  createdAt: 'desc'
                },
                take: 1
              }
            }
          }
        }
      })
      .then((fields) =>
        fields.map((f) => ({
          id: f.id,
          entityType: f.entity,
          dataType: f.dataType,
          namespace: f.entity,
          key: f.key,
          label: f.label,
          isInternal: f.isInternal,
          isAttachment: f.isAttachment,
          isMandatory: f.isMandatory,
          isPseudo: false,
          config: f.config,
          assignedEnrichments: f.enrichments
        }))
      );

    return [...pseudoFields, ...entityFields];
  }

  async setFieldConfig({ fieldId, config }: { fieldId: string; config: object }): Promise<boolean> {
    return db.entityField
      .findUnique({
        where: {
          id: fieldId,
          orgId: this.auth.oid
        },
        select: {
          config: true
        }
      })
      .then(async (field: { config: JsonValue } | null): Promise<boolean> => {
        if (!field) {
          throw new NotFoundError('Field not found');
        }
        const newConfig = { ...(field.config as object), ...config };
        return db.entityField
          .update({
            where: { id: fieldId },
            data: { config: newConfig }
          })
          .then(() => true)
          .catch((error) => {
            logger.error({ msg: 'Error setting field config', fieldId, error });
            return false;
          });
      });
  }

  async createContactMapping(contactId: string, companyId: string): Promise<ICreateMappingResponse> {
    return this.findKeyMappingForCompany(companyId).then(async (isKeyMappingExists) => {
      return this.findPrimaryMappingForContact(contactId).then(async (isPrimaryMappingExists) => {
        return db
          .$transaction(async (tx): Promise<ICreateMappingResponse> => {
            return this.resetMappingKeys(contactId, companyId, isKeyMappingExists, isPrimaryMappingExists, tx).then(() => {
              return tx.companyContactMapping.create({
                data: {
                  isKey: !isKeyMappingExists,
                  isPrimary: !isPrimaryMappingExists,
                  companyId,
                  contactId
                },
                select: {
                  isPrimary: true,
                  isKey: true,
                  companyId: true,
                  contactId: true
                }
              });
            });
          }, DB_TRANSACTION_SETTING)
          .catch((error) => {
            logger.error({ msg: 'Failed to createContactMapping', e: error.message, contactId, companyId });
            throw error;
          });
      });
    });
  }

  async validateCompanyData(data: ICompanyCreateWithContactId): Promise<void> {
    if (!Patterns.CompanyName.test(data.company.name)) {
      throw new BadRequestError('Invalid company name');
    }

    const domain = getDomainFromUrl(data.company.website);
    if (!domain) {
      throw new BadRequestError('Invalid company website');
    }

    const duplicate = await db.company.findUnique({
      select: {
        id: true
      },
      where: { unique_company: { domain: domain, orgId: this.auth.oid } }
    });

    if (duplicate?.id) {
      throw new ConflictError('Company with this domain already exists');
    }
  }

  validateContactData = (data: ICreateContactParams) => {
    if (!Patterns.FirstName.test(data.contact.firstName)) {
      throw new BadRequestError('Invalid first name');
    }
    if (!Patterns.LastName.test(data.contact.lastName)) {
      throw new BadRequestError('Invalid last name');
    }
    if (!Patterns.Email.test(data.contact.email)) {
      throw new BadRequestError('Invalid email address');
    }
    return true;
  };

  async createContact(data: ICreateContactParams): Promise<Contact | null> {
    this.validateContactData(data);
    return db.$transaction(async (tx) => {
      const newContact = await tx.contact.createEntity({
        data,
        orgId: this.auth.oid,
        userId: this.auth.uid,
        tx
      });

      if (!newContact || !uuidValidate(newContact.id)) {
        throw new UnprocessableContentError('Invalid contact id');
      }

      if (newContact && data.assignCompanyId) {
        await this.updateContactWithCompanyMapping(
          {
            contactId: newContact.id,
            companyId: data.assignCompanyId
          },
          tx
        );

        const existingKeyContact = await tx.companyContactMapping.findFirst({
          where: { companyId: data.assignCompanyId as string, isKey: true }
        });
        if (!existingKeyContact) {
          await this.updateCompanyWithContactMapping(
            {
              contactId: newContact.id,
              companyId: data.assignCompanyId as string,
              isKey: true
            },
            tx
          );
        }
      }

      return newContact;
    });
  }

  async modifyContact(data: ICreateContactParams): Promise<Contact | null> {
    return db.$transaction(async (tx) => {
      const updatedContact = await tx.contact.modifyEntity({ data, tx });

      if (!updatedContact || !uuidValidate(updatedContact.id)) {
        throw new UnprocessableContentError('Invalid contact id');
      }

      if (data.assignCompanyId) {
        await this.updateContactWithCompanyMapping(
          {
            contactId: updatedContact.id as string,
            companyId: data.assignCompanyId as string
          },
          tx
        );
      }

      return updatedContact;
    });
  }

  async updateContactWithCompanyMapping({ contactId, companyId, isPrimary = true, isKey = false }: TUpdateContactWithCompanyMapping, tx: any): Promise<void> {
    return tx.companyContactMapping
      .updateMany({
        where: { contactId },
        data: { isPrimary: false }
      })
      .then(() => {
        return tx.companyContactMapping
          .findMany({
            where: { contactId, companyId }
          })
          .then((result: CompanyContactMapping[]) => {
            if (!result.length) {
              return tx.companyContactMapping.create({
                data: {
                  isKey,
                  isPrimary,
                  companyId,
                  contactId
                }
              });
            }

            return tx.companyContactMapping.updateMany({
              where: { contactId, companyId },
              data: { isPrimary: true }
            });
          });
      });
  }

  async updateCompanyWithContactMapping({ contactId, companyId, isPrimary = false, isKey = true }: TUpdateCompanyWithContactMapping, tx: any): Promise<void> {
    return tx.companyContactMapping
      .updateMany({
        where: { companyId },
        data: { isKey: false }
      })
      .then(() => {
        return tx.companyContactMapping
          .findMany({
            where: { contactId, companyId }
          })
          .then((result: CompanyContactMapping[]) => {
            if (!result.length) {
              return tx.companyContactMapping.create({
                data: {
                  isKey,
                  isPrimary,
                  companyId,
                  contactId
                }
              });
            }

            return tx.companyContactMapping.updateMany({
              where: { contactId, companyId },
              data: { isKey: true }
            });
          });
      });
  }

  async createCompany(data: ICompanyCreateWithContactId): Promise<Company | null> {
    await this.validateCompanyData(data);
    const domain = getDomainFromUrl(data.company.website);
    return db.$transaction(async (tx: any) => {
      const newCompany = await tx.company.createEntity({
        data,
        domain,
        orgId: this.auth.oid,
        userId: this.auth.uid,
        tx
      });

      if (!newCompany || !uuidValidate(newCompany.id)) {
        throw new UnprocessableContentError('Invalid company id');
      }

      if (newCompany && data.assignContactId) {
        await this.updateCompanyWithContactMapping(
          {
            contactId: data.assignContactId as string,
            companyId: newCompany.id
          },
          tx
        );

        const existingPrimaryCompany = await tx.companyContactMapping.findFirst({
          where: { contactId: data.assignContactId as string, isPrimary: true }
        });
        if (!existingPrimaryCompany) {
          await this.updateContactWithCompanyMapping(
            {
              contactId: data.assignContactId as string,
              companyId: newCompany.id,
              isPrimary: true
            },
            tx
          );
        }
      }

      return newCompany;
    });
  }

  async modifyCompany(data: ICompanyCreateWithContactId): Promise<Company | null> {
    return db.$transaction(async (tx: any) => {
      const updatedCompany = await tx.company.modifyEntity({ data, tx });

      if (!updatedCompany || !uuidValidate(updatedCompany.id)) {
        throw new UnprocessableContentError('Invalid company id');
      }

      if (data.assignContactId) {
        await this.updateCompanyWithContactMapping(
          {
            contactId: data.assignContactId as string,
            companyId: updatedCompany.id as string
          },
          tx
        );
      }

      return updatedCompany;
    });
  }

  async createCompanyContactEntity(data: IContactCompanyEntity): Promise<CompanyContactMapping | null> {
    await this.validateCompanyData(data);
    this.validateContactData(data);
    const domain = getDomainFromUrl(data.company.website);
    return db.$transaction(async (tx: any) => {
      const newCompany = await tx.company.createEntity({
        data,
        domain,
        orgId: this.auth.oid,
        userId: this.auth.uid,
        tx
      });
      const newContact = await tx.contact.createEntity({
        data,
        orgId: this.auth.oid,
        userId: this.auth.uid,
        tx
      });

      if (!newContact || !uuidValidate(newContact.id)) {
        throw new UnprocessableContentError('Invalid contact id');
      }

      if (!newCompany || !uuidValidate(newCompany.id)) {
        throw new UnprocessableContentError('Invalid company id');
      }

      if (newCompany && newContact) {
        await this.updateContactWithCompanyMapping(
          {
            contactId: newContact.id as string,
            companyId: newCompany.id as string
          },
          tx
        );
        await this.updateCompanyWithContactMapping(
          {
            contactId: newContact.id as string,
            companyId: newCompany.id as string
          },
          tx
        );
      }

      return { ...newContact, ...newCompany, contactId: newContact.id };
    });
  }

  async updateCompanyContactEntity(data: IContactCompanyEntity): Promise<CompanyContactMapping | null> {
    return db.$transaction(async (tx: any) => {
      const updateCompany = await tx.company.modifyEntity({ data, tx });
      const newContact = await tx.contact.createEntity({
        data,
        orgId: this.auth.oid,
        userId: this.auth.uid,
        tx
      });

      if (!newContact || !uuidValidate(newContact.id)) {
        throw new UnprocessableContentError('Invalid contact id');
      }

      if (!updateCompany || !uuidValidate(updateCompany.id)) {
        throw new UnprocessableContentError('Invalid company id');
      }

      if (updateCompany && newContact) {
        await this.updateCompanyWithContactMapping(
          {
            contactId: newContact.id as string,
            companyId: updateCompany.id as string,
            isPrimary: true
          },
          tx
        );
      }

      return { ...newContact, ...updateCompany, contactId: newContact.id };
    });
  }

  async updateContactCompanyEntity(data: IContactCompanyEntity): Promise<CompanyContactMapping | null> {
    return db.$transaction(async (tx: any) => {
      const newCompany = await tx.company.createEntity({
        data,
        orgId: this.auth.oid,
        userId: this.auth.uid,
        tx
      });
      const updateContact = await tx.contact.modifyEntity({ data, tx });

      if (!updateContact || !uuidValidate(updateContact.id)) {
        throw new UnprocessableContentError('Invalid contact id');
      }

      if (!newCompany || !uuidValidate(newCompany.id)) {
        throw new UnprocessableContentError('Invalid company id');
      }

      if (newCompany && updateContact) {
        await this.updateContactWithCompanyMapping(
          {
            contactId: updateContact.id as string,
            companyId: newCompany.id as string,
            isKey: true
          },
          tx
        );
      }

      return { ...updateContact, ...newCompany, contactId: updateContact.id };
    });
  }

  async resetMappingKeys(contactId: string, companyId: string, isKeyMappingExists: boolean, isPrimaryMappingExists: boolean, tx: any) {
    if (!isKeyMappingExists) {
      await tx.companyContactMapping.updateMany({
        where: { companyId },
        data: { isKey: false }
      });
    }
    if (!isPrimaryMappingExists) {
      await tx.companyContactMapping.updateMany({
        where: { contactId },
        data: { isPrimary: false }
      });
    }
  }

  async findKeyMappingForCompany(companyId: string): Promise<boolean> {
    return db.companyContactMapping
      .findFirst({
        where: {
          companyId,
          isKey: true,
          contact: {
            deletedAt: null
          }
        }
      })
      .then((mapping: any) => !!mapping);
  }

  async findPrimaryMappingForContact(contactId: string): Promise<boolean> {
    return db.companyContactMapping
      .findFirst({
        where: {
          contactId,
          isPrimary: true,
          company: {
            deletedAt: null
          }
        }
      })
      .then((mapping: any) => !!mapping);
  }

  async getOwnerId(fields: ISyncField[], oid: string, crmType: CrmType): Promise<string | null> {
    const sfUserId = fields.find((field: ISyncField) => field.fieldKey === FieldKeys.OWNER_ID)?.value;

    logger.debug({ msg: 'Getting ownerId', sfUserId, oid });

    if (!sfUserId) {
      return null;
    }

    return db.userCrmMapping
      .findUnique({
        where: {
          user_crm_id_unique: {
            crmUserId: sfUserId,
            orgId: oid,
            crmType: crmType
          }
        }
      })
      .then((userCrmMapping: any) => {
        return userCrmMapping?.userId ?? null;
      })
      .catch((error: Error) => {
        logger.error({ msg: 'Failed to getOwnerId', e: error.message, sfUserId });
        throw error;
      });
  }

  async searchEntity({ query }: { query: string }): Promise<IEntitySearchResult[]> {
    const contacts = await db.companyContactMapping.findMany({
      where: {
        OR: [
          {
            company: {
              orgId: this.auth.oid,
              name: { contains: query, mode: 'insensitive' }
            }
          },
          {
            contact: {
              orgId: this.auth.oid,
              OR: [
                {
                  firstName: { contains: query, mode: 'insensitive' }
                },
                {
                  lastName: { contains: query, mode: 'insensitive' }
                },
                {
                  email: { contains: query, mode: 'insensitive' }
                }
              ]
            }
          }
        ]
      },
      select: {
        contact: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: { contact: { firstName: 'asc' } },
      take: 5
    });

    if (!contacts) {
      return [];
    }

    return contacts.map((contact: any) => {
      return {
        id: contact.contact.id,
        name: `${contact.contact.firstName} ${contact.contact.lastName}`,
        type: 'contact',
        info: contact.contact.email
      };
    });
  }

  async checkValidEntity(body: IValidEntityBody): Promise<IResponseStatus> {
    if (!body.companyId || !body.contactId) {
      return { success: false, message: ErrorMessages.INVALID_CONTACT_COMPANY };
    }

    return { success: true, message: '' };
  }

  async checkAiGenerated(data: checkAiGenerated): Promise<IResponseStatus> {
    return db.execution
      .findFirst({
        where: {
          AND: [{ id: data.executionId }, { status: ExecutionStatus.completed }]
        },
        select: {
          channelEmails: {
            select: {
              contactId: true,
              contact: true
            }
          }
        }
      })
      .then(async (executionDetail) => {
        if (!executionDetail?.channelEmails?.length) {
          return { success: true, message: '' };
        }

        return db.contact
          .findFirst({
            where: {
              id: data.contactId
            }
          })
          .then((contactDetails) => {
            if (!contactDetails) {
              throw new BadRequestError('Invalid contactId');
            }

            if (executionDetail.channelEmails[0].contactId !== data.contactId) {
              const contactInfo = executionDetail.channelEmails[0].contact;
              return {
                success: false,
                message: `This email generated for ${contactInfo.firstName} ${contactInfo.lastName}.Update recipient to ${contactInfo.firstName} ${contactInfo.lastName} or generate a new one for ${contactDetails.firstName} ${contactDetails.lastName}.`
              };
            }

            return { success: true, message: '' };
          });
      });
  }

  async findEnrichmentFieldsByKeys(keys: string[], entityType: EntityType): Promise<{ id: string; key: string }[]> {
    return db.entityField.findMany({
      where: {
        orgId: this.auth.oid,
        entity: entityType,
        key: { in: keys },
        deletedAt: null
      },
      select: {
        id: true,
        key: true
      }
    });
  }

  async createExecutionIdentifierIfNeeded(identifier: string, entityType: EntityType): Promise<void> {
    const field = await db.entityField.findFirst({
      where: {
        orgId: this.auth.oid,
        entity: entityType,
        key: identifier,
        deletedAt: null
      }
    });
    const validateResult = validateFieldKey(identifier);
    if (validateResult?.invalid) {
      throw new BadRequestError(validateResult.message);
    }

    if (!field) {
      await db.entityField.create({
        data: {
          entity: entityType,
          orgId: this.auth.oid,
          label: 'Execution Identifier',
          dataType: 'string',
          key: identifier,
          isInternal: true,
          isAttachment: false,
          isMandatory: false,
          config: defaultFieldConfiguration
        }
      });
    }
  }

  async createCompanyWithFields({ company, fields }: ICompanyDataToWrite): Promise<boolean> {
    await this.validateCompanyData({ company });
    const domain = getDomainFromUrl(company.website);
    if (!domain) {
      throw new BadRequestError('Invalid company website');
    }
    return db.company
      .create({
        data: {
          ...company,
          domain,
          fields: {
            create: fields
          }
        }
      })
      .then((result: Company | null): boolean => !!result)
      .catch((error): boolean => {
        logger.error({ msg: 'Error creating company', name: company.name, error });
        return false;
      });
  }

  async createContactWithFields({ contact, fields }: IContactDataToWrite): Promise<boolean> {
    this.validateContactData({ contact });

    return db.contact
      .create({
        data: {
          ...contact,
          fields: {
            create: fields
          }
        }
      })
      .then((result: Contact | null): boolean => !!result)
      .catch((error): boolean => {
        logger.error({ msg: 'Error creating contact', email: contact.email, error });
        return false;
      });
  }

  async getCompaniesForExport(): Promise<ICompanyDataForExport[]> {
    return db.company.findMany({
      where: {
        orgId: this.auth.oid
      },
      select: {
        id: true,
        name: true,
        website: true,
        fields: {
          select: {
            schemaFieldId: true,
            value: true
          }
        }
      },
      orderBy: { name: 'asc' }
    });
  }

  async getContactsForExport(): Promise<IContactDataForExport[]> {
    return db.contact.findMany({
      where: {
        orgId: this.auth.oid
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        fields: {
          select: {
            schemaFieldId: true,
            value: true
          }
        }
      },
      orderBy: { firstName: 'asc' }
    });
  }

  async getEntityIdsByFilters({ entityType, filters }: { entityType: EntityType; filters: object }): Promise<string[]> {
    const wheres: any[] = [];
    if ('query' in filters && entityType === EntityType.contact) {
      wheres.push({
        OR: [
          { firstName: { contains: filters.query, mode: 'insensitive' } },
          { lastName: { contains: filters.query, mode: 'insensitive' } },
          {
            email: {
              contains: filters.query,
              mode: 'insensitive'
            }
          }
        ]
      });
    }
    if ('query' in filters && entityType === EntityType.company) {
      wheres.push({
        OR: [{ name: { contains: filters.query, mode: 'insensitive' } }, { website: { contains: filters.query, mode: 'insensitive' } }]
      });
    }

    if (entityType === EntityType.idea) {
      const ideaFilters = filters as IListCompanyIdeaFilters;
      if (!ideaFilters.listCompanyId) {
        throw new BadRequestError('listCompanyId is required for idea filters');
      }
      return await db.listCompanyIdea.getFilteredListCompanyIdeaIds({
        orgId: this.auth.oid,
        ...IdeaFiltersMapping(ideaFilters),
        listCompanyId: ideaFilters.listCompanyId,
      });
    }

    if (entityType === EntityType.list) {
      throw new UnprocessableContentError('List company entity type is not supported');
    }

    const findObject = {
      where: {
        orgId: this.auth.oid,
        AND: wheres
      },
      select: {
        id: true,
        fields: {
          select: {
            schemaField: {
              select: {
                key: true
              }
            },
            value: true
          }
        }
      }
    };
    let results;
    if (entityType === EntityType.company) {
      results = await db.company.findMany(findObject);
    }
    if (entityType === EntityType.contact) {
      results = await db.contact.findMany(findObject);
    }

    let filteredResults = results;
    if ('rangednumber' in filters && entityType === EntityType.company) {
      const filter = filters.rangednumber as { field: string; range: [number, number] };
      filteredResults = results.filter((result) => {
        const field = result.fields.find((field) => field.schemaField.key === filter.field);
        if (!field) {
          return false;
        }
        const value = parseFloat(field.value);
        return value >= filter.range[0] && value <= filter.range[1];
      });
    }
    logger.debug({ msg: 'Entity found', entity: filteredResults });
    return filteredResults.map((entity: any) => entity.id);
  }

  async getFilterFields(): Promise<IFilterFields[]> {
    return [
      { label: FilterLabelEnum.LastActivity, dataType: FilterDataTypeEnum.Date, key: FilterKeyEnum.createdAt },
      { label: FilterLabelEnum.LastModified, dataType: FilterDataTypeEnum.Date, key: FilterKeyEnum.updatedAt },
      { label: FilterLabelEnum.PlayStats, dataType: FilterDataTypeEnum.Number, key: FilterKeyEnum.Stats },
      { label: FilterLabelEnum.Owner, dataType: FilterDataTypeEnum.Uuid, key: FilterKeyEnum.Owners }
    ];
  }

  async createEntity(postData: any): Promise<any> {
    return db.$transaction(async (tx) => {
      let company: Company | null = null;
      let contact: Contact | null = null;
      const companyId: string | undefined = postData.company?.id;
      const contactId: string | undefined = postData.contact?.id;

      if (companyId === 'new') {
        const domain = getDomainFromUrl(postData.company.website);
        if (!domain) {
          throw new BadRequestError('Invalid company website');
        }

        const existingCompany = await tx.company.findFirst({
          where: { domain, orgId: this.auth.oid }
        });

        if (existingCompany?.deletedAt) {
          company = await tx.company.update({
            where: { id: existingCompany.id },
            data: {
              deletedAt: null,
              deletedById: null,
              name: postData.company.name.trim(),
              website: postData.company.website.trim(),
              updatedAt: new Date()
            }
          });
        } else if (existingCompany) {
          throw new ConflictError('Company with this domain already exists');
        } else {
          company = await tx.company.create({
            data: {
              orgId: this.auth.oid,
              name: postData.company.name.trim(),
              website: postData.company.website.trim(),
              sourceType: DataSourceType.acqwired,
              createdAt: new Date(),
              createdById: this.auth.uid,
              domain
            }
          });
        }
      }

      if (postData.contact?.id === 'new') {
        contact = await tx.contact.create({
          data: {
            orgId: this.auth.oid,
            firstName: postData.contact.firstName.trim(),
            lastName: postData.contact.lastName.trim(),
            email: postData.contact.email.trim(),
            sourceType: DataSourceType.acqwired,
            createdAt: new Date(),
            createdById: this.auth.uid
          }
        });
      }

      if (company && contact) {
        // Both company and contact created
        logger.debug({ msg: 'Both company and contact created', companyId: company.id, contactId: contact.id });

        return tx.companyContactMapping.create({
          data: {
            companyId: company.id,
            contactId: contact.id,
            isPrimary: true,
            isKey: true
          },
          select: {
            companyId: true,
            contactId: true
          }
        });
      } else if (company && contactId) {
        // Company created, contact exists
        logger.debug({ msg: 'Company created, contact exists', companyId: company.id, contactId });

        return tx.companyContactMapping.create({
          data: {
            companyId: company.id,
            contactId,
            isPrimary: false,
            isKey: true
          },
          select: {
            companyId: true,
            contactId: true
          }
        });
      } else if (contact && companyId) {
        // Contact created, company exists
        logger.debug({ msg: 'Contact created, company exists', companyId, contactId: contact.id });

        return tx.companyContactMapping.create({
          data: {
            companyId,
            contactId: contact.id,
            isPrimary: true,
            isKey: false
          },
          select: {
            companyId: true,
            contactId: true
          }
        });
      } else if (company && !contactId) {
        // Company created, no key contact defined
        logger.debug({ msg: 'Company created, no key contact defined', companyId: company.id, contactId: null });

        return { companyId: company.id, contactId: null };
      } else if (contact && !companyId) {
        // Contact created, no primary company defined
        logger.debug({ msg: 'Contact created, no primary company defined', companyId: null, contactId: contact.id });

        return { companyId: null, contactId: contact.id };
      }
      throw new BadRequestError('Invalid company or contact');
    });
  }

  async updateEntity(postData: any): Promise<any> {
    return db.$transaction(async (tx) => {
      let company: Company | null = null;
      let contact: Contact | null = null;
      const companyId: string | undefined = postData.company?.id;
      const contactId: string | undefined = postData.contact?.id;
      const domain = getDomainFromUrl(postData.company.website);

      if (postData.company?.id === 'new') {
        if (!domain) {
          throw new BadRequestError('Invalid company website');
        }

        const existingCompany = await tx.company.findFirst({ where: { domain, orgId: this.auth.oid } });
        if (existingCompany?.deletedAt) {
          company = await tx.company.update({
            where: { id: existingCompany.id },
            data: {
              deletedAt: null,
              deletedById: null,
              name: postData.company.name.trim(),
              website: postData.company.website.trim(),
              updatedAt: new Date()
            }
          });
        } else if (existingCompany) {
          throw new ConflictError('Company with this domain already exists');
        } else {
          company = await tx.company.create({
            data: {
              orgId: this.auth.oid,
              name: postData.company.name.trim(),
              website: postData.company.website.trim(),
              sourceType: DataSourceType.acqwired,
              createdAt: new Date(),
              createdById: this.auth.uid,
              domain
            }
          });
        }
      }

      if (postData.contact?.id === 'new') {
        contact = await tx.contact.create({
          data: {
            orgId: this.auth.oid,
            firstName: postData.contact.firstName.trim(),
            lastName: postData.contact.lastName.trim(),
            email: postData.contact.email.trim(),
            sourceType: DataSourceType.acqwired,
            createdAt: new Date(),
            createdById: this.auth.uid
          }
        });
      }

      if (companyId && companyId !== 'new' && postData.company.name) {
        if (!domain) {
          throw new BadRequestError('Invalid company website');
        }

        const existingCompany = await tx.company.findFirst({
          where: { id: companyId }
        });
        if (!existingCompany) {
          throw new NotFoundError('Company not found');
        }

        await tx.company.update({
          where: { id: companyId },
          data: {
            name: postData.company.name.trim(),
            website: postData.company.website.trim(),
            domain: domain
          }
        });
      } else if (contactId && contactId !== 'new' && postData.contact.email) {
        const existingContact = await tx.contact.findFirst({
          where: { id: contactId }
        });
        if (!existingContact) {
          throw new NotFoundError('Contact not found');
        }

        await tx.contact.update({
          where: { id: contactId },
          data: {
            firstName: postData.contact.firstName.trim(),
            lastName: postData.contact.lastName.trim(),
            email: postData.contact.email.trim()
          }
        });
      } else {
        throw new BadRequestError('No company or contact to update');
      }

      // Companies or contacts created/updated
      // Now create mappings if required, and change the primary and key flags

      if (company && contact) {
        // Both company and contact created
        // This shouldn't happen here, but just in case
        throw new ConflictError('Both company and contact created in updateEntity');
      }

      if (company && contactId) {
        // Company created, contact exists
        logger.debug({ msg: 'Company created, contact exists', companyId: company.id, contactId });

        const existingMapping = await tx.companyContactMapping.findFirst({
          where: { companyId: company.id, contactId },
          select: { id: true }
        });

        return tx.companyContactMapping
          .updateMany({
            where: { companyId: company.id, contactId },
            data: {
              isKey: false
            }
          })
          .then(() => {
            if (existingMapping) {
              // Mapping already exists. Just change the primary flag
              logger.debug({ msg: 'Mapping already exists. Just change the primary flag', companyId: company.id, contactId });

              return tx.companyContactMapping.update({
                where: { id: existingMapping.id },
                data: {
                  isKey: true
                },
                select: {
                  companyId: true,
                  contactId: true
                }
              });
            }

            return tx.companyContactMapping.create({
              data: {
                companyId: company.id,
                contactId,
                isPrimary: false,
                isKey: true
              },
              select: {
                companyId: true,
                contactId: true
              }
            });
          });
      }

      if (contact && companyId) {
        // Contact created, company exists
        logger.debug({ msg: 'Contact created, company exists', companyId, contactId: contact.id });

        const existingMapping = await tx.companyContactMapping.findFirst({
          where: { companyId, contactId: contact.id },
          select: { id: true }
        });

        return tx.companyContactMapping
          .updateMany({
            where: { companyId, contactId: contact.id },
            data: {
              isPrimary: false
            }
          })
          .then(() => {
            if (existingMapping) {
              // Mapping already exists. Just change the primary flag
              logger.debug({ msg: 'Mapping already exists. Just change the primary flag', companyId, contactId: contact.id });

              return tx.companyContactMapping.update({
                where: { id: existingMapping.id },
                data: {
                  isPrimary: true
                },
                select: {
                  companyId: true,
                  contactId: true
                }
              });
            }

            return tx.companyContactMapping.create({
              data: {
                companyId,
                contactId: contact.id,
                isPrimary: true,
                isKey: false
              },
              select: {
                companyId: true,
                contactId: true
              }
            });
          });
      }

      if (company && !contactId) {
        // Company created, no key contact defined
        logger.debug({ msg: 'Company created, no key contact defined', companyId: company.id, contactId: null });
        await tx.companyContactMapping.updateMany({
          where: { companyId },
          data: { isKey: false }
        });

        return { companyId: company.id, contactId: null };
      }

      if (contact && !companyId) {
        // Contact created, no primary company defined
        logger.debug({ msg: 'Contact created, no primary company defined', companyId: null, contactId: contact.id });
        await tx.companyContactMapping.updateMany({
          where: { contactId: contact.id },
          data: { isPrimary: false }
        });

        return { companyId: null, contactId: contact.id };
      }

      if (companyId && !contactId) {
        // Company updated, no key contact defined
        logger.debug({ msg: 'Company updated, no key contact defined', companyId, contactId: null });
        await tx.companyContactMapping.updateMany({
          where: { companyId },
          data: { isKey: false }
        });

        return { companyId, contactId: null };
      }

      if (contactId && !companyId) {
        // Contact updated, no primary company defined
        logger.debug({ msg: 'Contact updated, no primary company defined', companyId: null, contactId });
        await tx.companyContactMapping.updateMany({
          where: { contactId },
          data: { isPrimary: false }
        });

        return { companyId: null, contactId };
      }

      if (companyId && contactId) {
        // Both company and contact exists
        logger.debug({ msg: 'Both company and contact exists', companyId, contactId });

        if (postData.company.name) {
          await tx.companyContactMapping.updateMany({
            where: { companyId },
            data: { isKey: false }
          });
        } else if (postData.contact.email) {
          await tx.companyContactMapping.updateMany({
            where: { contactId },
            data: { isPrimary: false }
          });
        }

        const existingMapping = await tx.companyContactMapping.findFirst({
          where: { companyId, contactId },
          select: { id: true }
        });

        let isPrimary: boolean = false;
        let isKey: boolean = false;

        if (postData.company.name) {
          isKey = true;
        }
        if (postData.contact.email) {
          isPrimary = true;
        }

        if (existingMapping) {
          return tx.companyContactMapping.update({
            where: { id: existingMapping.id },
            data: { isPrimary, isKey },
            select: {
              companyId: true,
              contactId: true
            }
          });
        }

        return tx.companyContactMapping.create({
          data: { companyId, contactId, isPrimary, isKey },
          select: {
            companyId: true,
            contactId: true
          }
        });
      }

      throw new BadRequestError('Invalid company or contact');
    });
  }
}
