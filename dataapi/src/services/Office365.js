import { logger } from "../logger";
import { Client } from '@microsoft/microsoft-graph-client';
import config from '../config';

export default class Office365Service {
  token;

  constructor({ accessToken }) {
    if (!accessToken) {
      throw new Error('No token!');
    }
    this.token = accessToken;
  }

  async readContacts() {
    logger.info({ msg: 'Reading Office365 contacts' });

    const client = Client.init({
      authProvider: (done) => {
        done(null, this.token);
      }
    });

    const contacts = [];

    await client.api(config.office365.endPoints.getContacts).get((error, response) => {
      if (error) {
        logger.info({ msg: 'Got error', error });
        return [];
      }
      for (let i = 0; i < response.value.length; i++) {
        const contact = response.value[i];
        contacts.push({
          firstname: contact.givenName,
          lastname: contact.surname,
          company: contact.companyName,
          email: contact.emailAddresses[0].address
        });
      }
    });
    return contacts;
  }

  async readCalendar() {
    logger.info({ msg: 'Reading Office365 calendar' });

    const client = Client.init({
      authProvider: (done) => {
        done(null, this.token);
      }
    });

    const events = [];

    await client.api(config.office365.endPoints.getEvents).get((error, response) => {
      if (error) {
        logger.info({ msg: 'Got error', error });
        return [];
      }
      for (let i = 0; i < response.value.length; i++) {
        const event = response.value[i];
        events.push({
          start: new Date(event.start.dateTime + 'Z'),
          end: new Date(event.end.dateTime + 'Z')
        });
      }
    });
    return events;
  }
}
