import { db } from '../database';
import { ConflictError, NotFoundError, ServerError, UnprocessableContentError } from '../lib/errors';
import { logger } from '../logger';
import { IAuth } from '../types/Common';
import { IEnrichment, IEnrichmentListResponse, IEnrichmentConfig, IEnrichmentFilters } from '../types/Enrichment';
import { Enrichment, EnrichmentType, EntityField, EntityType, ExecutionStatus, ExecutionType, IntegrationProvider, QueueTaskType } from '@prisma/client';
import NotificationService from './Notification';
import { TemplateService, TemplateVariableService } from './index';
import { JsonObject } from '@prisma/client/runtime/library';
import { IExecution } from '../types/Execution';
import { OpenAI } from 'openai';
import { ChatCompletionTool } from 'openai/resources/chat/completions';
import FunctionParameters = OpenAI.FunctionParameters;

interface EnrichmentServiceParams {
  auth: IAuth;
  templateService?: TemplateService;
  templateVariableService?: TemplateVariableService;
  notificationService?: NotificationService;
  writeResultsToSocket?: boolean;
}

interface IPagination {
  pageIndex: number;
  sortKey: string;
  sortOrder: string;
}

interface IEnrichmentProcessStep {
  status: string;
  statusMessage: string;
  time: Date | null;
}

export default class EnrichmentService {
  auth: IAuth;
  notificationService: NotificationService | null;
  writeResultsToSocket: boolean;
  templateService: TemplateService | undefined;
  templateVariableService: TemplateVariableService | undefined;

  constructor({ auth, templateService, templateVariableService, notificationService, writeResultsToSocket }: EnrichmentServiceParams) {
    this.auth = auth;

    if (!auth.oid) {
      logger.error({ error: 'Invalid organization Id' });
      throw new Error('Invalid service call! Check your permissions!');
    }
    this.templateService = templateService;
    this.templateVariableService = templateVariableService;
    this.notificationService = notificationService || null;
    this.writeResultsToSocket = writeResultsToSocket || false;
  }

  async getEnrichments({ filters, pagination }: { filters: IEnrichmentFilters; pagination: IPagination }): Promise<IEnrichmentListResponse | null> {
    const { oid: orgId } = this.auth;
    return db.enrichment.listEnrichments({ filters, pagination, orgId });
  }

  async getEnrichmentConfigs(): Promise<IEnrichmentConfig[]> {
    return db.enrichmentConfig.findMany({
      where: {
        entityType: { not: EntityType.idea }
      }
    });
  }

  async getListEnrichments({ listId }: { listId: string }): Promise<IEnrichmentListResponse | null> {
    const { oid: orgId } = this.auth;
    return db.enrichment
      .findMany({
        where: {
          orgId,
          deletedAt: null,
          listCompanyId: listId
        },
        select: {
          id: true,
          name: true,
          entityType: true,
          isPrimary: true,
          createdAt: true,
          updatedAt: true,
          createdById: true,
          llmModelId: true,
          listCompanyId: true,
          listColumns: true,
          data: true,
          entityField: {
            select: {
              id: true
            }
          },
          config: true,
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          }
        },
        orderBy: {
          createdAt: 'asc'
        }
      })
      .then((records) => {
        return { records, pagination: null };
      });
  }

  async getListEnrichmentConfigs(): Promise<IEnrichmentConfig[]> {
    return db.enrichmentConfig.findMany({
      where: {
        entityType: EntityType.idea
      }
    });
  }

  async getEnrichment({ enrichmentId }): Promise<IEnrichment | null> {
    return db.enrichment.getEnrichment({ orgId: this.auth.oid, enrichmentId });
  }

  async saveEnrichment({ name, llmModelId, entityType, configId, listId, ...fields }): Promise<any> {
    logger.debug({ msg: 'Saving enrichment', name, llmModelId, configId, fields });
    const enrichmentConfig = await db.enrichmentConfig.findUnique({ where: { id: configId } });
    if (!enrichmentConfig) {
      throw new NotFoundError('Enrichment config not found');
    }

    // … after you’ve loaded enrichmentConfig …
    const inputFields = ((enrichmentConfig.data as any).input?.fields as Array<{ name: string; label?: string; required: boolean; type?: string }>) || [];

    for (const fieldDef of inputFields) {
      const raw = (fields as any)[fieldDef.name];
      const fieldName = fieldDef.label || fieldDef.name;

      if (fieldDef.required) {
        const error = await this.validateEnrichmentFields(raw, fieldName, fieldDef.type);
        if (error) {
          throw new NotFoundError(error);
        }
      }
    }

    const enrichmentData: JsonObject = fields as JsonObject;
    const assignToField: JsonObject = fields.assignToField;
    delete enrichmentData.assignToField;

    const saveData = {
      name,
      data: enrichmentData,
      isPrimary: false,
      entityType,
      config: { connect: { id: configId } },
      createdBy: { connect: { id: this.auth.uid } },
      organization: { connect: { id: this.auth.oid } },
      createdAt: new Date()
    } as any;

    if (llmModelId !== 'default') {
      saveData.llmModel = { connect: { id: llmModelId } };
    }

    if (listId && entityType === EntityType.idea) {
      const enrichment = await db.enrichment.findFirst({
        where: { listCompanyId: listId, name: name, deletedAt: null },
        select: {
          id: true
        }
      });
      if (enrichment) {
        throw new ConflictError('Enrichment with the same name already exists');
      }

      saveData.listCompany = { connect: { id: listId } };
    }

    if (assignToField?.fieldId) {
      const field = await db.entityField.findUnique({ where: { id: assignToField.fieldId as string, entity: entityType } });
      if (!field) {
        throw new ConflictError(`Field & Enrichment entity types are different (${entityType})`);
      }

      saveData.entityField = { connect: { id: assignToField.fieldId } };
      if (assignToField.isPrimary) {
        saveData.isPrimary = true;
      } else {
        const primaryEnrichment = await db.enrichment.findFirst({
          where: { entityFieldId: assignToField.fieldId as string, isPrimary: true }
        });
        if (!primaryEnrichment) {
          saveData.isPrimary = true;
        }
      }
    }

    if (entityType === EntityType.idea) {
      if (saveData.data.jsonSchema) {
        try {
          saveData.data.jsonSchema = JSON.parse(saveData.data.jsonSchema);
        } catch (_e) {
          saveData.data.jsonSchema = {};
        }
      }
    }

    return db.$transaction(async (tx) => {
      return tx.enrichment
        .create({
          data: saveData
        })
        .then((enrichment: Enrichment) => {
          if (!assignToField?.isPrimary) {
            return enrichment;
          }
          return tx.enrichment
            .updateMany({
              where: { entityFieldId: assignToField.fieldId as string, id: { not: enrichment.id } },
              data: { isPrimary: false }
            })
            .then(() => enrichment);
        });
    });
  }

  async updateEnrichment({ id, name, llmModelId, assignToField, ...fields }): Promise<any> {
    const enrichment = await db.enrichment.findUnique({ where: { id } });
    if (!enrichment) {
      throw new NotFoundError('Enrichment not found');
    }

    if (enrichment.listCompanyId && enrichment.entityType === EntityType.idea) {
      const otherEnrichment = await db.enrichment.findFirst({
        where: { listCompanyId: enrichment.listCompanyId, name: name, deletedAt: null, id: { not: id } },
        select: {
          id: true
        }
      });
      if (otherEnrichment) {
        throw new ConflictError('Enrichment with the same name already exists');
      }
    }

    if (enrichment.data && typeof enrichment.data === 'object') {
      if ('color' in enrichment.data) {
        fields.color = enrichment.data.color;
      }
    }

    return db.$transaction(async (tx) => {
      if (assignToField?.fieldId) {
        const primaryEnrichment = await db.enrichment.findFirst({
          where: { entityFieldId: assignToField.fieldId as string, isPrimary: true }
        });
        if (!primaryEnrichment) {
          assignToField.isPrimary = true;
        }
      }

      if (fields.jsonSchema) {
        try {
          fields.jsonSchema = JSON.parse(fields.jsonSchema);
        } catch (_e) {
          fields.jsonSchema = {};
        }
      }

      return tx.enrichment
        .updateEnrichment({
          orgId: this.auth.oid,
          id,
          name,
          llmModelId,
          assignedField: assignToField || null,
          isPrimary: assignToField?.isPrimary || false,
          ...fields
        })
        .then((enrichment) => {
          if (!assignToField?.isPrimary) {
            return enrichment;
          }
          return tx.enrichment
            .updateMany({
              where: { entityFieldId: assignToField.fieldId as string, id: { not: enrichment.id } },
              data: { isPrimary: false }
            })
            .then(() => enrichment);
        });
    });
  }

  async updateEnrichmentColumn({ enrichmentId, listColumns }): Promise<boolean> {
    const enrichment = await db.enrichment.findUnique({ where: { id: enrichmentId, orgId: this.auth.oid } });
    if (!enrichment) {
      throw new NotFoundError('Enrichment not found');
    }

    return db.enrichment
      .update({
        where: { id: enrichmentId, orgId: this.auth.oid },
        data: { listColumns }
      })
      .then(() => true);
  }

  async executeEnrichment({ enrichmentId, entityType, entityIds, silent, options }): Promise<{ entityId: string; executionId: string; fieldId: string }[]> {
    if (!entityIds || entityIds.length === 0) {
      throw new UnprocessableContentError('No entity ids provided');
    }
    return db.enrichment
      .findUnique({
        where: { id: enrichmentId, orgId: this.auth.oid },
        include: {
          config: true,
          entityField: true
        }
      })
      .then(async (enrichment) => {
        if (!enrichment) {
          throw new NotFoundError('Enrichment not found');
        }
        if (!enrichment.config) {
          throw new UnprocessableContentError('Enrichment config not found');
        }
        if (!enrichment.config.data) {
          throw new UnprocessableContentError('Enrichment config not found');
        }
        if (!this.templateService) {
          throw new ServerError('Template service not available');
        }
        if (!this.templateVariableService) {
          throw new ServerError('Template variable service not available');
        }

        const enrichmentIdsToExecute: string[] = [enrichmentId];

        async function resolveEnrichments({ enrichmentId, options: _options }: { enrichmentId: string; options?: { autorun?: boolean; required?: boolean } }): Promise<void> {
          if (enrichmentIdsToExecute.includes(enrichmentId)) {
            return;
          }

          const enrichment = await db.enrichment.findUnique({
            where: { id: enrichmentId },
            include: {
              config: true,
              entityField: true
            }
          });

          if (!enrichment) {
            return;
          }

          const enrichmentData = enrichment.data as object;

          let promptObject: object[] = [];
          try {
            promptObject = JSON.parse(enrichmentData[enrichment.config.enrichmentType === EnrichmentType.agent ? 'objective' : 'prompt']);
          } catch {
            return;
          }

          enrichmentIdsToExecute.unshift(enrichment.id);

          for (const paragraph of promptObject) {
            for (const segment of paragraph['children']) {
              if (segment.type === 'enrich-box' && segment.enrichmentId) {
                if (segment.autorun) {
                  await resolveEnrichments({ enrichmentId: segment.enrichmentId, options: { autorun: segment.autorun, required: segment.required } });
                }
              }
              if (segment.type === 'field-box' && segment.fieldId) {
                await resolveFields({ fieldId: segment.fieldId, options: { autorun: segment.autorun, required: segment.required } });
              }
            }
          }
        }

        async function resolveFields({ fieldId, options: options }: { fieldId: string; options?: { autorun?: boolean; required?: boolean } }): Promise<void> {
          if (!fieldId) {
            return;
          }

          // Handle idea fields
          if (entityType === EntityType.idea) {
            // For idea fields, we don't need to resolve any enrichments
            return;
          }

          const enrichment = await db.enrichment.findFirst({ where: { entityFieldId: fieldId } });
          if (!enrichment) {
            // Not bound to any enrichment. No need to run anything.
            return;
          }

          if (enrichmentIdsToExecute.includes(enrichment.id)) {
            return;
          }

          const field = await db.entityField.findUnique({ where: { id: fieldId } });
          if (!field) {
            // Not a real entity field. No need to run anything.
            return;
          }
          if (options?.autorun === false) {
            // Not required, will not include the enrichment in the execution
            return;
          }

          await resolveEnrichments({ enrichmentId: enrichment.id });
        }

        const result: { entityId: string; executionId: string; fieldId: string; status: string }[] = entityIds.map((entityId: string) => ({
          entityId,
          executionId: '',
          fieldId: enrichment.entityFieldId,
          status: ''
        }));

        if (enrichment.config.enrichmentType === EnrichmentType.llm || enrichment.config.enrichmentType === EnrichmentType.agent) {
          for (const entityId of entityIds) {
            logger.debug({ msg: 'Entering the entity loop', entityId });

            // Check if execution already exists
            const existingExecution = await db.execution.findFirst({
              where: {
                orgId: this.auth.oid,
                entityType,
                entityId,
                type: ExecutionType.enrichment,
                typeId: enrichment.id,
                status: {
                  in: [ExecutionStatus.pending, ExecutionStatus.scheduled, ExecutionStatus.inqueue, ExecutionStatus.processing]
                }
              }
            });

            if (existingExecution) {
              const resultFound = result.find((r) => r.entityId === entityId);
              if (resultFound) {
                resultFound.status = 'duplicate';
              }
              continue;
            }

            const enrichmentData = enrichment.data as object;

            let promptObject: object[] = [];
            try {
              promptObject = JSON.parse(enrichmentData[enrichment.config.enrichmentType === EnrichmentType.agent ? 'objective' : 'prompt']);
            } catch (error) {
              logger.error({ msg: 'Error parsing prompt', error });
            }

            for (const paragraph of promptObject) {
              for (const segment of paragraph['children']) {
                if (segment.type === 'enrich-box' && segment.enrichmentId) {
                  if (segment.autorun) {
                    await resolveEnrichments({ enrichmentId: segment.enrichmentId, options: { autorun: segment.autorun, required: segment.required } });
                  }
                }
                if (segment.type === 'field-box' && segment.fieldId) {
                  await resolveFields({ fieldId: segment.fieldId, options: { autorun: segment.autorun, required: segment.required } });
                }
              }
            }
          }

          logger.debug({ msg: 'Enrichment v2 chain resolved', enrichmentIdsToExecute });
        }

        logger.debug({ msg: 'Creating executions...' });

        const executionIds: string[] = [];
        const firebaseUpdates: object = {};

        for (let i = 0; i < entityIds.length; i++) {
          const entityId = entityIds[i];
          const resultFound = result.find((r) => r.entityId === entityId);
          if (!resultFound) {
            continue;
          }
          if (resultFound && resultFound.status === 'duplicate') {
            continue;
          }

          const execution: { id: string } = await this.createEnrichmentExecution({ enrichment: { id: enrichmentIdsToExecute[0] }, entityType, entityId, silent });

          const taskData = {
            enrichmentIds: enrichmentIdsToExecute,
            currentStep: 0
          };
          if (typeof options === 'object' && 'runFor' in options) {
            taskData['runFor'] = options.runFor;
          }
          const task: { id: string } = await this.createEnrichmentTask({ execution, taskData });
          resultFound.status = 'started';
          resultFound.executionId = execution.id;

          if (entityType === EntityType.idea && this.writeResultsToSocket && this.notificationService && !silent) {
            for (let i = 0; i < enrichmentIdsToExecute.length; i++) {
              const enrichmentId = enrichmentIdsToExecute[i];
              let predefinedSteps: { [key: string]: IEnrichmentProcessStep } = {
                inqueue: {
                  status: 'processing',
                  statusMessage: 'In queue',
                  time: new Date()
                },
                prepare: {
                  status: 'pending',
                  statusMessage: 'Preparing request',
                  time: new Date()
                },
                decision: {
                  status: 'pending',
                  statusMessage: '',
                  time: null
                },
                format: {
                  status: 'pending',
                  statusMessage: '',
                  time: null
                }
              };
              if (enrichment.config.enrichmentType === EnrichmentType.agent) {
                if (enrichment.config.data['deepResearch']) {
                  predefinedSteps = {
                    inqueue: {
                      status: 'processing',
                      statusMessage: 'In queue',
                      time: new Date()
                    },
                    prepare: {
                      status: 'pending',
                      statusMessage: 'Preparing request',
                      time: new Date()
                    },
                    insight: {
                      status: 'pending',
                      statusMessage: '',
                      time: null
                    },
                    research: {
                      status: 'pending',
                      statusMessage: '',
                      time: null
                    },
                    analyze: {
                      status: 'pending',
                      statusMessage: '',
                      time: null
                    },
                    report: {
                      status: 'pending',
                      statusMessage: '',
                      time: null
                    },
                    classify: {
                      status: 'pending',
                      statusMessage: '',
                      time: null
                    }
                  };
                } else {
                  predefinedSteps = {
                    inqueue: {
                      status: 'processing',
                      statusMessage: 'In queue',
                      time: new Date()
                    },
                    prepare: {
                      status: 'pending',
                      statusMessage: 'Preparing request',
                      time: new Date()
                    },
                    decision: {
                      status: 'pending',
                      statusMessage: '',
                      time: null
                    },
                    format: {
                      status: 'pending',
                      statusMessage: '',
                      time: null
                    }
                  };
                }
              }
              if (i === 0) {
                predefinedSteps.inqueue.status = 'completed';
                predefinedSteps.prepare.status = 'processing';
              }

              firebaseUpdates[`ideas/${entityId}/enrichments/${enrichmentId}`] = {
                entityId,
                enrichmentId,
                executionId: execution.id,
                taskId: task.id,
                status: 'processing',
                time: new Date().toISOString(),
                steps: predefinedSteps,
                data: null
              };
              const randomUuid = crypto.randomUUID();
              firebaseUpdates[`newExecutions/${randomUuid}`] = {
                type: 'enrichment',
                entityId,
                enrichmentId,
                executionId: execution.id,
                time: new Date().toISOString()
              };
            }
          } else if (this.writeResultsToSocket && this.notificationService && !silent) {
            for (let i = 0; i < enrichmentIdsToExecute.length; i++) {
              const enrichmentId = enrichmentIdsToExecute[i];
              const predefinedSteps: { [key: string]: IEnrichmentProcessStep } = {
                inqueue: {
                  status: 'processing',
                  statusMessage: 'In queue',
                  time: new Date()
                },
                prepare: {
                  status: 'pending',
                  statusMessage: 'Preparing request',
                  time: new Date()
                },
                decision: {
                  status: 'pending',
                  statusMessage: '',
                  time: null
                },
                format: {
                  status: 'pending',
                  statusMessage: '',
                  time: null
                }
              };
              if (i === 0) {
                predefinedSteps.inqueue.status = 'completed';
                predefinedSteps.prepare.status = 'processing';
              }

              firebaseUpdates[`entities/${entityId}/enrichments/${enrichmentId}`] = {
                entityId,
                enrichmentId,
                executionId: execution.id,
                taskId: task.id,
                status: 'processing',
                time: new Date().toISOString(),
                steps: predefinedSteps,
                data: null
              };
              const randomUuid = crypto.randomUUID();
              firebaseUpdates[`newExecutions/${randomUuid}`] = {
                type: 'enrichment',
                entityId,
                enrichmentId,
                executionId: execution.id,
                time: new Date().toISOString()
              };
            }
          }
          executionIds.push(execution.id);
        }
        logger.debug({ msg: 'Enrichment executions created', enrichment });

        if (Object.keys(firebaseUpdates).length && this.writeResultsToSocket && this.notificationService && !silent) {
          if (entityType === EntityType.idea) {
            logger.debug({ msg: 'Pushing updates to firebase:', count: Object.keys(firebaseUpdates).length });
            await this.notificationService.updateData(`/organizations/${this.auth.oid}/lists/${enrichment.listCompanyId}`, firebaseUpdates);
          } else {
            logger.debug({ msg: 'Pushing updates to firebase:', count: Object.keys(firebaseUpdates).length });
            await this.notificationService.updateData(`/organizations/${this.auth.oid}/entities`, firebaseUpdates);
          }
        }

        await db.queueTask
          .updateMany({
            where: { executionId: { in: executionIds } },
            data: { status: ExecutionStatus.pending }
          })
          .catch((error) => {
            logger.error({ msg: 'Enrichment execution failed. Cannot update queue tasks', executionIds, error });
          });

        return result;
      });
  }

  async writeEnrichmentExecutionToNotificationProvider({ execution, entityType, entityId }): Promise<void> {
    if (!this.writeResultsToSocket || !this.notificationService) {
      return;
    }

    const task = await db.queueTask.findFirst({
      where: { executionId: execution.id },
      select: { id: true, data: true }
    });
    if (!task) {
      throw new Error('Task not found');
    }
    const taskData = task.data as object;
    if (!('enrichmentIds' in taskData)) {
      throw new Error('Enrichments not found in payload');
    }

    const enrichmentIds = taskData['enrichmentIds'] as string[];
    logger.debug({ msg: 'Enrichments to execute', enrichmentIds });

    let entity: { name: string } | { firstName: string; lastName: string } | null;
    if (entityType === EntityType.contact) {
      entity = await db.contact.findUnique({
        where: { id: entityId },
        select: { firstName: true, lastName: true }
      });
    } else {
      entity = await db.company.findUnique({ where: { id: entityId }, select: { name: true } });
    }

    const data = {
      [execution.id]: {
        id: execution.id,
        enrichments: enrichmentIds.reduce((acc, enrichmentId) => {
          acc[enrichmentId] = { id: enrichmentId, status: 'processing' };
          return acc;
        }, {}),
        status: 'processing',
        time: new Date().toISOString(),
        entity: {
          id: entityId,
          type: entityType as string,
          ...entity
        },
        data: null
      }
    };

    logger.debug({ msg: 'Firebase data ready to send', data });

    await this.notificationService.writeData(`/enrichmentExecutions/organizations/${this.auth.oid}/executions`, data);
  }

  async createEnrichmentExecution({ enrichment, entityType, entityId, silent }): Promise<{ id: string }> {
    return db.execution.create({
      data: {
        orgId: this.auth.oid,
        type: ExecutionType.enrichment,
        typeId: enrichment.id,
        entityType,
        entityId,
        status: ExecutionStatus.pending,
        statusMessage: '',
        createdById: this.auth.uid,
        createdAt: new Date(),
        scheduledAt: new Date(),
        isSilent: silent
      },
      select: { id: true }
    });
  }

  async createEnrichmentTask({ execution, taskData }): Promise<{ id: string }> {
    return db.queueTask.create({
      data: {
        executionId: execution.id,
        type: QueueTaskType.enrichment,
        status: ExecutionStatus.scheduled,
        data: taskData,
        createdAt: new Date(),
        scheduledAt: new Date()
      },
      select: { id: true }
    });
  }

  async getEnrichmentExecution(executionId: string): Promise<any> {
    return db.execution.findUnique({
      where: { id: executionId },
      select: {
        id: true,
        status: true,
        statusMessage: true,
        entityType: true,
        entityId: true
      }
    });
  }

  async getExecutions(): Promise<IExecution[]> {
    return db.execution.findMany({
      where: {
        orgId: this.auth.oid,
        status: { in: [ExecutionStatus.pending, ExecutionStatus.inqueue, ExecutionStatus.scheduled, ExecutionStatus.processing] },
        isSilent: false
      },
      select: {
        id: true,
        type: true,
        typeId: true,
        entityType: true,
        entityId: true,
        status: true,
        statusMessage: true,
        createdAt: true,
        scheduledAt: true,
        executedAt: true,
        queueTasks: {
          select: {
            id: true,
            type: true,
            status: true,
            scheduledAt: true
          }
        }
      }
    });
  }

  async assignEnrichmentToField({ fieldId, enrichmentId }) {
    return db.enrichment
      .findUnique({
        where: {
          id: enrichmentId,
          orgId: this.auth.oid
        }
      })
      .then(async (enrichment: Enrichment | null) => {
        if (!enrichment) {
          throw new NotFoundError('Enrichment not found');
        }
        return db.entityField
          .findUnique({
            where: {
              id: fieldId,
              orgId: this.auth.oid
            }
          })
          .then(async (field: EntityField | null) => {
            if (!field) {
              throw new NotFoundError('Field not found');
            }
            const existingEnrichment = await db.enrichment.findFirst({ where: { entityFieldId: fieldId } });
            let isPrimary = false;
            if (!existingEnrichment) {
              isPrimary = true;
            }
            return db.enrichment.update({
              where: { id: enrichmentId },
              data: {
                entityField: { connect: { id: fieldId } },
                isPrimary
              },
              select: {
                id: true,
                name: true,
                results: {
                  select: {
                    createdAt: true
                  },
                  orderBy: {
                    createdAt: 'desc'
                  },
                  take: 1
                }
              }
            });
          });
      });
  }

  async unassignEnrichmentFromField({ fieldId, enrichmentId }): Promise<boolean> {
    return db.enrichment
      .findUnique({
        where: {
          id: enrichmentId,
          orgId: this.auth.oid
        }
      })
      .then(async (enrichment: Enrichment | null): Promise<boolean> => {
        if (!enrichment) {
          throw new NotFoundError('Enrichment not found');
        }
        return db.entityField
          .findUnique({
            where: {
              id: fieldId,
              orgId: this.auth.oid
            }
          })
          .then(async (field: EntityField | null): Promise<boolean> => {
            if (!field) {
              throw new NotFoundError('Field not found');
            }
            return db.enrichment
              .update({
                where: { id: enrichmentId },
                data: {
                  entityField: { disconnect: true },
                  isPrimary: false
                }
              })
              .then((): boolean => true);
          });
      });
  }

  async setPrimaryEnrichmentForField({ fieldId, enrichmentId }): Promise<boolean> {
    return db.$transaction(async (trans) => {
      return trans.enrichment
        .findUnique({
          where: {
            id: enrichmentId,
            orgId: this.auth.oid
          }
        })
        .then(async (enrichment: Enrichment | null): Promise<boolean> => {
          if (!enrichment) {
            throw new NotFoundError('Enrichment not found');
          }
          return trans.entityField
            .findUnique({
              where: {
                id: fieldId,
                orgId: this.auth.oid
              }
            })
            .then(async (field: EntityField | null): Promise<boolean> => {
              if (!field) {
                throw new NotFoundError('Field not found');
              }
              return trans.enrichment
                .updateMany({
                  where: { entityFieldId: fieldId },
                  data: { isPrimary: false }
                })
                .then(async () =>
                  trans.enrichment
                    .update({
                      where: { id: enrichmentId },
                      data: {
                        isPrimary: true
                      }
                    })
                    .then(
                      async (): Promise<boolean> =>
                        trans.entityField
                          .update({
                            where: { id: fieldId },
                            data: {
                              invalidatedAt: new Date()
                            }
                          })
                          .then((): boolean => true)
                    )
                );
            });
        });
    });
  }

  async deleteEnrichment({ enrichmentId }): Promise<boolean> {
    const enrichment = await db.enrichment.findUnique({ where: { id: enrichmentId, orgId: this.auth.oid } });
    if (!enrichment) {
      throw new NotFoundError('Enrichment not found');
    }

    if (enrichment.entityType === EntityType.idea) {
      // Need to do hard delete for idea enrichments
      return db.enrichment.delete({ where: { id: enrichmentId } }).then((): boolean => true);
    }

    return db.enrichment
      .findUnique({
        where: { id: enrichmentId, orgId: this.auth.oid }
      })
      .then(async (enrichment: Enrichment | null) => {
        if (!enrichment) {
          throw new NotFoundError('Enrichment not found');
        }
        return db.enrichment
          .update({
            where: { id: enrichmentId },
            data: {
              deletedAt: new Date()
            }
          })
          .then((): boolean => true);
      });
  }

  async setColor({ enrichmentId, color }: { enrichmentId: string; color: object }): Promise<boolean> {
    const enrichment = await db.enrichment.findUnique({ where: { id: enrichmentId, orgId: this.auth.oid } });
    if (!enrichment) {
      throw new NotFoundError('Enrichment not found');
    }

    return db.enrichment
      .update({
        where: { id: enrichmentId },
        data: {
          data: { ...(enrichment.data as object), color }
        }
      })
      .then((): boolean => true);
  }

  async tagFind({ hint }: { hint: string }): Promise<ITagFinderResult | null> {
    const integration = await db.orgIntegration.findFirst({
      where: {
        orgId: this.auth.oid,
        provider: IntegrationProvider.openai,
        expiresAt: { gt: new Date() },
        active: true
      },
      select: { authToken: true }
    });
    if (!integration) {
      throw new Error('OpenAI integration not found');
    }
    const client = new OpenAI({ apiKey: integration.authToken as string });

    const functionParameters: FunctionParameters = {
      type: 'object',
      required: ['column_name', 'tags', 'data_type'],
      properties: {
        column_name: { type: 'string', description: 'Possible column name.' },
        definition: { type: 'string', description: 'Possible column definition.' },
        data_type: { type: 'string', description: 'Possible data type.' },
        tags: { type: 'string', description: 'Comma separated list of tags (possible datacell values).' }
      }
    };

    const tools: ChatCompletionTool[] = [
      {
        type: 'function',
        function: {
          name: 'get_structure_details',
          description: 'Provide structured information about a given topic.',
          parameters: functionParameters
        }
      }
    ];

    const messages = [
      {
        role: 'system',
        content: `
You have a datatable of companies. There is an info column about the company in this table. The data in the Y axis can be anything about the company.
You only know a part of its title. We call that "hint".

With the given hint, you need to find what the data can be on this column. For example, if given hint is "industry", you will return two things:
  1. Column name: Business Industry,
  2. Data type: string | number | enum
  3. Possible column data: If the data type is enum, give me possible data as comma separated string, eg: Agriculture, Automotive, Finance, Healthcare, etc, etc.. If type is not enum, ignore this field. Just response with empty string
  4. Column definition: The definition of the data to write into the schema.

You can be a little creative, but not too much. Give me strict answers only, no comments, no explanations needed.
`
      },
      {
        role: 'user',
        content: `Create a column with the hint: ${hint}`
      }
    ];

    const aiRawResult = await client.chat.completions.create({
      model: 'gpt-4.1-mini',
      temperature: 0.2,
      messages: messages as any,
      tools,
      tool_choice: 'required'
    });

    try {
      const response = aiRawResult['choices'][0]['message']['tool_calls']?.[0]['function']['arguments'];
      if (response) {
        return JSON.parse(response);
      }
    } catch (_e) {
      return null;
    }
    return null;
  }

  async generatePrompt({ prompt }: { prompt: string }): Promise<IPromptGeneratorResult | null> {
    const integration = await db.orgIntegration.findFirst({
      where: {
        orgId: this.auth.oid,
        provider: IntegrationProvider.openai,
        expiresAt: { gt: new Date() },
        active: true
      },
      select: { authToken: true }
    });
    if (!integration) {
      throw new Error('OpenAI integration not found');
    }
    const client = new OpenAI({ apiKey: integration.authToken as string });

    const functionParameters: FunctionParameters = {
      type: 'object',
      properties: {
        enrichmentName: {
          type: 'string',
          description:
            "Choose a short, human-readable name that clearly describes the big picture question that this user question is attempting to address. Use title case, no punctuation, and keep it under 24 characters if possible. PE professionals should quickly understand the label even if it's truncated. Prioritize clarity and put the most important words first."
        },
        keyName: {
          type: 'string',
          description:
            "Choose a short, human-readable name for this individual key in the eventual JSON schema that clearly describes what this column contains. This key will exist underneath the Enrichment Name as a child. Use title case, no punctuation, and keep it under 24 characters if possible. PE professionals should quickly understand the label even if it's truncated. Prioritize clarity and put the most important words first."
        },
        keyDescription: {
          type: 'string',
          description:
            'Write a brief, clear explanation of what this field represents and how it’s typically used (this will go into a JSON schema description field). Use plain language in 1–2 short sentences. Ensure this has explicit enough details to guide an eventual classification and data extraction expert about what sort of responses are allowed. Clarify any abbreviations or logic if applicable.'
        },
        keyDataType: {
          type: 'string',
          enum: ['categorical', 'string', 'number']
        },
        keyOptions: {
          type: 'string',
          description:
            'This should be a string containing a valid immediately parsable JSON array of category options, with each value in double quotes. Example: \'["5 - Most Favorable", "4 - Strong", "3 - Moderate", "2 - Weak", "1 - Very Weak", "0 - Not Enough Information"]\'. Each value must follow your team\'s naming conventions and match an approved tag. Ensure this string can be parsed by json.loads without modification.'
        }
      },
      required: ['enrichmentName', 'keyName', 'keyDescription', 'keyDataType', 'keyOptions']
    };

    /*
    const functionParameters: FunctionParameters = {
      type: 'object',
      required: ['enrichment_name', 'column_name', 'definition', 'options', 'data_type'],
      properties: {
        enrichment_name: { type: 'string', description: 'Name of the enrichment to create.' },
        column_name: { type: 'string', description: 'Possible column name.' },
        definition: { type: 'string', description: 'Possible column definition.' },
        data_type: { type: 'string', description: 'Possible data type.' },
        options: { type: 'string', description: 'Comma separated list of tags (possible datacell values).' }
      }
    };
*/

    const tools: ChatCompletionTool[] = [
      {
        type: 'function',
        function: {
          name: 'get_structure_details',
          description: 'Provide structured information about a given topic.',
          parameters: functionParameters
        }
      }
    ];

    const messages = [
      /*
      {
        role: 'system',
        content: `
You have a datatable of companies. There is an info column about the company in this table. The data in the Y axis can be anything about the company.
You only know a part of its title. We call that "hint".

With the given hint, you need to find what the data can be on this column. For example, if given hint is "industry", you will return two things:
  1. Column name: Business Industry,
  2. Data type: string | number | enum
  3. Possible column data: If the data type is enum, give me possible data as comma separated string, eg: Agriculture, Automotive, Finance, Healthcare, etc, etc.. If type is not enum, ignore this field. Just response with empty string
  4. Column definition: The definition of the data to write into the schema.
  5. Enrichment name: The name of the enrichment to create.

You can be a little creative, but not too much. Give me strict answers only, no comments, no explanations needed.
`
      },
*/
      {
        role: 'user',
        content: `
We've been hired by a private equity firm to suggest how we should extract and format **structured data** from unstructured sources (e.g., company websites, PDFs, research reports, sales decks) based on questions posed by **private equity investors**. These investors use structured data fields to assess opportunities, flag risks, and compare businesses efficiently.

Your job is **not** to answer the user’s question directly. Your job is to **design the best output format** for structuring the answer — making it consistent, machine-readable, and analytically useful.

This means thinking carefully about:
- What insight the user is seeking
- How to structure that insight
- What type of field best suits the answer (categorical, string, number)
- How to clearly define that field for consistent use across a research or AI team

---

## Skills You Will Rely Upon

You will draw on your experience in:

- **Ontology and Category Design**: Breaking down complex or fuzzy inputs into clearly defined, mutually exclusive labels.
- **Private Equity Acumen**: Understanding what investors care about (e.g., bootstrapped SaaS companies, highly engineered materials, medtech certifications, cap table and ownership dynamics, commercial defensibility).
- **Product + Data Modeling**: Structuring outputs that work inside spreadsheets, databases, and AI systems (often as chips, dropdown or data-validated options).
- **Clarity and Interpretability**: Your formats must be clear to both analysts and machines. Human consistency is key.

---

## Allowed Output Types

You must define one of the following as the **Ideal Format Type**:

| Type | Use When... | Example |
|------|-------------|---------|
| \`string\` | Freeform or semicolon/comma-separated list of text values | "This business serves..." OR “ISO-13485; AS9100” |
| \`number\` | A single numeric value is needed | “500000” |
| \`categorical\` | A single selection from a fixed list of possible answers (ordered or unordered) | “3 - Some Fluid Conveyance” |

When possible, you should:
- Use **enumerated, ordinal categories** for nuanced scoring (0–5 or equivalent), and in particular domain-specific ordinal enumerated categories (with semantic labelling contextualized for graded relevance scoring)
- Include buckets like **“0 – Not enough information”** as a fallback category
- For open categorical sets, multi-hot, multi-valued or multi-select categorical fields, use delimited string (primarily **semicolon-separated strings**) including for data like certifications, designations, comprehensive lists of offerings, or list of investor names

---

## Required Instructions and Fields

Given the provided question or input from the user, fill out the JSON schema provided, including:

### Enrichment Name
{Choose a short, human-readable name that clearly describes the big picture question that this user question is attempting to address. Use title case, no punctuation, and keep it under 24 characters if possible. PE professionals should quickly understand the label even if it's truncated. Prioritize clarity and put the most important words first.}

### Key Name
{Choose a short, human-readable name for this individual key in the eventual JSON schema that clearly describes what this column contains. This key will exist underneath the Enrichment Name as a child. Use title case, no punctuation, and keep it under 24 characters if possible. PE professionals should quickly understand the label even if it's truncated. Prioritize clarity and put the most important words first.}

### Key Description:
{Write a brief, clear explanation of what this field represents and how it’s typically used (this will go into a JSON schema description field). Use plain language in 1–2 short sentences. Ensure this has explicit enough details to guide an eventual classification and data extraction expert about what sort of responses are allowed. Clarify any abbreviations or logic if applicable.}

### Key Data Type:
{string, number, or categorical}

### Key Options (Enumerated Options):
{Only required if keyDataType is \`categorical\`. Use this comma separated string format: '["5 - Most Favorable", "4 - Strong", "3 - Moderate", "2 - Weak", "1 - Very Weak", "0 - Not Enough Information"]'}

---

## Examples of Great Output Formats

Use these real examples to guide your thinking and pattern reuse:

---

### Example #1:
**User Question:** "Do they offer wear components?"
**Key Data Type:** Categorical
**Key Description:** "Select the most appropriate label - either '5 - Primarily Wear Parts', '4 - Mostly Wear Parts', '3 - Some Wear Parts', '2 - Little Wear Parts', '1 - No Wear Parts' OR '0 - Not enough information'"
**Enumerated Key Options:**
"""
5 - Primarily Wear Parts
4 - Mostly Wear Parts
3 - Some Wear Parts
2 - Little Wear Parts
1 - No Wear Parts
0 - Not enough information
"""
**Example Responses:**
- "5 - Primarily Wear Parts"
- "1 - No Wear Parts"

---

### Example #2:
**User Question:** "How does their cap table look? Have they raised anything or been acquired?"
**Key Data Type:** Categorical
**Key Description:** "Select the most appropriate of 'Public', 'Acquired - PE or Strategic', 'Some VC', 'Some Angel', 'Likely Bootstrapped', 'Other - Unclear Funding'"
**Enumerated Key Options:**
"""
Public
Acquired - PE or Strategic
Some VC
Some Angel
Likely Bootstrapped
Other - Unclear Funding
"""
**Example Responses:**
- "Likely Bootstrapped"
- "Some Angel"

---

### Example #3:
**User Question:** "Do they offer fluid conveyance?"
**Key Data Type:** Categorical
**Key Description:** "Select the most appropriate label for the business's focus on fluid conveyance - one of: '5 - Primarily Fluid Conveyance', '4 - Mostly Fluid Conveyance', '3 - Some Fluid Conveyance', '2 - Little Fluid Conveyance', OR '1 - No Fluid Conveyance' OR '0 - Insufficient Information'"
**Enumerated Key Options:**
"""
0 - Insufficient Information
1 - No Fluid Conveyance
2 - Little Fluid Conveyance
3 - Some Fluid Conveyance
4 - Mostly Fluid Conveyance
5 - Primarily Fluid Conveyance
"""
**Example Responses:**
- "1 - No Fluid Conveyance"
- "5 - Primarily Fluid Conveyance"

---

### Example #4:
**User Question:** "What is the pricing/revenue model of this software business?"
**Key Data Type:** Categorical
**Key Description:** "Select one of 'Perpetual License + Annual Maintenance', 'Subscription/SaaS Model', 'Enterprise-Wide or Site License', 'Consumption/Usage-Based Model', OR 'Other/Custom Pricing Models' - whichever is most appropriate"
Enumerated Options:"
**Enumerated Key Options:**
"""
Perpetual License + Annual Maintenance
Subscription/SaaS Model
Enterprise-Wide or Site License
Consumption/Usage-Based Model
Other/Custom Pricing Models
"""
**Example Responses:**
- "Subscription/SaaS Model"
- "Other/None of the Above Pricing Models"

---

### Example #5:
**User Question:** "What certifications does this business have?"
**Key Data Type:** String (semicolon-separated list)
**Key Description:** "Return a semicolon-separated list of the certifications identified for this business."
**Example Responses:**
- "AS-9100; ISO-13485"
- "ISO 9001; AS9100; IPC-A-610 Class 3"

---

### Example #6:
**User Question:** "Do they have any special ownership designations?"
**Key Data Type:** String (semicolon-separated list)
**Key Description:** "What special ownership designations does this business have (i.e. 'Small Disadvantaged Business (SDB)' or 'Women-Owned Business')? Return a semicolon separated list."
**Example Responses:**
- "None"
- "8(a) Certification; Small Disadvantaged Business Certification"

---

### Example #7:
**User Question:** "Where are they based?"
**Key Data Type:** String
**Key Description:** "Where is the location of the headquarters of this company? Provide the name the city, state and country as follows: 'San Francisco, CA, USA'"
**Example Responses:**
- "Mountain View, CA, USA"
- "Santa Fe Springs, CA, USA"

---

### Example #8:
**User Question:** "Who are their investors?"
**Key Data Type:** String (comma-separated list)
**Key Description:** "Comma separated list of investors like 'First Investor, Second VC, Third Partners, Fourth Person, Fifth VC' (continuing on in comma separated format for as many as needed)"
**Example Responses:**
- "Sequoia Capital, Andreessen Horowitz, Accel"
- "Battery Ventures, Redpoint Ventures"
- "No Capital Raised"

---

### Example #9:
**User Question:** "How experienced is this CEO?"
**Key Data Type:** Categorical
**Key Description:** "Assess the background of the CEO - your answer should be one of 'Non-Founder', 'First Time Founder', 'Experienced Founder' or 'Multi-time Founder'."
**Enumerated Key Options:**
"""
Non-Founder
First Time Founder
Experienced Founder
Multi-time Founder
"""
**Example Responses:**
- "First Time Founder"
- "Non-Founder"

---

### Example #10:
**User Question:** "What is the square footage of their facilities?"
**Key Data Type:** Number
**Key Description:** "Please output a single value for the cumulative square footage as a whole number formatted similar to 1,000,000. If there is a reliable figure, but it is a range, output a value that is in the median of that range. If you haven't found a reliable source, just say 'UNKNOWN'."
**Example Responses:**
- "5000000"
- "100000"
- "UNKNOWN"

---

### Example #11:
**User Question:** "Give me a 1-liner for the business"
**Key Data Type:** String
**Key Description:** "1-liner for the business, describing it as if for a busy CEO with limited time."
**Example Responses:**
- "Tech decision making and spend analysis platform for k12 education district administrators"
- "Billing software & A/R automation including bill presentment, follow up, collection, and reconciliation"
- "Enterprise portfolio management, analytics and client servicing tools for wealth management"
- "Cost control, automated contract compliance, and real-time contractor activity monitoring software and payments for heavy process industries"

---
## User Input or Question to Evaluate Now (FOCUS HERE):
"""
${prompt}

"""

## Final Instructions:
Please follow this format for every user question you evaluate. Take a look at the User Input or Question to Evaluate Now and begin your work. Consistency is critical for scaling this system across analysts, researchers, and AI agents. Take a deep breath and begin.
`
      }
    ];

    const aiRawResult = await client.chat.completions.create({
      model: 'gpt-4.1-mini',
      temperature: 0.2,
      messages: messages as any,
      tools,
      tool_choice: 'required'
    });

    try {
      const response = aiRawResult['choices'][0]['message']['tool_calls']?.[0]['function']['arguments'];
      if (response) {
        return JSON.parse(response);
      }
    } catch (_e) {
      return null;
    }
    return null;
  }

  async validateEnrichmentFields(raw: any, fieldName: string, fieldType?: string): Promise<string | undefined> {
    if (fieldType === 'richtext') {
      if (typeof raw !== 'string' || raw.trim() === '') {
        return `${fieldName} is required and cannot be empty`;
      }
      try {
        const blocks = JSON.parse(raw);
        const hasText = Array.isArray(blocks) && blocks.some((block) => Array.isArray(block.children) && block.children.some((child) => typeof child.text === 'string' && child.text.trim() !== ''));
        if (!hasText) {
          return `${fieldName} is required and cannot be empty`;
        }
      } catch {
        return `Invalid ${fieldName} format`;
      }
    } else {
      const isEmptyString = typeof raw === 'string' && raw.trim() === '';
      if (raw === null || isEmptyString) {
        return `${fieldName} is required and cannot be empty`;
      }
    }
  }
}

interface ITagFinderResult {
  column_name: string;
  definition: string;
  data_type: string;
  tags: string;
}

interface IPromptGeneratorResult {
  enrichment_name: string;
  column_name: string;
  definition: string;
  data_type: string;
  options: string;
}
