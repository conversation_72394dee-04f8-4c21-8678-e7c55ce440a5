import { logger } from '../logger';
import { db } from '../database';
import { LoopDetectedError, NotFoundError, EntityFieldNotFound, ServerError, UnprocessableContentError } from '../lib/errors';
import { validate as uuidValidate } from 'uuid';
import { IDynamicPlayTask, IPlay, IPlaybookResponse, IPlayExecutionParams, IPlayServiceConstructor, ISinglePlay, IPlaybookFilters, IPlaySaveRequest, IPlaybookUsers } from '../types/Play';
import { IAuth } from '../types/Common';
import { IEntity, IResponseStatus } from '../types/Entity';
import { CompanyService, ContactService, NotificationService, TemplateService, TemplateVariableService } from './';
import { ExecutionStatus, ExecutionType, Play, PlayStatus, QueueTaskType, VariableType } from '@prisma/client';
import { EntityType, PlayChannel, TaskType, LlmProvider } from '@prisma/client';
import { Patterns } from '../utils';
import { PAGE_SIZE } from '../constants';
import { convertPrismaWhereToSql, playbookFilterClause } from '../models/Entity';

interface ITemplate {
  id: string;
  llmEngine: LlmProvider;
  template: string;
  fieldMapping: {
    id: bigint;
    schemaField: {
      id: string;
      entity: EntityType;
      key: string;
      label: string;
    };
  }[];
}

interface ISinglePlayForExecution {
  id: string;
  templates: ITemplate[];
  tasks: {
    id: string;
    queueTaskType: QueueTaskType;
    type: TaskType;
  }[];
}

export default class PlayService {
  auth: IAuth;
  companyService: CompanyService;
  contactService: ContactService;
  templateVariablesService: TemplateVariableService;
  templateService: TemplateService | undefined;
  notificationService: NotificationService | undefined;
  writeResultsToSocket: boolean;

  constructor({ auth, companyService, contactService, templateService, templateVariablesService, notificationService, writeResultsToSocket }: IPlayServiceConstructor) {
    this.auth = auth;
    if (!templateVariablesService) {
      throw new ServerError('Template variables service not found');
    }
    if (!companyService) {
      throw new ServerError('Company service not found');
    }
    if (!contactService) {
      throw new ServerError('Contact service not found');
    }

    this.companyService = companyService;
    this.contactService = contactService;
    this.templateService = templateService;
    this.templateVariablesService = templateVariablesService;
    this.notificationService = notificationService;
    this.writeResultsToSocket = writeResultsToSocket || false;

    if (!auth.oid) {
      logger.error({ error: 'Invalid organization Id' });
      throw new Error('Invalid service call! Check your permissions!');
    }
  }

  async delete(playId: string): Promise<boolean> {
    return db.play.deleteById({ orgId: this.auth.oid, playId, isDraft: true });
  }

  async deletePreviewIfExists(tx) {
    return tx.play.deleteMany({
      where: {
        orgId: this.auth.oid,
        createdById: this.auth.uid,
        status: PlayStatus.preview
      }
    });
  }

  async deleteDraftIfExists(tx) {
    return tx.play.deleteMany({
      where: {
        orgId: this.auth.oid,
        createdById: this.auth.uid,
        status: PlayStatus.draft
      }
    });
  }

  async savePlay({ id: playId, name, title, instructions, attachments, status, llmProvider }: IPlaySaveRequest): Promise<IPlay> {
    if (!(await this.analyzeTemplate({ template: instructions }))) {
      throw new UnprocessableContentError('Invalid template');
    }
    const llmEngine = llmProvider ?? LlmProvider.openai;
    return db.$transaction(async (tx): Promise<IPlay> => {
      let oldPlay: Play | null = null;
      if (!status) {
        status = PlayStatus.published;
      }
      if (status === PlayStatus.draft) {
        await this.deleteDraftIfExists(tx);
      }
      if (playId) {
        oldPlay = await tx.play.findUnique({ where: { id: playId } });
        if ((status as PlayStatus) !== PlayStatus.preview) {
          await tx.play.deleteMany({
            where: {
              id: playId,
              orgId: this.auth.oid
            }
          });
        }
      }
      if ((status as PlayStatus) === PlayStatus.preview) {
        logger.debug({ msg: 'Deleting preview play' });
        playId = null;
        await this.deletePreviewIfExists(tx);
      }

      // TODO: find the right configuration based on user savePlay request, for now, it is static:
      const config = await tx.playConfig.findFirst({
        where: {
          channel: PlayChannel.email,
          llmEngine
        }
      });

      if (!config) {
        throw new ServerError('Play configuration invalid');
      }

      const playData = {
        id: playId || undefined,
        configId: config.id,
        name: name.trim().substring(0, 120),
        description: null,
        orgId: this.auth.oid,
        createdById: this.auth.uid,
        status: status as PlayStatus,
        templates: {
          create: {
            llmEngine,
            title: title,
            template: instructions,
            orgId: this.auth.oid
          }
        },
        createdAt: oldPlay?.createdAt || new Date(),
        updatedAt: playId ? new Date() : null
      };

      const newPlay = await tx.play.create({ data: playData });

      if (!newPlay) {
        throw new ServerError('Unable to create play');
      }
      const template = await tx.template.findFirst({
        where: {
          playId: newPlay.id
        }
      });
      if (!template) {
        throw new ServerError('Unable to create play template');
      }

      if (attachments) {
        await tx.templateAttachmentFieldMapping
          .createMany({
            data: attachments?.map((field) => {
              return {
                templateId: template.id,
                schemaFieldId: field
              };
            })
          })
          .catch((err: unknown) => {
            logger.error({ msg: 'Error creating play attachments', err });
            throw new ServerError('Unable to create play attachments', { err });
          });
      }

      const play: ISinglePlayForExecution | null = await tx.play.findUnique({
        where: { id: newPlay.id },
        select: {
          id: true,
          templates: {
            // @todo: TBD: We need to find a way to get the template from the right configuration with parameters
            where: { playId: newPlay.id, llmEngine },
            select: {
              id: true,
              llmEngine: true,
              template: true,
              fieldMapping: {
                select: {
                  id: true,
                  schemaField: {
                    select: {
                      id: true,
                      entity: true,
                      key: true,
                      label: true
                    }
                  }
                }
              }
            }
          },
          tasks: {
            select: {
              id: true,
              queueTaskType: true,
              type: true
            },
            orderBy: { priority: 'asc' }
          }
        }
      });
      if (!play) {
        throw new NotFoundError('Could not find newly created play');
      }
      logger.debug({ msg: 'Play created', playId: play.id });

      const rawTasks = await this.createTasks({ play });
      if (!rawTasks.length) {
        throw new UnprocessableContentError('Cannot create tasks');
      }
      logger.debug({ msg: 'Tasks created', tasks: rawTasks });

      const orderedEnrichmentTasks = this.orderTasks(rawTasks.filter((t: IDynamicPlayTask) => t?.priority >= 200 && t?.priority < 300)).map((task: IDynamicPlayTask, i: number) => {
        task.priority = 200 + i + 1;
        return task;
      });
      logger.debug({ msg: 'Enrichment tasks ordered', orderedEnrichmentTasks });

      const orderedPlayTasks = this.orderTasks(rawTasks.filter((t: IDynamicPlayTask) => t && t.priority >= 300)).map((task: IDynamicPlayTask, i: number) => {
        task.priority = 300 + i + 1;
        return task;
      });
      logger.debug({ msg: 'Play tasks ordered', orderedPlayTasks });

      const tasks = [...orderedEnrichmentTasks, ...orderedPlayTasks].map((task: IDynamicPlayTask, i: number) => {
        task.priority = i + 1;
        return task;
      });
      logger.debug({ msg: 'Final ordered tasks', tasks });

      await tx.playTask.createMany({
        data: tasks.map((task: IDynamicPlayTask) => ({
          playId: play.id,
          enrichmentId: task.enrichmentId || null,
          priority: task.priority,
          data: task.data,
          queueTaskType: task.queueTaskType,
          type: task.taskType as TaskType
        }))
      });

      return newPlay;
    });
  }

  async getPlays(): Promise<IPlay[]> {
    return db.play.listPlays({ orgId: this.auth.oid }).catch((err: unknown) => {
      throw err;
    });
  }

  async getPlaybook(filters: IPlaybookFilters): Promise<IPlaybookResponse> {
    const whereClause = playbookFilterClause(filters, this.auth.oid);
    const whereQuery = convertPrismaWhereToSql(whereClause, 'p');

    const playIds = (await db.play.getSortedPlayIds(filters, whereQuery)).map((a) => a.id);

    return db.play.listPlaybook({ orgId: this.auth.oid, playIds, pageSize: PAGE_SIZE, whereClause });
  }

  async getDraft(llmEngine?: LlmProvider): Promise<ISinglePlay | null> {
    const draft = await db.play.findFirst({
      where: {
        orgId: this.auth.oid,
        createdById: this.auth.uid,
        status: PlayStatus.draft
      },
      select: {
        id: true
      }
    });
    if (!draft) {
      return null;
    }

    return this.getPlay({ playId: draft.id, llmEngine });
  }

  async removeDraft(): Promise<true> {
    await this.deleteDraftIfExists(db);
    return true;
  }

  async getPlay({ playId, llmEngine }: { playId: string; llmEngine?: LlmProvider }): Promise<ISinglePlay | null> {
    const play = await db.play.getPlayById({ playId, orgId: this.auth.oid, llmEngine });
    if (!play?.templates?.length) {
      return play;
    }
    logger.debug({ msg: 'Play found', play: play });
    return play;
  }

  mandatoryFields: string[] = ['company.name', 'company.website', 'contact.first_name', 'contact.last_name', 'contact.email'];

  async analyzeTemplate({ template }: { template: string }): Promise<true> {
    logger.info({ msg: 'Template analyze started' });
    if (!this.templateService) {
      throw new ServerError('Template service not found');
    }
    const templateVariables = this.templateService.extractVariablesFromTemplate({ template });

    if (!templateVariables) {
      // No template variables found. But this is still valid, it's simple static content
      return true;
    }

    for (let i = 0; i < templateVariables.length; i++) {
      const variable = templateVariables[i];
      if (!Patterns.VariableKey.test(variable)) {
        throw new UnprocessableContentError('Invalid template variable', { variable });
      }
      const variableType: string = this.templateVariablesService.getType(variable);
      const [namespace, key]: string[] = variable.split('.');
      if (!namespace || !key) {
        throw new UnprocessableContentError('Invalid template variable!', { variable });
      }

      if (variableType === VariableType.pseudo) {
        logger.debug({ msg: 'Pseudo field found for variable', variable });
        continue;
      }

      if (this.mandatoryFields.includes(variable)) {
        logger.debug({ msg: 'Mandatory field found for variable', variable });
        continue;
      }

      const entityField = await db.entityField.findFirst({
        where: {
          entity: namespace as EntityType,
          key: key
        },
        select: {
          id: true,
          isMandatory: true
        }
      });
      if (!entityField) {
        throw new UnprocessableContentError(`Entity field not found ${variable}`, { variable });
      }
      if (entityField.isMandatory) {
        // This passes 100%. It's mandatory, we know it's there.
        logger.debug({
          msg: 'Entity field (mandatory) found for variable',
          variable,
          entityField
        });
        continue;
      }
      if (!entityField.isMandatory) {
        // This one, we don't know. If the data exists, we process. If not, we can't do anything, we'll pass that.
        logger.debug({
          msg: 'Entity field (non-mandatory) found for variable',
          variable,
          entityField
        });
        continue;
      }

      // @todo: All conditions were checked here?
    }
    return true;
  }

  async analyzePlay({ play }): Promise<true> {
    const template: string = play.templates[0].template;
    return this.analyzeTemplate({ template });
  }

  async createTasks({ play }): Promise<IDynamicPlayTask[]> {
    logger.info({ msg: 'Creating tasks for the play', playId: play.id });
    if (!this.templateService) {
      throw new ServerError('Template service not found');
    }
    const template = play.templates[0];
    const templateVariables = this.templateService.extractVariablesFromTemplate({ template: template.template });

    if (template.fieldMapping.length > 0) {
      const fieldMapping = template.fieldMapping;
      for (let i = 0; i < fieldMapping.length; i++) {
        const mapping = fieldMapping[i];
        const variable = `${mapping.schemaField.entity}.${mapping.schemaField.key}`;
        if (!templateVariables.includes(variable)) {
          templateVariables.push(variable);
        }
      }
    }

    const tasks: IDynamicPlayTask[] = [];
    for (let i = 0; i < templateVariables.length; i++) {
      const variable = templateVariables[i];
      const variableType = this.templateVariablesService.getType(variable);
      const [namespace, key] = variable.split('.');

      if ((variableType as VariableType) === VariableType.pseudo) {
        continue;
      }
      if (this.mandatoryFields.includes(variable)) {
        continue;
      }
      const entityField = await db.entityField.findFirst({
        where: { entity: namespace as EntityType, key: key },
        select: { id: true }
      });
      if (!entityField) {
        throw new EntityFieldNotFound(`Entity field not found ${variable}`, {
          param: variable
        });
      }
    }

    tasks.push({
      variable: '',
      dependencies: [],
      playId: play.id,
      priority: 300,
      queueTaskType: QueueTaskType.play,
      data: {},
      taskType: TaskType.channeloutput
    });

    return tasks;
  }

  async findEnrichmentDependencies(enrichment: any): Promise<string> {
    if (!enrichment.data && !('fieldIdToProcess' in (enrichment.data as object))) {
      throw new UnprocessableContentError('Invalid enrichment data');
    }

    const dependencyFieldId = (enrichment.data as object)['fieldIdToProcess'];
    if (!dependencyFieldId && typeof dependencyFieldId !== 'string') {
      return '';
    }

    const dependencyField = await db.entityField.findFirst({
      where: {
        id: dependencyFieldId
      },
      select: {
        id: true,
        key: true,
        entity: true
      }
    });

    return dependencyField ? `${dependencyField.entity}.${dependencyField.key}` : '';
  }

  orderTasks(tasks: IDynamicPlayTask[]): IDynamicPlayTask[] {
    const graph: { [key: string]: string[] } = {};
    const visited: { [key: string]: boolean } = {};
    const result: any[] = [];

    // Step 1: Create the graph
    for (const task of tasks) {
      graph[task.variable] = task.dependencies;
      visited[task.variable] = false;
    }

    // Step 2: Topological sort
    for (const task of tasks) {
      if (!visited[task.variable]) {
        const stack: string[] = [];
        stack.push(task.variable);

        const SAFE_QUIT_COUNTER = 100;
        let quitCounter = 0;
        while (stack.length) {
          if (quitCounter++ > SAFE_QUIT_COUNTER) {
            throw new LoopDetectedError('Circular dependency detected');
          }
          const node = stack[stack.length - 1];
          if (!graph[node] || graph[node].every((v) => visited[v] || v === node)) {
            visited[node] = true;
            const taskToAdd = tasks.find((t) => t.variable === node);
            if (taskToAdd) {
              result.push(taskToAdd);
            }
            stack.pop();
          } else {
            for (const neighbor of graph[node]) {
              if (!visited[neighbor]) {
                stack.push(neighbor);
              }
            }
          }
        }
      }
    }

    return result;
  }

  async executePlay({ playId, entityType: targetType, targetIds, executionId, channelEmailId, entityId }: IPlayExecutionParams) {
    if (!Object.values(EntityType).includes(targetType)) {
      throw new UnprocessableContentError('Invalid target type: ' + targetType);
    }

    return db.$transaction(async (tx: any) => {
      const getLlmEngine = await tx.template.findFirst({
        where: { playId },
        select: {
          llmEngine: true
        }
      });
      const play: ISinglePlayForExecution | null = await tx.play.findUnique({
        where: { id: playId },
        select: {
          id: true,
          templates: {
            // @todo: TBD: We need to find a way to get the template from the right configuration with parameters
            where: { playId, llmEngine: getLlmEngine?.llmEngine },
            select: {
              id: true,
              title: true,
              template: true
            }
          },
          tasks: {
            select: {
              id: true,
              queueTaskType: true,
              type: true
            },
            orderBy: { priority: 'asc' }
          }
        }
      });

      if (!play) {
        throw new NotFoundError('Could not find Play');
      }
      logger.info({ msg: 'Play found', playId: play.id });

      if (!play.tasks?.length) {
        throw new NotFoundError('Could not find Play tasks');
      }

      const isPlayValid = await this.analyzePlay({ play });
      if (!isPlayValid) {
        throw new UnprocessableContentError('Play analyze failed. Bad template!');
      }
      logger.debug({ msg: 'Play analyzed', isPlayValid });
      return Promise.all(
        targetIds.map(async (id: string) => {
          const result: { playExecutionId: string; entityId: string; success: boolean }[] = [];
          if (targetType === EntityType.contact) {
            const contact: IEntity | null = await tx.contact.findUnique({ where: { id }, include: { entityMapping: true } });
            if (!contact) {
              result.push({ playExecutionId: '', entityId: id, success: false });
              return result;
            }

            if (!contact.entityMapping?.length) {
              result.push({ playExecutionId: '', entityId: id, success: false });
              return result;
            }
          } else if (targetType === EntityType.company) {
            const company: IEntity | null = await tx.company.findUnique({ where: { id }, include: { entityMapping: true } });
            if (!company) {
              result.push({ playExecutionId: '', entityId: id, success: false });
              return result;
            }

            if (!company.entityMapping?.length) {
              result.push({ playExecutionId: '', entityId: id, success: false });
              return result;
            }
          }

          const playExec = await tx.execution.createExecution({
            orgId: this.auth.oid,
            type: ExecutionType.play,
            typeId: play.id,
            userId: this.auth.uid,
            entityType: targetType as EntityType,
            entityId: id,
            scheduledAt: new Date(),
            executionId
          });

          if (!uuidValidate(playExec.id)) {
            result.push({ playExecutionId: '', entityId: id, success: false });
            return result;
          }

          const firstTask = await tx.playTask.findFirst({
            where: { playId: play.id, priority: 1 },
            select: { id: true, type: true, enrichmentId: true }
          });

          if (!firstTask) {
            result.push({ playExecutionId: '', entityId: id, success: false });
            return result;
          }

          const payload = {
            orgId: this.auth.oid,
            executionId: playExec.id,
            playTaskId: firstTask.id,
            channelEmailId,
            entityId
          };

          if (firstTask.type === TaskType.enrichment) {
            payload['enrichmentIds'] = [firstTask.enrichmentId];
            payload['currentStep'] = 0;
          }

          const queueTask = await tx.queueTask.createQueueTask({
            executionId: playExec.id,
            type: play.tasks[0].queueTaskType,
            status: ExecutionStatus.scheduled,
            taskData: payload,
            scheduledAt: new Date()
          });

          await tx.queueTask.update({
            where: { id: queueTask.id },
            data: { status: ExecutionStatus.pending }
          });

          if (this.writeResultsToSocket && this.notificationService) {
            await this.writePlayExecutionToNotificationProvider({ execution: playExec, entityType: targetType, entityId: id });
          }

          result.push({ playExecutionId: playExec.id, entityId: id, success: true });
          return result;
        })
      )
        .then((allResults) => allResults.flat())
        .catch((err: unknown): void => {
          logger.error({ msg: 'Prisma rollback', err });
          throw err;
        });
    });
  }

  async cancelPlayExecution({ executionId }: { executionId: string }): Promise<IResponseStatus> {
    return db.execution
      .getPlayExecution({ executionId })
      .then(async (playExecution) => {
        if (!playExecution) {
          throw new NotFoundError('playExecution not found');
        }
        if (playExecution.execution.status === ExecutionStatus.completed) {
          return { success: true, message: '' };
        }
        return db.execution
          .cancelExecution({ executionId })
          .then(async () => {
            if (this.writeResultsToSocket && this.notificationService) {
              await this.removePlayExecutionToNotificationProvider({ executionId });
            }

            return { success: true, message: '' };
          })
          .catch((err: any) => {
            logger.error({ msg: 'Error cancelling play execution', err });
            return { success: false, message: err.details };
          });
      })
      .catch((err: any) => {
        logger.error({ msg: 'Error cancelling play execution', err });
        return { success: false, message: err.details };
      });
  }

  async getPlayExecution({ executionId }: { executionId: string }) {
    return db.execution.getPlayExecution({ executionId });
  }

  async writePlayExecutionToNotificationProvider({ execution, entityType, entityId }): Promise<void> {
    if (!this.writeResultsToSocket || !this.notificationService) {
      return;
    }

    const task = await db.queueTask.findFirst({
      where: { executionId: execution.id },
      orderBy: { createdAt: 'asc' },
      select: { id: true }
    });
    if (!task) {
      throw new Error('Task not found');
    }

    let entity: { name: string } | { firstName: string; lastName: string } | null;
    if (entityType === EntityType.contact) {
      entity = await db.contact.findUnique({
        where: { id: entityId },
        select: { firstName: true, lastName: true }
      });
    } else {
      entity = await db.company.findUnique({
        where: { id: entityId },
        select: { name: true }
      });
    }

    const data = {
      [execution.id]: {
        id: execution.id,
        status: 'processing',
        time: new Date().toISOString(),
        entity: {
          id: entityId,
          type: entityType as string,
          ...entity
        }
      }
    };
    logger.debug({ msg: 'Firebase data ready to send', data });

    await this.notificationService.writeData(`/playExecutions/users/${this.auth.uid}/executions`, data);
  }

  async removePlayExecutionToNotificationProvider({ executionId }: { executionId: string }): Promise<void> {
    if (!this.writeResultsToSocket || !this.notificationService) {
      return;
    }
    const cancellationPayload = {
      status: 'cancelled',
      cancelledAt: new Date().toISOString()
    };

    await this.notificationService.writeData(`/playExecutions/users/${this.auth.uid}/executions/${executionId}`, cancellationPayload);
  }

  async duplicatePlay(playId: string): Promise<IPlay> {
    const play: ISinglePlay | null = await db.play.getPlayById({ playId, orgId: this.auth.oid });
    if (!play) {
      throw new NotFoundError('Play not found');
    }
    if (!play.templates?.length) {
      throw new NotFoundError('Play template not found');
    }
    if (!play.templates[0].fieldMapping) {
      throw new NotFoundError('Play attachments not found. Possible bad data');
    }

    const newPlay = await this.savePlay({
      id: null,
      name: play.name,
      title: play.templates[0].title as string,
      instructions: play.templates[0].template,
      attachments: play.templates[0].fieldMapping.map((f) => f.schemaField.id),
      status: PlayStatus.published,
      llmProvider: play.templates[0].llmEngine
    });
    if (!newPlay) {
      throw new ServerError('Failed to duplicate play');
    }
    return newPlay;
  }

  async remove(playId: string): Promise<true> {
    return db.play
      .updateMany({
        where: { orgId: this.auth.oid, id: playId },
        data: {
          deletedAt: new Date()
        }
      })
      .then(() => true);
  }

  async getPlaybookUsers(): Promise<IPlaybookUsers[]> {
    return db.play.getPlaybookUsers({ orgId: this.auth.oid });
  }
}
