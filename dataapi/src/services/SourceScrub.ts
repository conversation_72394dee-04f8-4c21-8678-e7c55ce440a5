import config from '../config';
import { logger } from '../logger';
import { ISourceScrubAuthResponse, ISourceScrubSource, ISourceScrubSourcesResponse, ISourceScrubCompaniesResponse, ISourceScrubCompany, SourceScrubFilters, ISourceScrubSourceRequest } from '../types/SourceScrub';

export default class SourceScrubService {
  private static authToken: string | null = null;
  private static tokenExpiration: Date | null = null;

  /**
   * Get a valid authentication token, either from cache or by generating a new one
   * @returns A promise resolving to the authentication token
   */
  private static async getAuthToken(): Promise<string> {
    // Check if token exists and is still valid (with 5 minute buffer before 1-hour expiration)
    const now = new Date();
    if (this.authToken && this.tokenExpiration && this.tokenExpiration > new Date(now.getTime() + 5 * 60 * 1000)) {
      logger.info({ msg: 'Using cached SourceScrub token', token: this.authToken });
      return this.authToken;
    }

    // If no token or token is expired, get a new one
    const url = config.sourceScrub.identityUrl;

    const authHeader = `Basic ${config.sourceScrub.clientKey}`;
    const formData = new URLSearchParams();
    formData.append('grant_type', 'password');
    formData.append('username', config.sourceScrub.username);
    formData.append('password', config.sourceScrub.password);
    formData.append('scope', 'client_api');

    logger.info({ msg: 'Fetching new SourceScrub auth token', url, authHeader, formData: formData.toString() });

    try {
      logger.info({ msg: 'Fetching new SourceScrub auth token' });

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: authHeader
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Authentication failed with status ${response.status}: ${response.statusText}`);
      }

      const data = (await response.json()) as ISourceScrubAuthResponse;

      // Set token and calculate expiration time (subtracting 60 seconds for safety)
      this.authToken = data.access_token;
      logger.info({ msg: 'Using SourceScrub token', token: this.authToken });
      this.tokenExpiration = new Date(Date.now() + (data.expires_in - 60) * 1000);

      logger.info({
        msg: 'Successfully obtained new SourceScrub token',
        expiresAt: this.tokenExpiration
      });

      return this.authToken;
    } catch (error) {
      logger.error({ msg: 'Failed to authenticate with SourceScrub', error });
      throw new Error('Failed to authenticate with SourceScrub');
    }
  }

  /**
   * Search for sources based on provided filters
   * @param request - Object containing search filters
   * @param limit - Optional limit for the number of results per page (default is 100)
   * @returns A promise resolving to an array of sources
   */
  static async searchSources({ request, limit = 100 }: { request: ISourceScrubSourceRequest; limit?: number }): Promise<ISourceScrubSourcesResponse> {
    const token = await this.getAuthToken();
    const url = `${config.sourceScrub.apiUrl}/search/sources`;

    // Start with empty results array
    let allSources: ISourceScrubSource[] = [];
    let offset = 0;
    let totalItems = 0;
    let fetchedItems = 0;
    let total = 0;

    try {
      // Paginate through all results
      do {
        logger.info({
          msg: 'Fetching SourceScrub sources',
          offset,
          limit,
          searchText: request.searchText,
          filters: request.filters
        });

        const requestBody = {
          offset,
          limit,
          searchText: request.searchText,
          filters: request.filters
        };

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`
          },
          body: JSON.stringify(requestBody)
        });

        // Handle potential errors
        if (!response.ok) {
          if (response.status === 429) {
            throw new Error('Monthly API request limit reached');
          } else {
            throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
          }
        }

        const data = (await response.json()) as ISourceScrubSourcesResponse;
        total = data.total;

        // Set total items on first iteration
        if (offset === 0) {
          totalItems = Math.min(data.total, 200);
        }

        // Add fetched items to our collection
        allSources = [...allSources, ...data.items];
        fetchedItems = allSources.length;

        // Update offset for next page
        offset++;
      } while (fetchedItems < totalItems);

      logger.info({
        msg: 'Successfully fetched all SourceScrub sources',
        count: allSources.length
      });

      return {
        total,
        items: allSources
      };
    } catch (error) {
      logger.error({ msg: 'Failed to search for SourceScrub sources', error });
      throw error;
    }
  }

  /**
   * Search for companies based on provided filters
   * @param filters - Object containing search filters
   * @param maxResultCount - Maximum number of companies to return
   * @param limit - Optional limit for the number of results per page (default is 100)
   * @returns A promise resolving to an array of companies
   */
  static async searchCompanies({ filters, maxResultCount, limit = 100 }: { filters: SourceScrubFilters; maxResultCount: number; limit?: number }): Promise<ISourceScrubCompany[]> {
    const token = await this.getAuthToken();
    const url = `${config.sourceScrub.apiUrl}/search/companies`;

    // Start with empty results array
    let allCompanies: ISourceScrubCompany[] = [];
    let offset = 0;
    let totalItems = 0;
    let fetchedItems = 0;

    limit = Math.min(limit, maxResultCount);

    try {
      // Paginate through all results
      do {
        logger.info({
          msg: 'Fetching SourceScrub companies',
          offset,
          limit,
          filters
        });

        const requestBody = {
          offset,
          limit,
          filters
        };

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`
          },
          body: JSON.stringify(requestBody)
        });

        // Handle potential errors
        if (!response.ok) {
          if (response.status === 429) {
            throw new Error('Monthly API request limit reached');
          } else {
            throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
          }
        }

        const data = (await response.json()) as ISourceScrubCompaniesResponse;

        // Set total items on first iteration
        if (offset === 0) {
          totalItems = Math.min(data.total, maxResultCount);
        }

        // Add fetched items to our collection
        allCompanies = [...allCompanies, ...data.items];
        fetchedItems = allCompanies.length;

        // Update offset for next page
        offset++;
      } while (fetchedItems < totalItems);

      logger.info({
        msg: 'Successfully fetched all SourceScrub companies',
        count: allCompanies.length
      });

      return allCompanies;
    } catch (error) {
      logger.error({ msg: 'Failed to search for SourceScrub companies', error });
      throw error;
    }
  }

  /**
   * Get companies associated with multiple sources
   * @param sourceIds - Array of source IDs to get companies for
   * @param limit - Optional limit for the number of results per page (default is 100)
   * @returns A promise resolving to an array of companies
   */
  static async getCompaniesByMultipleSources({ sourceIds, limit = 100 }: { sourceIds: string[]; limit?: number }): Promise<ISourceScrubCompany[]> {
    try {
      logger.info({
        msg: 'Fetching SourceScrub companies for multiple sources',
        sourceIds
      });

      // Create an array of promises, each fetching companies for a source
      const companyPromises = [...new Set(sourceIds)].map((sourceId) => this.getCompaniesBySource({ sourceId, limit }));

      // Wait for all promises to resolve concurrently
      const companiesArrays = await Promise.all(companyPromises);

      // Flatten the array of arrays into a single array
      const allCompanies = companiesArrays.flat();

      logger.info({
        msg: 'Successfully fetched all companies for multiple sources',
        sourceIds,
        count: allCompanies.length
      });

      return allCompanies;
    } catch (error) {
      logger.error({
        msg: 'Failed to fetch companies for multiple sources',
        sourceIds,
        error
      });
      throw error;
    }
  }

  /**
   * Get companies associated with a specific source
   * @param sourceId - The ID of the source to get companies for
   * @param limit - Optional limit for the number of results per page (default is 100)
   * @returns A promise resolving to an array of companies
   */
  static async getCompaniesBySource({ sourceId, limit = 100 }: { sourceId: string; limit?: number }): Promise<ISourceScrubCompany[]> {
    const token = await this.getAuthToken();
    const baseUrl = `${config.sourceScrub.apiUrl}/sources/${sourceId}/companies`;

    // Start with empty results array
    let allCompanies: ISourceScrubCompany[] = [];
    let offset = 0;
    let totalItems = 0;
    let fetchedItems = 0;

    try {
      // Paginate through all results
      do {
        logger.info({
          msg: 'Fetching SourceScrub companies for source',
          sourceId,
          offset,
          limit
        });

        const url = new URL(baseUrl);
        url.searchParams.append('Limit', limit.toString());
        url.searchParams.append('Offset', offset.toString());

        const response = await fetch(url.toString(), {
          method: 'GET',
          headers: {
            Accept: 'application/json',
            Authorization: `Bearer ${token}`
          }
        });

        // Handle potential errors
        if (!response.ok) {
          if (response.status === 429) {
            throw new Error('Monthly API request limit reached');
          } else if (response.status === 404) {
            throw new Error(`Source with ID ${sourceId} not found`);
          } else {
            throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
          }
        }

        const data = (await response.json()) as ISourceScrubCompaniesResponse;

        // Set total items on first iteration
        if (offset === 0) {
          totalItems = Math.min(data.total, 200);
        }

        // Add fetched items to our collection
        allCompanies = [...allCompanies, ...data.items];
        fetchedItems = allCompanies.length;

        // Update offset for next page
        offset++;
      } while (fetchedItems < totalItems);

      logger.info({
        msg: 'Successfully fetched all companies for source',
        sourceId,
        count: allCompanies.length
      });

      return allCompanies;
    } catch (error) {
      logger.error({
        msg: 'Failed to fetch companies for source',
        sourceId,
        error
      });
      throw error;
    }
  }

  /**
   * Search for companies by website URL
   * @param websiteUrl - The website URL to search for
   * @param limit - Optional limit for the number of results (default is 1)
   * @returns A promise resolving to an array of matching companies
   */
  private static async searchCompaniesByWebsite({ websiteUrl, limit = 1 }: { websiteUrl: string; limit?: number }): Promise<ISourceScrubCompany[]> {
    const token = await this.getAuthToken();
    const url = `${config.sourceScrub.apiUrl}/search/companies`;

    try {
      logger.info({
        msg: 'Searching companies by website',
        websiteUrl,
        limit
      });

      const requestBody = {
        searchText: websiteUrl,
        offset: 0,
        limit
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
      });

      // Handle potential errors
      if (!response.ok) {
        if (response.status === 429) {
          throw new Error('Monthly API request limit reached');
        } else {
          throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
        }
      }

      const data = (await response.json()) as ISourceScrubCompaniesResponse;

      logger.info({
        msg: 'Successfully searched companies by website',
        count: data.items.length
      });

      return data.items;
    } catch (error) {
      logger.error({
        msg: 'Failed to search companies by website',
        websiteUrl,
        error
      });
      throw error;
    }
  }

  /**
   * Find similar companies based on multiple company URLs
   * @param domains - Array of website URLs to find similar companies for
   * @param maxResultCount - Maximum number of companies to return
   * @param limit - Optional limit for the number of results per page (default is 100)
   * @returns A promise resolving to an array of similar companies
   */
  static async lookalikeCompanies({ domains, maxResultCount, limit = 100 }: { domains: string[]; maxResultCount: number; limit?: number }): Promise<ISourceScrubCompany[]> {
    try {
      logger.info({
        msg: 'Finding similar companies for multiple domains',
        domains
      });

      // Create an array of promises, each finding similar companies for a website
      const similarCompaniesPromises = domains.map((domain) => this.findSimilarCompanies({ domain, limit }));

      // Wait for all promises to resolve concurrently
      const companiesArrays = await Promise.all(similarCompaniesPromises);

      // Flatten the array of arrays into a single array
      const allSimilarCompanies = companiesArrays.flat();
      const uniqueCompaniesMap = new Map(allSimilarCompanies.map((company) => [company.id, company]));
      const dividedCompanies = this.divideCompaniesByBaseCompanyId({ allSimilarCompanies: [...uniqueCompaniesMap.values()], maxResultCount });

      logger.info({
        msg: 'Successfully fetched similar companies for multiple websites',
        websiteUrls: domains,
        count: dividedCompanies.length
      });

      return dividedCompanies;
    } catch (error) {
      logger.error({
        msg: 'Failed to find similar companies for multiple websites',
        domains,
        error
      });
      throw error;
    }
  }

  static divideCompaniesByBaseCompanyId({ allSimilarCompanies, maxResultCount }: { allSimilarCompanies: ISourceScrubCompany[]; maxResultCount: number }): ISourceScrubCompany[] {
    // Group companies by baseCompanyId
    const groupedCompanies = allSimilarCompanies.reduce(
      (acc, company) => {
        const baseCompanyId = company.baseCompanyId || '';
        if (!acc[baseCompanyId]) {
          acc[baseCompanyId] = [];
        }
        acc[baseCompanyId].push(company);
        return acc;
      },
      {} as Record<string, ISourceScrubCompany[]>
    );

    // Calculate the maximum number of companies to take from each group
    const baseCompanyIds = Object.keys(groupedCompanies);
    const maxPerGroup = Math.floor(maxResultCount / baseCompanyIds.length);

    // Collect companies from each group
    const result: ISourceScrubCompany[] = [];
    for (const baseCompanyId of baseCompanyIds) {
      const companies = groupedCompanies[baseCompanyId];
      result.push(...companies.slice(0, maxPerGroup));
    }

    // If the result is still less than maxResultCount, add remaining companies
    if (result.length < maxResultCount) {
      const remainingCount = maxResultCount - result.length;
      const remainingCompanies = allSimilarCompanies.filter((company) => !result.includes(company));
      result.push(...remainingCompanies.slice(0, remainingCount));
    }

    return result;
  }

  /**
   * Find similar companies based on a given company URL
   * @param domain - The website URL to find similar companies for
   * @param maxCompanies - Maximum number of companies to return
   * @param limit - Optional limit for the number of results per page (default is 100)
   * @returns A promise resolving to an array of similar companies
   */
  static async findSimilarCompanies({ domain, limit = 100 }: { domain: string; limit?: number }): Promise<ISourceScrubCompany[]> {
    // First, search for the company to get its ID
    const companiesFound = await this.searchCompaniesByWebsite({ websiteUrl: domain });

    if (companiesFound.length === 0) {
      logger.warn({
        msg: 'No company found for the given website',
        domain
      });
      return [];
    }

    const baseCompany = companiesFound[0];
    const token = await this.getAuthToken();
    const url = `${config.sourceScrub.apiUrl}/search/companies`;

    // Start with empty results array
    let allSimilarCompanies: ISourceScrubCompany[] = [];
    let offset = 0;
    let totalItems = 0;
    let fetchedItems = 0;

    try {
      // Paginate through all results
      do {
        logger.info({
          msg: 'Fetching similar companies',
          baseCompanyId: baseCompany.id,
          offset,
          limit
        });

        const requestBody = {
          searchText: '',
          offset,
          limit,
          filters: {
            companiesLikeThese: [{ companyId: baseCompany.id }]
          }
        };

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`
          },
          body: JSON.stringify(requestBody)
        });

        // Handle potential errors
        if (!response.ok) {
          if (response.status === 429) {
            throw new Error('Monthly API request limit reached');
          } else {
            throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
          }
        }

        const data = (await response.json()) as ISourceScrubCompaniesResponse;
        data.items.forEach((item) => {
          item.baseCompanyId = baseCompany.id;
        });

        // Set total items on first iteration
        if (offset === 0) {
          totalItems = Math.min(data.total, 100);
        }

        // Add fetched items to our collection
        allSimilarCompanies = [...allSimilarCompanies, ...data.items];
        fetchedItems = allSimilarCompanies.length;

        // Update offset for next page
        offset++;
      } while (fetchedItems < totalItems);

      logger.info({
        msg: 'Successfully fetched similar companies',
        baseCompanyUrl: domain,
        baseCompanyId: baseCompany.id,
        count: allSimilarCompanies.length
      });

      return allSimilarCompanies;
    } catch (error) {
      logger.error({
        msg: 'Failed to find similar companies',
        websiteUrl: domain,
        error
      });
      throw error;
    }
  }
}
