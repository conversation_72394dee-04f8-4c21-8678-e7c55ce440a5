import { CrmType, FieldDataType, IntegrationProvider, IntegrationType, CrmFieldType, DataSourceType, EntityType, ActivityType, ActivityStatus, CrmEntityType } from '@prisma/client';
import { CompanyMandatoryFields, ContactMandatoryFields, FieldKeys } from '../constants';
import { Promise } from 'bluebird';
import dayjs from 'dayjs';
import {
  IFilterItem,
  ICrmFields,
  ICrmBasicEntity,
  ICrmUser,
  IAffinityAuth,
  IAffinityServiceParam,
  ISyncRecordResult,
  ICrmFieldMapping,
  IContactMandatoryFields,
  ICompanyMandatoryFields,
  ICrmListEntity,
  ICrmDefaultFields,
  ICrmListMapping,
  CrmEntity,
  IActivity,
  IActivityParticipant
} from '../types/Crm';
import { ICrmService } from '../lib/crm/ICrmService';
import CrmBaseService from './CrmBaseService';
import { ISyncData, ISyncField } from '../types/Entity';
import { logger } from '../logger';
import { db } from '../database';
import { promises as fs } from 'fs';
import {
  IAffinityList,
  IChatInteraction,
  ICustomField,
  IDropdownOption,
  IEmailInteraction,
  IFieldValueResponse,
  IInteraction,
  IInteractionPerson,
  IInteractionResponse,
  IListEntry,
  IMeetingOrCallInteraction,
  INote,
  InteractionType,
  IOrganization,
  IOrganizationsResponse,
  IPerson,
  IPersonsResponse,
  IRateLimit,
  IRateLimitInfo,
  IWhoAmIResponse
} from '../types/Affinity';
import config from '../config';
import { APIError } from '../lib';

export default class AffinityService extends CrmBaseService implements ICrmService {
  affAuth: IAffinityAuth | null;
  mockApiKey: string;
  dataSourceType: DataSourceType;
  standardPersonFields: ICrmFields[] = [
    { label: 'Person ID', uniqueId: 'id', key: 'id', type: 'id', source: CrmFieldType.standard },
    { label: 'Type', uniqueId: 'type', key: 'type', type: 'double', source: CrmFieldType.standard },
    { label: 'Owner ID', uniqueId: 'owner_id', key: 'owner_id', type: 'reference', source: CrmFieldType.standard },
    { label: 'First Name', uniqueId: 'first_name', key: 'first_name', type: 'string', source: CrmFieldType.standard },
    { label: 'Last Name', uniqueId: 'last_name', key: 'last_name', type: 'string', source: CrmFieldType.standard },
    { label: 'Primary Email', uniqueId: 'primary_email', key: 'email', type: 'string', source: CrmFieldType.standard },
    { label: 'Emails', uniqueId: 'emails', key: 'emails', type: 'array', source: CrmFieldType.standard },
    { label: 'Organization IDs', uniqueId: 'organization_ids', key: 'organization_ids', type: 'array', source: CrmFieldType.standard },
    { label: 'Opportunity IDs', uniqueId: 'opportunity_ids', key: 'opportunity_ids', type: 'array', source: CrmFieldType.standard },
    { label: 'Current Organization IDs', uniqueId: 'current_organization_ids', key: 'current_organization_ids', type: 'array', source: CrmFieldType.standard },
    { label: 'Interaction Dates', uniqueId: 'interaction_dates', key: 'interaction_dates', type: 'object', source: CrmFieldType.standard },
    { label: 'Interactions', uniqueId: 'interactions', key: 'interactions', type: 'object', source: CrmFieldType.standard }
  ];

  standardCompanyFields: ICrmFields[] = [
    { label: 'Organization ID', uniqueId: 'id', key: 'id', type: 'id', source: CrmFieldType.standard },
    { label: 'Organization Name', uniqueId: 'name', key: 'name', type: 'string', source: CrmFieldType.standard },
    { label: 'Owner ID', uniqueId: 'owner_id', key: 'owner_id', type: 'reference', source: CrmFieldType.standard },
    { label: 'Domain', uniqueId: 'domain', key: 'website', type: 'string', source: CrmFieldType.standard },
    { label: 'Domains', uniqueId: 'domains', key: 'domains', type: 'array', source: CrmFieldType.standard },
    { label: 'Person IDs', uniqueId: 'person_ids', key: 'person_ids', type: 'array', source: CrmFieldType.standard },
    { label: 'Opportunity IDs', uniqueId: 'opportunity_ids', key: 'opportunity_ids', type: 'array', source: CrmFieldType.standard },
    { label: 'Interaction Dates', uniqueId: 'interaction_dates', key: 'interaction_dates', type: 'object', source: CrmFieldType.standard },
    { label: 'Interactions', uniqueId: 'interactions', key: 'interactions', type: 'object', source: CrmFieldType.standard }
  ];

  DEFAULT_CONTACT_OPTIONS = [
    {
      label: 'ID',
      uniqueId: 'id',
      key: 'id',
      type: 'id',
      locked: true
    },
    {
      label: 'Organization IDs',
      uniqueId: 'organization_ids',
      key: 'organization_ids',
      type: 'array',
      locked: true
    },
    {
      label: 'Owner ID',
      uniqueId: 'owner_id',
      key: 'owner_id',
      type: 'reference',
      locked: true
    },
    {
      label: 'First Name',
      uniqueId: 'first_name',
      key: 'first_name',
      type: 'string',
      locked: true,
      isMandatory: true
    },
    {
      label: 'Last Name',
      uniqueId: 'last_name',
      key: 'last_name',
      type: 'string',
      locked: true,
      isMandatory: true
    },
    {
      label: 'Email',
      uniqueId: 'primary_email',
      key: 'email',
      type: 'email',
      locked: true,
      isMandatory: true
    }
  ];

  DEFAULT_ACCOUNT_OPTIONS = [
    {
      label: 'ID',
      key: 'id',
      uniqueId: 'id',
      type: 'id',
      locked: true
    },
    {
      label: 'Owner ID',
      key: 'owner_id',
      uniqueId: 'owner_id',
      type: 'reference',
      locked: true
    },
    {
      label: 'Name',
      key: 'name',
      uniqueId: 'name',
      type: 'string',
      locked: true,
      isMandatory: true
    },
    {
      label: 'Domain',
      uniqueId: 'domain',
      key: 'website',
      type: 'string',
      locked: true,
      isMandatory: true
    }
  ];

  needActivitySync = true;

  // #region Constructor
  constructor({ affAuth, auth, companyService, contactService, activityService }: IAffinityServiceParam) {
    super({
      auth,
      companyService,
      contactService,
      activityService,
      crmType: CrmType.affinity
    });
    this.affAuth = affAuth;
    this.crmInstance = this;
    this.dataSourceType = DataSourceType.affinity;
    this.mockApiKey = '**********';
  }

  // #endregion Constructor

  // #region Affinity Implementation of ICrmService Interface methods

  getContactMandatoryFields(contact: ISyncData): IContactMandatoryFields {
    const crmCompanyIds = this.getField<string[]>(contact, FieldKeys.ORG_IDS) ?? [];
    const firstName = this.getField<string>(contact, ContactMandatoryFields.FIRST_NAME);
    const lastName = this.getField<string>(contact, ContactMandatoryFields.LAST_NAME);
    const email = this.getField<string>(contact, ContactMandatoryFields.EMAIL);
    return { crmCompanyIds, firstName, lastName, email };
  }

  getCompanyMandatoryFields(company: ISyncData): ICompanyMandatoryFields {
    const name = this.getField<string>(company, CompanyMandatoryFields.NAME);
    const website = this.getField<string>(company, CompanyMandatoryFields.WEB_URL);
    return { name, website };
  }

  async getCrmLists(): Promise<ICrmListEntity[]> {
    logger.debug({ msg: 'Reading Affinity fields' });

    this.userRoleService.validateAdminAccess();

    if (!this.affAuth) {
      throw new Error('Affinity API key is not set');
    }

    return this.getCrmData<IAffinityList[]>(this.affAuth.accessToken, `${config.affinity.instanceUrl}${config.affinity.endPoints.lists}`, 'IAffinityList', true)
      .then((response: IAffinityList[]) => {
        const crmLists: ICrmListEntity[] = response
          .filter((item) => item.type !== 8) // Skip type 8 (opportunity)
          .map((item) => ({
            id: item.id.toString(),
            name: item.name,
            entityType: item.type === 0 ? EntityType.contact : EntityType.company,
            ownerId: item.owner_id.toString()
          }));

        logger.debug({ msg: 'Affinity lists fetched', crmLists });
        return crmLists;
      })
      .catch((e) => {
        logger.error({
          msg: 'Error fetching Affinity lists',
          e: e.message
        });
        throw e;
      });
  }

  async getSyncRecords(entityType: EntityType, _userSync: boolean): Promise<ISyncRecordResult> {
    try {
      const mappings = await this.getMappings(entityType);
      const relatedMappings = await this.getMappings(this.getRelatedEntityType(entityType));
      const lists = await this.getMappedLists().then((lists) => lists.filter((list) => list.entityType === entityType));

      const mainData: ISyncData[] = [];
      const relatedData: ISyncData[] = [];
      const processedRelatedIds: Set<number> = new Set();

      const organizations: IOrganization[] = [];
      const persons: IPerson[] = [];

      const processEntities = async (entities: (IOrganization | IPerson)[]) => {
        for (const entity of entities) {
          let enrichedEntity = entity;
          if (entity.fetchRelated || entityType === EntityType.contact || this.needActivitySync) {
            enrichedEntity = await this.enrichEntity(entity, entityType);
          }

          const fields = await this.getFieldValues(enrichedEntity, entityType, mappings);
          mainData.push({ id: enrichedEntity.id.toString(), fields });
          logger.debug({ msg: `Mapped fields for ${entityType} with ID: ${enrichedEntity.id}`, fields, entity: enrichedEntity });

          // Add entity to respective organizations or persons array
          if (entityType === EntityType.company) {
            organizations.push(enrichedEntity as IOrganization);
          } else {
            persons.push(enrichedEntity as IPerson);
          }

          // Process related entities and add them to relatedData
          if (enrichedEntity.fetchRelated) {
            const relatedEntities = await this.fetchRelatedEntities(enrichedEntity, entityType, processedRelatedIds);
            for (const relatedEntity of relatedEntities) {
              logger.debug({ msg: `Processing related entity for ${entityType} with ID: ${enrichedEntity.id}`, relatedEntity });
              const relatedFields = await this.getFieldValues(relatedEntity, this.getRelatedEntityType(entityType), relatedMappings);
              relatedData.push({ id: relatedEntity.id.toString(), fields: relatedFields });
              logger.debug({ msg: `Mapped related fields for ${this.getRelatedEntityType(entityType)} with ID: ${relatedEntity.id}`, relatedFields });

              // Add related entities to organizations or persons based on their type
              if (this.getRelatedEntityType(entityType) === EntityType.company) {
                organizations.push(relatedEntity as IOrganization);
              } else {
                persons.push(relatedEntity as IPerson);
              }
            }
          }
        }
        logger.info({ crm: this.crmType, msg: `Processed ${entityType} entities`, count: entities.length });
      };

      // Fetch main entities and process them
      if (lists && lists.length > 0) {
        await this.processPaginatedEntities(() => this.fetchEntitiesFromListWithPagination(entityType, lists), processEntities);
      } else {
        await this.processPaginatedEntities(this.fetchEntitiesWithPagination.bind(this, entityType), processEntities);
      }

      // Fetch activities from Affinity API using both main and related entities
      const activities = await this.fetchActivitiesFromAffinity(organizations, persons);

      logger.info({ crm: this.crmType, msg: `Processed ${entityType} entities`, count: mainData.length, relatedCount: relatedData.length });

      return { mainData, relatedData, activities };
    } catch (error) {
      logger.error({ msg: 'Error occurred during getSyncRecords', error: (error as Error).message });
      throw error;
    }
  }

  async syncDeletion(userSync: boolean): Promise<boolean> {
    if (userSync) {
      return true;
    }

    return this.syncDeletionForEntity(CrmEntity.ACCOUNT, userSync)
      .then((companyResult) => {
        return this.syncDeletionForEntity(CrmEntity.CONTACT, userSync).then((contactResult) => {
          return this.syncDeletionForEntity(CrmEntity.INTERACTION, userSync).then((interactionResult) => {
            return companyResult && contactResult && interactionResult;
          });
        });
      })
      .catch((error) => {
        logger.error({ msg: 'Error occurred during syncDeletion', error: error.message });
        return false;
      });
  }

  async submitApiKey(apiKey: string): Promise<boolean> {
    logger.debug({ msg: 'Reading Affinity user data' });
    this.userRoleService.validateAdminAccess();
    const key = apiKey === this.maskApiKey() ? this.affAuth?.accessToken || '' : apiKey;

    try {
      const data = await this.getWhoAmI(key);
      const expiresAt = new Date();
      // TODO: Set the expiry date based on logic of Affinity API
      expiresAt.setFullYear(expiresAt.getFullYear() + 5);
      const integrationData = {
        provider: IntegrationProvider.affinity,
        type: IntegrationType.crm,
        integrationData: {
          username: `${data.user.firstName} ${data.user.lastName}`,
          name: data.tenant.name,
          organizationId: data.tenant.id,
          accessToken: key,
          createdAt: new Date(),
          rTokenExpiresAt: expiresAt,
          refreshedAt: new Date(),
          instanceUrl: config.affinity.instanceUrl
        },
        tokens: {
          accessToken: key
        },
        expiresAt: expiresAt
      };
      return await this.createIntegration(integrationData);
    } catch (error) {
      logger.error({
        msg: 'Error submitting API key:',
        e: (error as Error).message
      });
      throw error;
    }
  }

  async getCrmFields(entityType: EntityType): Promise<ICrmFields[]> {
    logger.debug({ msg: 'Reading Affinity fields' });
    this.userRoleService.validateAdminAccess();

    const standardFields = entityType === EntityType.contact ? this.standardPersonFields : this.standardCompanyFields;
    const globalFields = await this.getGlobalFields(entityType);
    const listFields = await this.getAllListFields(entityType);

    return [...standardFields, ...globalFields, ...listFields];
  }

  async getEntities(_filter: IFilterItem[], _entityType: EntityType): Promise<ICrmBasicEntity[]> {
    return [];
  }

  async getUsers(email: string): Promise<ICrmUser[]> {
    try {
      if (!this.affAuth) {
        throw new Error('Affinity API key is not set');
      }

      const url = `${config.affinity.instanceUrl}${config.affinity.endPoints.persons}?term=${email}`;
      const response = await this.getCrmData<IPersonsResponse>(this.affAuth.accessToken, url, `IPersonsResponse_${email.replace('@', '_')}`);
      const persons = response?.persons;

      if (!persons || persons.length === 0) {
        return [];
      }

      const lowerCaseEmail = email.toLowerCase();
      const matchingPerson = persons.find((person) => person.primary_email.toLowerCase() === lowerCaseEmail);
      if (!matchingPerson) {
        return [];
      }

      logger.debug({ msg: 'Affinity users fetched', persons });
      return [
        {
          id: matchingPerson.id.toString(),
          email: matchingPerson.primary_email,
          firstName: matchingPerson.first_name,
          lastName: matchingPerson.last_name,
          exists: true
        }
      ];
    } catch (error) {
      logger.error({
        msg: 'Error fetching Affinity users',
        e: (error as Error).message
      });
      throw error;
    }
  }

  async getDefaultFields(mappingType: EntityType): Promise<ICrmDefaultFields[]> {
    this.userRoleService.validateAdminAccess();

    if (mappingType === EntityType.company) {
      return this.DEFAULT_ACCOUNT_OPTIONS;
    }
    return this.DEFAULT_CONTACT_OPTIONS;
  }

  async revoke(): Promise<boolean> {
    return Promise.resolve(true);
  }

  isAuthenticated(): boolean {
    return !this.affAuth ? false : true;
  }

  getApiKey(): string {
    return this.maskApiKey();
  }

  // #endregion Affinity Implementation of ICrmService Interface methods

  // #region Affinity Specific methods

  private async syncDeletionForEntity(crmEntityType: CrmEntity, userSync: boolean): Promise<boolean> {
    try {
      logger.debug({ msg: `Syncing deletions for ${crmEntityType}` });

      // Extract CRM IDs from syncedRecords for the given entity type
      const syncedIds = this.syncedRecords.filter((record) => record.entity === crmEntityType).map((record) => record.entityId);
      logger.debug({ msg: `Synced IDs for ${crmEntityType}`, syncedIds });

      // Get active CRM IDs from the system for the given entity type
      const activeIds = await this.getCrmIds(crmEntityType, userSync);
      if (activeIds && activeIds.length > 0) {
        logger.debug({ msg: `Active IDs for ${crmEntityType}`, activeIds });

        // Determine which records are no longer active
        const deletionIds = activeIds.filter((id) => !syncedIds.includes(id));

        logger.debug({ msg: `Deletion IDs for ${crmEntityType}`, deletionIds });

        // Call deleteRecords if there are records to delete
        if (deletionIds.length > 0) {
          return this.deleteRecords(deletionIds, crmEntityType);
        }
      }
      return true;
    } catch (error) {
      logger.error({ msg: `Error occurred during syncDeletionForEntity for ${crmEntityType}`, error: (error as Error).message });
      return false;
    }
  }

  private async getGlobalFields(entityType: EntityType) {
    if (!this.affAuth) {
      throw new Error('Affinity API key is not set');
    }

    const endpoint = `${config.affinity.instanceUrl}${entityType === EntityType.contact ? config.affinity.endPoints.personFields : config.affinity.endPoints.orgFields}`;
    const mockName = entityType === EntityType.contact ? 'IPersonFields' : 'IOrgFields';
    return this.getCrmData<ICustomField[]>(this.affAuth.accessToken, endpoint, mockName, true)
      .then((response: ICustomField[]) => {
        const uniqueCustomFields = new Set<string>();
        const customFields: ICrmFields[] = response
          .map((field) => ({
            uniqueId: field.id.toString(),
            label: field.name,
            key: field.name,
            type: this.mapValueTypeToFieldDataType(field.value_type),
            source: CrmFieldType.global
          }))
          .filter((field) => {
            if (!uniqueCustomFields.has(field.uniqueId)) {
              uniqueCustomFields.add(field.uniqueId);
              return true;
            }
            return false;
          });

        logger.debug({ msg: 'Affinity fields fetched', customFields });
        return customFields;
      })
      .catch((e) => {
        logger.error({
          msg: 'Error fetching Affinity fields',
          e: e.message
        });
        throw e;
      });
  }

  private async getAllListFields(entityType: EntityType): Promise<ICrmFields[]> {
    if (!this.affAuth) {
      throw new Error('Affinity API key is not set');
    }

    const lists = await this.getMappedLists();
    const filteredLists = lists.filter((list) => list.entityType === entityType);
    const listFields = await Promise.all(filteredLists.map((list) => this.getListFields(list.id)));
    return listFields.flat();
  }

  private async getListFields(listId: string): Promise<ICrmFields[]> {
    logger.debug({ msg: 'Reading Affinity list fields', listId });
    if (!this.affAuth) {
      throw new Error('Affinity API key is not set');
    }

    return this.getCrmData<IAffinityList>(this.affAuth.accessToken, `${config.affinity.instanceUrl}${config.affinity.endPoints.lists}/${listId}`, `IAffinityList_${listId}`)
      .then((resList: IAffinityList) => {
        if (!resList?.fields) {
          return [];
        }
        const customFields: ICrmFields[] = resList.fields.map((field) => ({
          uniqueId: field.id.toString(),
          label: field.name,
          key: field.name,
          type: this.mapValueTypeToFieldDataType(field.value_type),
          source: CrmFieldType.list,
          sourceId: resList.id.toString(),
          sourceName: resList.name
        }));

        logger.debug({ msg: 'Affinity list fields fetched', customFields });
        return customFields;
      })
      .catch((e) => {
        logger.error({
          msg: 'Error fetching Affinity list fields',
          e: e.message,
          listId
        });
        throw e;
      });
  }

  private async getWhoAmI(apiKey: string): Promise<IWhoAmIResponse> {
    return this.getCrmData<IWhoAmIResponse>(apiKey, `${config.affinity.instanceUrl}${config.affinity.endPoints.whoami}`, 'IWhoAmIResponse');
  }

  private async createIntegration(data): Promise<boolean> {
    logger.info({ crm: this.crmType, msg: 'Creating new integration', auth: this.auth });
    const { uid, oid } = this.auth;

    try {
      await db.$transaction(async (tx) => {
        await tx.orgIntegration.deleteMany({
          where: {
            orgId: oid,
            provider: data.provider
          }
        });

        await tx.orgIntegration.updateMany({
          where: {
            orgId: oid,
            type: data.type
          },
          data: {
            active: false
          }
        });

        await tx.orgIntegration.create({
          data: {
            organization: {
              connect: {
                id: oid
              }
            },
            createdBy: {
              connect: {
                id: uid
              }
            },
            authToken: data.tokens,
            data: data.integrationData,
            name: data.integrationData.name,
            provider: data.provider,
            expiresAt: data.expiresAt,
            createdAt: new Date(),
            refreshedAt: new Date(),
            active: true,
            type: data.type
          }
        });
      });
      return true;
    } catch (error) {
      logger.error({
        msg: 'Error creating integration:',
        e: (error as Error).message
      });
      throw error;
    }
  }

  private maskApiKey(): string {
    const apiKey = this.affAuth?.accessToken || '';

    if (apiKey.length <= 4) {
      // If the key is too short to mask, return it as is or handle appropriately.
      return apiKey;
    }

    const firstPart = apiKey.slice(0, 2);
    const lastPart = apiKey.slice(-2);
    const maskedPart = '*'.repeat(apiKey.length - 4);

    return `${firstPart}${maskedPart}${lastPart}`;
  }

  private async fetchCustomFields(entityId: number, entityType: EntityType): Promise<IFieldValueResponse[]> {
    if (!this.affAuth) {
      throw new Error('Affinity API key is not set');
    }
    logger.debug({ msg: `Fetching custom fields for ${entityType} with ID: ${entityId}` });
    const url = `${config.affinity.instanceUrl}${config.affinity.endPoints.fieldValues}?${entityType === EntityType.company ? `organization_id=${entityId}` : `person_id=${entityId}`}`;
    return this.getCrmData<IFieldValueResponse[]>(this.affAuth.accessToken, url, `IFieldValueResponse_${entityType}`, true);
  }

  private async fetchListFields(listEntryId: number): Promise<IFieldValueResponse[]> {
    if (!this.affAuth) {
      throw new Error('Affinity API key is not set');
    }
    try {
      logger.debug({ msg: `Fetching list fields for list entry with ID: ${listEntryId}` });
      const url = `${config.affinity.instanceUrl}${config.affinity.endPoints.fieldValues}?list_entry_id=${listEntryId}`;
      return await this.getCrmData<IFieldValueResponse[]>(this.affAuth.accessToken, url, `IFieldValueResponse_ListEntry_${listEntryId}`, true);
    } catch (error) {
      logger.error({ msg: `Error occurred during fetchListFields for list entry with ID: ${listEntryId}`, error: (error as Error).message });
      throw error;
    }
  }

  private isDropdownOption(value: any): value is IDropdownOption {
    return value !== null && typeof value === 'object' && 'id' in value && typeof value.id === 'number' && 'text' in value && typeof value.text === 'string' && 'rank' in value && typeof value.rank === 'number' && 'color' in value && typeof value.color === 'number';
  }

  private async mapFields(data: any, customFieldsData: IFieldValueResponse[] | null, mappings: ICrmFieldMapping[]): Promise<ISyncField[]> {
    try {
      return mappings
        .map((mapping) => {
          let value: any = null;

          if (mapping.source === CrmFieldType.standard) {
            // Handle standard fields from the main data object
            value = Object.prototype.hasOwnProperty.call(data, mapping.uniqueId) ? data[mapping.uniqueId] : null;
          } else if (customFieldsData && customFieldsData.length > 0) {
            // Handle custom fields from the custom fields data
            logger.debug({ msg: `Mapping custom field for ${mapping.uniqueId}`, mapping, customFieldsData });
            const customField = customFieldsData.find((field) => field.field_id.toString() === mapping.uniqueId);
            if (customField) {
              if (mapping.type === FieldDataType.picklist && this.isDropdownOption(customField.value)) {
                value = customField.value.text;
              } else {
                value = customField.value;
              }
            }
          }

          return {
            crmFieldId: mapping.uniqueId,
            fieldKey: mapping.key,
            value: value !== undefined ? value : null,
            label: mapping.label || '',
            schemaFieldId: mapping.schemaFieldId,
            locked: mapping.locked,
            type: mapping.type,
            isMandatory: mapping.isMandatory
          };
        })
        .filter((field) => field.value !== null);
    } catch (error) {
      logger.error({ msg: 'Error occurred during mapFields', error: (error as Error).message });
      throw error;
    }
  }

  private async getCrmData<T>(apiKey: string, url: string, fileName: string, isArray: boolean = false): Promise<T> {
    try {
      if (apiKey === this.mockApiKey) {
        return await this.fetchMockData<T>(fileName, isArray);
      } else {
        return await this.retryFetchWithRateLimit<T>(apiKey, url);
      }
    } catch (error) {
      if (!(error instanceof APIError)) {
        logger.error({
          msg: 'Error fetching data from Affinity:',
          e: (error as Error).message
        });
      }
      throw error;
    }
  }

  private async retryFetchWithRateLimit<T>(apiKey: string, url: string, retries: number = 3): Promise<T> {
    let attempt = 0;

    while (attempt < retries) {
      try {
        // Make the actual request
        const data = await this.fetchCrmDataFromApi<T>(apiKey, url);
        return data;
      } catch (error) {
        if (error instanceof APIError && (error as any)?.code === 429) {
          attempt++;
          logger.warn({ msg: `Rate limit exceeded. Attempt ${attempt} of ${retries}` });

          if (attempt >= retries) {
            throw new Error('Rate limit exceeded and retries exhausted');
          }

          // Call getRateLimitInfo to determine how long to wait
          const rateLimitInfo = await this.getRateLimitInfo(apiKey);
          const waitTime = (rateLimitInfo.apiKeyPerMinute.reset + 1) * 1000; // Convert seconds to milliseconds
          logger.warn({ msg: `Waiting for ${waitTime}ms before retrying...` });

          await this.delay(waitTime);
        } else {
          logger.error({ msg: 'Error occurred during retryFetchWithRateLimit', error: (error as Error).message });
          // If the error is not a rate limit error or retries are exhausted, rethrow it
          throw error;
        }
      }
    }

    logger.error({ msg: 'Failed to fetch CRM data after retries' });
    throw new Error('Failed to fetch CRM data after retries');
  }

  private async getRateLimitInfo(apiKey: string): Promise<IRateLimitInfo> {
    const url = `${config.affinity.instanceUrl}${config.affinity.endPoints.rateLimit}`;

    try {
      const data = await this.fetchCrmDataFromApi<{ rate: { api_key_per_minute: IRateLimit; org_monthly: IRateLimit } }>(apiKey, url);

      return {
        apiKeyPerMinute: data.rate.api_key_per_minute,
        orgMonthly: data.rate.org_monthly
      };
    } catch (error) {
      logger.error({ msg: 'Error fetching rate limit information', e: (error as Error).message });
      throw error;
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Sanitizes file names to be compatible with all operating systems
   * Replaces characters that are not allowed in Windows file names
   */
  private sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/:/g, '_') // Replace colons with underscores
      .replace(/[<>"|?*]/g, '_') // Replace other invalid Windows characters
      .replace(/\//g, '_') // Replace forward slashes
      .replace(/\\/g, '_'); // Replace backslashes
  }

  private async fetchMockData<T>(fileName: string, isArray: boolean): Promise<T> {
    const sanitizedFileName = this.sanitizeFileName(fileName);
    const file = `src/mock/${sanitizedFileName}.json`;

    try {
      const mockData = await fs.readFile(file, 'utf8');
      const data = JSON.parse(mockData) as T;
      logger.debug({ msg: 'Mock data fetched', data });
      return data;
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        // File not found
        logger.debug({
          msg: `Mock file not found: ${sanitizedFileName}. Returning empty data.`
        });
        if (isArray) {
          return [];
        } else {
          return null;
        }
      } else {
        logger.error({
          msg: `Failed to read mock data from file: ${sanitizedFileName}`,
          e: error.message
        });
        throw new APIError('Mock data error', {
          name: 'MockDataError',
          details: error.message,
          code: 500
        });
      }
    }
  }

  private async fetchCrmDataFromApi<T>(apiKey: string, url: string): Promise<T> {
    const encodedApiKey = Buffer.from(`:${apiKey}`).toString('base64');
    const headers = new Headers();
    headers.set('Content-Type', 'application/json');
    headers.set('Authorization', `Basic ${encodedApiKey}`);

    const response = await fetch(url, { headers });

    if (!response.ok) {
      this.handleApiError(response);
    }

    const data: T = await response.json();
    logger.debug({ msg: 'Data fetched from Affinity', data });
    return data;
  }

  private handleApiError(response: Response): never {
    if (response.status === 401) {
      const errorMessage = 'Unauthorized! Please check your API key.';
      logger.error({
        msg: 'HTTP error! Status: 401 Unauthorized',
        e: errorMessage
      });
      throw new APIError('Unauthorized request', {
        name: 'AuthTokenError',
        details: errorMessage,
        code: 401
      });
    } else {
      const errorMessage = `HTTP error! Status: ${response.status}`;
      logger.error({
        msg: errorMessage,
        response: response,
        e: response.statusText
      });
      throw new APIError('HTTP error', {
        name: 'HTTPError',
        details: errorMessage,
        code: response.status
      });
    }
  }

  private mapValueTypeToFieldDataType(valueType: number): FieldDataType {
    switch (valueType) {
      case 0:
        return FieldDataType.reference; // Person
      case 1:
        return FieldDataType.reference; // Organization
      case 2:
        return FieldDataType.picklist; // Dropdown
      case 3:
        return FieldDataType.double; // Number
      case 4:
        return FieldDataType.date; // Date
      case 5:
        return FieldDataType.address; // Location
      case 6:
        return FieldDataType.textarea; // Text
      case 7:
        return FieldDataType.picklist; // Ranked Dropdown
      default:
        return FieldDataType.string;
    }
  }

  private getRelatedEntityType(entityType: EntityType): EntityType {
    return entityType === EntityType.company ? EntityType.contact : EntityType.company;
  }

  private async enrichEntity(entity: IOrganization | IPerson, entityType: EntityType): Promise<IOrganization | IPerson> {
    try {
      if (entityType === EntityType.company) {
        if (!('person_ids' in entity) || !entity.person_ids || !entity.interaction_dates) {
          const enrichedOrganization = await this.fetchOrganizationsById(entity.id);
          if (enrichedOrganization) {
            entity = enrichedOrganization;
          }
        }
      } else if (entityType === EntityType.contact) {
        if (!('organization_ids' in entity) || !entity.organization_ids || !entity.interaction_dates) {
          const enrichedPerson = await this.fetchPersonsById(entity.id);
          if (enrichedPerson) {
            entity = enrichedPerson;
          }
        }
      }
      return entity;
    } catch (error) {
      logger.error({ msg: `Error occurred during enrichEntity :${entityType} ${entity.id}`, error: (error as Error).message });
      throw error;
    }
  }

  private async fetchRelatedEntities(entity: IOrganization | IPerson, entityType: EntityType, processedRelatedIds: Set<number>): Promise<(IOrganization | IPerson)[]> {
    try {
      const relatedEntities: (IOrganization | IPerson)[] = [];
      if (entityType === EntityType.company && 'person_ids' in entity) {
        for (const personId of entity.person_ids!) {
          if (!processedRelatedIds.has(personId)) {
            const person = await this.fetchPersonsById(personId);
            if (person) {
              processedRelatedIds.add(personId);
              relatedEntities.push(person);
            }
          }
        }
      } else if (entityType === EntityType.contact && 'organization_ids' in entity) {
        for (const orgId of entity.organization_ids!) {
          if (!processedRelatedIds.has(orgId)) {
            const organization = await this.fetchOrganizationsById(orgId);
            if (organization) {
              processedRelatedIds.add(orgId);
              relatedEntities.push(organization);
            }
          }
        }
      }
      return relatedEntities;
    } catch (error) {
      logger.error({ msg: 'Error occurred during fetchRelatedEntities', error: (error as Error).message });
      throw error;
    }
  }

  private async fetchPersonsById(personId: number): Promise<IPerson> {
    try {
      if (!this.affAuth) {
        throw new Error('Affinity API key is not set');
      }

      const url = `${config.affinity.instanceUrl}${config.affinity.endPoints.persons}/${personId}?with_interaction_dates=true`;
      const person = await this.getCrmData<IPerson>(this.affAuth.accessToken, url, `IPerson_${personId}`);
      return person;
    } catch (error) {
      logger.error({ msg: 'Error occurred during fetchPersonsById', error: (error as Error).message });
      throw error;
    }
  }

  private async fetchOrganizationsById(organizationId: number): Promise<IOrganization> {
    try {
      if (!this.affAuth) {
        throw new Error('Affinity API key is not set');
      }

      const url = `${config.affinity.instanceUrl}${config.affinity.endPoints.organizations}/${organizationId}?with_interaction_dates=true`;
      const organization = await this.getCrmData<IOrganization>(this.affAuth.accessToken, url, `IOrganization_${organizationId}`);
      return organization;
    } catch (error) {
      logger.error({ msg: 'Error occurred during fetchOrganizationsById', error: (error as Error).message });
      throw error;
    }
  }

  private async getFieldValues(data: IOrganization | IPerson, entityType: EntityType, mappings: ICrmFieldMapping[]): Promise<ISyncField[]> {
    try {
      const fieldData: IFieldValueResponse[] = [];
      const hasCustomFields = mappings.some((mapping) => mapping.source === CrmFieldType.global);
      const hasListFields = mappings.some((mapping) => mapping.source === CrmFieldType.list);
      if (hasCustomFields) {
        const customFieldsData = await this.fetchCustomFields(data.id, entityType);
        logger.debug({ msg: `Custom fields data for ${entityType} with ID: ${data.id}`, customFieldsData });
        fieldData.push(...customFieldsData);
      }
      if (hasListFields) {
        const listFieldsData = await this.fetchListFieldValues(data);
        logger.debug({ msg: `List fields data for ${entityType} with ID: ${data.id}`, listFieldsData });
        if (listFieldsData && listFieldsData.length > 0) {
          fieldData.push(...listFieldsData);
        }
      }
      logger.debug({ msg: `Fields data for ${entityType} with ID: ${data.id}`, fieldData, data });
      return this.mapFields(data, fieldData, mappings);
    } catch (error) {
      logger.error({ msg: 'Error occurred during getFieldValues', error: (error as Error).message });
      throw error;
    }
  }

  private async fetchListFieldValues(entity: IOrganization | IPerson): Promise<IFieldValueResponse[]> {
    try {
      logger.debug({ msg: `Fetching list fields for entity`, entity });
      const listEntryIds = entity.listEntryIds;
      const listFieldsData: IFieldValueResponse[] = [];
      if (listEntryIds && listEntryIds.length > 0) {
        for (const listEntryId of listEntryIds) {
          const listFields = await this.fetchListFields(listEntryId);
          listFieldsData.push(...listFields);
        }
      }
      return listFieldsData;
    } catch (error) {
      logger.error({ msg: 'Error occurred during fetchListFieldValues', error: (error as Error).message });
      throw error;
    }
  }

  private async processPaginatedEntities(fetchPageFunction: (pageToken?: string) => Promise<{ entities: (IOrganization | IPerson)[]; nextPageToken?: string }>, processEntities: (entities: (IOrganization | IPerson)[]) => Promise<void>) {
    try {
      let nextPageToken: string | undefined = undefined;
      do {
        const { entities, nextPageToken: newPageToken } = await fetchPageFunction(nextPageToken);
        await processEntities(entities);
        logger.info({ crm: this.crmType, msg: 'Processed entities', count: entities.length });
        nextPageToken = newPageToken;
      } while (nextPageToken);
    } catch (error) {
      logger.error({ msg: 'Error occurred during processPaginatedEntities', error: (error as Error).message });
      throw error;
    }
  }

  private async fetchEntitiesWithPagination(entityType: EntityType, pageToken?: string): Promise<{ entities: (IOrganization | IPerson)[]; nextPageToken?: string }> {
    try {
      if (entityType === EntityType.company) {
        return this.fetchAllOrganizationsWithPagination(pageToken);
      } else if (entityType === EntityType.contact) {
        return this.fetchAllPersonsWithPagination(pageToken);
      }
      return { entities: [], nextPageToken: undefined };
    } catch (error) {
      logger.error({ msg: 'Error occurred during fetchEntitiesWithPagination', error: (error as Error).message });
      throw error;
    }
  }

  private async fetchAllOrganizationsWithPagination(pageToken?: string): Promise<{ entities: IOrganization[]; nextPageToken?: string }> {
    try {
      if (!this.affAuth) {
        throw new Error('Affinity API key is not set');
      }
      const url = `${config.affinity.instanceUrl}${config.affinity.endPoints.organizations}${pageToken ? `?page_token=${pageToken}` : ''}`;
      const response = await this.getCrmData<IOrganizationsResponse>(this.affAuth.accessToken, url, `IOrganizationsResponse_${pageToken ? `${pageToken}` : 'NIL'}`);
      return { entities: response.organizations, nextPageToken: response.next_page_token };
    } catch (error) {
      logger.error({ msg: 'Error occurred during fetchAllOrganizationsWithPagination', error: (error as Error).message });
      throw error;
    }
  }

  private async fetchAllPersonsWithPagination(pageToken?: string): Promise<{ entities: IPerson[]; nextPageToken?: string }> {
    try {
      if (!this.affAuth) {
        throw new Error('Affinity API key is not set');
      }
      const url = `${config.affinity.instanceUrl}${config.affinity.endPoints.persons}${pageToken ? `?page_token=${pageToken}` : ''}`;
      const response = await this.getCrmData<IPersonsResponse>(this.affAuth.accessToken, url, `IPersonsResponse_${pageToken ? `${pageToken}` : 'NIL'}`);
      return { entities: response.persons, nextPageToken: response.next_page_token };
    } catch (error) {
      logger.error({ msg: 'Error occurred during fetchAllPersonsWithPagination', error: (error as Error).message });
      throw error;
    }
  }

  private async fetchEntitiesFromListWithPagination(entityType: EntityType, lists: ICrmListMapping[]): Promise<{ entities: (IOrganization | IPerson)[]; nextPageToken?: string }> {
    try {
      const entities: (IOrganization | IPerson)[] = [];

      for (const list of lists) {
        let nextPageToken: string | undefined = undefined;
        do {
          const { entries, nextPageToken: newPageToken } = await this.fetchAllListEntriesWithPagination(list.id, nextPageToken);

          for (const entry of entries) {
            const entity = entry.entity as IOrganization | IPerson;
            const existingEntity = entities.find((e) => e.id === entity.id);
            if (existingEntity) {
              if (list.fetchRelated && !existingEntity.fetchRelated) {
                existingEntity.fetchRelated = true;
              }
              existingEntity.listEntryIds = [...(existingEntity.listEntryIds || []), entry.id];
              existingEntity.listIds = [...(existingEntity.listIds || []), list.id];
            } else {
              entities.push({ ...entity, listEntryIds: [entry.id], listIds: [list.id], fetchRelated: list.fetchRelated });
            }
          }
          logger.info({ crm: this.crmType, msg: `Processed ${entityType} entities from list ${list.id}`, count: entries.length });

          nextPageToken = newPageToken;
        } while (nextPageToken);
      }

      return { entities, nextPageToken: undefined }; // Pagination logic is internal, so we don't need to return nextPageToken
    } catch (error) {
      logger.error({ msg: 'Error occurred during fetchEntitiesFromListWithPagination', error: (error as Error).message });
      throw error;
    }
  }

  private async fetchAllListEntriesWithPagination(listId: string, pageToken?: string): Promise<{ entries: IListEntry[]; nextPageToken?: string }> {
    if (!this.affAuth) {
      throw new Error('Affinity API key is not set');
    }
    try {
      const url = `${config.affinity.instanceUrl}${config.affinity.endPoints.lists}/${listId}/list-entries?page_size=200${pageToken ? `&page_token=${pageToken}` : ''}`;
      const response = await this.getCrmData<{ list_entries: IListEntry[]; next_page_token?: string }>(this.affAuth.accessToken, url, `IListEntry_${listId}_${pageToken ? `${pageToken}` : 'NIL'}`);
      return { entries: response?.list_entries ?? [], nextPageToken: response?.next_page_token };
    } catch (error) {
      logger.error({ msg: 'Error occurred during fetchAllListEntriesWithPagination', error: (error as Error).message });
      throw error;
    }
  }

  private async fetchActivitiesFromAffinity(organizations: IOrganization[], persons: IPerson[]): Promise<IActivity[]> {
    const activities: IActivity[] = [];

    const appendNotesToInteraction = async (interaction: IInteraction) => {
      if (interaction.notes && interaction.notes.length > 0) {
        const notes: INote[] = [];

        for (const noteId of interaction.notes) {
          const note = await this.fetchNoteContent(noteId);
          if (note) {
            notes.push(note);
          }
        }

        // Attach the fetched notes to the interaction
        interaction.noteObjects = notes; // store the full notes object
      }
    };

    const fetchInteractionData = async (entityId: number, entityType: EntityType, interactionType: InteractionType, startDate: string, endDate: string): Promise<IInteraction[]> => {
      const allInteractions: IInteraction[] = [];
      let pageToken: string | undefined = undefined;

      // Helper to fetch a single year of interactions at a time
      const fetchSingleYear = async (start: string, end: string) => {
        if (!this.affAuth) {
          throw new Error('Affinity API key is not set');
        }
        let safeQuit = 1000; // Safety net to prevent infinite loop
        do {
          const url = `${config.affinity.instanceUrl}${config.affinity.endPoints.interactions}?${entityType === EntityType.company ? 'organization_id' : 'person_id'}=${entityId}&type=${interactionType}&start_time=${start}&end_time=${end}`;
          logger.debug({ msg: `Fetching interaction data for ${entityType} with ID: ${entityId} from ${start} to ${end}`, url });
          const response: IInteractionResponse = await this.getCrmData<IInteractionResponse>(this.affAuth.accessToken, url, `IInteractions_${entityType === EntityType.company ? 'organization_id' : 'person_id'}_${entityId}_type_${interactionType}_start_time_${start}_end_time_${end}`, true);

          if (response) {
            switch (interactionType) {
              case InteractionType.Email:
                if (response.emails) {
                  logger.debug({ msg: `Fetched ${response.emails.length} email interactions for ${entityType} with ID: ${entityId}` });
                  allInteractions.push(...response.emails);
                }
                break;
              case InteractionType.Call:
              case InteractionType.Meeting:
                if (response.events) {
                  allInteractions.push(...response.events);
                }
                break;
              case InteractionType.ChatMessage:
                if (response.chat_messages) {
                  allInteractions.push(...response.chat_messages);
                }
                break;
            }
          }

          // Check for pagination token in the response
          pageToken = response?.next_page_token || undefined;
        } while (pageToken && --safeQuit);
      };

      try {
        let currentStartDate = dayjs(startDate);
        const currentEndDate = dayjs(endDate);

        // Loop through the date range in one-year chunks
        while (currentEndDate.diff(currentStartDate, 'days') > 365) {
          const oneYearLater = currentStartDate.clone().add(1, 'year');
          const chunkEndDate = oneYearLater < currentEndDate ? oneYearLater : currentEndDate;

          // Fetch data for this one-year chunk
          await fetchSingleYear(currentStartDate.toISOString(), chunkEndDate.toISOString());

          // Move to the next chunk
          currentStartDate = chunkEndDate;
        }

        // Fetch data for the final chunk (less than one year)
        await fetchSingleYear(currentStartDate.toISOString(), currentEndDate.toISOString());
      } catch (error) {
        logger.error({ msg: `Error fetching interaction data for ${entityType} with ID: ${entityId}`, error });
        throw error;
      }

      if (allInteractions) {
        for (const interaction of allInteractions) {
          await appendNotesToInteraction(interaction);
        }
      }

      return allInteractions;
    };

    const processEntityInteractions = async (entity: IOrganization | IPerson, entityType: EntityType) => {
      const interactionDates = entity.interaction_dates;

      // Email interactions
      if (interactionDates?.last_email_date && interactionDates?.first_email_date) {
        const emailInteractions = await fetchInteractionData(entity.id, entityType, InteractionType.Email, interactionDates.first_email_date, interactionDates.last_email_date);

        activities.push(...this.transformToActivities(emailInteractions, ActivityType.email, entity, entityType));
      }

      // Chat message interactions
      if (interactionDates?.last_chat_message_date && interactionDates?.first_event_date) {
        const chatInteractions = await fetchInteractionData(entity.id, entityType, InteractionType.ChatMessage, new Date(new Date(interactionDates.last_chat_message_date).setFullYear(new Date().getFullYear() - 1)).toISOString(), interactionDates.last_chat_message_date);
        activities.push(...this.transformToActivities(chatInteractions, ActivityType.chat, entity, entityType));
      }

      // Meeting and Call interactions (refactored common logic)
      const processMeetingsAndCalls = async (interactionType: InteractionType, activityType: ActivityType, first_event_date: string, last_event_date: string) => {
        const eventInteractions = await fetchInteractionData(entity.id, entityType, interactionType, first_event_date, last_event_date);
        activities.push(...this.transformToActivities(eventInteractions, activityType, entity, entityType));
      };

      if (interactionDates?.last_event_date && interactionDates?.first_event_date) {
        await processMeetingsAndCalls(InteractionType.Meeting, ActivityType.f2f, interactionDates.first_event_date, interactionDates.last_event_date);
        await processMeetingsAndCalls(InteractionType.Call, ActivityType.call, interactionDates.first_event_date, interactionDates.last_event_date);
      }
    };

    // Process interactions for organizations
    for (const organization of organizations) {
      if (organization.interaction_dates?.last_interaction_date) {
        await processEntityInteractions(organization, EntityType.company);
      }
    }

    // Process interactions for persons
    for (const person of persons) {
      // Only process interactions for persons if they have a last interaction date and are an internal contact person
      if (person.interaction_dates?.last_interaction_date && person.type === 1) {
        await processEntityInteractions(person, EntityType.contact);
      }
    }

    logger.info({ crm: this.crmType, msg: 'Fetched activities', count: activities.length });

    return activities;
  }

  private transformToActivities(interactions: IInteraction[], activityType: ActivityType, entity: IOrganization | IPerson, entityType: EntityType): IActivity[] {
    if (!Array.isArray(interactions)) {
      logger.error({ msg: 'Invalid interactions data passed to transformToActivities', interactions });
      return [];
    }

    return interactions.map((interaction) => {
      const emailInteractions = interaction as IEmailInteraction;
      // Type check for Email interactions
      if (activityType === ActivityType.email) {
        return {
          id: emailInteractions.id.toString(),
          entityType: CrmEntityType.interaction, // or contact depending on your logic
          type: ActivityType.email,
          status: ActivityStatus.unknown, // Assuming completed; adjust as necessary
          title: emailInteractions.subject || null,
          content: this.buildEmailContent(emailInteractions),
          dueDate: new Date(emailInteractions.date),
          participant: this.transformParticipants([...emailInteractions.to, emailInteractions.from, ...(emailInteractions.cc || [])], entity, entityType),
          startDateTime: new Date(emailInteractions.date),
          endDateTime: null,
          isAllDayEvent: false,
          crmActivity: interaction
        };
      }
      // Type check for Chat message interactions
      else if (activityType === ActivityType.chat) {
        const chatInteractions = interaction as IChatInteraction;
        return {
          id: chatInteractions.id.toString(),
          entityType: CrmEntityType.interaction, // or company depending on your logic
          type: ActivityType.chat,
          status: ActivityStatus.unknown,
          title: 'Chat Message',
          content: this.buildChatContent(interaction as IChatInteraction),
          dueDate: new Date(chatInteractions.date),
          participant: this.transformParticipants(chatInteractions.persons, entity, entityType),
          startDateTime: new Date(chatInteractions.date),
          endDateTime: null,
          isAllDayEvent: false,
          crmActivity: interaction
        };
      }
      // Type check for Meeting or Call interactions
      else {
        const meetingInteractions = interaction as IMeetingOrCallInteraction;
        return {
          id: meetingInteractions.id.toString(),
          entityType: CrmEntityType.interaction, // or contact depending on your logic
          type: activityType,
          status: ActivityStatus.unknown,
          title: meetingInteractions.title || 'Meeting/Call',
          content: this.buildEventContent(interaction as IMeetingOrCallInteraction),
          dueDate: new Date(interaction.date),
          participant: this.transformParticipants(interaction.persons, entity, entityType),
          startDateTime: new Date(meetingInteractions.start_time),
          endDateTime: meetingInteractions.end_time ? new Date(meetingInteractions.end_time) : null,
          isAllDayEvent: false,
          crmActivity: interaction
        };
      }
    });
  }

  private buildEmailContent(emailInteraction: IEmailInteraction): string {
    const from = emailInteraction.from ? `${emailInteraction.from.first_name} ${emailInteraction.from.last_name} <${emailInteraction.from.primary_email}>` : 'Unknown Sender';
    const to = emailInteraction.to?.map((person) => `${person.first_name} ${person.last_name} <${person.primary_email}>`).join(', ') || 'Unknown Recipients';
    const cc = emailInteraction.cc?.map((person) => `${person.first_name} ${person.last_name} <${person.primary_email}>`).join(', ') || 'No CC';

    return `From: ${from}\nTo: ${to}\nCC: ${cc}`;
  }

  private buildEventContent(eventInteraction: IMeetingOrCallInteraction): Promise<string> {
    const title = eventInteraction.title || 'No Title';
    const startTime = eventInteraction.start_time ? new Date(eventInteraction.start_time).toLocaleString() : 'Unknown Start Time';
    const endTime = eventInteraction.end_time ? new Date(eventInteraction.end_time).toLocaleString() : 'Unknown End Time';
    const attendees = eventInteraction.persons?.map((person) => `${person.first_name} ${person.last_name} <${person.primary_email}>`).join(', ') || 'No Attendees';

    // Fetch and append note content dynamically
    let notesContent = '';
    if (eventInteraction.noteObjects) {
      for (const note of eventInteraction.noteObjects) {
        if (note) {
          notesContent += `Note: ${note.content}\n`;
        }
      }
    }

    return `Event: ${title}\nStart: ${startTime}\nEnd: ${endTime}\nAttendees: ${attendees}\n${notesContent}`;
  }

  private buildChatContent(chatInteraction: IChatInteraction): Promise<string> {
    const direction = chatInteraction.direction === 0 ? 'Outgoing' : 'Incoming';
    const participants = chatInteraction.persons?.map((person) => `${person.first_name} ${person.last_name} <${person.primary_email}>`).join(', ') || 'No Participants';

    // Fetch and append note content dynamically
    let notesContent = '';
    if (chatInteraction.noteObjects) {
      for (const note of chatInteraction.noteObjects) {
        if (note) {
          notesContent += `Note: ${note.content}\n`;
        }
      }
    }

    return `Chat: ${direction}\nParticipants: ${participants}\n${notesContent}`;
  }

  private transformParticipants(persons: IInteractionPerson[], entity: IOrganization | IPerson, entityType: EntityType): IActivityParticipant[] {
    const participants: IActivityParticipant[] = [];
    if (entityType === EntityType.company) {
      participants.push({
        companyId: entity.id.toString()
      });
    } else {
      participants.push({
        contactId: entity.id.toString()
      });
    }

    persons?.map((person) => {
      // skip the entity is it is already added
      if (participants.some((participant) => participant.contactId === person.id.toString())) {
        return;
      }
      participants.push({
        contactId: person.id.toString()
      });
    });

    return participants;
  }

  private async fetchNoteContent(noteId: number): Promise<INote | null> {
    if (!this.affAuth) {
      throw new Error('Affinity API key is not set');
    }

    try {
      const url = `${config.affinity.instanceUrl}/notes/${noteId}`;
      logger.debug({ msg: `Fetching note content for note ID: ${noteId}`, url });
      const response = await this.getCrmData<INote>(this.affAuth.accessToken, url, `INote_${noteId}`, false);

      return response || null; // Return the entire INote object
    } catch (error) {
      logger.error({ msg: `Error fetching note content for note ID: ${noteId}`, error: (error as Error).message });
      return null;
    }
  }

  // #endregion Affinity Specific methods
}
