import { db } from '../database';
import { logger } from '../logger';
import { IAuth } from '../types/Common';

enum IntegrationOwnerType {
  User = 'user',
  Organization = 'organization'
}

interface IIntegration {
  id: string;
  type?: string;
  ownerType: IntegrationOwnerType;
  provider: string;
  data: any;
}

export default class EnrichmentService {
  auth: IAuth;

  constructor({ auth }: { auth: IAuth }) {
    this.auth = auth;

    if (!auth.oid) {
      logger.error({ error: 'Invalid organization Id' });
      throw new Error('Invalid service call! Check your permissions!');
    }
  }

  async getIntegrations(): Promise<IIntegration[]> {
    const userIntegrations = await this.getUserIntegrations();
    const organizationIntegrations = await this.getOrganizationIntegrations();

    return [...userIntegrations, ...organizationIntegrations];
  }

  private async getUserIntegrations(): Promise<IIntegration[]> {
    return db.userIntegration
      .findMany({
        where: { userId: this.auth.uid },
        select: {
          id: true,
          provider: true,
          name: true,
          createdAt: true,
          expiresAt: true,
          refreshedAt: true,
          metadata: true
        }
      })
      .then((integrations) => {
        if (!integrations) {
          return [];
        }
        return integrations.map((integration) => ({
          id: integration.id,
          ownerType: IntegrationOwnerType.User,
          provider: integration.provider,
          data: integration
        }));
      });
  }

  private async getOrganizationIntegrations(): Promise<IIntegration[]> {
    return db.orgIntegration
      .findMany({
        where: { orgId: this.auth.oid },
        select: {
          id: true,
          provider: true,
          type: true,
          active: true,
          name: true,
          createdAt: true,
          expiresAt: true,
          refreshedAt: true,
          data: true,
          createdById: true
        }
      })
      .then((integrations) => {
        if (!integrations) {
          return [];
        }
        return integrations.map((integration) => ({
          id: integration.id,
          type: integration.type,
          ownerType: IntegrationOwnerType.Organization,
          provider: integration.provider,
          data: integration
        }));
      });
  }

}
