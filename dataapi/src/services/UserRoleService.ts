import { SERVER_ERROR_MESSAGE } from '../constants';
import { APIError } from '../lib';
import { logger } from '../logger';
import { IAuth } from '../types/Common';

export default class UserRoleService {
  auth: IAuth;
  constructor(auth: IAuth) {
    this.auth = auth;

    if (!auth.oid) {
      logger.error({ error: 'Invalid organization Id' });
      throw new Error('Invalid service call! Check your permissions!');
    }
  }

  isAdmin() {
    return this.auth.scope
      .split(' ')
      .map((s) => s.trim())
      .includes('admin');
  }

  validateAdminAccess() {
    if (!this.isAdmin()) {
      logger.error({ msg: SERVER_ERROR_MESSAGE.Unauthorized, auth: this.auth });
      throw new APIError('Unauthorized', {
        code: 403,
        details: SERVER_ERROR_MESSAGE.Unauthorized
      });
    }
  }
}
