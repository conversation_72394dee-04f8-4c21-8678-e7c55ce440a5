import config from '../config';
import { logger } from '../logger';
import { IOceanCompanySuggestion, IOceanCompanySearchResult } from '../types/Ocean';

export default class OceanService {
  /**
   * Autocomplete companies based on a partial name.
   * Uses the Ocean.io API endpoint: POST /v2/autocomplete/companies
   *
   * @param input - The input object containing the company name and optional filters.
   * @returns A promise resolving to an array of company suggestions.
   */
  static async autocomplete(name: string): Promise<IOceanCompanySuggestion[]> {
    const url = new URL(`${config.ocean.OCEAN_API_URL}/autocomplete/companies`);

    logger.info({ msg: 'Fetching autocomplete companies', name, url, token: config.ocean.apiToken });

    url.searchParams.append('apiToken', config.ocean.apiToken);

    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ name })
    })
      .then((res) => res.json())
      .catch((err) => {
        logger.error({ msg: 'Failed to fetch autocomplete companies', error: err });
        return null;
      });

    return response?.companies ?? [];
  }

  /**
   * Searches for lookalike companies based on an array of company queries.
   * Each query must include a company name and domain.
   *
   * Uses the Ocean.io API endpoint: POST /v2/search/companies.
   * 
   * @param queries - Array of objects with properties { name, domain }
   * @param maxResultCount - Optional limit for the number of results to return (default is 50)
   * @param from - Optional starting offset for results (default is 1)
   * @returns A promise resolving to an array of company details.
   */
  static async lookalikeCompanies(
    domains: string[],
    maxResultCount: number = 1,
    from: number = 1
  ): Promise<IOceanCompanySearchResult | null> {
    const url = new URL(`${config.ocean.OCEAN_API_URL}/search/companies`);
    url.searchParams.append('apiToken', config.ocean.apiToken);

    const requestBody = {
      size: maxResultCount,
      from: from,
      companiesFilters: {
        lookalikeDomains: domains,
        minScore: 0.8
      }
    };

    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Api-Token': config.ocean.apiToken
      },
      body: JSON.stringify(requestBody)
    })
      .then((res) => res.json() as Promise<IOceanCompanySearchResult>)
      .catch((err) => {
        logger.error({ msg: 'Failed to search lookalike companies', error: err });
        return null;
      });

    logger.info({ msg: 'Lookalike companies response', response });

    return response;
  }
}
