import { logger } from '../logger';
import { db } from '../database';
import BaseService from './BaseService';
import { NotFoundError } from '../lib/errors';
import { IUserTableLayout } from '../types/User';
import { EntityType } from '@prisma/client';

export default class UserService extends BaseService {
  constructor({ auth }) {
    super({ auth });
  }

  async getTimezone() {
    const { uid: userId } = this.auth;
    try {
      return db.userCalendar.findFirst({
        where: {
          AND: [{ userId }, { active: true }]
        },
        select: {
          id: true,
          weeklyHours: true,
          timezone: true,
          meetingLength: true
        }
      });
    } catch (error) {
      logger.error(error);
      throw new Error('Timezone not found');
    }
  }

  async setTimezone({ calendarTimezone, weeklyHours, meetingLength }) {
    if (calendarTimezone.length > 5) {
      throw new Error('Invalid data');
    }
    const payload = {
      user: {
        connect: { id: this.auth.uid }
      },
      active: true
    };
    if (calendarTimezone) {
      payload['timezone'] = Number(calendarTimezone);
    }
    if (weeklyHours) {
      payload['weeklyHours'] = weeklyHours;
    }
    if (meetingLength) {
      payload['meetingLength'] = Number(meetingLength);
    }
    const timezone = await this.getTimezone();
    if (timezone) {
      return db.userCalendar.update({
        where: { id: timezone.id },
        data: payload,
        select: {
          id: true,
          weeklyHours: true,
          timezone: true,
          meetingLength: true
        }
      });
    }
    return db.userCalendar.create({
      data: payload,
      select: {
        id: true,
        weeklyHours: true,
        timezone: true,
        meetingLength: true
      }
    });
  }

  async upsertUserPreference(data) {
    const { uid: userId } = this.auth;

    return db.userPreference
      .upsert({
        where: {
          unique_user_key: {
            userId,
            key: data.key
          }
        },
        update: {
          value: data.value,
          key: data.key
        },
        create: {
          userId,
          value: data.value,
          key: data.key
        }
      })
      .then((result) => {
        if (result) {
          return result;
        }
        return [];
      })
      .catch((error) => {
        logger.error({ msg: 'Failed to upsert user preferences.', error });
        throw error;
      });
  }

  async getUserPreference(key) {
    const { uid: userId } = this.auth;

    return db.userPreference
      .findFirst({
        where: {
          userId,
          key
        }
      })
      .then((result) => {
        if (result) {
          return result;
        }
        return [];
      })
      .catch((error) => {
        logger.error({ msg: 'Failed to get user preferences.', error });
        throw error;
      });
  }

  async getUserData() {
    const { uid: userId } = this.auth;
    return db.user
      .findFirst({
        where: {
          id: userId,
          active: true,
          deletedAt: null
        },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          role: {
            select: {
              organization: {
                select: {
                  id: true,
                  name: true
                }
              },
              userRole: {
                select: {
                  role: true
                }
              }
            }
          },
          userCrmMappings: {
            select: {
              crmUserId: true,
              crmType: true,
              email: true
            }
          },
          integrations: {
            select: {
              id: true,
              name: true,
              provider: true,
              userId: true,
              expiresAt: true,
              metadata: true,
              createdAt: true,
              refreshedAt: true
            }
          },
          settings: {
            select: {
              key: true,
              value: true
            }
          },
          userPreference: {
            select: {
              key: true,
              value: true
            }
          },
          tableLayouts: {
            select: {
              entityType: true,
              value: true
            }
          },
          calendars: {
            where: { active: true },
            select: {
              weeklyHours: true,
              timezone: true,
              meetingLength: true
            }
          }
        }
      })
      .then((user) => {
        if (!user) {
          throw new NotFoundError(`User not found. ${userId}`);
        }
        if (!user.role) {
          throw new NotFoundError(`User role not found. ${userId}`);
        }
        const companyTableLayout = !user.tableLayouts ? [] : user.tableLayouts.find((layout) => layout.entityType === EntityType.company)?.value || [];
        const contactTableLayout = !user.tableLayouts ? [] : user.tableLayouts.find((layout) => layout.entityType === EntityType.contact)?.value || [];
        // const companyTableLayout = user.userPreference.find((preference) => preference.key === 'companyTableLayout')?.value;
        // const contactTableLayout = user.userPreference.find((preference) => preference.key === 'contactTableLayout')?.value;
        return {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phone: user.phone,
          role: user.role.userRole.role,
          crmMappings: user.userCrmMappings,
          integrations: user.integrations.map((integration) => {
            if (!integration.metadata) {
              throw new NotFoundError(`Integration metadata not found. ${integration.id}`);
            }
            const excludeFields = ({ name: _n, metadata: _m, ...rest }) => rest;
            return {
              ...excludeFields(integration),
              loginUser: integration.name,
              loginName: integration.metadata['name']
            };
          }),
          settings: user.settings.reduce(
            (acc, setting) => {
              acc[setting.key] = setting.value;
              return acc;
            },
            {
              companyTableLayout: companyTableLayout || [],
              contactTableLayout: contactTableLayout || []
            }
          ),
          calendar: user.calendars[0]
        };
      });
  }

  async setUserData({ settings, calendar }) {
    const { uid: userId, oid: orgId } = this.auth;
    return db.$transaction(async (tx) => {
      return tx.userSetting
        .upsert({
          where: { uniqueUserKey: { userId, orgId, key: 'EMAIL_SIGNATURE' } },
          update: { value: settings.emailSignature, updatedAt: new Date() },
          create: { userId, orgId, key: 'EMAIL_SIGNATURE', value: settings.emailSignature, createdAt: new Date() }
        })
        .then(() => {
          return tx.userSetting
            .upsert({
              where: { uniqueUserKey: { userId, orgId, key: 'ORG_SUMMARY' } },
              update: { value: settings.aiSummary, updatedAt: new Date() },
              create: { userId, orgId, key: 'ORG_SUMMARY', value: settings.aiSummary, createdAt: new Date() }
            })
            .then(() => {
              return tx.userCalendar
                .updateMany({
                  where: { userId, active: true },
                  data: { ...calendar, updatedAt: new Date() }
                })
                .then(() => true)
                .catch((error) => {
                  logger.error({ msg: 'Failed to update user calendar', error });
                  return false;
                });
            })
            .catch((error) => {
              logger.error({ msg: 'Failed to update org summary', error });
              return false;
            });
        })
        .catch((error) => {
          logger.error({ msg: 'Failed to update email signature', error });
          return false;
        });
    });
  }

  async getTableLayout({ entityType }: { entityType: EntityType }): Promise<IUserTableLayout[]> {
    return db.userTableLayout
      .findUnique({
        where: {
          userid_orgid_type_unique: {
            userId: this.auth.uid,
            orgId: this.auth.oid,
            entityType
          }
        },
        select: {
          value: true
        }
      })
      .then((layout) => (layout?.value as unknown as IUserTableLayout[]) || []);
  }

  async setTableLayout({ entityType, layout }: { entityType: EntityType; layout: object }): Promise<IUserTableLayout[]> {
    const existingRecord = await db.userTableLayout.findUnique({
      where: {
        userid_orgid_type_unique: {
          userId: this.auth.uid,
          orgId: this.auth.oid,
          entityType
        }
      }
    });

    if (existingRecord) {
      // Update the existing record
      const updatedRecord = await db.userTableLayout.update({
        where: {
          id: existingRecord.id // Use the unique ID to update
        },
        data: {
          value: layout,
          updatedAt: new Date()
        },
        select: {
          value: true
        }
      });

      return (updatedRecord?.value as unknown as IUserTableLayout[]) || [];
    } else {
      // Create a new record
      const newRecord = await db.userTableLayout.create({
        data: {
          userId: this.auth.uid,
          orgId: this.auth.oid,
          entityType,
          value: layout,
          createdAt: new Date()
        },
        select: {
          value: true
        }
      });

      return (newRecord?.value as unknown as IUserTableLayout[]) || [];
    }
  }

  async pushTableLayout({ entityType, layout }: { entityType: EntityType; layout: object }): Promise<IUserTableLayout[]> {
    return this.getTableLayout({ entityType }).then((currentLayout: IUserTableLayout[]): Promise<IUserTableLayout[]> => {
      currentLayout.push(layout as IUserTableLayout);
      return this.setTableLayout({ entityType, layout: currentLayout });
    });
  }

  async removeFromTableLayout({ entityType, itemId }: { entityType: EntityType; itemId: string }): Promise<IUserTableLayout[]> {
    return this.getTableLayout({ entityType }).then((currentLayout: IUserTableLayout[]): Promise<IUserTableLayout[]> => {
      const newLayout = currentLayout.filter((layout) => layout.id !== itemId);
      return this.setTableLayout({ entityType, layout: newLayout });
    });
  }
}
