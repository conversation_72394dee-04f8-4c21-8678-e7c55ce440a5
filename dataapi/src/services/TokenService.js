import { promisify } from 'util';
import config from '../config';
import { logger } from '../logger';
import jwt from 'jsonwebtoken';
import { db } from '../database';
import AuthService from './AuthService';

const verify = promisify(jwt.verify);

export default class TokenService {
  static detectAppSource(req) {
    let appSource = 'console'; // default

    // Check the host header to determine the app
    const host = req.headers['x-forwarded-host'] || req.headers['host'];
    if (host && host.includes('admin')) {
      appSource = 'admin';
    }

    // Try to determine app source from cookies as backup
    if (req.headers.cookie) {
      const cookies = req.headers.cookie.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=');
        acc[key] = value;
        return acc;
      }, {});

      if (cookies['admin_accessToken']) {
        appSource = 'admin';
      } else if (cookies['console_accessToken']) {
        appSource = 'console';
      }
    }

    // Also check authorization header for Bearer tokens
    if (req.headers.authorization?.startsWith('Bearer ')) {
      const token = req.headers.authorization.replace('Bearer ', '');
      const decoded = jwt.decode(token);
      if (decoded?.appSource) {
        appSource = decoded.appSource;
      }
    }

    return appSource;
  }

  static async verifyRefreshToken(token, uniqueKey, appSource = 'console') {
    return verify(token, `${uniqueKey}-${appSource}`).then((result) => {
      if (!result) {
        throw new Error('Invalid token. Signin with your username and password.');
      }

      // Verify app source matches
      if (result.appSource !== appSource) {
        throw new Error('Invalid token for this application.');
      }

      this.updateLastUsed(result.lid);
      return result;
    });
  }

  static async decodeAccessToken(token) {
    return jwt.decode(token);
  }

  static async verifyAccessToken(token, appSource = 'console') {
    const decoded = jwt.decode(token);
    var userId = decoded?.uid;

    // Verify app source from token
    if (decoded?.appSource !== appSource) {
      logger.error({ msg: 'Token app source mismatch', tokenAppSource: decoded?.appSource, expectedAppSource: appSource });
      return false;
    }

    var user = await new AuthService().isUserExistsById({ userId: userId });

    return verify(token, `${user.uniqueKey}-${appSource}`, { algorithms: [config.jwtAlgo] })
      .then(async (result) => {
        if (!result) {
          throw new Error('Invalid token. Signin with your username and password.');
        }

        // Double-check app source
        if (result.appSource !== appSource) {
          throw new Error('Invalid token for this application.');
        }

        const isInvalidated = await db.User.findUnique({
          where: {
            id: result.uid,
            invalidatedAt: { gte: new Date(result.iat * 1000) }
          }
        });
        if (isInvalidated) {
          throw new Error('Invalidated token. Re-login needed.');
        }
        logger.info(`Access token verified for ${result.email} on ${appSource}`);
        return !!result;
      })
      .catch((error) => {
        logger.error({ msg: 'Access token verification failed', error, appSource });
        return false;
      });
  }

  static async generateRefreshToken(user, appSource = 'console') {
    user.tokenType = 'refresh';
    user.appSource = appSource;
    const logModel = { logId: crypto.randomUUID() };
    const options = {
      audience: `${config.jwtAudience}-${appSource}`,
      issuer: config.jwtIssuer,
      algorithm: config.jwtAlgo,
      expiresIn: config.jwtExpRefresh
    };
    const payload = {
      uid: user.id,
      lid: logModel.logId,
      tokenType: user.tokenType,
      appSource: appSource
    };
    const refreshToken = jwt.sign(payload, `${user.uniqueKey}-${appSource}`, options);
    const log = await this.logToken(refreshToken, logModel, user);
    return { refreshToken, log };
  }

  static async generateAccessToken(user, appSource = 'console') {
    logger.info({ msg: 'Generate access token', appSource });
    user.tokenType = 'access';
    user.appSource = appSource;
    const logModel = { logId: crypto.randomUUID() };

    const options = {
      audience: `${config.jwtAudience}-${appSource}`,
      issuer: config.jwtIssuer,
      algorithm: config.jwtAlgo,
      expiresIn: config.jwtExpAccess
    };
    logger.info({ msg: 'Is active?', user });

    if (!user?.active) {
      return null;
    }

    const payload = {
      uid: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      lid: logModel.logId,
      oid: user.role?.orgId,
      tokenType: user.tokenType,
      email: user.email,
      scope: user.role?.userRole?.role,
      appSource: appSource
    };

    logger.info({ msg: 'Payload', payload });
    let accessToken;
    try {
      accessToken = jwt.sign(payload, `${user.uniqueKey}-${appSource}`, options);
    } catch (e) {
      throw Error(`Unable to generate access token! ${e.message}`);
    }

    logger.info({ msg: 'Log token', accessToken });
    const log = await this.logToken(accessToken, logModel, user);

    return { accessToken, log };
  }

  static async logToken(token = null, model = null, user = null) {
    if (!token || !model || !user) {
      throw new Error('You have to pass token, model and user');
    }
    const decoded = jwt.decode(token);
    model = {
      ...model,
      token: '',
      status: 'active',
      ip: user.ip,
      device: { ...user.device, userAgent: user.appSource },
      location: null,
      type: decoded.tokenType,
      services: user.services,
      createdAt: new Date(),
      usedAt: new Date(),
      user: {
        connect: { id: user.id }
      }
    };

    logger.info({ msg: 'Token insert', model });

    return await db.UserToken.create({ data: model });
  }

  static async updateLastUsed(id) {
    return db.UserToken.updateMany({
      where: { logId: id },
      data: { usedAt: new Date() }
    });
  }
}
