import { IntegrationProvider, IntegrationType, Organization, User, UserInvitation, CrmType, OrgStatus, OrgType, TransactionType, EnrichmentType, PlayChannel } from '@prisma/client';
import { APIError } from '../lib';
import { logger } from '../logger';
import { db } from '../database';
import { EmailService, NotificationService } from './index';
import config from '../config';
import { NotFoundError } from '../lib/errors';
import { EmailTemplate } from '../constants';
import BaseService from './BaseService';
import { IAuth, IOrganization, IOrganizationResult, IUserRole } from '../types/Common';
import { CrmUtils } from '../lib/crm/CrmUtils';
import { DefaultTime } from '../utils';
import AuthService from './AuthService';
import { Decimal } from '@prisma/client/runtime/library';

interface IOrganizationBasicInfo {
  id: string;
  name: string;
  website: string;
  status: string;
  aiSummary: string;
  type: string;
}

interface IOrganizationMembership {
  maxUser: number;
  type: string;
  startedAt: Date;
  endsAt: Date | null;
}

interface IOrganizationAddress {
  title: string;
  purpose: string;
  addressLine1: string;
  addressLine2: string;
  zipCode: number;
  cityCode: number;
  cityName: string;
  stateCode: string;
  stateName: string;
  countryCode: string;
  countryName: string;
}

interface IOrganizationUser {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  active: boolean;
  invitationAccepted: boolean;
  role: string;
  deleted: boolean;
  createdAt: Date;
}

interface IOrganizationContact {
  purpose: string;
  label: string;
  value: string;
  type: string;
}

interface IOrganizationCredits {
  usage: {
    remaining: number;
    total: number;
    expiresAt: Date | null;
  };
}

interface IEnrichmentSetting {
  name: string;
  defaultLlmModelId: string;
  type: EnrichmentType;
  llmModels: string[];
}

interface IPlaySetting {
  name: string;
  defaultLlmModelId: string;
  type: PlayChannel;
  llmModels: string[];
}

interface ILlmSetting {
  id: string;
  useSystemLlmSetting: boolean;
  enrichmentSettings: IEnrichmentSetting[];
  playSettings: IPlaySetting[];
}

interface IOrganizationSettings {
  // @todo: Proper types should be defined with real data
  activeCrm: CrmType | null;
  llm: any;
  llmSettings: ILlmSetting | null;
}
interface IBasicOrganization {
  info: IOrganizationBasicInfo;
}

interface IDetailedOrganization extends IBasicOrganization {
  membership: IOrganizationMembership;
  addresses: IOrganizationAddress[];
  users: IOrganizationUser[];
  contacts: IOrganizationContact[];
  credits: IOrganizationCredits;
  settings: IOrganizationSettings;
}

interface IOrganizationServiceParams {
  auth: IAuth;
  emailService?: EmailService;
  notificationService?: NotificationService;
}

export default class OrganizationService extends BaseService {
  private readonly emailService: EmailService | null;
  private readonly notificationService: NotificationService | null;

  constructor({ auth, emailService, notificationService }: IOrganizationServiceParams) {
    super({ auth });
    this.emailService = emailService || null;
    this.notificationService = notificationService || null;
  }

  async getSystemRoles(): Promise<IUserRole[]> {
    return db.userRole.findMany({
      select: {
        id: true,
        role: true
      }
    });
  }

  async getOrganization(): Promise<Organization | null> {
    return db.organization.findUnique({
      where: { id: this.auth.oid }
    });
  }

  private async getOrganizationAddresses(orgId: string) {
    return db.orgAddress
      .findMany({
        where: { orgId },
        select: {
          title: true,
          purpose: true,
          addressLine1: true,
          addressLine2: true,
          zipCode: true,
          city: {
            select: {
              cityName: true,
              phoneCode: true,
              state: {
                select: {
                  code: true,
                  state: true,
                  country: {
                    select: {
                      isoCode: true,
                      country: true
                    }
                  }
                }
              }
            }
          }
        }
      })
      .then((addresses) => {
        if (!addresses) {
          return [];
        }
        return addresses.map((address) => ({
          title: address.title,
          purpose: address.purpose as string,
          addressLine1: address.addressLine1,
          addressLine2: address.addressLine2 || '',
          zipCode: address.zipCode,
          cityCode: address.city.phoneCode || 0,
          cityName: address.city.cityName,
          stateCode: address.city.state.code || '',
          stateName: address.city.state.state,
          countryCode: address.city.state.country.isoCode,
          countryName: address.city.state.country.country
        }));
      });
  }

  private async getOrganizationUsers(orgId: string) {
    return db.orgUserRole
      .findMany({
        where: { orgId },
        select: {
          userRole: {
            select: {
              role: true
            }
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              active: true,
              invitationAccepted: true,
              deletedAt: true,
              createdAt: true
            }
          }
        }
      })
      .then((users) => {
        if (!users) {
          return [];
        }

        return users.map((userRole) => ({
          id: userRole.user.id,
          firstName: userRole.user.firstName,
          lastName: userRole.user.lastName,
          email: userRole.user.email,
          active: userRole.user.active,
          invitationAccepted: !!userRole.user.invitationAccepted,
          role: userRole.userRole.role,
          deleted: !!userRole.user.deletedAt,
          createdAt: userRole.user.createdAt
        }));
      });
  }

  private async getOrganizationContacts(orgId: string) {
    return db.orgContact.findMany({
      where: { orgId },
      select: {
        purpose: true,
        label: true,
        value: true,
        type: true
      }
    });
  }

  async setUseSystemLLMSetting({ id, status }): Promise<true> {
    this.userRoleService.validateAdminAccess();

    return db.orgSetting
      .findFirst({
        where: { id, orgId: this.auth.oid },
        select: {
          id: true,
          useSystemLlmSetting: true
        }
      })
      .then(async (setting): Promise<true> => {
        if (!setting) {
          throw new NotFoundError('Setting not found');
        }
        return db.orgSetting
          .update({
            where: { id: setting.id },
            data: { useSystemLlmSetting: status }
          })
          .then(() => {
            return true;
          });
      });
  }

  async updateLLMSettings(settings: ILlmSetting): Promise<boolean> {
    this.userRoleService.validateAdminAccess();
    return db.orgSetting
      .update({
        where: { id: settings.id },
        data: {
          enrichmentSettings: settings.enrichmentSettings,
          playSettings: settings.playSettings
        }
      })
      .then(() => {
        return true;
      })
      .catch((err) => {
        logger.error({ msg: 'Unable to update LLM settings', err });
        throw new Error('Unable to update LLM settings');
      });
  }

  private getOrganizationLlmSettings(orgId: string) {
    return db.orgLlmModels.findMany({
      where: {
        orgId,
        llmModel: { isEnabled: true }
      },
      select: {
        id: true,
        isEnabled: true,
        isDefault: true,
        llmModel: {
          select: {
            id: true,
            name: true,
            provider: true,
            model: true
          }
        }
      }
    });
  }

  async getOrganizationById({ orgId, admin }): Promise<IBasicOrganization | IDetailedOrganization> {
    const organization = await db.organization.findFirst({
      where: { id: orgId },
      select: {
        id: true,
        name: true,
        website: true,
        aiSummary: true,
        membership: {
          select: {
            status: true,
            organizationType: true
          }
        },
        settings: {
          select: {
            id: true,
            useSystemLlmSetting: true,
            enrichmentSettings: true,
            playSettings: true
          }
        }
      }
    });

    if (!organization) {
      throw new NotFoundError('Organization not found');
    }

    const info = {
      ...organization,
      status: organization.membership?.status || OrgStatus.active,
      type: organization.membership?.organizationType || OrgType.Private_Equity
    };

    await this.handleCreditExpiry(orgId);
    const membership = await db.orgMembership.findFirst({
      where: { orgId },
      select: {
        maxUser: true,
        type: true,
        startedAt: true,
        endsAt: true,
        creditBalance: true
      }
    });
    if (!membership) {
      throw new Error('Missing organization data! Membership info not found');
    }

    const addresses = await this.getOrganizationAddresses(orgId);
    const users = await this.getOrganizationUsers(orgId);
    const contacts = await this.getOrganizationContacts(orgId);
    const llmSettings = await this.getOrganizationLlmSettings(orgId);

    const purchases = await db.creditPurchase.findMany({
      where: {
        orgId
      },
      select: {
        creditAmount: true,
        expiresAt: true,
        purchasedAt: true
      }
    });
    const currentDate = new Date();
    const activePurchases = purchases.filter((purchase) => purchase.expiresAt === null || purchase.expiresAt >= currentDate);
    const nonActivePurchases = purchases.filter((purchase) => purchase.expiresAt && purchase.expiresAt < currentDate);

    const validPurchases = [...activePurchases];
    nonActivePurchases.forEach((purchase) => {
      if (activePurchases.some((active) => purchase.expiresAt && purchase.expiresAt <= active.purchasedAt)) {
        validPurchases.push(purchase);
      }
    });

    const total = Math.floor(
      validPurchases
        .reduce((total, purchase) => purchase.creditAmount.add(total), new Decimal(0))
        .dividedBy(100)
        .toNumber()
    );
    const expiresAt: Date | null = validPurchases.reduce<Date | null>((latest, { expiresAt }) => {
      if (!expiresAt) return latest;
      return !latest || expiresAt > latest ? expiresAt : latest;
    }, null);

    const credits = {
      usage: {
        remaining: Math.floor(membership.creditBalance.dividedBy(100).toNumber()),
        total,
        expiresAt
      }
    };

    logger.debug({ msg: 'Organization credits', credits: credits });

    const settings: IOrganizationSettings = {
      llmSettings: organization.settings as unknown as ILlmSetting,
      activeCrm: await this.getActiveCrm(),
      llm: llmSettings
    };

    if (!admin) {
      return {
        info,
        settings
      };
    }

    return {
      info,
      membership,
      addresses,
      users,
      contacts,
      credits,
      settings
    };
  }

  /**
   * Handles the expiration of credits for purchases that have reached their expiry date.
   * Always creates negative credit transaction logs for expired purchases.
   * Only updates membership balance if no active purchases exist.
   */
  async handleCreditExpiry(orgId: string): Promise<void> {
    try {
      const currentDate = new Date();
      // First, get all purchases (both active and expired) for scenario determination
      const [expiredPurchases, activePurchases] = await Promise.all([
        // Find expired purchases without expire transactions
        db.creditPurchase.findMany({
          where: {
            orgId,
            expiresAt: {
              lt: currentDate
            },
            transactions: {
              none: {
                type: TransactionType.expire
              }
            }
          },
          orderBy: {
            expiresAt: 'asc'
          }
        }),
        // Get active (non-expired) purchases
        db.creditPurchase.findMany({
          where: {
            orgId,
            OR: [{ expiresAt: { gte: currentDate } }, { expiresAt: null }]
          }
        })
      ]);

      if (expiredPurchases.length === 0) {
        return;
      }

      // Get current membership details
      const membership = await db.orgMembership.findFirst({
        where: {
          orgId,
          status: OrgStatus.active
        },
        select: {
          creditBalance: true,
          tier: true
        }
      });

      if (!membership) {
        throw new NotFoundError('Active organization membership not found');
      }

      const hasActivePurchases = activePurchases.length > 0;

      // Process the expiry in a transaction
      await db.$transaction(async (tx) => {
        // Create expire transactions for each purchase with negated amounts
        for (const purchase of expiredPurchases) {
          await tx.creditTransactionLog.create({
            data: {
              orgId,
              type: TransactionType.expire,
              creditPurchaseId: purchase.id,
              creditAmount: purchase.creditAmount.negated(),
              metadata: {
                expiredAt: currentDate.toISOString(),
                originalPurchase: {
                  purchaseId: purchase.id,
                  amount: purchase.creditAmount,
                  priceInCents: purchase.purchasePrice,
                  purchasedAt: purchase.purchasedAt.toISOString()
                },
                tier: membership.tier
              },
              createdAt: currentDate
            }
          });
        }

        // Only update membership balance if all purchases are expired
        if (!hasActivePurchases) {
          const totalCreditsToExpire = expiredPurchases.reduce((sum, purchase) => sum.add(purchase.creditAmount), new Decimal(0));

          // Calculate new balance with minimum of 0
          const newBalance = Decimal.max(membership.creditBalance.sub(totalCreditsToExpire), new Decimal(0));

          // Update membership balance
          await tx.orgMembership.update({
            where: { orgId },
            data: {
              creditBalance: newBalance
            }
          });

          logger.info({
            msg: 'Processed credit expiry with balance reduction',
            orgId,
            expiredPurchases: expiredPurchases.length,
            creditsExpired: totalCreditsToExpire,
            oldBalance: membership.creditBalance,
            newBalance: newBalance,
            tier: membership.tier
          });
        } else {
          logger.info({
            msg: 'Processed credit expiry without balance reduction (active purchases exist)',
            orgId,
            expiredPurchases: expiredPurchases.length,
            activePurchases: activePurchases.length,
            currentBalance: membership.creditBalance,
            tier: membership.tier
          });
        }
      });
    } catch (error) {
      logger.error({
        msg: 'Failed to process credit expiry',
        orgId,
        error
      });
      throw error;
    }
  }

  async setUserRole({ userId, role }): Promise<true> {
    this.userRoleService.validateAdminAccess();
    const roleObj = await db.userRole.findFirst({
      where: { role },
      select: { id: true }
    });
    if (!roleObj) {
      throw new NotFoundError('Role not found');
    }

    return db.orgUserRole
      .update({
        where: { userId, orgId: this.auth.oid },
        data: { userRole: { connect: { id: roleObj.id } } }
      })
      .then(() => {
        if (this.notificationService) {
          this.notificationService.writeData(`notifications/users/${userId}/updated`, { time: new Date().toISOString() });
        }
        return true;
      });
  }

  async setUserStatus({ userId, status }): Promise<true> {
    this.userRoleService.validateAdminAccess();

    return db.orgUserRole
      .findFirst({
        where: { userId, orgId: this.auth.oid },
        select: { userId: true }
      })
      .then(async (role): Promise<true> => {
        if (!role) {
          throw new NotFoundError('User not found');
        }
        return db.user
          .update({
            where: { id: role.userId },
            data: { active: status }
          })
          .then(() => {
            if (this.notificationService) {
              this.notificationService.writeData(`notifications/users/${userId}/updated`, { time: new Date().toISOString() });
            }
            return true;
          });
      });
  }

  async getMyOrganization(): Promise<IOrganizationResult | null> {
    const { oid } = this.auth;

    if (!this.userRoleService.isAdmin()) {
      const org = await db.organization.findFirst({
        where: {
          id: oid
        },
        select: {
          id: true,
          name: true,
          website: true,
          membership: {
            select: {
              status: true
            }
          },
          aiSummary: true
        }
      });

      if (!org) {
        throw new NotFoundError('Org not found');
      }

      return {
        id: org.id,
        name: org.name,
        website: org.website,
        aiSummary: org.aiSummary,
        status: org.membership?.status || OrgStatus.active
      };
    }
    const membership = {
      select: {
        maxUser: true,
        type: true,
        status: true,
        organizationType: true,
        startedAt: true,
        endsAt: true
      }
    };

    const addresses = {
      select: {
        title: true,
        zipCode: true,
        purpose: true,
        addressLine1: true,
        addressLine2: true,
        city: {
          select: {
            cityName: true,
            phoneCode: true,
            state: {
              select: {
                code: true,
                state: true,
                country: {
                  select: {
                    isoCode: true,
                    country: true
                  }
                }
              }
            }
          }
        }
      }
    };
    const user = {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        active: true,
        invitationAccepted: true,
        updatedAt: true,
        deletedById: true,
        deletedAt: true,
        role: {
          select: {
            userRole: {
              select: {
                role: true
              }
            }
          }
        }
      }
    };

    const userRoles = {
      select: {
        userRole: {
          select: {
            role: true
          }
        },
        user
      }
    };
    const orgContacts = {
      select: {
        purpose: true,
        label: true,
        value: true,
        type: true
      }
    };

    return db.organization
      .findUnique({
        where: { id: oid },
        select: {
          id: true,
          name: true,
          website: true,
          aiSummary: true,
          membership,
          addresses,
          userRoles,
          orgContacts
        }
      })
      .then((org: any) => {
        if (org && org.userRoles) {
          org.user = org.userRoles.map((userRoles) => userRoles.user);
        }
        return {
          ...org,
          status: org.membership?.status || OrgStatus.active,
          type: org.membership?.organizationType || OrgType.Private_Equity
        };
      });
  }

  async isUserExists(user, userList): Promise<boolean> {
    return !!user.find((dbUser: any) => {
      return userList.find((user: any) => user.email === dbUser.email && !dbUser.deletedAt);
    });
  }

  async createInvitations(userList: string[]) {
    this.userRoleService.validateAdminAccess();
    return db.organization
      .findUnique({
        where: { id: this.auth.oid },
        include: {
          membership: true,
          userRoles: {
            include: {
              user: true
            }
          }
        }
      })
      .then(async (organization) => {
        if (!organization) {
          logger.error({ msg: 'organization not found!' });
          throw new Error('organization not found!');
        }

        const users = organization.userRoles.map((role: any) => role.user);
        const maxUsers = organization.membership?.maxUser ?? 0;
        const totalUsers = users.filter((user: any) => !user.deletedAt).length;

        if (totalUsers + userList.length > maxUsers) {
          throw new Error(`Not enough seats to add ${userList.length}! ${maxUsers - totalUsers} seats available.`);
        }
        await this.isUserExists(users, userList).then((userFound) => {
          if (userFound) {
            throw new Error('Some user you posted already exists!');
          }
        });
        return await Promise.all(
          userList.map(async (user: any) => {
            const findUser = await db.user.findFirst({ where: { email: user.email } });
            if (findUser) {
              return db.user
                .update({
                  where: { id: findUser.id },
                  data: {
                    active: true,
                    updatedAt: new Date(),
                    deletedAt: null
                  }
                })
                .catch((err: any) => {
                  logger.error({ msg: 'Unable to update user', err });
                });
            } else {
              const newUser = await db.user
                .create({
                  data: {
                    email: user.email,
                    active: false,
                    role: {
                      create: {
                        userRole: {
                          connect: {
                            id: user.roles[0]
                          }
                        },
                        organization: {
                          connect: {
                            id: organization.id
                          }
                        }
                      }
                    },
                    firstName: null,
                    lastName: null,
                    createdAt: new Date(),
                    updatedAt: null,
                    deletedAt: null
                  },
                  include: {
                    role: {
                      include: {
                        userRole: true
                      }
                    }
                  }
                })
                .catch((err: any) => {
                  logger.error({ msg: 'Unable to create user', err });
                });

              // Convert any BigInt values in the response to strings
              if (newUser && newUser.role) {
                return {
                  ...newUser,
                  role: {
                    ...newUser.role,
                    id: Number(newUser.role.id)
                  }
                };
              }
              return newUser;
            }
          })
        );
      })
      .catch((err: any) => {
        logger.error(err);
        throw err;
      });
  }

  async updateUser(user): Promise<boolean> {
    logger.info({ msg: 'User data is updating...', user });
    this.userRoleService.validateAdminAccess();
    if (!user.id) {
      logger.error({ error: 'Invalid user Id', user });
      throw new APIError('Invalid user Id', {
        code: 400,
        details: 'Invalid user Id'
      });
    }

    if (user.id === this.auth.uid) {
      logger.error({ error: 'Trying to set self', user });
      throw new APIError('You cannot change your permissions', {
        code: 400,
        details: 'You cannot change your permissions'
      });
    }

    const orgUser = await db.user.findFirst({ where: { id: user.id } });

    if (!orgUser) {
      logger.error({ msg: 'Invalid user' });
      throw new APIError('User not found', {
        code: 404,
        details: 'User not found'
      });
    }

    return await this.updateUserInfo(user, orgUser);
  }

  async deleteUser({ userId }: { userId: string }): Promise<boolean> {
    logger.info({ msg: 'User data is updating...', userId });
    this.userRoleService.validateAdminAccess();
    if (!userId) {
      logger.error({ error: 'Invalid user Id', userId });
      throw new Error('Invalid user Id');
    }

    if (userId === this.auth.uid) {
      logger.error({ error: 'Trying to delete self', userId });
      throw new Error('You cannot delete yourself');
    }

    const orgUser = await db.user.findFirst({
      where: {
        id: userId,
        deletedAt: null,
        deletedById: null
      }
    });

    if (!orgUser) {
      logger.error({ msg: 'Invalid user' });
      throw new Error('User not found');
    }
    const deleted = await db.user.update({
      where: {
        id: userId
      },
      data: {
        active: false,
        deletedAt: new Date(),
        deletedById: this.auth.uid
      }
    });
    await this.revokeUserTokens({ userId });
    if (deleted && this.notificationService) {
      await this.notificationService.writeData(`notifications/users/${userId}/updated`, { time: new Date().toISOString() });
    }
    return !!deleted;
  }

  async revokeUserTokens({ userId }: { userId: string }): Promise<User | boolean> {
    const service = new AuthService();
    return await service.revokeUserTokens({ userId: userId });
  }

  async updateInvitation(users) {
    const user = users.pop();
    const foundUser = await this.getOrganizationwithUserInfo(user.id);
    if (!foundUser) {
      throw Error('Update invitation error');
    }
    return await this.updateOrganizationUserInfo(foundUser.id, { updatedAt: new Date() });
  }

  async getIntegrations() {
    if (!this.userRoleService.isAdmin()) {
      return db.orgIntegration.findMany({
        where: {
          orgId: this.auth.oid
        },
        select: {
          id: true,
          provider: true,
          expiresAt: true,
          type: true,
          active: true
        }
      });
    }
    return db.orgIntegration.findMany({
      where: {
        orgId: this.auth.oid
      },
      select: {
        id: true,
        provider: true,
        name: true,
        expiresAt: true,
        type: true,
        orgId: true,
        refreshedAt: true,
        createdAt: true,
        updatedAt: true,
        active: true,
        createdBy: {
          select: {
            firstName: true,
            lastName: true
          }
        }
      }
    });
  }

  async createIntegration(data): Promise<void> {
    logger.info({ msg: 'Creating new integration', auth: this.auth });
    const org = await this.getOrganization();
    if (!org) {
      throw new Error('Cannot find organization');
    }
    const { uid, oid } = this.auth;

    if (!this.userRoleService.isAdmin() && data.provider === IntegrationProvider.salesforce) {
      throw new APIError('Your roles does not accept this operation!', {
        code: 403,
        details: 'Forbidden'
      });
    }

    return db
      .$transaction(async (tx) => {
        await tx.orgIntegration.deleteMany({
          where: {
            orgId: oid,
            provider: data.provider
          }
        });

        await tx.orgIntegration.updateMany({
          where: {
            orgId: oid,
            type: data.type
          },
          data: {
            active: false
          }
        });

        await tx.orgIntegration.create({
          data: {
            organization: {
              connect: {
                id: oid
              }
            },
            createdBy: {
              connect: {
                id: uid
              }
            },
            authToken: data.tokens,
            data: data.integrationData,
            name: data.integrationData.username,
            provider: data.provider,
            expiresAt: data.integrationData.rTokenExpiresAt,
            createdAt: data.integrationData.createdAt,
            refreshedAt: data.integrationData.refreshedAt,
            active: true,
            type: data.type
          }
        });
      })
      .catch((err) => {
        logger.error({ msg: 'Unable to create integration', err });
        throw new Error('Unable to create integration');
      });
  }

  async getOrganizationwithUserInfo(id: string) {
    return await db.user.findUnique({
      where: {
        invitationAccepted: false,
        id
      },
      select: {
        email: true,
        id: true,
        updatedAt: true,
        invitationAccepted: true,
        deletedById: true
      }
    });
  }

  async updateOrganizationUserInfo(userId: string, data): Promise<User> {
    return await db.user.update({
      where: { id: userId },
      data: data
    });
  }

  genRandomHex(size: number): string {
    return [...Array(size)].map(() => Math.floor(Math.random() * 16).toString(16)).join('');
  }

  async isUserInvitationExists(user, tx): Promise<UserInvitation> {
    return await tx.userInvitation.findFirst({
      where: {
        email: user.email,
        orgId: this.auth.oid,
        userId: user.id,
        deletedAt: null
      }
    });
  }

  async updateUserInvitation(invitation: UserInvitation, hash: string, template: string, tx: any): Promise<string> {
    return tx.UserInvitation.update({
      where: {
        id: invitation.id
      },
      data: {
        updatedAt: new Date()
      },
      select: {
        orgId: true,
        userId: true
      }
    })
      .then((_result: any) => {
        return this.sendInvitationEmail(invitation.orgId, invitation.userId, hash, template, tx);
      })
      .catch((err: any) => {
        logger.error(err);
        throw new Error('Unable to re-send invitation');
      });
  }

  async createUserInvitation(user, org: IOrganization, hash: string, template: string, tx): Promise<string> {
    const { id: orgId } = org;
    const { id: userId, email } = user;

    return tx.UserInvitation.createInvitation({ orgId, userId, hash, email }, { tx })
      .then(async (result) => {
        if (!result) {
          throw new Error('Unable to create the requested invitation');
        }
        return await this.sendInvitationEmail(org.id, user.id, hash, template, tx);
      })
      .catch((err) => {
        logger.error(err);
        throw new Error('Unable to create invitation');
      });
  }

  async sendInvitationEmail(orgId: string, userId: string, hash: string, template: string, tx): Promise<string> {
    logger.info({ msg: 'Sending invitation email', orgId, userId });
    if (!this.emailService) {
      throw new Error('Email service is not available');
    }

    const user = await tx.user.findUnique({
      where: { id: userId },
      select: {
        email: true,
        firstName: true,
        invitations: { select: { hash: true } }
      }
    });
    const orgName = await tx.organization
      .findUnique({
        where: { id: orgId },
        select: { name: true }
      })
      .then((org: any) => org.name);

    logger.info({ msg: 'User found', user });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    await this.emailService.createEmail({
      from: config.systemEmailSender,
      to: user.email,
      template: template,
      data: {
        linkDomain: config.email.data.linkDomain,
        assetDomain: config.email.data.assetDomain,
        source: config.email.data.source,
        consoleDomain: config.email.data.consoleDomain,
        firstName: user.firstName,
        invitationLink: `${config.email.data.invitationLink}?hash=${hash}`,
        companyName: orgName
      },
      subject: config.email.subjects.invitation
    });

    return userId;
  }

  async inviteUsersWithEmail(userList, org): Promise<string[]> {
    this.userRoleService.validateAdminAccess();
    return db
      .$transaction(async (tx) => {
        for (let i = 0; i < userList.length; i++) {
          const userInvitation = await this.isUserInvitationExists(userList[i], tx);
          let hash = '';

          if (userInvitation) {
            hash = userInvitation.hash;
            await this.updateUserInvitation(userInvitation, userInvitation.hash, EmailTemplate.INVITE_USER, tx);
          } else {
            hash = this.genRandomHex(32);
            const userId = await this.createUserInvitation(userList[i], org, hash, EmailTemplate.INVITE_USER, tx);
            await tx.userCalendar.create({
              data: {
                organization: {
                  connect: {
                    id: org.id
                  }
                },
                user: {
                  connect: {
                    id: userId
                  }
                },
                active: true,
                weeklyHours: DefaultTime
              }
            });
          }
        }
        return userList.map((user) => {
          return user.email;
        });
      })
      .catch((err) => {
        logger.error(err);
        throw new Error('Unable to invite users');
      });
  }

  async cancelInvitation(userId: string): Promise<User> {
    this.userRoleService.validateAdminAccess();

    const inv = await db.user.update({
      where: {
        id: userId
      },
      data: {
        deletedAt: new Date()
      }
    });
    return inv;
  }

  async getInvitations() {
    const invitations = await db.userInvitation.findMany({
      where: {
        AND: [{ orgId: this.auth.oid }, { deletedAt: null }]
      },
      select: {
        email: true,
        createdBy: {
          select: {
            role: {
              select: {
                userRole: {
                  select: {
                    role: true
                  }
                }
              }
            }
          }
        }
      }
    });
    return invitations.map((i) => {
      const email = i.email;
      const roles = i.createdBy?.role?.userRole?.role;
      return { email, roles };
    });
  }

  async updateUserInfo(user, orgUser): Promise<boolean> {
    try {
      await db.user.update({
        where: { id: user.id },
        data: { invalidatedAt: new Date() }
      });
      if ('roles' in user) {
        await db.orgUserRole.update({
          where: {
            userId: orgUser.id
          },
          data: {
            userRole: {
              connect: {
                id: user.roles
              }
            }
          }
        });
      }
      if ('active' in user) {
        await db.user.update({
          where: {
            id: user.id
          },
          data: { active: user.active, updatedAt: new Date() }
        });
        await this.revokeUserTokens({ userId: user.id });
      }
      return true;
    } catch (error) {
      logger.error({ msg: 'User update failed.', error });
      throw new APIError('User update failed', {
        code: 400,
        details: 'User update failed'
      });
    }
  }

  async getActiveCrm(): Promise<CrmType | null> {
    return db.orgIntegration
      .findFirst({
        where: {
          orgId: this.auth.oid,
          active: true,
          type: IntegrationType.crm
        }
      })
      .then((crmIntegration) => {
        return crmIntegration ? CrmUtils.getCrmTypeForProvider(crmIntegration.provider) : null;
      })
      .catch((error) => {
        logger.error({ msg: 'Unable to get active CRM', error });
        throw new Error('Unable to get active CRM');
      });
  }

}
