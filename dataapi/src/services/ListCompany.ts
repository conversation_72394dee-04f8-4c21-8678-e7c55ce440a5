import { DataSourceType, EntityField, EntityType, ExecutionStatus, ExecutionType, ListCompany, ListCompanyIdea, ListCompanyStatus, ListSource, ListSourceStatus, ListSourceType, Prisma, QueueTaskType } from '@prisma/client';
import _ from 'lodash';
import { CompanyMandatoryFields, CONCURRENT_TRANSACTION_COUNT, DB_TRANSACTION_SETTING, PAGE_SIZE } from '../constants';
import { db } from '../database';
import { logger } from '../logger';
import { IAuth } from '../types/Common';
import { IListCompanyFilters, IListCompanyResponse } from '../types/ListCompany';
import { ICompanyFilter, ICompanyIdeaStatus, IEntityField, IFilterParameter, IFilterSampleData, IListCompanyIdeaFilters, IListCompanyIdeaResponse } from '../types/ListCompanyIdea';
import GoogleMapsService, { GoogleMapsServiceSearchTextInput } from './GoogleMaps';
import { CSV } from '../lib';
import crypto from 'crypto';
import dayjs from 'dayjs';
import NotificationService from './Notification';
import { ConflictError, NotFoundError, BadRequestError } from '../lib/errors';
import { getDomainFromUrl } from '../lib/utils';
import { Promise } from 'bluebird';
import { Parser } from 'json2csv';
import { getValueByKeys } from '../lib/jsonUtils';
import OceanService from './Ocean';
import { IOceanCompanyResult, IOceanFilteredCompany, IOceanSourcePayload } from '../types/Ocean';
import SourceScrubService from './SourceScrub';
import { ISourceScrubCompany, ISourceScrubFilteredCompany, ISourceScrubFilterPayload, ISourceScrubPayload, ISourceScrubSourceRequest, ISourceScrubSourcesPayload, SourceScrubFilters } from '../types/SourceScrub';
import { IdeaFiltersMapping } from '../utils/IdeaFiltersMapping';
import ExaService from './Exa';
import { IExaCompany, IExaFilteredCompany, IExaSearchPayload } from '../types/Exa';

/**
 * A small interface to wrap the parameters for upsertListCompanyIdea
 */
interface UpsertListCompanyIdeaParams {
  orgId: string;
  companyName?: string | null;
  website?: string | null;
  companyId?: string | null;
  fields?: Record<string, string>;
  metadata?: any;
  sourceId: string;
}

interface ListServiceParams {
  auth: IAuth;
  notificationService?: NotificationService;
}

/**
 * Interface for detailed source creation results
 */
interface ISourceCreationResult {
  success: boolean;
  totalProcessed: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  failures: Array<{
    item: string;
    reason: string;
    error?: string;
  }>;
  skipped: Array<{
    item: string;
    reason: string;
  }>;
}

interface EnrichmentColumn {
  label: string;
  keys: string[];
}

export default class ListService {
  auth: IAuth;
  notificationService: NotificationService | null;

  constructor({ auth, notificationService }: ListServiceParams) {
    this.auth = auth;
    this.notificationService = notificationService || null;

    if (!auth.oid) {
      logger.error({ error: 'Invalid organization Id' });
      throw new Error('Invalid service call! Check your permissions!');
    }
  }

  // ------------------------------------------------
  //                   PUBLIC METHODS
  // ------------------------------------------------

  async getIntentions() {
    return db.intention.findMany({
      include: {
        intentionSources: {
          include: {
            intention: {
              select: {
                name: true
              }
            }
          },
          orderBy: {
            orderBy: 'asc'
          }
        }
      },
      orderBy: {
        orderBy: 'asc'
      }
    });
  }

  async getUsers() {
    /**
     * @todo: This is returning all user data INCLUDING PASSWORD!
     * Check if removed parts are correct
     */
    const usersWithCompanies = await db.user.findMany({
      where: {
        listCompany: {
          some: {
            orgId: this.auth.oid,
            createdById: {
              not: null
            }
          }
        }
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true
      }
    });
    return usersWithCompanies;
  }

  async updateListCompany(listCompanyId: string, listCompany: Prisma.ListCompanyUpdateInput) {
    if (!listCompanyId) {
      throw new Error('List company id is required');
    }
    await db.listCompany.update({
      where: {
        id: listCompanyId
      },
      data: listCompany
    });
  }

  async deleteListCompany(listCompanyId: string) {
    return db.listCompany.delete({
      where: {
        id: listCompanyId
      }
    });
  }

  /**
   * Deletes the given ideas from the specified list mappings (source and idea mappings).
   *
   * For each idea ID:
   *  - Remove entries from ListCompanyIdeaMapping for this list.
   *  - Remove entries from ListCompanySourceIdeaMapping for this list's sources.
   *  - Do not delete the idea itself from ListCompanyIdea.
   */
  async deleteListCompanyIdea({ listCompanyId, listCompanyIdeaIds }: { listCompanyId: string; listCompanyIdeaIds: string[] }) {
    if (!listCompanyId || !listCompanyIdeaIds || listCompanyIdeaIds.length === 0) {
      throw new Error('Invalid listCompanyId or listCompanyIdeaIds');
    }

    return db.$transaction(async (tx) => {
      // 1) Fetch all sources for this list
      const listSources = await tx.listSource.findMany({
        where: { listCompanyId },
        select: { id: true }
      });
      const listSourceIds = listSources.map((ls) => ls.id);

      // 2) Delete pivot rows from ListCompanyIdeaMapping for this list
      await tx.listCompanyIdeaMapping.deleteMany({
        where: {
          listCompanyIdeaId: { in: listCompanyIdeaIds },
          listCompanyId
        }
      });

      // 3) Delete pivot rows from ListCompanySourceIdeaMapping for this list's sources
      await tx.listCompanySourceIdeaMapping.deleteMany({
        where: {
          listCompanyIdeaId: { in: listCompanyIdeaIds },
          listSourceId: { in: listSourceIds }
        }
      });

      // Note: ListCompanyIdea records remain untouched
    });
  }

  async updateListName({ listId, name }: { listId: string; name: string }): Promise<boolean> {
    return db.listCompany
      .update({
        where: { orgId: this.auth.oid, id: listId },
        data: { name, updatedAt: new Date() }
      })
      .then((result): boolean => {
        if (!result) {
          return false;
        }

        if (this.notificationService) {
          this.notificationService.writeData(`/organizations/${this.auth.oid}/lists/${listId}/info`, {
            title: name,
            titleUpdatedById: this.auth.uid,
            titleUpdatedAt: new Date().toISOString()
          });
        }
        return true;
      });
  }

  async updateListCompanyIdea(listCompanyIdea: ListCompanyIdea) {
    const trimmedCompanyName = listCompanyIdea.companyName?.trim().slice(0, 255);
    const domain = getDomainFromUrl(listCompanyIdea?.website);
    const trimmedWebsite = listCompanyIdea.website?.trim().slice(0, 255);

    const existingIdea = await db.listCompanyIdea.findFirst({
      where: {
        orgId: this.auth.oid,
        domain,
        id: {
          not: listCompanyIdea.id
        }
      }
    });

    if (existingIdea) {
      logger.error({ msg: 'Company with the same domain already exists', domain });
      throw new ConflictError('Company with the same domain already exists');
    }

    return db.listCompanyIdea.update({
      where: {
        id: listCompanyIdea.id
      },
      data: {
        companyName: trimmedCompanyName,
        website: trimmedWebsite,
        domain: domain
      }
    });
  }

  async getFilterSamples(parameters: IFilterParameter[]): Promise<IFilterSampleData[]> {
    logger.debug({ msg: 'Getting filter samples', parameters });

    // Return empty array if no filter set properly
    if (parameters?.length === 0 || !parameters.every((p) => p.entityField && p.filter && p.value)) {
      return [];
    }

    // Fetch entity fields from the database
    const entityFieldIds = parameters.map((p) => p.entityField);
    const entityFields: IEntityField[] = await db.entityField.findMany({
      where: {
        id: {
          in: entityFieldIds
        }
      },
      select: {
        id: true,
        key: true,
        label: true,
        isMandatory: true
      }
    });

    // Construct company filters
    const companyFilters: ICompanyFilter[] = this.constructCompanyFilters(parameters, entityFields);

    // Fetch the company data based on the filter conditions
    const companies = await db.company.findMany({
      where: {
        AND: companyFilters,
        orgId: this.auth.oid
      },
      distinct: ['id'],
      take: 5,
      select: {
        id: true,
        name: true,
        website: true
      }
    });

    logger.debug({ msg: 'Filtered companies', companies });

    // Fetch company fields for the filtered companies
    const companyFieldEntries = await db.companyField.findMany({
      where: {
        companyId: {
          in: companies.map((company) => company.id)
        }
      },
      include: {
        schemaField: true
      }
    });

    // Add dummy entries for name and website fields
    const fields: IFilterSampleData[] = companyFieldEntries.map((field) => ({
      ...field,
      schemaField: {
        label: field.schemaField.label
      }
    }));

    companies.forEach((company) => {
      const nameField = entityFields.find((ef) => ef.key === CompanyMandatoryFields.NAME);
      if (nameField) {
        fields.push({
          id: `dummy-${company.id}-name`,
          companyId: company.id,
          schemaFieldId: nameField.id,
          value: company.name,
          schemaField: {
            label: nameField.label
          }
        });
      }

      const websiteField = entityFields.find((ef) => ef.key === CompanyMandatoryFields.WEB_URL);
      if (websiteField) {
        fields.push({
          id: `dummy-${company.id}-website`,
          companyId: company.id,
          schemaFieldId: websiteField.id,
          value: company.website,
          schemaField: {
            label: websiteField.label
          }
        });
      }
    });

    return fields ?? [];
  }

  async shortlistListCompanyIdea({ listId, ideaIds, status }: { listId: string; ideaIds: string[]; status: boolean }) {
    return db.listCompanyIdeaMapping.updateMany({
      where: {
        listCompanyIdeaId: { in: ideaIds },
        listCompany: { id: listId, orgId: this.auth.oid }
      },
      data: {
        inShortlist: status
      }
    });
  }

  async getListCompanyIdeaIdsByFilters(filters: IListCompanyIdeaFilters) {
    if (!filters.listCompanyId) {
      throw new BadRequestError('Invalid list company id');
    }
    return await db.listCompanyIdea.getFilteredListCompanyIdeaIds({
      orgId: this.auth.oid,
      ...IdeaFiltersMapping(filters),
      listCompanyId: filters.listCompanyId
    });
  }

  async getFilteredListCompanies(filters: IListCompanyFilters): Promise<IListCompanyResponse> {
    return db.listCompany.getFilteredListCompanies({ orgId: this.auth.oid, pageSize: PAGE_SIZE, ...filters });
  }

  async getCompleteConfirmation(listCompanyId: string) {
    const allIdeas = await db.listCompanyIdea.getListCompanyIdeas({ listCompanyId });
    const candidates = allIdeas.filter((idea) => idea.status === ICompanyIdeaStatus.Idea && idea.inShortlist);
    const existing = allIdeas.filter((idea) => idea.status === ICompanyIdeaStatus.InAcqwired);
    return {
      total: allIdeas.length,
      candidate: candidates.length,
      existing: existing.length
    };
  }

  async getFilteredListCompanyIdeas(filters: IListCompanyIdeaFilters): Promise<IListCompanyIdeaResponse> {
    if (!filters.listCompanyId) {
      throw new BadRequestError('Invalid list company id');
    }
    return db.listCompanyIdea.getFilteredListCompanyIdeas({ orgId: this.auth.oid, pageSize: PAGE_SIZE, ...filters });
  }

  async deleteSource(sourceId: string): Promise<void> {
    return db.$transaction(async (tx) => {
      // 1) Get the listCompanyId for the source being deleted.
      const sourceRecord = await tx.listSource.findUnique({
        where: { id: sourceId },
        select: { listCompanyId: true }
      });
      if (!sourceRecord) {
        throw new Error('Source not found');
      }
      const listCompanyId = sourceRecord.listCompanyId;

      // 2) Delete the source.
      // Cascading rules will remove related ListCompanySourceIdeaMapping rows.
      await tx.listSource.delete({
        where: { id: sourceId }
      });

      // 3) Get the IDs of all remaining sources for the same list.
      const remainingSources = await tx.listSource.findMany({
        where: { listCompanyId },
        select: { id: true }
      });
      const remainingSourceIds = remainingSources.map((s) => s.id);

      // 4) Determine which ideas are still linked via any remaining source.
      const remainingMappings = await tx.listCompanySourceIdeaMapping.findMany({
        where: {
          // If no sources remain, the array will be empty and no mappings will be found.
          listSourceId: { in: remainingSourceIds }
        },
        select: { listCompanyIdeaId: true }
      });
      // Create a unique set of idea IDs still associated with a source.
      const stillLinkedIdeaIds = Array.from(new Set(remainingMappings.map((mapping) => mapping.listCompanyIdeaId)));

      // 5) Delete orphaned mappings from ListCompanyIdeaMapping.
      // Only delete those where the idea is no longer associated with any source in this list.
      await tx.listCompanyIdeaMapping.deleteMany({
        where: {
          listCompanyId,
          listCompanyIdeaId: { notIn: stillLinkedIdeaIds }
        }
      });
    });
  }

  async getGoogleMapsSamples({ locationBias, textQuery, radius, maxResultCount }) {
    const places = await GoogleMapsService.searchText({ locationBias, textQuery, radius, maxResultCount, fields: ['places.displayName', 'places.websiteUri', 'places.location'] });
    logger.debug({ msg: 'Google Maps places', places });
    return places;
  }

  async getListCompanies(): Promise<any[]> {
    return await db.listCompany.findMany({
      where: {
        orgId: this.auth.oid
      },
      include: {
        listSource: true
      }
    });
  }

  async getListCompany(listCompanyId: string): Promise<any> {
    return db.listCompany.getListCompany({ orgId: this.auth.oid, listCompanyId });
  }

  /**
   * Creates ideas from a CSV resource
   */
  async createCSVListIdeasForSource(source: ListSource, file?: Express.Multer.File): Promise<ISourceCreationResult> {
    if (!file) {
      return {
        success: false,
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        skippedCount: 0,
        failures: [{ item: 'CSV File', reason: 'No file provided' }],
        skipped: []
      };
    }

    try {
      // 1) Parse the CSV in memory
      const csv = new CSV({
        separator: (source.parameters as { delimiter: string })?.delimiter || ','
      });

      const csvContent: string = file.buffer.toString('utf8').trim();
      const rows = await csv.load(csvContent).clear().parse();

      // 2) Fetch entity fields for the organization
      const entityFields = await db.entityField.findMany({
        where: {
          orgId: this.auth.oid,
          entity: EntityType.company
        }
      });

      // Separate mandatory and non-mandatory fields
      const mandatoryFields = entityFields.filter((field) => field.isMandatory);
      const nonMandatoryFields = entityFields.filter((field) => !field.isMandatory);

      // Map mandatory fields to their corresponding columns
      const entityColumnMap: Record<string, string> = {};
      for (const field of mandatoryFields) {
        const column = (source.parameters as { columns: { column: string; value: string }[] })?.columns?.find((param) => param.value === field.id);
        if (!column) {
          throw new Error(`Required column for mandatory field ${field.label} not found`);
        }
        entityColumnMap[field.key] = column.column;
      }

      // 3) Process each CSV row and collect results
      const results = await Promise.map(
        rows,
        async (row) => {
          try {
            // Extract mandatory fields
            const companyName = row[entityColumnMap['name']];
            const website = row[entityColumnMap['website']];
            if (!companyName && !website) {
              logger.warn({ msg: 'Skipping row with no company name or website', row });
              return { row, result: { success: true, skipped: true, reason: 'No company name or website' } };
            }

            // Extract non-mandatory fields
            const ideaFields: Record<string, string> = {};
            for (const field of nonMandatoryFields) {
              const column = (source.parameters as { columns: { column: string; value: string }[] })?.columns?.find((param) => param.value === field.id);
              if (column && row[column.column]) {
                ideaFields[field.id] = row[column.column];
              }
            }

            // Perform the DB upsert logic in its own transaction
            const result = await this.upsertListCompanyIdea({
              orgId: this.auth.oid,
              companyName,
              website,
              fields: Object.keys(ideaFields).length ? ideaFields : undefined,
              metadata: row,
              sourceId: source.id
            });

            return { row, result };
          } catch (error) {
            // Handle any unexpected errors during row processing
            logger.error({
              msg: 'Error processing CSV row',
              error: (error as Error).message,
              row,
              companyName: row[entityColumnMap['name']],
              website: row[entityColumnMap['website']]
            });
            return {
              row,
              result: {
                success: false,
                error: (error as Error).message || 'Unknown error processing row'
              }
            };
          }
        },
        { concurrency: CONCURRENT_TRANSACTION_COUNT }
      );

      // 4) Analyze results - count based on source linking success
      const successCount = results.filter((r) => r.result.success && !r.result.skipped).length;
      const skippedCount = results.filter((r) => r.result.success && r.result.skipped).length;
      const failureCount = results.filter((r) => !r.result.success).length;

      // Verify counts add up correctly
      const totalCounted = successCount + skippedCount + failureCount;
      if (totalCounted !== rows.length) {
        logger.error({
          msg: 'Count mismatch detected in CSV processing',
          totalProcessed: rows.length,
          successCount,
          skippedCount,
          failureCount,
          totalCounted,
          resultsLength: results.length
        });
      }

      const failures = results
        .filter((r) => !r.result.success)
        .map((r) => ({
          item: r.row[entityColumnMap['name']] || r.row[entityColumnMap['website']] || 'Unknown row',
          reason: r.result.error || 'Unknown error'
        }));

      const skipped = results
        .filter((r) => r.result.success && r.result.skipped)
        .map((r) => ({
          item: r.row[entityColumnMap['name']] || r.row[entityColumnMap['website']] || 'Unknown row',
          reason: r.result.reason || 'Skipped'
        }));

      // 5) Create a resource record for the CSV file
      const hash = crypto.createHash('md5');
      hash.update(file.buffer);
      const md5Hash = hash.digest('hex');

      await db.resource.create({
        data: {
          name: file.originalname,
          fileType: 'csv',
          fileSize: file.size,
          accessUrl: '',
          md5: md5Hash,
          createdBy: {
            connect: { id: this.auth.uid }
          }
        }
      });

      // 6) Update ListSource status to completed with detailed reason
      const reason = failureCount > 0 || skippedCount > 0 ? `Linked ${successCount} records to source, ${skippedCount} skipped, ${failureCount} failed` : undefined;

      await db.listSource.update({
        where: { id: source.id },
        data: {
          status: ListSourceStatus.completed,
          reason
        }
      });

      return {
        success: true,
        totalProcessed: rows.length,
        successCount,
        failureCount,
        skippedCount,
        failures,
        skipped
      };
    } catch (e) {
      logger.error({ msg: 'Error processing CSV file', error: e });
      // Update ListSource status to failed on error
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.failed, reason: (e as Error).message ?? 'Error processing CSV file' }
      });

      return {
        success: false,
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        skippedCount: 0,
        failures: [{ item: 'CSV Processing', reason: (e as Error).message ?? 'Error processing CSV file' }],
        skipped: []
      };
    }
  }

  /**
   * Creates ideas from a Google Maps search
   */
  async createGoogleMapsListIdeasForSource(source: ListSource): Promise<ISourceCreationResult> {
    try {
      // 1) Retrieve places from Google Maps
      const places = await GoogleMapsService.searchText(_.pick(source.parameters as object as GoogleMapsServiceSearchTextInput, ['locationBias', 'textQuery', 'radius', 'maxResultCount']));

      // 2) If no places, mark as failed quickly
      if (!places || places.length === 0) {
        await db.listSource.update({
          where: { id: source.id },
          data: { status: ListSourceStatus.failed, reason: 'No places found' }
        });
        return {
          success: false,
          totalProcessed: 0,
          successCount: 0,
          failureCount: 0,
          skippedCount: 0,
          failures: [{ item: 'Google Maps Search', reason: 'No places found' }],
          skipped: []
        };
      }

      // 3) Process each place and collect results
      const results = await Promise.map(
        places,
        async (place) => {
          const result = await this.upsertListCompanyIdea({
            orgId: this.auth.oid,
            companyName: place.displayName.text,
            website: place.websiteUri,
            metadata: place,
            sourceId: source.id
          });
          return { place, result };
        },
        { concurrency: CONCURRENT_TRANSACTION_COUNT }
      );

      // 4) Analyze results
      const successCount = results.filter((r) => r.result.success && !r.result.skipped).length;
      const skippedCount = results.filter((r) => r.result.success && r.result.skipped).length;
      const failureCount = results.filter((r) => !r.result.success).length;

      const failures = results
        .filter((r) => !r.result.success)
        .map((r) => ({
          item: r.place.displayName.text,
          reason: r.result.error || 'Unknown error'
        }));

      const skipped = results
        .filter((r) => r.result.success && r.result.skipped)
        .map((r) => ({
          item: r.place.displayName.text,
          reason: r.result.reason || 'Skipped'
        }));

      // 5) Mark the source as completed with detailed reason
      const reason = failureCount > 0 || skippedCount > 0 ? `Processed ${successCount} places successfully, ${skippedCount} skipped, ${failureCount} failed` : undefined;

      await db.listSource.update({
        where: { id: source.id },
        data: {
          status: ListSourceStatus.completed,
          reason
        }
      });

      return {
        success: true,
        totalProcessed: places.length,
        successCount,
        failureCount,
        skippedCount,
        failures,
        skipped
      };
    } catch (e) {
      logger.error({ msg: 'Error processing GoogleMaps', error: e });
      // Mark the ListSource as failed
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.failed, reason: (e as Error).message ?? 'Error processing Google Maps' }
      });

      return {
        success: false,
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        skippedCount: 0,
        failures: [{ item: 'Google Maps Processing', reason: (e as Error).message ?? 'Error processing Google Maps' }],
        skipped: []
      };
    }
  }

  /**
   * Creates ideas from filtered companies
   */
  async createCompanyFilterListIdeasForSource(source: ListSource): Promise<ISourceCreationResult> {
    try {
      // 1) Cast the `parameters` JSON field
      const parameters = source.parameters as { filters?: IFilterParameter[] };

      // 2) Validate that filters exist
      if (!parameters?.filters || !Array.isArray(parameters.filters) || parameters.filters.length === 0) {
        // Mark as completed (nothing to filter)
        await db.listSource.update({
          where: { id: source.id },
          data: { status: ListSourceStatus.completed }
        });
        return {
          success: true,
          totalProcessed: 0,
          successCount: 0,
          failureCount: 0,
          skippedCount: 0,
          failures: [],
          skipped: []
        };
      }

      // First, fetch the data outside of transaction to avoid long-running transactions
      const filters = parameters.filters || [];
      // 3) Fetch entity fields
      const entityFieldIds = filters.map((p) => p.entityField);
      const entityFields: IEntityField[] = await db.entityField.findMany({
        where: {
          id: { in: entityFieldIds }
        },
        select: {
          id: true,
          key: true,
          label: true,
          isMandatory: true
        }
      });

      // 4) Construct filters
      const companyFilters: ICompanyFilter[] = this.constructCompanyFilters(filters, entityFields);

      // 5) Fetch companies based on filters (distinct by ID)
      const companies = await db.company.findMany({
        where: { AND: companyFilters, orgId: this.auth.oid },
        distinct: ['id'],
        include: { fields: true }
      });

      // 6) Upsert each matched company as an idea
      await Promise.map(
        companies,
        async (company) => {
          await this.upsertListCompanyIdea({
            orgId: this.auth.oid,
            companyId: company.id, // important for filter-based
            companyName: company.name,
            website: company.website,
            metadata: company.fields,
            sourceId: source.id
          });
        },
        { concurrency: CONCURRENT_TRANSACTION_COUNT }
      );

      // 7) Mark list source as completed
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.completed }
      });

      return {
        success: true,
        totalProcessed: companies.length,
        successCount: companies.length,
        failureCount: 0,
        skippedCount: 0,
        failures: [],
        skipped: []
      };
    } catch (e) {
      logger.error({ msg: 'Error in createCompanyFilterListIdeasForSource', error: e });
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.failed, reason: (e as Error).message ?? 'Error processing company filter' }
      });

      return {
        success: false,
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        skippedCount: 0,
        failures: [{ item: 'Company Filter', reason: (e as Error).message ?? 'Error processing company filter' }],
        skipped: []
      };
    }
  }

  async createAIAgentListIdeasForSource(source: ListSource): Promise<ISourceCreationResult> {
    // Example stub; presumably your AI logic goes here
    await db.listSource.update({
      where: { id: source.id },
      data: {
        status: ListSourceStatus.inProgress
      }
    });

    return {
      success: true,
      totalProcessed: 0,
      successCount: 0,
      failureCount: 0,
      skippedCount: 0,
      failures: [],
      skipped: []
    };
  }

  /**
   * Central entry point to create ideas from a given source
   */
  async createListCompanyIdeasForSource(source: ListSource & { intentionSource: { sourceType: ListSourceType } }, file?: Express.Multer.File): Promise<ISourceCreationResult> {
    switch (source.intentionSource.sourceType) {
      case ListSourceType.googleMaps:
        return this.createGoogleMapsListIdeasForSource(source);
      case ListSourceType.resource:
        return this.createCSVListIdeasForSource(source, file);
      case ListSourceType.companyFilter:
        return this.createCompanyFilterListIdeasForSource(source);
      case ListSourceType.aiAgent:
        return this.createAIAgentListIdeasForSource(source);
      case ListSourceType.oceanIo:
        return this.createOceanIoListIdeasForSource(source);
      case ListSourceType.sourceScrub:
        return this.createSourceScrubIdeasForSource(source);
      case ListSourceType.exa:
        return this.createExaListIdeasForSource(source);
      default:
        return {
          success: false,
          totalProcessed: 0,
          successCount: 0,
          failureCount: 1,
          skippedCount: 0,
          failures: [{ item: 'Source Type', reason: 'List source type not implemented' }],
          skipped: []
        };
    }
  }

  /**
   * Completes a list company by creating real Company records from the short-listed ideas.
   */
  async completeListCompany(listCompanyId: string): Promise<{ createdCount: number; skippedCount: number }> {
    try {
      logger.info({ msg: `Starting to process ListCompany ${listCompanyId}` });

      // Step 1: Fetch all ListCompanyIdeas
      const allIdeas = await db.listCompanyIdea.getListCompanyIdeas({ listCompanyId });

      if (!allIdeas || allIdeas.length === 0) {
        logger.warn({ msg: `No ideas found for processing in ListCompany ${listCompanyId}` });
        // complete the list company
        await db.listCompany.update({
          where: { id: listCompanyId },
          data: { status: ListCompanyStatus.completed }
        });
        return { createdCount: 0, skippedCount: 0 };
      }

      // Step 2: Filter ideas with status "ICompanyIdeaStatus.Idea" and inShortlist
      const ideasToProcess = allIdeas.filter((idea) => idea.status === ICompanyIdeaStatus.Idea && idea.inShortlist);

      if (ideasToProcess.length === 0) {
        logger.warn({ msg: `No ideas with status 'Idea' found for ListCompany ${listCompanyId}` });
        // complete the list company
        await db.listCompany.update({
          where: { id: listCompanyId },
          data: { status: ListCompanyStatus.completed }
        });
        return { createdCount: 0, skippedCount: allIdeas.length };
      }

      logger.info({ msg: `Found ${ideasToProcess.length} ideas with status 'Idea' for processing in ListCompany ${listCompanyId}` });

      // Step 3: Fetch all active entity fields for validation
      const activeEntityFields = await db.entityField.findMany({
        where: {
          orgId: this.auth.oid,
          entity: EntityType.company,
          deletedAt: null
        }
      });

      const validEntityFieldMap = activeEntityFields.reduce(
        (map, field) => {
          map[field.id] = field;
          return map;
        },
        {} as Record<string, EntityField>
      );

      logger.debug({ msg: `Fetched ${activeEntityFields.length} active entity fields` });

      // Step 4: Wrap all operations in a transaction
      let createdCount = 0;
      let skippedCount = 0;

      await db.$transaction(async (tx) => {
        await Promise.all(
          ideasToProcess.map(async (idea) => {
            logger.debug({ msg: `Processing ListCompanyIdea ${idea.id}` });

            if (!idea.companyName || !idea.website) {
              logger.warn({ msg: `Skipping idea ${idea.id} with missing companyName or website` });
              skippedCount++;
              return;
            }

            const domain = getDomainFromUrl(idea.website);

            if (!domain) {
              logger.warn({ msg: `Skipping idea ${idea.id} with invalid website ${idea.website}` });
              skippedCount++;
              return;
            }

            // Step 4.3: Check for soft-deleted linked company and restore it
            if (idea.companyId) {
              const linkedCompany = await tx.company.findUnique({
                where: { id: idea.companyId },
                select: { deletedAt: true, deletedById: true }
              });

              if (linkedCompany?.deletedAt) {
                await tx.company.update({
                  where: { id: idea.companyId },
                  data: { deletedAt: null, deletedById: null }
                });
                logger.info({ msg: `Restored soft-deleted company ${idea.companyId} for idea ${idea.id}` });
                createdCount++;
                return;
              }
            }

            // Step 4.4: Check for an existing company with the domain
            const existingCompany = await tx.company.findFirst({
              where: {
                orgId: this.auth.oid,
                domain: domain
              }
            });

            if (existingCompany) {
              // Link the idea to the existing company
              await tx.listCompanyIdea.update({
                where: { id: idea.id },
                data: { companyId: existingCompany.id }
              });

              if (existingCompany.deletedAt) {
                await tx.company.update({
                  where: { id: existingCompany.id },
                  data: { deletedAt: null, deletedById: null }
                });
                logger.info({ msg: `Restored soft-deleted company ${existingCompany.id} for idea ${idea.id}` });
              }

              logger.info({
                msg: `Linked idea ${idea.id} to existing company ${existingCompany.id} with matching name and website`
              });
              createdCount++;
              return;
            }

            // Step 4.5: Create a new Company record
            const company = await tx.company.create({
              data: {
                name: idea.companyName,
                website: idea.website,
                domain: domain,
                orgId: this.auth.oid,
                sourceType: DataSourceType.acqwired,
                ownerId: this.auth.uid,
                createdById: this.auth.uid
              }
            });

            logger.debug({ msg: `Created company ${company.id} for idea ${idea.id}` });

            // Step 4.6: Process entity fields
            const fields = idea.fields as Record<string, string> | null;
            if (fields) {
              await Promise.all(
                Object.entries(fields).map(async ([schemaFieldId, value]) => {
                  const entityField = validEntityFieldMap[schemaFieldId];
                  if (entityField) {
                    await tx.companyField.create({
                      data: {
                        companyId: company.id,
                        schemaFieldId,
                        sourceType: DataSourceType.acqwired,
                        value
                      }
                    });
                    logger.debug({
                      msg: `Created companyField for company ${company.id}`,
                      schemaFieldId,
                      value
                    });
                  } else {
                    logger.warn({
                      msg: `Invalid schemaFieldId ${schemaFieldId} for company ${company.id}`,
                      ideaId: idea.id
                    });
                  }
                })
              );
            }

            // Step 4.7: Update ListCompanyIdea with the created Company ID
            await tx.listCompanyIdea.update({
              where: { id: idea.id },
              data: { companyId: company.id }
            });
            logger.debug({ msg: `Updated ListCompanyIdea ${idea.id} with companyId ${company.id}` });

            createdCount++;
          })
        );

        // Step 5: Update ListCompany status to completed
        await tx.listCompany.update({
          where: { id: listCompanyId },
          data: { status: ListCompanyStatus.completed }
        });

        logger.info({ msg: `Successfully completed processing for ListCompany ${listCompanyId}` });
      });

      return { createdCount, skippedCount };
    } catch (error) {
      logger.error({
        msg: `Error occurred while processing ListCompany ${listCompanyId}`,
        error: error
      });
      throw error; // Rethrow to propagate the error
    }
  }

  async createSource(listCompanyId: string, intentionSourceId: string, source: Prisma.ListSourceCreateInput, file?: Express.Multer.File): Promise<{ createdList: ListCompany | null; createdSource: any; sourceResult: ISourceCreationResult }> {
    let list: ListCompany | null = null;
    if (listCompanyId) {
      list = await db.listCompany.findUnique({
        where: {
          id: listCompanyId,
          orgId: this.auth.oid
        }
      });
    }

    let createdList: ListCompany | null = null;
    if (!list) {
      list = await db.listCompany.create({
        data: {
          name: `Untitled List ${dayjs().format('MM/DD/YYYY HH:mm:ss')}`,
          status: ListCompanyStatus.inProgress,
          organization: {
            connect: {
              id: this.auth.oid
            }
          },
          createdBy: {
            connect: {
              id: this.auth.uid
            }
          }
        }
      });
      createdList = list;
    }
    if (!list) {
      throw new Error('List not found');
    }

    if (!intentionSourceId) {
      throw new Error('Invalid intentionSourceId');
    }

    const intentionSource = await db.intentionSource.findUnique({
      where: {
        id: intentionSourceId
      }
    });

    if (!intentionSource) {
      throw new Error('Intention Source not found');
    }

    // Validate AI Agent source parameters
    if (intentionSource.sourceType === ListSourceType.aiAgent) {
      const params = source.parameters as any;
      if (!params.search) {
        throw new Error('Search field is required');
      }
      if (!params.prompt) {
        throw new Error('Objective is required');
      }
      if (!params.aiModel) {
        throw new Error('AI Model is required');
      }
      if (!params.maxCompanies) {
        throw new Error('Max number of companies is required');
      }

      // Validate prompt content
      try {
        const promptObj = JSON.parse(params.prompt);
        if (!Array.isArray(promptObj) || !promptObj.some((block) => block.type === 'paragraph' && block.children?.some((child) => child.type === 'text' && child.text.trim()))) {
          throw new Error('Objective cannot be empty');
        }
      } catch (_error) {
        throw new Error('Invalid prompt format');
      }
    }

    const createdSource = await db.listSource.create({
      data: {
        status: source.status,
        parameters: source.parameters,
        reason: source.reason,
        intentionSource: {
          connect: {
            id: intentionSource.id
          }
        },
        listCompany: {
          connect: {
            id: list.id
          }
        }
      },
      include: {
        intentionSource: true
      }
    });

    const sourceResult = await this.createListCompanyIdeasForSource(createdSource, file);

    if (this.notificationService) {
      await this.notificationService.writeData(`/organizations/${this.auth.oid}/lists/${list.id}/info`, {
        sourcesUpdatedById: this.auth.uid,
        sourcesUpdatedAt: new Date().toISOString()
      });
    }

    return { createdList, createdSource, sourceResult };
  }

  async exportListCompanyIdeas(filters: IListCompanyIdeaFilters): Promise<string> {
    if (!filters.listCompanyId) {
      throw new Error('Invalid listCompanyId');
    }

    // 1. Retrieve ideas
    const response = await db.listCompanyIdea.getFilteredListCompanyIdeas({ orgId: this.auth.oid, pageSize: 10000, ...filters });
    const listId = filters.listCompanyId;
    const ideas = response.listCompanyIdeas;

    logger.debug({ msg: 'Exporting ListCompanyIdeas', listId, ideas: ideas.length });

    // 2. Retrieve enrichments
    const enrichments = await db.enrichment.findMany({
      where: { listCompanyId: listId, orgId: this.auth.oid },
      select: {
        id: true,
        name: true,
        listColumns: true
      }
    });

    logger.debug({ msg: 'Exporting ListCompanyIdeas', listId, enrichments: enrichments.length });

    // 3. Define base headers
    const headers = ['Company Name', 'Website'];

    // 4. Add headers from Enrichments
    enrichments.forEach((enrichment) => {
      if (enrichment.listColumns) {
        const columns = enrichment.listColumns as unknown as EnrichmentColumn[];
        columns.forEach((col) => {
          headers.push(`${enrichment.name} - ${col.label}`);
        });
      }
    });

    // 5. Prepare CSV rows as array of objects
    const rows: Record<string, any>[] = ideas.map((idea) => {
      // Create a row object with the base fields
      const row: Record<string, any> = {
        'Company Name': idea.companyName || '',
        Website: idea.website || ''
      };

      // Populate enrichment fields
      enrichments.forEach((enrichment) => {
        if (enrichment.listColumns) {
          const columns = enrichment.listColumns as unknown as EnrichmentColumn[];
          columns.forEach((col) => {
            // Find the matching enrichment result

            const result = idea.enrichmentResults?.find((er) => er.enrichmentId === enrichment.id);
            const columnKey = `${enrichment.name} - ${col.label}`;

            if (result?.value) {
              row[columnKey] = getValueByKeys(result.value, col.keys) || '';
            } else {
              row[columnKey] = '';
            }
          });
        }
      });

      return row;
    });

    logger.info({ msg: 'Exporting ListCompanyIdeas', listId, rows: rows.length });

    // 6. Use json2csv to parse rows into CSV
    const parser = new Parser({ fields: headers });
    return parser.parse(rows);
  }

  async execute({ typeId, entityType, entityId }): Promise<{ entityId: string; executionId: string; fieldId: string }> {
    // Execute main database operations within a transaction
    const result = await db.$transaction(async (tx) => {
      const listCompany = await tx.listCompany.findUnique({
        where: { id: entityId, orgId: this.auth.oid },
        include: {
          listSource: true
        }
      });

      if (!listCompany) {
        throw new NotFoundError('List Company not found');
      }

      const listSource = listCompany.listSource.find((source) => source.id === typeId);
      if (!listSource) {
        logger.error({ msg: 'List Source not found', listCompany, typeId });
        throw new Error('List Company Source not found');
      }

      const execution = await this.createListCompanyExecution(tx, { typeId, entityType, entityId });

      logger.debug({ msg: 'List Company execution created', listCompany });

      await this.createQueueTask(tx, { execution });

      return {
        entityId,
        executionId: execution.id,
        fieldId: typeId
      };
    });

    await this.writeExecutionToNotificationProvider({ execution: { id: result.executionId }, entityType, entityId, fieldId: typeId });

    await db.queueTask.updateMany({
      where: {
        executionId: result.executionId
      },
      data: {
        status: ExecutionStatus.pending
      }
    });

    return result;
  }

  async getAutoCompleteSuggestions({ query, enableOceanIoSuggestions }: { query: { value: string; listCompanyId: string }; enableOceanIoSuggestions: boolean }) {
    return db.company
      .findMany({
        where: {
          AND: [
            { orgId: this.auth.oid },
            {
              OR: [{ name: { contains: query.value, mode: 'insensitive' } }, { website: { contains: query.value, mode: 'insensitive' } }]
            }
          ]
        },
        take: 3,
        select: {
          id: true,
          name: true,
          domain: true
        }
      })
      .then(async (orgCompanies) => {
        const oceanCompanies = enableOceanIoSuggestions ? await OceanService.autocomplete(query.value) : [];
        const currentCompanies = await this.getCurrentListCompanies(query);
        return {
          orgCompanies,
          oceanCompanies,
          currentCompanies
        };
      });
  }

  private getCurrentListCompanies = async (query: { value: string; listCompanyId: string }) => {
    return db.listCompanyIdea
      .findMany({
        where: {
          AND: [
            { orgId: this.auth.oid },
            { listCompanyIdeaMapping: { some: { listCompanyId: query.listCompanyId } } },
            {
              OR: [{ companyName: { contains: query.value, mode: 'insensitive' } }, { domain: { contains: query.value, mode: 'insensitive' } }]
            }
          ]
        },
        take: 3,
        select: {
          companyName: true,
          domain: true
        }
      })
      .then((orgCompanies) => {
        return orgCompanies.map((company) => ({
          name: company.companyName,
          domain: company.domain
        }));
      })
      .catch((error) => {
        logger.error({ msg: 'Error fetching current list companies', error });
        return [];
      });
  };

  async getOceanPreview(domains: string[]) {
    const oceanResponse = await OceanService.lookalikeCompanies(domains);
    if (!oceanResponse?.companies.length || oceanResponse?.companies.length === 0) {
      return { companies: [], total: 0 };
    }

    const companies: IOceanFilteredCompany[] = oceanResponse.companies.map((companyResult) => ({
      name: companyResult.company.name || '',
      domain: companyResult.company.domain || '',
      website: companyResult.company.rootUrl || '',
      logo: companyResult.company.logo || '',
      score: typeof companyResult.score === 'number' ? companyResult.score : 0
    }));

    const total: number = oceanResponse?.total ?? companies.length;

    return { companies, total };
  }

  async createOceanIoListIdeasForSource(source: ListSource): Promise<ISourceCreationResult> {
    try {
      // 1) Retrieve companies from Ocean.io
      const { domains, maxResultCount } = source.parameters as object as IOceanSourcePayload;
      const oceanResponse = await OceanService.lookalikeCompanies(domains, maxResultCount);
      if (!oceanResponse?.companies.length || oceanResponse?.companies.length === 0) {
        await db.listSource.update({
          where: { id: source.id },
          data: { status: ListSourceStatus.failed, reason: 'No companies found' }
        });
        return {
          success: false,
          totalProcessed: 0,
          successCount: 0,
          failureCount: 0,
          skippedCount: 0,
          failures: [{ item: 'Ocean.io Search', reason: 'No companies found' }],
          skipped: []
        };
      }

      // 2) Upsert each company
      await Promise.map(
        oceanResponse.companies,
        async (companyResult: IOceanCompanyResult) => {
          await this.upsertListCompanyIdea({
            orgId: this.auth.oid,
            companyName: companyResult.company.name,
            website: companyResult.company.rootUrl,
            metadata: companyResult,
            sourceId: source.id
          });
        },
        { concurrency: CONCURRENT_TRANSACTION_COUNT }
      );

      // 3) Mark the source as completed
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.completed }
      });

      return {
        success: true,
        totalProcessed: oceanResponse.companies.length,
        successCount: oceanResponse.companies.length,
        failureCount: 0,
        skippedCount: 0,
        failures: [],
        skipped: []
      };
    } catch (e) {
      logger.error({ msg: 'Error processing Ocean.io', error: e });
      // Mark the ListSource as failed
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.failed, reason: (e as Error).message ?? 'Error processing Ocean.io' }
      });

      return {
        success: false,
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        skippedCount: 0,
        failures: [{ item: 'Ocean.io Processing', reason: (e as Error).message ?? 'Error processing Ocean.io' }],
        skipped: []
      };
    }
  }

  async createExaListIdeasForSource(source: ListSource): Promise<ISourceCreationResult> {
    try {
      // 1) Retrieve companies from Exa
      const { query, maxResultCount } = source.parameters as object as IExaSearchPayload;
      const exaResponse = await ExaService.getCompanies({ query, limit: maxResultCount });
      if (!exaResponse?.length || exaResponse?.length === 0) {
        await db.listSource.update({
          where: { id: source.id },
          data: { status: ListSourceStatus.failed, reason: 'No companies found' }
        });
        return {
          success: false,
          totalProcessed: 0,
          successCount: 0,
          failureCount: 0,
          skippedCount: 0,
          failures: [{ item: 'Exa Search', reason: 'No companies found' }],
          skipped: []
        };
      }

      // 2) Upsert each company
      await Promise.map(
        exaResponse,
        async (companyResult: IExaCompany) => {
          await this.upsertListCompanyIdea({
            orgId: this.auth.oid,
            companyName: companyResult.title,
            website: companyResult.url,
            metadata: companyResult,
            sourceId: source.id
          });
        },
        { concurrency: CONCURRENT_TRANSACTION_COUNT }
      );

      // 3) Mark the source as completed
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.completed }
      });

      return {
        success: true,
        totalProcessed: exaResponse.length,
        successCount: exaResponse.length,
        failureCount: 0,
        skippedCount: 0,
        failures: [],
        skipped: []
      };
    } catch (e) {
      logger.error({ msg: 'Error processing exa', error: e });
      // Mark the ListSource as failed
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.failed, reason: (e as Error).message ?? 'Error processing Exa' }
      });

      return {
        success: false,
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        skippedCount: 0,
        failures: [{ item: 'Exa Processing', reason: (e as Error).message ?? 'Error processing Exa' }],
        skipped: []
      };
    }
  }

  async createSourceScrubIdeasForSource(source: ListSource): Promise<ISourceCreationResult> {
    try {
      if (source.parameters && typeof source.parameters === 'object') {
        if ('domains' in source.parameters) {
          return this.createSourceScrubListIdeasForSource(source);
        }

        if ('filters' in source.parameters) {
          return this.createSourceScrubCompaniesListIdeasForSource(source);
        }

        if ('sourceIds' in source.parameters) {
          return this.createSourceScrubBySourcesListIdeasForSource(source);
        }
      }

      throw new Error('Invalid parameters for SourceScrub source');
    } catch (error) {
      logger.error({ msg: 'Error processing SourceScrub source', error });
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.failed, reason: (error as Error).message ?? 'Error processing SourceScrub source' }
      });

      return {
        success: false,
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        skippedCount: 0,
        failures: [{ item: 'SourceScrub Configuration', reason: (error as Error).message ?? 'Error processing SourceScrub source' }],
        skipped: []
      };
    }
  }

  async createSourceScrubListIdeasForSource(source: ListSource): Promise<ISourceCreationResult> {
    try {
      // 1) Retrieve companies from Source scrub
      const { domains, maxResultCount } = source.parameters as object as ISourceScrubPayload;
      const response = await SourceScrubService.lookalikeCompanies({ domains, maxResultCount });
      if (!response?.length || response?.length === 0) {
        await db.listSource.update({
          where: { id: source.id },
          data: { status: ListSourceStatus.failed, reason: 'No companies found' }
        });
        return {
          success: false,
          totalProcessed: 0,
          successCount: 0,
          failureCount: 0,
          skippedCount: 0,
          failures: [{ item: 'SourceScrub Search', reason: 'No companies found' }],
          skipped: []
        };
      }

      // 2) Upsert each company
      await Promise.map(
        response,
        async (companyResult: ISourceScrubCompany) => {
          await this.upsertListCompanyIdea({
            orgId: this.auth.oid,
            companyName: companyResult.name,
            website: companyResult.website,
            metadata: companyResult,
            sourceId: source.id
          });
        },
        { concurrency: CONCURRENT_TRANSACTION_COUNT }
      );

      // 3) Mark the source as completed
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.completed }
      });

      return {
        success: true,
        totalProcessed: response.length,
        successCount: response.length,
        failureCount: 0,
        skippedCount: 0,
        failures: [],
        skipped: []
      };
    } catch (e) {
      logger.error({ msg: 'Error processing SourceScrub', error: e });
      // Mark the ListSource as failed
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.failed, reason: (e as Error).message ?? 'Error processing SourceScrub' }
      });

      return {
        success: false,
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        skippedCount: 0,
        failures: [{ item: 'SourceScrub Processing', reason: (e as Error).message ?? 'Error processing SourceScrub' }],
        skipped: []
      };
    }
  }

  async createSourceScrubCompaniesListIdeasForSource(source: ListSource): Promise<ISourceCreationResult> {
    try {
      // 1) Retrieve companies from Source scrub
      const { filters, maxResultCount } = source.parameters as object as ISourceScrubFilterPayload;
      const response = await SourceScrubService.searchCompanies({ filters, maxResultCount });
      if (!response?.length || response?.length === 0) {
        await db.listSource.update({
          where: { id: source.id },
          data: { status: ListSourceStatus.failed, reason: 'No companies found' }
        });
        return {
          success: false,
          totalProcessed: 0,
          successCount: 0,
          failureCount: 0,
          skippedCount: 0,
          failures: [{ item: 'SourceScrub Companies Search', reason: 'No companies found' }],
          skipped: []
        };
      }

      // 2) Upsert each company
      await Promise.map(
        response,
        async (companyResult: ISourceScrubCompany) => {
          await this.upsertListCompanyIdea({
            orgId: this.auth.oid,
            companyName: companyResult.name,
            website: companyResult.website,
            metadata: companyResult,
            sourceId: source.id
          });
        },
        { concurrency: CONCURRENT_TRANSACTION_COUNT }
      );

      // 3) Mark the source as completed
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.completed }
      });

      return {
        success: true,
        totalProcessed: response.length,
        successCount: response.length,
        failureCount: 0,
        skippedCount: 0,
        failures: [],
        skipped: []
      };
    } catch (e) {
      logger.error({ msg: 'Error processing SourceScrub', error: e });
      // Mark the ListSource as failed
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.failed, reason: (e as Error).message ?? 'Error processing SourceScrub' }
      });

      return {
        success: false,
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        skippedCount: 0,
        failures: [{ item: 'SourceScrub Companies Processing', reason: (e as Error).message ?? 'Error processing SourceScrub' }],
        skipped: []
      };
    }
  }

  async createSourceScrubBySourcesListIdeasForSource(source: ListSource): Promise<ISourceCreationResult> {
    try {
      // 1) Retrieve companies by sources from Source Scrub
      const parameters = source.parameters as object as ISourceScrubSourcesPayload;
      const sourceIdResponse = await SourceScrubService.getCompaniesByMultipleSources({ sourceIds: parameters.sourceIds });
      if (!sourceIdResponse?.length || sourceIdResponse?.length === 0) {
        await db.listSource.update({
          where: { id: source.id },
          data: { status: ListSourceStatus.failed, reason: 'No companies found' }
        });
        return {
          success: false,
          totalProcessed: 0,
          successCount: 0,
          failureCount: 0,
          skippedCount: 0,
          failures: [{ item: 'SourceScrub Sources Search', reason: 'No companies found' }],
          skipped: []
        };
      }

      // 2) Upsert each company
      await Promise.map(
        sourceIdResponse,
        async (companyResult: ISourceScrubCompany) => {
          await this.upsertListCompanyIdea({
            orgId: this.auth.oid,
            companyName: companyResult.name,
            website: companyResult.website,
            metadata: companyResult,
            sourceId: source.id
          });
        },
        { concurrency: CONCURRENT_TRANSACTION_COUNT }
      );

      // 3) Mark the source as completed
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.completed }
      });

      return {
        success: true,
        totalProcessed: sourceIdResponse.length,
        successCount: sourceIdResponse.length,
        failureCount: 0,
        skippedCount: 0,
        failures: [],
        skipped: []
      };
    } catch (e) {
      logger.error({ msg: 'Error processing SourceScrub', error: e });
      // Mark the ListSource as failed
      await db.listSource.update({
        where: { id: source.id },
        data: { status: ListSourceStatus.failed, reason: (e as Error).message ?? 'Error processing SourceScrub' }
      });

      return {
        success: false,
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        skippedCount: 0,
        failures: [{ item: 'SourceScrub Sources Processing', reason: (e as Error).message ?? 'Error processing SourceScrub' }],
        skipped: []
      };
    }
  }

  async getSourceScrubSourcesPreview(filters: ISourceScrubSourceRequest) {
    const sourcesResponse = await SourceScrubService.searchSources({ request: filters });
    if (!sourcesResponse?.items.length || sourcesResponse?.items.length === 0) {
      return { sources: [], total: 0 };
    }

    return { sources: sourcesResponse.items, total: sourcesResponse.total };
  }

  async getSourceScrubCompaniesPreview({ filters, maxResultCount }: { filters: SourceScrubFilters; maxResultCount: number }) {
    const sourceScrubResponse = await SourceScrubService.searchCompanies({ filters, maxResultCount });
    if (!sourceScrubResponse?.length || sourceScrubResponse?.length === 0) {
      return { companies: [], total: 0 };
    }

    const companies: ISourceScrubFilteredCompany[] = sourceScrubResponse.map((companyResult) => ({
      name: companyResult.name || '',
      domain: companyResult.domain || '',
      website: companyResult.website || '',
      logo: '',
      score: 1
    }));

    return { companies, total: companies.length };
  }

  async getSourceScrubPreview({ domains, maxResultCount }: { domains: string[]; maxResultCount: number }) {
    const sourceScrubResponse = await SourceScrubService.lookalikeCompanies({ domains, maxResultCount });
    if (!sourceScrubResponse?.length || sourceScrubResponse?.length === 0) {
      return { companies: [], total: 0 };
    }

    const companies: ISourceScrubFilteredCompany[] = sourceScrubResponse.map((companyResult) => ({
      name: companyResult.name || '',
      domain: companyResult.domain || '',
      website: companyResult.website || '',
      logo: '',
      score: 1
    }));

    return { companies, total: companies.length };
  }

  async getExaPreview(query: string) {
    const exaResponse = await ExaService.getCompanies({ query, limit: 100 });
    if (!exaResponse?.length || exaResponse?.length === 0) {
      return { companies: [], total: 0 };
    }

    const companies: IExaFilteredCompany[] = exaResponse.map((companyResult) => ({
      name: companyResult.title || '',
      domain: companyResult.url || '',
      website: companyResult.url || '',
      logo: companyResult.favicon || '',
      score: companyResult.score || 0
    }));

    const total: number = exaResponse?.length ?? companies.length;

    return { companies, total };
  }

  // ------------------------------------------------
  //                 PRIVATE METHODS
  // ------------------------------------------------

  /**
   * Helper to create or link a ListCompanyIdea record, ensuring uniqueness:
   *   1. If companyId is present, check for existing idea with { orgId, companyId }.
   *   2. Otherwise, check for existing idea with unique constraint { orgId, companyName, website }.
   *   3. If found, link that idea to the given list source if not already linked.
   *   4. If not found, create a new ListCompanyIdea and link it.
   */
  private async upsertListCompanyIdea({ orgId, companyId, companyName, website, fields, metadata, sourceId }: UpsertListCompanyIdeaParams, transactionSettings = DB_TRANSACTION_SETTING): Promise<{ success: boolean; error?: string; skipped?: boolean; reason?: string; created?: boolean }> {
    try {
      let isNewRecord = false;
      let isNewSourceMapping = false;
      let listCompanyIdea: ListCompanyIdea;

      await db.$transaction(async (tx) => {
        // 1) Find the source, because we need the listCompanyId
        const source = await tx.listSource.findUniqueOrThrow({
          where: { id: sourceId },
          select: { listCompanyId: true }
        });
        const listCompanyId = source.listCompanyId;

        // Clean up domain, website, and company name
        const domain = getDomainFromUrl(website);
        const trimmedWebsite = website ? website.trim().slice(0, 255) : null;
        const trimmedCompanyName = companyName ? companyName.trim().slice(0, 255) : null;

        // 2) Attempt to find an existing idea
        let existingIdea: ListCompanyIdea | null = null;
        if (companyId) {
          existingIdea = await tx.listCompanyIdea.findFirst({
            where: { orgId, companyId }
          });
        }

        // If not found by companyId, try orgId + domain (fallback to companyName)
        if (!existingIdea) {
          if (domain) {
            existingIdea = await tx.listCompanyIdea.findFirst({ where: { orgId, domain } });
          } else if (trimmedCompanyName) {
            existingIdea = await tx.listCompanyIdea.findFirst({ where: { orgId, companyName: trimmedCompanyName } });
          }
        }

        // 3) Upsert the idea (main record, no metadata here)
        if (existingIdea) {
          listCompanyIdea = await tx.listCompanyIdea.update({
            where: { id: existingIdea.id },
            data: {
              // Merge or overwrite fields if desired
              fields: fields !== undefined ? fields : (existingIdea.fields as Prisma.InputJsonValue | undefined),
              // Possibly update the `companyId` if it was null
              companyId: companyId ?? existingIdea.companyId
            }
          });
        } else {
          // Try to create the new record
          listCompanyIdea = await tx.listCompanyIdea.create({
            data: {
              orgId,
              companyId: companyId ?? undefined,
              companyName: trimmedCompanyName ?? null,
              website: trimmedWebsite ?? null,
              domain: domain ?? null,
              fields
            }
          });
          isNewRecord = true;
        }

        // 4) Upsert the pivot (idea, source) in ListCompanySourceIdeaMapping
        const existingSourcePivot = await tx.listCompanySourceIdeaMapping.findUnique({
          where: {
            listCompanyIdeaId_listSourceId: {
              listCompanyIdeaId: listCompanyIdea.id,
              listSourceId: sourceId
            }
          }
        });

        if (!existingSourcePivot) {
          // Create new pivot with metadata - this is a new mapping to the source
          await tx.listCompanySourceIdeaMapping.create({
            data: {
              listCompanyIdeaId: listCompanyIdea.id,
              listSourceId: sourceId,
              metadata: metadata ?? {}
            }
          });
          isNewSourceMapping = true;
        } else {
          // Optionally merge or overwrite existing pivot metadata - already mapped to source
          await tx.listCompanySourceIdeaMapping.update({
            where: {
              listCompanyIdeaId_listSourceId: {
                listCompanyIdeaId: listCompanyIdea.id,
                listSourceId: sourceId
              }
            },
            data: {
              metadata: metadata ?? existingSourcePivot.metadata
            }
          });
        }

        // 5) Upsert the pivot (list, idea) in ListCompanyIdeaMapping
        await tx.listCompanyIdeaMapping.upsert({
          where: {
            listCompanyIdeaId_listCompanyId: {
              listCompanyIdeaId: listCompanyIdea.id,
              listCompanyId
            }
          },
          update: {
            // e.g., keep existing inShortlist or set inShortlist: false
          },
          create: {
            listCompanyIdeaId: listCompanyIdea.id,
            listCompanyId,
            inShortlist: false
          }
        });

        // If everything succeeded, we're done.
        // Log successful creation for debugging
        logger.debug({
          msg: isNewRecord ? 'Successfully created new ListCompanyIdea and linked to source' : isNewSourceMapping ? 'Successfully linked existing ListCompanyIdea to new source' : 'Record already linked to this source - updated metadata',
          ideaId: listCompanyIdea.id,
          companyName,
          website,
          domain: getDomainFromUrl(website),
          sourceId,
          listCompanyId,
          orgId,
          isNewRecord,
          isNewSourceMapping
        });
      }, transactionSettings);

      // Return success only if this is a new mapping to the source
      if (isNewSourceMapping) {
        return { success: true, created: true };
      } else {
        return { success: true, skipped: true, reason: 'Record already linked to this source' };
      }
    } catch (error: any) {
      // Handle unique constraint violation on (org_id, domain) gracefully
      if (error.code === 'P2002' && error.meta?.target?.includes('domain')) {
        // This is a duplicate domain error - log it but don't fail the entire process
        logger.warn({
          msg: 'Skipping duplicate company idea due to domain constraint',
          domain: getDomainFromUrl(website),
          companyName,
          website,
          orgId,
          sourceId
        });

        // Try to find and link the existing record to this source
        try {
          const domain = getDomainFromUrl(website);
          if (domain) {
            const existingIdea = await db.listCompanyIdea.findFirst({
              where: { orgId, domain }
            });

            if (existingIdea) {
              // Find the source to get listCompanyId
              const source = await db.listSource.findUniqueOrThrow({
                where: { id: sourceId },
                select: { listCompanyId: true }
              });

              // Link the existing idea to this source if not already linked
              await db.listCompanySourceIdeaMapping.upsert({
                where: {
                  listCompanyIdeaId_listSourceId: {
                    listCompanyIdeaId: existingIdea.id,
                    listSourceId: sourceId
                  }
                },
                update: {},
                create: {
                  listCompanyIdeaId: existingIdea.id,
                  listSourceId: sourceId,
                  metadata: {}
                }
              });

              // Link to the list if not already linked
              await db.listCompanyIdeaMapping.upsert({
                where: {
                  listCompanyIdeaId_listCompanyId: {
                    listCompanyIdeaId: existingIdea.id,
                    listCompanyId: source.listCompanyId
                  }
                },
                update: {},
                create: {
                  listCompanyIdeaId: existingIdea.id,
                  listCompanyId: source.listCompanyId,
                  inShortlist: false
                }
              });

              logger.debug({
                msg: 'Successfully linked existing duplicate company to list',
                existingIdeaId: existingIdea.id,
                domain,
                companyName,
                website,
                sourceId,
                listCompanyId: source.listCompanyId,
                orgId
              });
            }
          }
        } catch (linkError) {
          // If linking fails, just log it but don't fail the process
          logger.warn({
            msg: 'Failed to link existing duplicate company to source',
            error: (linkError as Error).message,
            domain: getDomainFromUrl(website),
            sourceId
          });
        }

        // Return successfully - we've handled the duplicate gracefully
        return { success: true, skipped: true, reason: 'Duplicate domain - linked to existing record' };
      }

      // For other errors, log and rethrow
      logger.error({
        msg: 'Error in upsertListCompanyIdea while adding/updating idea',
        error: (error as Error).message,
        data: {
          orgId,
          companyId,
          companyName,
          website,
          sourceId
        }
      });

      // Return error result instead of throwing
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * Creates a new execution entry
   */
  async createListCompanyExecution(transaction: any, { typeId, entityType, entityId }): Promise<{ id: string }> {
    return transaction.execution.create({
      data: {
        orgId: this.auth.oid,
        type: ExecutionType.list,
        typeId,
        entityType,
        entityId,
        status: ExecutionStatus.pending,
        statusMessage: '',
        createdById: this.auth.uid,
        createdAt: new Date(),
        scheduledAt: new Date(),
        isSilent: false
      },
      select: { id: true }
    });
  }

  /**
   * Creates a new queue task
   */
  async createQueueTask(tx, { execution }): Promise<{ id: string }> {
    return tx.queueTask.create({
      data: {
        executionId: execution.id,
        type: QueueTaskType.listcompany,
        status: ExecutionStatus.scheduled,
        data: {},
        createdAt: new Date(),
        scheduledAt: new Date()
      },
      select: { id: true }
    });
  }

  /**
   * Sends execution info to the notification provider
   */
  async writeExecutionToNotificationProvider({ execution, entityType, entityId, fieldId }): Promise<void> {
    if (!this.notificationService) {
      return;
    }

    const task = await db.queueTask.findFirst({
      where: { executionId: execution.id },
      select: { id: true }
    });

    if (!task) {
      throw new Error('Task not found');
    }

    const entityFields = {};
    entityFields[fieldId] = {
      id: fieldId,
      status: 'processing'
    };

    const data = {
      [execution.id]: {
        id: execution.id,
        status: 'processing',
        time: new Date().toISOString(),
        entity: {
          id: entityId,
          type: entityType as string
        },
        entityFields
      }
    };

    logger.debug({ msg: 'Firebase data ready to send', data });

    await this.notificationService.writeData(`/listExecutions/organizations/${this.auth.oid}/executions`, data);
  }

  /**
   * Constructs Prisma filters for Company queries from filter parameters.
   */
  private constructCompanyFilters(parameters: IFilterParameter[], entityFields: IEntityField[]): ICompanyFilter[] {
    return parameters.map((f) => {
      // Find the corresponding entity field from the database result
      const entityField = entityFields.find((ef) => ef.id === f.entityField);
      if (!entityField) {
        throw new Error(`Entity field with id ${f.entityField} not found`);
      }

      // Determine the field name based on entity field key
      const fieldName = this.getFieldNameFromEntityKey(entityField.key);

      // Build the filter condition
      const filterCondition = this.buildFilterCondition(f, !fieldName);

      // Return the appropriate filter structure
      if (fieldName) {
        // For direct fields (name, website)
        return { [fieldName]: filterCondition };
      } else {
        // For custom fields, filterCondition is always an object with 'value' property when wrapInValue is true
        return {
          fields: {
            some: {
              schemaFieldId: f.entityField,
              ...(filterCondition as Record<string, any>)
            }
          }
        };
      }
    });
  }

  /**
   * Maps entity field keys to database field names
   */
  private getFieldNameFromEntityKey(key: string): string | null {
    const fieldMap: Record<string, string> = {
      [CompanyMandatoryFields.NAME]: 'name',
      [CompanyMandatoryFields.WEB_URL]: 'website'
    };
    return fieldMap[key] || null;
  }

  /**
   * Builds the filter condition based on filter type
   */
  private buildFilterCondition(filter: { filter: string; value: string }, wrapInValue: boolean = true) {
    const condition = (() => {
      switch (filter.filter) {
        case 'equals':
          return filter.value;
        case 'not-equal':
          return { not: filter.value };
        case 'less-than':
          return { lt: filter.value };
        case 'greater-than':
          return { gt: filter.value };
        case 'less-or-equal':
          return { lte: filter.value };
        case 'greater-or-equal':
          return { gte: filter.value };
        case 'contains':
          return {
            contains: filter.value,
            mode: Prisma.QueryMode.insensitive
          };
        case 'not-contains':
          return {
            not: {
              contains: filter.value
            },
            mode: Prisma.QueryMode.insensitive
          };
        case 'starts-with':
          return {
            startsWith: filter.value,
            mode: Prisma.QueryMode.insensitive
          };
        default:
          throw new Error(`Filter type '${filter.filter}' not implemented`);
      }
    })();

    // For custom fields, wrap in 'value' property
    // For direct fields (name, website), return as is
    return wrapInValue ? { value: condition } : condition;
  }
}
