import { logger } from '../logger';
import { validate as uuidValidate } from 'uuid';
import { db } from '../database';
import EntityService from './Entity';
import { BadRequestError, NotFoundError, ServerError } from '../lib/errors';
import { IAuth, SortOrder } from '../types/Common';
import { IContact, ICreateContactMappingResponse, ISyncData, ISyncField, IOwner } from '../types/Entity';
import { IContactDetailsParams, IContactFilters, IContactMapping, IGetEmailFieldsResult, IGetFilteredContactResult, ISetDefaultEmailParams, ISetEmailFieldParams, ISetEmailFieldResult } from '../types/Contact';
import { IField, IFilterResults, IFlattenedField, IFlattenedMapping } from '../types/Company';
import { Contact, DataSourceType, FieldMetadataType, FieldDataType, Prisma, EntityType } from '@prisma/client';
import { ContactMandatoryFields, PAGE_SIZE, DB_TRANSACTION_SETTING, prismaFieldMapping } from '../constants';
import { contactWhereClause, convertPrismaWhereToSql, generateMultipleFilterConditions } from '../models/Entity';
import { ICrmService } from '../lib/crm/ICrmService';
import { FieldType } from '../types/Field';
import { IActivityParticipantResponse } from '../types/Activity';
import ActivityService from './Activity';

enum MappingTypes {
  Company = 0,
  Email = 1,
  Contact = 2
}

export default class ContactService extends EntityService {
  constructor({ auth }: { auth: IAuth }) {
    super({ auth });
  }

  async getContactDetailsById(contactId: string) {
    const contact = await db.contact.findUnique({
      where: { id: contactId, orgId: this.auth.oid, deletedAt: null },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        sourceType: true,
        fields: {
          select: {
            id: true,
            schemaFieldId: true,
            value: true
          }
        },
        entityMapping: {
          select: {
            isPrimary: true,
            company: {
              select: {
                id: true,
                name: true,
                website: true
              }
            }
          }
        }
      }
    });

    if (!contact) {
      return null;
    }

    const enrichmentResult = await db.enrichmentResult.findFirst({
      where: {
        entityId: contact.id,
        entityType: EntityType.contact
      },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        enrichmentId: true,
        value: true,
        createdAt: true,
        execution: {
          select: {
            id: true,
            status: true,
            queueTasks: {
              select: {
                id: true,
                type: true,
                status: true
              }
            }
          }
        }
      }
    });

    return { ...contact, enrichmentResults: [enrichmentResult] };
  }

  async getContacts(request: IContactFilters): Promise<IGetFilteredContactResult | null> {
    const whereClause = contactWhereClause(request, this.auth.oid);
    const whereQuery = convertPrismaWhereToSql(whereClause);
    const activityService = new ActivityService({ auth: this.auth });

    return this.getSortedContactIds(request, whereQuery).then(async (sortedResult) => {
      const { ids: contactIds, count: recordCount } = sortedResult;

      const allRecordCount = await db.contact.count({
        where: { orgId: this.auth.oid, deletedAt: null }
      });

      return db.contact
        .getContactsWithRawWhere({
          whereClause: [{ id: { in: contactIds } }]
        })
        .then(async (rawContacts) => {
          if (!rawContacts) {
            return {
              contactList: [],
              hasRecords: false
            };
          }

          return {
            entityList: (
              await Promise.all(
                sortedResult.ids.map(async (id) => {
                  const contact = rawContacts.find((c) => c.id === id);
                  const enrichmentResults = await db.enrichmentResult.findMany({
                    where: {
                      entityId: contact?.id,
                      entityType: EntityType.contact
                    }
                  });
                  if (!contact) {
                    return null;
                  }

                  return {
                    ...contact,
                    entityMapping: contact.entityMapping.map((mapping) => {
                      return {
                        id: mapping.id.toString(),
                        companyId: mapping.company.id,
                        type: MappingTypes.Company,
                        label: `${mapping.company.name}`,
                        value: mapping.company.website,
                        isKey: false,
                        isPrimary: mapping.isPrimary
                      };
                    }),
                    enrichmentResults: enrichmentResults.map((result) => {
                      return {
                        id: result.id,
                        value: result.value,
                        createdAt: result.createdAt,
                        enrichmentId: result.enrichmentId
                      };
                    }),
                    fields: contact.fields.map((field) => {
                      return {
                        id: field.schemaField.id,
                        key: field.schemaField.key,
                        label: field.schemaField.label,
                        type: FieldType.Acqwired,
                        entityType: EntityType.contact,
                        dataType: field.schemaField.dataType,
                        value: field.value
                      };
                    }),
                    mappings: contact.entityMapping.map((mapping) => {
                      return {
                        ...mapping.company,
                        companyId: mapping.company.id,
                        isPrimary: mapping.isPrimary,
                        id: mapping.id.toString()
                      };
                    }),
                    activities: await activityService.formatActivityResponse(contact.participants as IActivityParticipantResponse[], contact.id, EntityType.contact)
                  };
                })
              )
            ).filter((c) => !!c),
            totalRecords: recordCount,
            totalPages: Math.ceil(recordCount / PAGE_SIZE) || 0,
            hasRecords: !!allRecordCount
          };
        })
        .catch((err: Error) => {
          logger.error({ msg: 'Error getting contacts', err });
          throw err;
        });
    }) as unknown as IGetFilteredContactResult;
  }

  async getContactById(contactId: string): Promise<IContact | null> {
    return db.contact.getContactById({ contactId, orgId: this.auth.oid });
  }

  async getSortedContactIds(model: IContactFilters, filterQuery: string): Promise<IFilterResults> {
    const statsLeftJoin = ` LEFT JOIN stats s ON c.id = s.type_id AND s.type = 'contact' AND s.key = 'successCount'`;
    const directFilterWhere = model.text ? ` AND (cm.name ILIKE '%${model.text}%' OR cm.website ILIKE '%${model.text}%' OR c.first_name ILIKE '%${model.text}%' OR c.last_name ILIKE '%${model.text}%' OR c.email ILIKE '%${model.text}%')` : '';
    const directFilterJoin = model.text ? `${statsLeftJoin} LEFT JOIN contact_company_mapping ccm ON c.id = ccm.contact_id AND ccm.is_primary = true LEFT JOIN company cm ON ccm.company_id = cm.id ` : statsLeftJoin;
    const selectFields = 'c.id, c.first_name, c.last_name, c.email, COALESCE(s.value, 0) AS play_run_count, c.owner_id';
    let joinCondition = '';

    if (filterQuery.length > 0) {
      filterQuery = ` WHERE ${filterQuery}${directFilterWhere}`;
    } else if (directFilterWhere.length > 0) {
      filterQuery = ` WHERE ${directFilterWhere}`;
    }

    if (model.filters && model.filters.length > 0) {
      const { joinCondition: generatedJoinCondition, filterQueryAddition } = generateMultipleFilterConditions(model.filters, 'contact_field', 'contact_id');
      joinCondition = generatedJoinCondition;
      filterQuery += filterQueryAddition;
    }

    if (model.valueStats && model.valueStats.length > 1 && !(model.valueStats[0] === 0 && model.valueStats[1] === 0)) {
      if (model.valueStats[1] > 9) {
        filterQuery = filterQuery + ` AND (COALESCE(s.value, 0) >= ${model.valueStats[0]})`;
      } else {
        filterQuery = filterQuery + ` AND (COALESCE(s.value, 0) >= ${model.valueStats[0]} AND COALESCE(s.value, 0) <= ${model.valueStats[1]})`;
      }
    }

    const sortOrder = model.sortOrder === SortOrder.DESC ? SortOrder.DESC : SortOrder.ASC;
    let sortQuery: string;

    if (!model.sortKey || model.sortKey === 'contact' || model.sortKey === 'first_name') {
      sortQuery = ` SELECT id, UPPER(fc.first_name) as sort_flag from filtered_contacts fc `;
    } else if (!model.sortKey || model.sortKey === 'last_name') {
      sortQuery = ` SELECT id, UPPER(fc.last_name) as sort_flag from filtered_contacts fc `;
    } else if (model.sortKey === 'email') {
      sortQuery = ` SELECT id, UPPER(fc.email) as sort_flag from filtered_contacts fc `;
    } else if (Object.keys(prismaFieldMapping).includes(model.sortKey)) {
      sortQuery = ` SELECT id, fc.${prismaFieldMapping[model.sortKey]} as sort_flag from filtered_contacts fc `;
    } else if (model.sortKey === 'company') {
      sortQuery = ` SELECT id, (
        SELECT UPPER(cmp.name) from contact_company_mapping mp inner join company cmp on cmp.id = mp.company_id
        WHERE mp.contact_id = fc.id and mp.is_primary=true LIMIT 1
      ) as sort_flag from filtered_contacts fc `;
    } else if (model.sortKey === 'accountOwner') {
      sortQuery = ` SELECT id, (
        SELECT UPPER(u.first_name) from "user" u where u.id = fc.owner_id limit 1
      ) as sort_flag from filtered_contacts fc `;
    } else if (model.sortKey === 'lastActivityDate') {
      sortQuery = `SELECT id, (
        SELECT MAX(created_at) from execution ex WHERE ex.entity_id = fc.id and ex.type = 'play' and entity_type = 'contact'
      ) as sort_flag from filtered_contacts fc`;
    } else {
      sortQuery = ` SELECT id, (
            SELECT value from contact_field WHERE schema_field_id = (
          SELECT id from entity_field WHERE key = '${model.sortKey}' AND entity='contact'
        ) AND contact_id = fc.id
      ) as sort_flag from filtered_contacts fc `;
    }

    const offset = PAGE_SIZE * model.pageIndex;

    const finalQuery = `WITH filtered_contacts as (
        SELECT ${selectFields} from contact c ${directFilterJoin}${joinCondition}${filterQuery}
        GROUP BY c.id, c.first_name, c.last_name, c.email, s.value
        ),
    sorted_contacts as (
        ${sortQuery}
        ORDER BY sort_flag ${sortOrder}
    ) SELECT sc.id FROM sorted_contacts sc
      LIMIT ${PAGE_SIZE} OFFSET ${offset};`;

    const finalCountQuery = `SELECT COUNT(c.id) from contact c ${directFilterJoin}${joinCondition}${filterQuery};`;
    logger.debug({ msg: 'Final query', finalQuery, finalCountQuery });

    const ids = (await db.$queryRaw`${Prisma.raw(finalQuery)}`) as { id: string }[];
    const rows = (await db.$queryRaw`${Prisma.raw(finalCountQuery)}`) as { count: number }[];
    return { ids: ids.map((a) => a.id), count: Number(rows[0].count) };
  }

  async getFields(contactId: string): Promise<IFlattenedField[] | null> {
    return db.contact
      .getFields({
        contactId,
        orgId: this.auth.oid
      })
      .then((fields: IField[] | null) => {
        if (!fields) {
          return null;
        }
        return db.contactFieldMetadata
          .findMany({
            where: {
              contactId,
              type: FieldMetadataType.default
            }
          })
          .then((defaultFields) => {
            return fields.map((f: IField) => {
              return {
                id: f.schemaField.id,
                key: f.schemaField.key,
                fkey: f.schemaField.key,
                label: f.schemaField.label,
                value: f.value,
                dataType: f.schemaField.dataType,
                isDefault: defaultFields?.some((d) => d.schemaFieldId === f.schemaField?.id && d.dataType === f.schemaField.dataType) || (f.schemaField.isMandatory && f.schemaField.key === ContactMandatoryFields.EMAIL && defaultFields?.some((d) => d.dataType === FieldDataType.email) === false)
              };
            });
          });
      });
  }

  async getMappings(contactId: string): Promise<IFlattenedMapping[] | null> {
    return db.contact.getMappings({ contactId }).then((mappings: IContactMapping[] | null): IFlattenedMapping[] | null => {
      if (!mappings) {
        return null;
      }
      return mappings.map(
        (m: IContactMapping): IFlattenedMapping => ({
          id: m.company.id,
          label: m.company.name,
          value: m.company.website,
          isKey: !!m.isKey, // @todo: Use isKey/isPrimary or key/primary
          isPrimary: !!m.isPrimary // @todo: Use isKey/isPrimary or key/primary
        })
      );
    });
  }

  async updatePrimaryCompany(contactId: string, companyId: string): Promise<boolean> {
    if (!uuidValidate(contactId)) {
      throw new BadRequestError('Invalid contact id!');
    }
    if (!uuidValidate(companyId)) {
      throw new BadRequestError('Invalid company id!');
    }

    return db
      .$transaction(async (trans): Promise<boolean> => {
        return trans.company
          .findUnique({
            where: { id: companyId, orgId: this.auth.oid },
            select: { id: true }
          })
          .then(async (company: { id: string } | null): Promise<boolean> => {
            if (!company) {
              throw new NotFoundError('Company not found');
            }

            return trans.companyContactMapping
              .updateMany({
                where: { contactId },
                data: { isPrimary: false }
              })
              .then(() =>
                trans.companyContactMapping
                  .updateMany({
                    where: { contactId, companyId },
                    data: { isPrimary: true }
                  })
                  .then((): boolean => true)
              );
          });
      })
      .catch((err: Error) => {
        logger.info({ msg: 'Prisma rollback on update primary company', err });
        throw err;
      });
  }

  async deleteContact(contactId: string): Promise<boolean> {
    if (!uuidValidate(contactId)) {
      throw new BadRequestError('Invalid contact id!');
    }
    return db.$transaction(async (tx): Promise<boolean> => {
      return tx.contact
        .update({
          where: {
            id: contactId,
            orgId: this.auth.oid
          },
          data: {
            deletedAt: new Date(),
            deletedById: this.auth.uid
          }
        })
        .then(() => {
          return db.companyContactMapping
            .deleteMany({
              where: { contactId }
            })
            .then(() => true);
        })
        .catch((err: Error) => {
          logger.info({ msg: 'Unable to delete contact', err });
          throw err;
        });
    });
  }

  async searchContact({ query }: { query: string }): Promise<IContact[] | null> {
    return db.contact.findMany({
      where: {
        AND: [
          { orgId: this.auth.oid },
          { deletedAt: null },
          {
            OR: [
              {
                firstName: { contains: query, mode: 'insensitive' }
              },
              {
                lastName: { contains: query, mode: 'insensitive' }
              },
              {
                email: { contains: query, mode: 'insensitive' }
              }
            ]
          }
        ]
      },
      orderBy: { firstName: 'asc' },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        entityMapping: {
          select: {
            companyId: true
          },
          orderBy: { isKey: 'desc' }
        }
      },
      take: 10
    });
  }

  async removeContact(id: string): Promise<boolean | null> {
    const findAcqwiredContact = db.contact.findFirst({
      where: {
        id,
        sourceType: 'acqwired'
      }
    });
    if (!findAcqwiredContact) {
      throw new BadRequestError('Cannot remove CRM contact. Only contacts created within the application (Acqwired) can be deleted.');
    }
    logger.debug({ msg: 'Soft delete contact', id });
    return db.contact
      .update({
        where: {
          id: id,
          sourceType: 'acqwired',
          orgId: this.auth.oid
        },
        data: {
          deletedAt: new Date(),
          deletedById: this.auth.uid
        }
      })
      .then((): boolean => true)
      .catch((error: Error) => {
        logger.error({ msg: 'Error to remove contact', error });
        return null;
      });
  }

  async createMapping(contactId: string, companyId: string): Promise<ICreateContactMappingResponse | null> {
    if (!uuidValidate(companyId)) {
      throw new BadRequestError('Invalid company id');
    }
    if (!uuidValidate(contactId)) {
      throw new BadRequestError('Invalid contact id');
    }

    const contact = await db.contact.findUnique({
      where: { id: contactId, orgId: this.auth.oid },
      select: { id: true }
    });

    if (!contact) {
      throw new NotFoundError('Contact not found');
    }

    const existingMapping: ICreateContactMappingResponse | null = await db.companyContactMapping.findFirst({
      where: { companyId, contactId }
    });
    if (existingMapping) {
      return existingMapping;
    }

    return this.createContactMapping(contactId, companyId);
  }

  async saveContact(contact: ISyncData, oid: string, crmInstance: ICrmService | undefined): Promise<boolean> {
    if (!crmInstance) {
      logger.error({ msg: 'Failed to sync  contact', crmId: contact?.id, e: 'CRM instance not found', contact: contact });
      return false;
    }

    const ownerId = await this.getOwnerId(contact.fields, oid, crmInstance.crmType);
    logger.debug({ msg: '1 : Going to save contact', crmId: contact?.id, contactObject: contact, ownerId });

    const { crmCompanyIds, firstName, lastName, email } = crmInstance.getContactMandatoryFields(contact);

    if (!firstName || !lastName || !email) {
      logger.warn({ crm: crmInstance.crmType, msg: 'Skipped Saving contact, due to missing mandatory fields', missingFields: `Missing Mandatory fields: ${!firstName ? 'firstName' : ''} ${!lastName ? 'lastName' : ''} ${!email ? 'email' : ''}`, contact: contact, oid: oid });
      return false;
    }

    if (firstName.length > 128) {
      logger.warn({ crm: crmInstance.crmType, msg: 'Skipped Saving contact, due to field length exceeds the limit', error: `firstName length ${firstName.length} greater than limit 128`, contact: contact, oid: oid });
      return false;
    }

    if (lastName.length > 128) {
      logger.warn({ crm: crmInstance.crmType, msg: 'Skipped Saving contact, due to field length exceeds the limit', error: `lastName length ${lastName.length} greater than limit 128`, contact: contact, oid: oid });
      return false;
    }

    if (email.length > 128) {
      logger.warn({ crm: crmInstance.crmType, msg: 'Skipped Saving contact, due to field length exceeds the limit', error: `email length ${email.length} greater than limit 128`, contact: contact, oid: oid });
      return false;
    }

    const contactDetails: IContactDetailsParams = {
      sourceType: crmInstance.dataSourceType,
      firstName: firstName,
      lastName: lastName,
      email: email,
      updatedAt: new Date(),
      ownerId: ownerId,
      deletedAt: null,
      deletedById: null
    };

    return db
      .$transaction(
        (trans) =>
          crmInstance.getOrCreateCrmContact(contact, oid, contactDetails, crmCompanyIds, trans).then(async (crmContact) => {
            logger.debug({ msg: '2 : Found or created Crm Contact data', crmId: contact?.id, crmContact });
            return this.updateContact(crmContact, contactDetails, trans).then(async (mainContact) => {
              logger.debug({ msg: '3 : Saved contact main data', crmId: contact?.id, mainContact });
              return this.deleteAndUpsertContactFields(contact, mainContact, trans).then(async () => {
                logger.debug({ msg: '4 Deleted and update contact fields', crmId: contact?.id, contactId: mainContact.id });
                return crmInstance.updateCrmContactFields(contact, oid, crmCompanyIds, trans).then(() => {
                  logger.debug({ msg: '5 Updated Crm Contact for contact', crmId: contact?.id, mainContactId: mainContact.id });
                  return true;
                });
              });
            });
          }),
        DB_TRANSACTION_SETTING
      )
      .catch((error: Error) => {
        logger.error({ msg: 'Failed to sync salesforce contact', crmId: contact?.id, e: error.message });
        return false;
      });
  }

  async updateContact(crmContact: { contactId: string }, contactDetails: IContactDetailsParams, trans: any): Promise<Contact> {
    return trans.contact
      .update({
        where: {
          id: crmContact.contactId,
          orgId: this.auth.oid
        },
        data: {
          ...contactDetails
        }
      })
      .catch((error: Error) => {
        logger.error({
          msg: 'Failed to update contact',
          e: error.message
        });
        throw error;
      });
  }

  async deleteAndUpsertContactFields(contact: ISyncData, mainContact: Contact, trans: any): Promise<void> {
    return trans.contactField
      .deleteMany({
        where: {
          contactId: mainContact.id,
          sourceType: DataSourceType.salesforce
        }
      })
      .then(() => {
        return Promise.all(
          contact.fields
            .filter((field: ISyncField) => field.value && field.schemaFieldId && field.isMandatory !== true)
            .map((field: ISyncField) => {
              return this.upsertContactField(mainContact.id, field, trans);
            })
        );
      })
      .catch((error: Error) => {
        logger.error({
          msg: 'Failed to delete and upsert contact fields',
          crmId: contact?.id,
          contactId: mainContact.id,
          e: error.message
        });
        throw error;
      });
  }

  async upsertContactField(contactId: string, field: ISyncField, trans: any): Promise<void> {
    const isObj = typeof field.value === 'object' ? JSON.stringify(field.value) : String(field.value);
    const payload = {
      value: isObj,
      sourceType: DataSourceType.salesforce,
      updatedAt: new Date()
    };

    return trans.contactField
      .upsert({
        where: {
          contactFieldMapping: {
            contactId: contactId,
            schemaFieldId: field.schemaFieldId
          }
        },
        create: {
          contactId: contactId,
          schemaFieldId: field.schemaFieldId,
          ...payload
        },
        update: {
          ...payload
        }
      })
      .catch((error: Error) => {
        logger.error({ msg: 'Failed to upsert contact field', contactId, field, e: error.message });
        throw error;
      });
  }

  async getEmailFields(): Promise<IGetEmailFieldsResult[] | null> {
    return db.contact.getEmailFields({ orgId: this.auth.oid }) as Promise<IGetEmailFieldsResult[] | null>;
  }

  async setEmailField({ schemaFieldId, value, contactId, label }: ISetEmailFieldParams): Promise<ISetEmailFieldResult | null> {
    if (!label) {
      throw new ServerError('Label cannot be empty');
    }
    if (!schemaFieldId) {
      const newSchemaField = await db.contact.createEmailField({ label: label, orgId: this.auth.oid });
      if (!newSchemaField) {
        throw new ServerError('Failed to create email field');
      }
      schemaFieldId = newSchemaField.id;
    }
    return db.contactField.upsert({
      where: {
        contactFieldMapping: {
          schemaFieldId,
          contactId
        }
      },
      update: {
        value
      },
      create: {
        contactId,
        schemaFieldId,
        value,
        sourceType: DataSourceType.acqwired
      },
      select: {
        id: true,
        value: true
      }
    });
  }

  async setDefaultEmail({ contactId, fieldKey }: ISetDefaultEmailParams): Promise<boolean | null> {
    if (!fieldKey) {
      throw new ServerError('Field key cannot be empty');
    }

    if (!uuidValidate(contactId)) {
      throw new BadRequestError('Invalid contact id!');
    }

    logger.debug({ msg: 'Setting default email field', contactId, fieldKey });

    if (fieldKey === ContactMandatoryFields.EMAIL) {
      await db.contactFieldMetadata
        .deleteMany({
          where: {
            contactId,
            type: FieldMetadataType.default,
            dataType: FieldDataType.email
          }
        })
        .catch((error) => {
          logger.error({
            msg: 'Failed to delete default email field',
            contactId,
            e: error.message
          });
          throw error;
        });
      return true;
    }

    const schemaFieldId = await db.entityField
      .findFirst({
        where: {
          key: fieldKey,
          orgId: this.auth.oid,
          entity: EntityType.contact,
          deletedAt: null
        },
        select: {
          id: true
        }
      })
      .then((field) => {
        if (field !== null) {
          return field.id;
        }
        return null;
      });

    logger.debug({ msg: 'Found schema field id', contactId, fieldKey, schemaFieldId });

    if (schemaFieldId) {
      await db.contactFieldMetadata
        .upsert({
          where: {
            contactFieldMetadata: {
              type: FieldMetadataType.default,
              contactId,
              dataType: FieldDataType.email
            }
          },
          update: {
            schemaFieldId
          },
          create: {
            contactId,
            type: FieldMetadataType.default,
            schemaFieldId,
            dataType: FieldDataType.email
          },
          select: {
            id: true
          }
        })
        .catch((error: Error) => {
          logger.error({
            msg: 'Failed to set default email field',
            contactId,
            fieldKey,
            e: error.message
          });
          throw error;
        });
      return true;
    } else {
      return false;
    }
  }

  async getEnirchmentsFields(contactId: string): Promise<IFlattenedField[] | null> {
    return db.entityField.getEnrichmentFields({ contactId, orgId: this.auth.oid, entity: EntityType.contact }).then((fields: IFlattenedField[] | null) => {
      if (!fields) {
        return null;
      }
      return fields;
    });
  }

  async getOwnersList(): Promise<IOwner[]> {
    return db.contact
      .findMany({
        select: {
          owner: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          }
        }
      })
      .then((response) => {
        const uniqueOwnersMap = new Map<string, IOwner>();
        response.forEach((contact) => {
          const { id, firstName, lastName } = contact.owner as IOwner;
          if (!uniqueOwnersMap.has(id)) {
            uniqueOwnersMap.set(id, { id, firstName, lastName });
          }
        });

        const uniqueOwners = Array.from(uniqueOwnersMap.values());
        return uniqueOwners;
      });
  }
}
