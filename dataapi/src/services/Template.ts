import Handlebars from 'handlebars';
import { logger } from '../logger';
import MustacheStatement = hbs.AST.MustacheStatement;
import PathExpression = hbs.AST.PathExpression;
import { UnprocessableContentError } from '../lib/errors';

class TemplateService {
  extractVariablesFromTemplate({ template }: { template: string }): string[] {
    let hbObj: hbs.AST.Program;
    try {
      hbObj = Handlebars.parseWithoutProcessing(template);
    } catch (error) {
      logger.error({ msg: 'Failed to parse template', error });
      throw new UnprocessableContentError('Template parse error', { error: 'HB parser error' });
    }

    const variables = hbObj.body
      .filter((node: hbs.AST.Statement): boolean => node.type === 'MustacheStatement')
      .map((node: hbs.AST.Statement): string | null => {
        try {
          return ((node as MustacheStatement).path as PathExpression).original;
        } catch (_error) {
          return null;
        }
      })
      .filter((v: string | null): boolean => !!v);
    logger.info({ msg: 'Template parse completed:', variables });
    return variables as string[];
  }

  public async getTemplate(): Promise<string> {
    return 'Template';
  }
}

export default TemplateService;
