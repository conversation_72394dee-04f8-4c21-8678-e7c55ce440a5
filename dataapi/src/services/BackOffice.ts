import { MembershipType, OrgStatus, OrgType, OrgAddress, SystemCity, SystemState, SystemCountry, EmailMessageStatus, ContactType, ContactPurpose, IntegrationProvider, IntegrationType, EnrichmentType, PlayChannel, TransactionType } from '@prisma/client';
import { IAuth, IContactInfo, IOrganization, InviteOrgRequest, InviteOrgResponse, SysAddress, SysCity, SysCountry, SysState } from '../types/Common';
import { DB_TRANSACTION_SETTING, EmailTemplate } from '../constants';

import { logger } from '../logger';
import { db } from '../database';
import config from '../config';
import { ServerError } from '../lib/errors';
import Patterns from '../utils/patterns';
import { OrganizationService } from './';
import { DefaultTime } from '../utils';

export default class BackOffice {
  auth: IAuth;
  organizationService: OrganizationService;

  constructor({ auth, organizationService }: { auth: IAuth; organizationService: OrganizationService }) {
    this.auth = auth;
    this.organizationService = organizationService || null;

    if (!auth || !auth.oid || auth.oid !== config.superadminOrgID) {
      logger.error({ error: 'Invalid organization Id', orgId: auth.oid });
      throw new ServerError('Invalid service call! Check your permissions!');
    }
    if (!organizationService) {
      throw new ServerError('Organization service not found');
    }

    this.organizationService = organizationService;
  }

  genRandomHex(size: number): string {
    return [...Array(size)].map(() => Math.floor(Math.random() * 16).toString(16)).join('');
  }

  async createOrgAndInvitation(req: InviteOrgRequest): Promise<InviteOrgResponse> {
    logger.debug({ msg: 'create Organization and Invitation', req });
    const validationErrors = await this.validateOrgRequest(req);
    if (validationErrors.length > 0) {
      logger.error({ msg: 'Validation errors', errors: validationErrors });
      throw new Error('Validation errors: ' + validationErrors.join('; '));
    }

    return db
      .$transaction(async (tx): Promise<InviteOrgResponse> => {
        logger.info({ msg: 'Creating organization' });
        const org: IOrganization = await tx.organization
          .create({
            data: this.getOrganizationPayload(req),
            select: { id: true }
          })
          .then((org) => {
            return { id: org.id };
          })
          .catch((e: Error) => {
            logger.error({ msg: 'Unable to create organization', e });
            throw new Error('Unable to create organization');
          });

        logger.debug({ msg: 'Organization created', org });

        const { id: orgId } = org;

        // Create organization contacts
        const contactData: IContactInfo[] = [];
        if (req.contact && req.contact.email) {
          contactData.push({
            type: ContactType.email,
            purpose: ContactPurpose.administration,
            value: req.contact.email,
            label: req.contact.firstName
          });
        }

        if (req.phone && req.phone.length > 0) {
          for (const phone of req.phone) {
            contactData.push({
              type: ContactType.phone,
              purpose: phone.purpose ?? ContactPurpose.billing,
              value: phone.phone,
              label: phone.contactName
            });
          }
        }

        if (contactData.length > 0) {
          await tx.orgContact
            .createMany({
              data: contactData.map((contact) => ({
                orgId: orgId,
                type: contact.type as ContactType,
                purpose: contact.purpose as ContactPurpose,
                value: contact.value,
                label: contact.label
              })),
              skipDuplicates: true
            })
            .catch((e: Error) => {
              logger.error({ msg: 'Unable to create organization contacts', e });
              throw new Error('Unable to create organization contacts');
            });
        }

        // Create organization membership (now required)
        await tx.orgMembership
          .create({
            data: {
              orgId,
              type: req.membership.type as MembershipType,
              maxUser: req.membership.maxUsers,
              tier: 'tier_standard',
              status: OrgStatus.active,
              organizationType: req.firmType as OrgType,
              creditBalance: 0 // Default credit balance is $0
            }
          })
          .catch((e: Error) => {
            logger.error({ msg: 'Unable to create organization membership', e });
            throw new Error('Unable to create organization membership');
          });

        // Create organization address (now required)
        logger.debug({ msg: 'Creating organization address' });
        await this.createOrgAddress(req.address, org, tx).catch((e: Error) => {
          logger.error({ msg: 'Unable to create organization address', e });
          throw new Error('Unable to create organization address');
        });

        // Get admin role ID
        const adminRole = await tx.userRole.findFirst({
          where: { role: 'admin' },
          select: { id: true }
        });

        if (!adminRole) {
          throw new Error('Admin role not found');
        }

        logger.debug({
          msg: 'Creating organization admin user',
          orgId,
          firstName: req.contact.firstName,
          lastName: req.contact.lastName,
          email: req.contact.email,
          active: false
        });

        // Create admin user
        const user = await tx.user
          .create({
            data: {
              email: req.contact.email,
              firstName: req.contact.firstName,
              lastName: req.contact.lastName,
              active: false,
              createdAt: new Date()
            },
            select: { id: true, email: true }
          })
          .catch((e: Error) => {
            logger.error({ msg: 'Unable to create organization admin user', e });
            throw new Error('Unable to create organization admin user');
          });

        const { id: userId } = user;

        // Create user role mapping
        await tx.orgUserRole
          .create({
            data: {
              orgId,
              userId,
              roleId: adminRole.id
            }
          })
          .catch((e: Error) => {
            logger.error({ msg: 'Unable to create user role mapping', e });
            throw new Error('Unable to create user role mapping');
          });

        // Create organization integrations if provided
        if (req.integrations && req.integrations.length > 0) {
          logger.debug({ msg: 'Creating organization integrations', orgId, userId });
          await tx.orgIntegration
            .createMany({
              data: req.integrations.map((integration) => ({
                orgId,
                provider: integration.provider as IntegrationProvider,
                name: integration.keyName,
                expiresAt: integration.activeUntil ? new Date(integration.activeUntil) : null,
                type: integration.type as IntegrationType,
                active: true,
                authToken: integration.key,
                data: { aiCredits: integration.aiCredits },
                createdById: userId
              })),
              skipDuplicates: true
            })
            .catch((e: Error) => {
              logger.error({ msg: 'Unable to create organization integrations', e });
              throw new Error('Unable to create organization integrations');
            });
        }

        // Create user calendar
        logger.debug({ msg: 'Creating user calendar', orgId, userId });
        await tx.userCalendar
          .create({
            data: {
              orgId,
              userId,
              active: true,
              weeklyHours: DefaultTime
            }
          })
          .catch((e: Error) => {
            logger.error({ msg: 'Unable to create user calendar', e });
            throw new Error('Unable to create user calendar');
          });

        const hash = this.genRandomHex(32);

        // Create user invitation
        logger.debug({ msg: 'Creating invitation', orgId, userId });
        await tx.userInvitation
          .create({
            data: {
              userId,
              orgId,
              email: req.contact.email,
              hash
            }
          })
          .catch((e: Error) => {
            logger.error({ msg: 'Unable to create user invitation', e });
            throw new Error('Unable to create user invitation');
          });

        // Create email message
        await tx.emailMessage
          .create({
            data: {
              userId,
              orgId,
              from: config.systemEmailSender,
              to: user.email,
              template: EmailTemplate.INVITE_ORGANIZATION,
              data: {
                linkDomain: config.email.data.linkDomain,
                assetDomain: config.email.data.assetDomain,
                source: config.email.data.source,
                consoleDomain: config.email.data.consoleDomain,
                firstName: req.contact.firstName,
                invitationLink: `${config.email.data.invitationLink}?hash=${hash}`,
                companyName: req.firmName
              },
              subject: config.email.subjects.invitation,
              scheduledAt: new Date(),
              status: EmailMessageStatus.raw
            }
          })
          .catch((error: Error) => {
            logger.error({ msg: 'Failed to create email message', error });
            throw new Error('Failed to create email message');
          });

        // Create mandatory entity fields
        const initialFields = await tx.entityFieldInitialData.findMany({
          select: {
            entity: true,
            label: true,
            dataType: true,
            key: true,
            isInternal: true,
            isAttachment: true,
            isMandatory: true
          }
        });

        if (initialFields.length > 0) {
          await tx.entityField
            .createMany({
              data: initialFields.map((field) => ({
                ...field,
                orgId,
                config: {
                  reconciliation: 'use_latest',
                  runOnFieldCall: {
                    enabled: true,
                    runFor: {
                      allRows: false,
                      autoRun: 'never'
                    }
                  }
                }
              }))
            })
            .catch((error: Error) => {
              logger.error({ msg: 'Failed to create mandatory fields', error });
              throw new Error('Failed to create mandatory fields');
            });
        }

        // Create organization settings

        // Get default LLM models and create org LLM models
        const defaultLlmModels = await tx.systemLlmModel.findMany({
          where: { isEnabled: true },
          select: { id: true, isDefault: true }
        });

        const defaultLlmModelId = defaultLlmModels.find((model) => model.isDefault)?.id;
        const enrichmentSettings = [
          { type: EnrichmentType.llm, defaultLlmModelId, name: 'LLM Prompt', llmModels: [] },
          { type: EnrichmentType.agent, defaultLlmModelId, name: 'AI Research Agent', llmModels: [] }
        ];

        const playSettings = [{ type: PlayChannel.email, defaultLlmModelId, name: 'Email', llmModels: [] }];

        await tx.orgSetting
          .create({
            data: {
              orgId,
              useSystemLlmSetting: true,
              enrichmentSettings,
              playSettings
            }
          })
          .catch((error: Error) => {
            logger.error({ msg: 'Failed to create organization settings', error });
            throw new Error('Failed to create organization settings');
          });

        if (defaultLlmModels.length > 0) {
          await tx.orgLlmModels
            .createMany({
              data: defaultLlmModels.map((model) => ({
                orgId,
                llmModelId: model.id,
                isEnabled: true,
                isDefault: model.isDefault
              }))
            })
            .catch((error: Error) => {
              logger.error({ msg: 'Failed to create organization LLM models', error });
              throw new Error('Failed to create organization LLM models');
            });
        }

        return {
          isInvited: true,
          orgId: org.id,
          userId: user.id
        } as InviteOrgResponse;
      }, DB_TRANSACTION_SETTING)
      .catch((err: Error) => {
        logger.error({ msg: 'Unable to create Organization and Invitation', err });
        throw new Error('Unable to create Organization and Invitation');
      });
  }

  async validateOrgRequest(req: InviteOrgRequest): Promise<string[]> {
    const errors: string[] = [];

    if (!req) {
      errors.push('Invalid organization request');
    }

    if (!req.contact || !req.contact.email || !req.contact.firstName) {
      errors.push('Contact information is required');
    }

    if (req.contact.email && !Patterns.Email.test(req.contact.email)) {
      errors.push('Invalid contact email');
    }

    if (req.contact.firstName && !Patterns.FirstName.test(req.contact.firstName)) {
      errors.push('Invalid contact first name');
    }

    // Check for duplicate user email (only active users)
    if (req.contact.email) {
      const existingUser = await db.user.findFirst({
        where: {
          email: req.contact.email,
          deletedAt: null // Only check active users (partial index constraint)
        },
        select: { id: true }
      });

      if (existingUser) {
        errors.push('A user with this email address already exists');
      }
    }

    // Validate firmType using the OrgType enum
    const validFirmTypes = Object.values(OrgType); // Get all enum values

    if (!req.firmType || !validFirmTypes.includes(req.firmType as OrgType)) {
      errors.push('Invalid firm type');
    }

    if (!req.firmName) {
      errors.push('Firm name is required');
    } else if (!Patterns.CompanyName.test(req.firmName)) {
      errors.push('Invalid last name');
    }

    if (!req.aiSummary) {
      errors.push('AI summary is required');
    }

    if (!req.website) {
      errors.push('Website URL is required');
    } else if (!Patterns.URL.test(req.website)) {
      errors.push('Invalid website URL');
    }

    // Validate address (now required)
    if (!req.address) {
      errors.push('Address information is required');
    } else {
      const { title, addressLine1, addressLine2, zipCode, city, purpose } = req.address;

      if (!title || !addressLine1 || !zipCode || !city || !city.cityName || !city.state || !city.state.state || !city.state.country || !city.state.country.isoCode) {
        errors.push('Address information is incomplete');
      }

      if (!purpose) {
        errors.push('Address purpose is required');
      }

      // Check for duplicate address (unique constraint: title, addressLine1, addressLine2, cityId)
      if (title && addressLine1 && city && city.cityName && city.state && city.state.state && city.state.country) {
        try {
          // First, we need to find or get the city ID to check for duplicates
          const existingCity = await db.systemCity.findFirst({
            where: {
              cityName: city.cityName,
              state: {
                state: city.state.state,
                country: {
                  country: city.state.country.country,
                  isoCode: city.state.country.isoCode
                }
              }
            },
            select: { id: true }
          });

          if (existingCity) {
            const existingAddress = await db.orgAddress.findFirst({
              where: {
                title,
                addressLine1,
                addressLine2: addressLine2 || null,
                cityId: existingCity.id
              },
              select: { id: true }
            });

            if (existingAddress) {
              errors.push('An address with the same title, address line 1, address line 2, and city already exists');
            }
          }
        } catch (error) {
          logger.error({ msg: 'Error checking for duplicate address', error });
          // Don't add error here as it's a validation check failure, not a business logic error
        }
      }
    }

    // Validate membership (now required)
    if (!req.membership) {
      errors.push('Membership information is required');
    } else {
      const { type, maxUsers } = req.membership;

      // Check if the membership type is valid
      const validMembershipTypes = Object.values(MembershipType);

      if (!type || !validMembershipTypes.includes(type as MembershipType)) {
        errors.push('Invalid membership type');
      }

      // Check if maxUsers is a positive number
      if (!maxUsers || isNaN(maxUsers) || maxUsers <= 0) {
        errors.push('Invalid maxUsers');
      }
    }

    // Validate integrations if they exist
    if (req.integrations) {
      for (const integration of req.integrations) {
        const { provider, keyName, key, aiCredits, type } = integration;

        if (!provider || !keyName || !key || isNaN(aiCredits) || aiCredits < 0 || !type) {
          errors.push('Integration information is invalid');
          break; // Break the loop if any integration is invalid
        }
      }
    }

    // Validate phone if it exists
    if (req.phone && req.phone.length > 0) {
      // Validate purpose using the ContactPurpose enum
      const validPurpose = Object.values(ContactPurpose); // Get all enum values
      for (const phoneNumber of req.phone) {
        const { phone, contactName, purpose } = phoneNumber;
        if (!phone || !contactName) {
          errors.push('Phone fields are incomplete');
          break; // Stop further validation if one phone entry is invalid
        } else if (purpose && !validPurpose.includes(purpose as ContactPurpose)) {
          errors.push('Invalid phone purpose');
          break; // Stop further validation if one phone entry is invalid
        }
      }
    }

    if (errors.length > 0) {
      logger.error({ msg: 'Validation errors', errors });
      return errors;
    }

    logger.debug({ msg: 'Organization request validated successfully', request: req });
    return [];
  }

  async createOrgAddress(address: SysAddress, org: IOrganization, trans): Promise<OrgAddress> {
    try {
      const { title, addressLine1, addressLine2, zipCode, city, purpose } = address;
      logger.debug({ msg: 'Creating Organization Address', address, org });

      const cityObject = await this.getOrCreateCity(city, trans);
      const addressData = {
        title,
        addressLine1,
        addressLine2,
        zipCode,
        purpose,
        cityId: cityObject.id,
        orgId: org.id
      };

      logger.debug({ msg: 'Creating Organization Address', addressData });

      // Create the organization address
      const createdAddress = await trans.orgAddress.create({
        data: addressData
      });

      logger.debug({ msg: 'Organization Address created', createdAddress });
      return createdAddress;
    } catch (error) {
      logger.error({ msg: 'Unable to create Organization Address', address, org, error });
      throw new Error('Unable to create Organization Address');
    }
  }

  async getOrCreateCity(city: SysCity, trans): Promise<SystemCity> {
    const { cityName, state, phoneCode } = city;
    const stateObject = await this.getOrCreateState(state, trans);
    // Find or create the city
    let existingCity = await trans.systemCity.findFirst({
      where: { cityName, stateId: stateObject.id }
    });

    logger.debug({ msg: 'Existing City', existingCity });

    if (!existingCity) {
      existingCity = await trans.systemCity.create({
        data: { cityName, phoneCode, state: { connect: { id: stateObject.id } } }
      });
      logger.debug({ msg: 'Existing City', existingCity });
    }
    return existingCity;
  }

  async getOrCreateState(state: SysState, trans): Promise<SystemState> {
    const { state: stateName, country } = state;
    const countryObject = await this.getOrCreateCountry(country, trans);

    // Find or create the state
    let existingState = await trans.systemState.findFirst({
      where: { state: stateName, countryId: countryObject.id }
    });
    logger.debug({ msg: 'Existing State', existingState });
    if (!existingState) {
      existingState = await trans.systemState.create({
        data: { state: stateName, country: { connect: { id: countryObject.id } } }
      });
      logger.debug({ msg: 'Existing State', existingState });
    }

    return existingState;
  }

  async getOrCreateCountry(country: SysCountry, trans): Promise<SystemCountry> {
    const { country: countryName, isoCode } = country;
    // Find or create the country
    let existingCountry = await trans.systemCountry.findUnique({ where: { country: countryName } });
    logger.debug({ msg: 'Existing Country', existingCountry });
    if (!existingCountry) {
      existingCountry = await trans.systemCountry.create({
        data: { country: countryName, phoneCode: country.phoneCode, isoCode }
      });
      logger.debug({ msg: 'Created New Country', existingCountry });
    } else {
      // Update existing country if isoCode has changed
      if (existingCountry.isoCode !== isoCode) {
        existingCountry = await trans.systemCountry.update({
          where: { id: existingCountry.id },
          data: { isoCode, phoneCode: country.phoneCode }
        });
        logger.debug({ msg: 'Updated Existing Country', existingCountry });
      }
    }
    return existingCountry;
  }

  getOrganizationPayload(req: InviteOrgRequest) {
    const payload = {
      name: req.firmName,
      aiSummary: req.aiSummary,
      website: req.website,
      createdAt: new Date()
    };

    logger.debug({ msg: 'Organization Payload', payload });
    return payload;
  }

  async getOrganizationById({ orgId, admin = false }: { orgId: string; admin?: boolean }) {
    try {
      logger.debug({ msg: 'Getting organization by ID', orgId, admin });

      const organization = await db.organization.findUnique({
        where: { id: orgId },
        include: {
          membership: true,
          addresses: {
            include: {
              city: {
                include: {
                  state: {
                    include: {
                      country: true
                    }
                  }
                }
              }
            }
          },
          orgContacts: true,
          orgIntegrations: true
        }
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Convert credit balance from cents to dollars for display
      const result = {
        ...organization,
        membership: organization.membership
          ? {
            ...organization.membership,
            creditBalance: organization.membership.creditBalance.dividedBy(100).toNumber()
          }
          : null
      };

      return result;
    } catch (error) {
      logger.error({ msg: 'Error getting organization by ID', error, orgId });
      throw error;
    }
  }

  async updateOrganization({ orgId, data }: { orgId: string; data: { name?: string; website?: string; aiSummary?: string; membership?: { type: string; organizationType: string; maxUser: number; creditBalance: number; status?: string; tier?: string; endsAt?: string } } }) {
    try {
      logger.debug({ msg: 'Updating organization', orgId, data });

      const updateData: any = {};

      // Only include fields that are present in the update data
      if (data.name !== undefined) updateData.name = data.name;
      if (data.website !== undefined) updateData.website = data.website;
      if (data.aiSummary !== undefined) updateData.aiSummary = data.aiSummary;

      // Handle membership update if present
      if (data.membership) {
        updateData.membership = {
          update: {
            type: data.membership.type,
            organizationType: data.membership.organizationType,
            maxUser: data.membership.maxUser,
            creditBalance: data.membership.creditBalance,
            status: data.membership.status,
            tier: data.membership.tier,
            endsAt: data.membership.endsAt ? new Date(data.membership.endsAt) : null
          }
        };
      }

      const organization = await db.organization.update({
        where: { id: orgId },
        data: updateData,
        include: {
          membership: true,
          addresses: {
            include: {
              city: {
                include: {
                  state: {
                    include: {
                      country: true
                    }
                  }
                }
              }
            }
          },
          orgContacts: true,
          orgIntegrations: true
        }
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      return organization;
    } catch (error) {
      logger.error({ msg: 'Error updating organization', error, orgId });
      throw error;
    }
  }

  async getAllOrganizations() {
    try {
      logger.debug({ msg: 'Getting all organizations' });

      const organizations = await db.organization.findMany({
        select: {
          id: true,
          name: true,
          website: true,
          createdAt: true,
          membership: {
            select: {
              status: true,
              organizationType: true,
              creditBalance: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return organizations.map((org) => ({
        id: org.id,
        name: org.name,
        website: org.website,
        status: org.membership?.status || 'unknown',
        type: org.membership?.organizationType || 'unknown',
        creditBalance: org.membership?.creditBalance ? org.membership.creditBalance.dividedBy(100).toNumber() : 0, // Convert cents to dollars
        createdAt: org.createdAt
      }));
    } catch (error) {
      logger.error({ msg: 'Error getting all organizations', error });
      throw error;
    }
  }

  // Credit Management Methods
  async getOrganizationCredits({ orgId }: { orgId: string }) {
    try {
      logger.debug({ msg: 'Getting organization credits overview', orgId });

      // Get current credit balance from membership
      const membership = await db.orgMembership.findFirst({
        where: { orgId },
        select: {
          creditBalance: true,
          tier: true
        }
      });

      if (!membership) {
        logger.error({ msg: 'Organization membership not found', orgId });
        throw new Error('Organization membership not found');
      }

      // Get total purchased credits
      const totalPurchased = await db.creditPurchase.aggregate({
        where: { orgId },
        _sum: {
          creditAmount: true
        }
      });

      // Get total used credits
      const totalUsed = await db.creditUsage.aggregate({
        where: { orgId },
        _sum: {
          creditCost: true
        }
      });

      logger.debug({
        msg: 'Credit aggregation results',
        orgId,
        totalPurchased: totalPurchased._sum.creditAmount?.toString(),
        totalUsed: totalUsed._sum.creditCost?.toString()
      });

      // Get active credit purchases (not expired)
      const activePurchases = await db.creditPurchase.findMany({
        where: {
          orgId,
          OR: [{ expiresAt: null }, { expiresAt: { gt: new Date() } }]
        },
        select: {
          creditAmount: true,
          expiresAt: true
        }
      });

      // Convert from cents to dollars for display
      const currentBalance = membership.creditBalance.dividedBy(100).toNumber();
      const totalPurchasedAmount = totalPurchased._sum.creditAmount?.dividedBy(100).toNumber() || 0;
      const totalUsedAmount = totalUsed._sum.creditCost?.dividedBy(100).toNumber() || 0;

      // Find earliest expiration date
      const nextExpiration = activePurchases.filter((p) => p.expiresAt).sort((a, b) => a.expiresAt!.getTime() - b.expiresAt!.getTime())[0]?.expiresAt;

      return {
        currentBalance,
        totalPurchased: totalPurchasedAmount,
        totalUsed: totalUsedAmount,
        tier: membership.tier,
        nextExpiration,
        activePurchasesCount: activePurchases.length
      };
    } catch (error) {
      logger.error({ msg: 'Error getting organization credits', error, orgId });
      throw error;
    }
  }

  async getCreditPurchases({ orgId, page = 1, limit = 10 }: { orgId: string; page?: number; limit?: number }) {
    try {
      logger.debug({ msg: 'Getting credit purchases', orgId, page, limit });

      const offset = (page - 1) * limit;

      const [purchases, total] = await Promise.all([
        db.creditPurchase.findMany({
          where: { orgId },
          select: {
            id: true,
            creditAmount: true,
            purchasePrice: true,
            expiresAt: true,
            purchasedAt: true,
            createdAt: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          skip: offset,
          take: limit
        }),
        db.creditPurchase.count({
          where: { orgId }
        })
      ]);

      return {
        purchases: purchases.map((p) => ({
          ...p,
          creditAmount: p.creditAmount.dividedBy(100).toNumber(), // Convert cents to dollars
          purchasePrice: p.purchasePrice.dividedBy(100).toNumber() // Convert cents to dollars
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error({ msg: 'Error getting credit purchases', error, orgId });
      throw error;
    }
  }

  async getCreditUsage({ orgId, page = 1, limit = 10 }: { orgId: string; page?: number; limit?: number }) {
    try {
      logger.debug({ msg: 'Getting credit usage', orgId, page, limit });

      const offset = (page - 1) * limit;

      // First, let's try a simple count to see if the table is accessible
      logger.debug({ msg: 'Testing credit usage table access', orgId });
      const testCount = await db.creditUsage.count({ where: { orgId } });
      logger.debug({ msg: 'Credit usage count result', orgId, count: testCount });

      // Try a simpler query first to isolate the issue
      logger.debug({ msg: 'Attempting simplified credit usage query', orgId });

      const [usage, total] = await Promise.all([
        db.creditUsage.findMany({
          where: { orgId },
          // Remove potentially problematic fields and add them back one by one
          select: {
            id: true,
            apiProvider: true,
            apiOperation: true,
            apiUnit: true,
            apiAmount: true,
            usedFor: true,
            tier: true,
            creditCost: true,
            realCost: true,
            createdAt: true
            // Temporarily removing metadata to see if that's the issue
          },
          orderBy: {
            createdAt: 'desc'
          },
          skip: offset,
          take: limit
        }),
        db.creditUsage.count({
          where: { orgId }
        })
      ]);

      return {
        usage: usage.map((u) => ({
          ...u,
          id: u.id.toString(), // Convert BigInt to string
          creditCost: u.creditCost.dividedBy(100).toNumber(), // Convert cents to dollars
          realCost: u.realCost.toNumber() // Real cost is already in correct format
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error: any) {
      logger.error({
        msg: 'Error getting credit usage',
        error: error.message,
        stack: error.stack,
        code: error.code,
        orgId
      });
      throw error;
    }
  }

  async getCreditTransactions({ orgId, page = 1, limit = 10 }: { orgId: string; page?: number; limit?: number }) {
    try {
      logger.debug({ msg: 'Getting credit transactions', orgId, page, limit });

      const offset = (page - 1) * limit;

      // First, let's try a simple count to see if the table is accessible
      logger.debug({ msg: 'Testing credit transaction log table access', orgId });
      const testCount = await db.creditTransactionLog.count({ where: { orgId } });
      logger.debug({ msg: 'Credit transaction log count result', orgId, count: testCount });

      const [transactions, total] = await Promise.all([
        db.creditTransactionLog.findMany({
          where: { orgId },
          select: {
            id: true,
            type: true,
            creditUsageId: true,
            creditPurchaseId: true,
            creditAmount: true,
            metadata: true,
            createdAt: true,
            creditUsage: {
              select: {
                apiProvider: true,
                apiOperation: true,
                usedFor: true
              }
            },
            creditPurchase: {
              select: {
                purchasePrice: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          skip: offset,
          take: limit
        }),
        db.creditTransactionLog.count({
          where: { orgId }
        })
      ]);

      return {
        transactions: transactions.map((t) => ({
          ...t,
          id: t.id.toString(), // Convert BigInt to string
          creditUsageId: t.creditUsageId?.toString() || null, // Convert BigInt to string
          creditAmount: t.creditAmount.dividedBy(100).toNumber(), // Convert cents to dollars
          creditPurchase: t.creditPurchase
            ? {
              ...t.creditPurchase,
              purchasePrice: t.creditPurchase.purchasePrice.dividedBy(100).toNumber() // Convert cents to dollars
            }
            : null
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error: any) {
      logger.error({
        msg: 'Error getting credit transactions',
        error: error.message,
        stack: error.stack,
        code: error.code,
        orgId
      });
      throw error;
    }
  }

  async addCreditPurchase({ orgId, creditPurchase, userId }: { orgId: string; creditPurchase: any; userId: string }) {
    try {
      logger.debug({ msg: 'Adding credit purchase', orgId, creditPurchase, userId });

      // Validate credit purchase data
      const errors: string[] = [];

      if (!creditPurchase.creditAmount || creditPurchase.creditAmount <= 0) {
        errors.push('Credit amount must be greater than 0');
      }

      if (!creditPurchase.purchasePrice || creditPurchase.purchasePrice <= 0) {
        errors.push('Purchase price must be greater than 0');
      }

      // Validate expiration date if provided
      if (creditPurchase.expiresAt) {
        const expirationDate = new Date(creditPurchase.expiresAt);
        const now = new Date();
        if (expirationDate <= now) {
          errors.push('Expiration date must be in the future');
        }
      }

      if (errors.length > 0) {
        throw new Error(`Validation errors: ${errors.join('; ')}`);
      }

      // Verify organization exists
      const organization = await db.organization.findUnique({
        where: { id: orgId },
        include: {
          membership: true
        }
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      return await db.$transaction(async (tx) => {
        // Create credit purchase record (convert dollars to cents for storage)
        const purchase = await tx.creditPurchase.create({
          data: {
            orgId,
            creditAmount: creditPurchase.creditAmount * 100, // Convert dollars to cents
            purchasePrice: creditPurchase.purchasePrice * 100, // Convert dollars to cents
            expiresAt: creditPurchase.expiresAt ? new Date(creditPurchase.expiresAt) : null,
            purchasedAt: new Date()
          }
        });

        // Create transaction log entry (convert dollars to cents for storage)
        await tx.creditTransactionLog.create({
          data: {
            orgId,
            type: TransactionType.purchase,
            creditPurchaseId: purchase.id,
            creditAmount: creditPurchase.creditAmount * 100, // Convert dollars to cents
            metadata: {
              purchasePrice: creditPurchase.purchasePrice * 100, // Convert dollars to cents
              addedBy: userId,
              notes: creditPurchase.notes || null
            }
          }
        });

        // Update organization credit balance (convert dollars to cents for storage)
        const creditAmountInCents = creditPurchase.creditAmount * 100;
        const newBalance = organization.membership!.creditBalance.plus(creditAmountInCents);

        await tx.orgMembership.update({
          where: { orgId },
          data: {
            creditBalance: newBalance
          }
        });

        logger.info({
          msg: 'Credit purchase added successfully',
          orgId,
          purchaseId: purchase.id,
          creditAmount: creditPurchase.creditAmount,
          newBalance: newBalance.dividedBy(100).toNumber() // Log in dollars
        });

        return {
          purchase: {
            ...purchase,
            creditAmount: purchase.creditAmount.dividedBy(100).toNumber(), // Return in dollars
            purchasePrice: purchase.purchasePrice.dividedBy(100).toNumber() // Return in dollars
          },
          newBalance: newBalance.dividedBy(100).toNumber() // Return in dollars
        };
      });
    } catch (error) {
      logger.error({ msg: 'Error adding credit purchase', error, orgId });
      throw error;
    }
  }

  // Address Management
  async addOrganizationAddress({ orgId, address }: { orgId: string; address: any }) {
    try {
      logger.debug({ msg: 'Adding organization address', orgId, address });

      return db.$transaction(async (tx) => {
        const cityObject = await this.getOrCreateCity(address.city, tx);

        const newAddress = await tx.orgAddress.create({
          data: {
            orgId,
            title: address.title,
            addressLine1: address.addressLine1,
            addressLine2: address.addressLine2,
            zipCode: address.zipCode,
            purpose: address.purpose,
            cityId: cityObject.id
          },
          include: {
            city: {
              include: {
                state: {
                  include: {
                    country: true
                  }
                }
              }
            }
          }
        });

        return newAddress;
      });
    } catch (error) {
      logger.error({ msg: 'Error adding organization address', error, orgId });
      throw error;
    }
  }

  async updateOrganizationAddress({ addressId, address }: { addressId: string; address: any }) {
    try {
      logger.debug({ msg: 'Updating organization address', addressId, address });

      return db.$transaction(async (tx) => {
        const cityObject = await this.getOrCreateCity(address.city, tx);

        const updatedAddress = await tx.orgAddress.update({
          where: { id: addressId },
          data: {
            title: address.title,
            addressLine1: address.addressLine1,
            addressLine2: address.addressLine2,
            zipCode: address.zipCode,
            purpose: address.purpose,
            cityId: cityObject.id
          },
          include: {
            city: {
              include: {
                state: {
                  include: {
                    country: true
                  }
                }
              }
            }
          }
        });

        return updatedAddress;
      });
    } catch (error) {
      logger.error({ msg: 'Error updating organization address', error, addressId });
      throw error;
    }
  }

  async deleteOrganizationAddress({ addressId }: { addressId: string }) {
    try {
      logger.debug({ msg: 'Deleting organization address', addressId });

      await db.orgAddress.delete({
        where: { id: addressId }
      });

      return { success: true };
    } catch (error) {
      logger.error({ msg: 'Error deleting organization address', error, addressId });
      throw error;
    }
  }

  // Contact Management
  async addOrganizationContact({ orgId, contact }: { orgId: string; contact: any }) {
    try {
      logger.debug({ msg: 'Adding organization contact', orgId, contact });

      const newContact = await db.orgContact.create({
        data: {
          orgId,
          type: contact.type,
          purpose: contact.purpose,
          value: contact.value,
          label: contact.label
        }
      });

      return newContact;
    } catch (error) {
      logger.error({ msg: 'Error adding organization contact', error, orgId });
      throw error;
    }
  }

  async updateOrganizationContact({ contactId, contact }: { contactId: string; contact: any }) {
    try {
      logger.debug({ msg: 'Updating organization contact', contactId, contact });

      const updatedContact = await db.orgContact.update({
        where: { id: contactId },
        data: {
          type: contact.type,
          purpose: contact.purpose,
          value: contact.value,
          label: contact.label
        }
      });

      return updatedContact;
    } catch (error) {
      logger.error({ msg: 'Error updating organization contact', error, contactId });
      throw error;
    }
  }

  async deleteOrganizationContact({ contactId }: { contactId: string }) {
    try {
      logger.debug({ msg: 'Deleting organization contact', contactId });

      await db.orgContact.delete({
        where: { id: contactId }
      });

      return { success: true };
    } catch (error) {
      logger.error({ msg: 'Error deleting organization contact', error, contactId });
      throw error;
    }
  }

  // Integration Management
  async addOrganizationIntegration({ orgId, integration, userId }: { orgId: string; integration: any; userId: string }) {
    try {
      logger.debug({ msg: 'Adding organization integration', orgId, integration });

      const newIntegration = await db.orgIntegration.create({
        data: {
          orgId,
          provider: integration.provider,
          name: integration.name,
          type: integration.type,
          active: integration.active || true,
          expiresAt: integration.expiresAt ? new Date(integration.expiresAt) : null,
          authToken: integration.key,
          data: integration.data || {},
          createdById: userId
        }
      });

      return newIntegration;
    } catch (error) {
      logger.error({ msg: 'Error adding organization integration', error, orgId });
      throw error;
    }
  }

  async updateOrganizationIntegration({ integrationId, integration }: { integrationId: string; integration: any }) {
    try {
      logger.debug({ msg: 'Updating organization integration', integrationId, integration });

      const updateData: any = {
        provider: integration.provider,
        name: integration.name,
        type: integration.type,
        active: integration.active,
        expiresAt: integration.expiresAt ? new Date(integration.expiresAt) : null,
        data: integration.data || {}
      };

      // Only update authToken if a new key is provided (for security)
      if (integration.key && integration.key.trim() !== '') {
        updateData.authToken = integration.key;
      }

      const updatedIntegration = await db.orgIntegration.update({
        where: { id: integrationId },
        data: updateData
      });

      return updatedIntegration;
    } catch (error) {
      logger.error({ msg: 'Error updating organization integration', error, integrationId });
      throw error;
    }
  }

  async deleteOrganizationIntegration({ integrationId }: { integrationId: string }) {
    try {
      logger.debug({ msg: 'Deleting organization integration', integrationId });

      await db.orgIntegration.delete({
        where: { id: integrationId }
      });

      return { success: true };
    } catch (error) {
      logger.error({ msg: 'Error deleting organization integration', error, integrationId });
      throw error;
    }
  }
}
