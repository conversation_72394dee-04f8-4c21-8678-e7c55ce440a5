import { APIError } from '../lib';
import { logger } from '../logger';
import { db } from '../database';
import bcrypt from 'bcryptjs';
import config from '../config';
import { EmailTemplate } from '../constants';
import { generateKeyPair } from '../lib/utils';
import { TokenService, EmailService } from '../services';
import { Prisma } from '@prisma/client';

export default class AuthService {
  emailService: EmailService | null;
  constructor(emailService: EmailService | null = null) {
    this.emailService = emailService;
  }

  async checkResetPasswordHash(hash) {
    return db.user
      .findFirst({
        where: { AND: [{ resetHash: hash }, { resetExpiresAt: { gt: new Date() } }] }
      })
      .then((user) => {
        if (!user) {
          throw new Error('Invalid hash!');
        }
        return user;
      });
  }

  async setPassword(uid: string, password: string) {
    return db.user
      .findUnique({ where: { id: uid } })
      .then(async (user) => {
        if (!user) {
          throw new APIError('', {
            code: 404,
            details: 'User not found'
          });
        }
        const salt = await bcrypt.genSalt(12);
        const data: { password: string; invalidatedAt: Date | null; resetHash: string; uniqueKey: string } = { password, invalidatedAt: null, resetHash: '', uniqueKey: '' };
        data.password = await bcrypt.hash(password, salt);
        data.uniqueKey = generateKeyPair();
        if (user.resetHash) {
          data.invalidatedAt = new Date();
          data.resetHash = '';
        }
        await db.user.update({
          where: {
            id: uid
          },
          data
        });
      })
      .catch((_error) => {
        throw new APIError('', {
          code: 404,
          details: 'User not found'
        });
      });
  }

  async revokeUserTokens({ userId }) {
    const updatedUser = await db.user.update({
      where: { id: userId },
      data: { uniqueKey: null }
    });
    return updatedUser || false;
  }

  async getUserByEmail(email) {
    return db.user.findFirst({
      where: { email, invitationAccepted: true, active: true, deletedAt: null },
      include: {
        role: true
      }
    });
  }

  async setEmailService(emailService: EmailService) {
    this.emailService = emailService;
  }

  genRandomHex(size: number) {
    return [...Array(size)].map(() => Math.floor(Math.random() * 16).toString(16)).join('');
  }

  async createResetPasswordLink(email: string) {
    logger.info({ msg: 'Creating reset link', email });
    const resetHash = this.genRandomHex(32);
    const resetExpiresAt = new Date();
    resetExpiresAt.setHours(resetExpiresAt.getHours() + 24);

    return db.user
      .updateMany({
        where: { email },
        data: { resetHash, resetExpiresAt }
      })
      .then(async () => {
        if (!this.emailService) {
          throw new Error('Email service does not exists!');
        }
        const updatedUser = await db.user.findFirst({
          where: { email }
        });

        return this.emailService
          .createEmail({
            from: config.systemEmailSender,
            to: email,
            template: EmailTemplate.RESET_PASSWORD,
            data: {
              linkDomain: config.email.data.linkDomain,
              assetDomain: config.email.data.assetDomain,
              source: config.email.data.source,
              consoleDomain: config.email.data.consoleDomain,
              firstName: updatedUser?.firstName,
              resetLink: `/account/resetpassword/${resetHash}`
            },
            subject: config.email.subjects.resetPassword
          })
          .then(() => true)
          .catch((err) => {
            logger.error({ msg: 'Failed to send email', err });
            return new APIError('', {
              code: 500,
              details: 'Could not send email'
            });
          });
      })
      .catch((_saveError) => {
        logger.error({ msg: 'Failed to resetpassword', _saveError });
        return new APIError('', {
          code: 500,
          details: 'Could not reset password'
        });
      });
  }

  async authenticate({ password, email }) {
    const result = await this.getUser({
      email: email,
      deletedAt: null,
      active: true,
      invitationAccepted: true
    });

    logger.info({ msg: 'Existence result', result });

    if (!result) {
      logger.info({ msg: 'No result! Throw error' });
      throw new Error('Invalid credentials!');
    }

    const isPassValid = await bcrypt.compare(password, result.password);

    logger.info({ msg: 'Password Validation result', isPassValid });
    if (!isPassValid) {
      throw new Error('Wrong credentials');
    }

    return result;
  }

  async isUserExistsById({ userId }) {
    return this.getUser({ id: userId });
  }

  async getUser(query?: Prisma.UserWhereInput) {
    try {
      const user = await db.user.findFirst({
        where: query,
        include: {
          role: {
            include: { userRole: true }
          }
        }
      });

      if (user && !user.uniqueKey) {
        const uniqueKey = generateKeyPair();
        const updatedUser = await db.user.update({
          where: { id: user.id },
          data: { uniqueKey: uniqueKey },
          include: {
            role: {
              include: { userRole: true }
            }
          }
        });
        return updatedUser || null;
      }

      return user ? user : null;
    } catch (error) {
      logger.error({ msg: 'Error in getting user', error });
      return null;
    }
  }

  async refreshAccessToken(data, appSource = 'console') {
    const { ip, device, uid: id } = data;
    const user = await db.user.findUnique({
      where: { id },
      include: {
        role: {
          include: { userRole: true }
        }
      }
    });

    if (!user) {
      return { accessToken: null };
    }

    (user as typeof user & { ip: string }).ip = ip;
    (user as typeof user & { device: string }).device = device;
    (user as typeof user & { appSource: string }).appSource = appSource;

    const tokenObject = await TokenService.generateAccessToken(user, appSource);

    if (!tokenObject) {
      return { accessToken: null };
    }

    const { accessToken } = tokenObject;
    return { accessToken };
  }

  async authorize(user, appSource = 'console'): Promise<{ accessToken: null; refreshToken: null }> {
    const tokenObject = await TokenService.generateAccessToken(user, appSource);
    const { refreshToken } = await TokenService.generateRefreshToken(user, appSource);

    if (!tokenObject) {
      return { accessToken: null, refreshToken: null };
    }

    const { accessToken } = tokenObject;

    return { accessToken, refreshToken };
  }

  async checkSuperAdminAccess(userId: string): Promise<boolean> {
    try {
      const user = await db.user.findUnique({
        where: { id: userId },
        include: {
          role: {
            include: {
              userRole: true,
              organization: true
            }
          }
        }
      });

      if (!user || !user.role) {
        return false;
      }

      // Check if user has admin role or is from superadmin organization
      const hasAdminRole = user.role.userRole?.role === 'admin';
      const isSuperAdminOrg = user.role.orgId === config.superadminOrgID;

      return hasAdminRole && isSuperAdminOrg;
    } catch (error) {
      logger.error({ msg: 'Error checking admin access', error, userId });
      return false;
    }
  }
}
