import { logger } from '../logger';
import { db } from '../database';
import { Company, DataSourceType, EntityType, Prisma } from '@prisma/client';
import EntityService from './Entity';
import { validate as uuidValidate } from 'uuid';
import { BadRequestError, NotFoundError } from '../lib/errors';
import { ICompany, ICreateCompanyMappingResponse, ICreateContactMappingResponse, IOwner, ISyncData, ISyncField } from '../types/Entity';
import { ICompanyFilters, ICompanyGetResults, ICompanyMapping, IFlattenedField, IFlattenedMapping, ICompanyDetailsParams, IFilterResults, IGetFilteredCompanyResult } from '../types/Company';
import { IAuth, SortOrder } from '../types/Common';
import { PAGE_SIZE, DB_TRANSACTION_SETTING, prismaFieldMapping } from '../constants';
import { companyWhereClause, convertPrismaWhereToSql, generateMultipleFilterConditions } from '../models/Entity';
import { ICrmService } from '../lib/crm/ICrmService';
import { FieldType, ISimpleField } from '../types/Field';
import { Parser } from 'json2csv';
import ActivityService from './Activity';
import { IActivityParticipantResponse } from '../types/Activity';
import { getDomainFromUrl } from '../lib/utils';

enum MappingTypes {
  Company = 0,
  Email = 1,
  Contact = 2
}

export default class CompanyService extends EntityService {
  constructor({ auth }: { auth: IAuth }) {
    super({ auth });
  }

  async getCompanyDetailsById(companyId: string) {
    const company = await db.company.findUnique({
      where: { id: companyId, orgId: this.auth.oid, deletedAt: null },
      select: {
        id: true,
        name: true,
        website: true,
        sourceType: true,
        fields: {
          select: {
            id: true,
            schemaFieldId: true,
            value: true
          }
        },
        entityMapping: {
          select: {
            isKey: true,
            contact: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        }
      }
    });

    if (!company) {
      return null;
    }

    const enrichmentResult = await db.enrichmentResult.findFirst({
      where: {
        entityId: company.id,
        entityType: EntityType.company
      },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        enrichmentId: true,
        value: true,
        createdAt: true,
        execution: {
          select: {
            id: true,
            status: true,
            queueTasks: {
              select: {
                id: true,
                type: true,
                status: true
              }
            }
          }
        }
      }
    });

    return { ...company, enrichmentResults: [enrichmentResult] };
  }

  async getFilteredCompanies(request: ICompanyFilters, allRecords: boolean): Promise<IGetFilteredCompanyResult | null> {
    const whereClause = companyWhereClause(request, this.auth.oid);
    const activityService = new ActivityService({ auth: this.auth });

    let whereQuery = convertPrismaWhereToSql(whereClause);
    if (request.lists && request.lists?.length > 0) {
      whereQuery += (whereQuery?.length > 0 ? ' and ' : '') + ` lcim.list_company_id IN (${request.lists.map((a) => `'${a}'`).join(',')})`;
    }

    return this.getSortedCompanyIds(request, whereQuery, allRecords)
      .catch((err) => {
        logger.error({ msg: 'Error getting sorted company ids', err });
        throw err;
      })
      .then(async (sortedResult) => {
        const { ids: companyIds, count: recordCount } = sortedResult;

        const allRecordCount = await db.company.count({
          where: { orgId: this.auth.oid, deletedAt: null }
        });

        return db.company
          .getCompaniesWithRawWhere({
            whereClause: [{ id: { in: companyIds } }]
          })
          .then(async (rawCompanies) => {
            if (!rawCompanies) {
              return {
                entityList: [],
                hasRecords: false
              };
            }

            return {
              entityList: (
                await Promise.all(
                  sortedResult.ids.map(async (id) => {
                    const company = rawCompanies.find((c) => c.id === id);
                    const enrichmentResults = await db.enrichmentResult.findMany({
                      where: {
                        entityId: company?.id,
                        entityType: EntityType.company
                      }
                    });
                    if (!company) {
                      return null;
                    }

                    return {
                      ...company,
                      entityMapping: company.entityMapping.map((mapping) => {
                        return {
                          id: mapping.id.toString(),
                          contactId: mapping.contact.id,
                          type: MappingTypes.Contact,
                          label: `${mapping.contact.firstName} ${mapping.contact.lastName}`,
                          value: mapping.contact.email,
                          isKey: mapping.isKey,
                          isPrimary: false
                        };
                      }),
                      enrichmentResults: enrichmentResults.map((result) => {
                        return {
                          id: result.id,
                          value: result.value,
                          createdAt: result.createdAt,
                          enrichmentId: result.enrichmentId
                        };
                      }),
                      fields: company.fields.map((field) => {
                        return {
                          id: field.schemaField.id,
                          key: field.schemaField.key,
                          label: field.schemaField.label,
                          type: FieldType.Acqwired,
                          entityType: EntityType.company,
                          dataType: field.schemaField.dataType,
                          value: field.value
                        };
                      }),
                      mappings: company.entityMapping.map((mapping) => {
                        return {
                          ...mapping.contact,
                          contactId: mapping.contact.id,
                          isKey: mapping.isKey,
                          id: mapping.id.toString()
                        };
                      }),
                      activities: await activityService.formatActivityResponse(company.participants as IActivityParticipantResponse[], company.id, EntityType.company)
                    };
                  })
                )
              ).filter((c) => !!c),
              totalRecords: recordCount,
              totalPages: Math.ceil(recordCount / PAGE_SIZE) || 0,
              hasRecords: !!allRecordCount
            };
          })
          .catch((err: Error) => {
            logger.error({ msg: 'Error getting companies', err });
            throw err;
          });
      }) as unknown as IGetFilteredCompanyResult;
  }

  async prepareCompaniesCsvData(companies: IGetFilteredCompanyResult): Promise<string> {
    if (!companies.entityList) {
      throw new Error('No companies found');
    }

    // Fetch user-specific table layout
    const tableLayout = await db.userTableLayout.findFirst({
      where: {
        orgId: this.auth.oid,
        userId: this.auth.uid,
        entityType: EntityType.company
      },
      select: {
        value: true // this is JSON
      }
    });

    // Safely parse table layout value
    let layoutFields: { id: string; type: string }[] = [];
    if (tableLayout?.value) {
      layoutFields = tableLayout.value as { id: string; type: string }[];
    }

    const dynamicFieldIds = layoutFields.filter((field) => field.type === 'field').map((field) => field.id);

    let entityFields: { id: string; key: string; label: string }[] = [];
    if (dynamicFieldIds.length) {
      entityFields = await db.entityField.findMany({
        where: {
          orgId: this.auth.oid,
          deletedAt: null,
          entity: EntityType.company,
          id: { in: dynamicFieldIds }
        },
        select: {
          id: true,
          key: true,
          label: true
        }
      });
    }

    // Prepare CSV rows
    const csvData = companies.entityList.map((company) => {
      const row: Record<string, any> = {
        Name: company.name,
        Website: company.website,
        Owner: company.owner ? `${company.owner.firstName} ${company.owner.lastName}` : ''
      };

      // Add dynamic fields based on `entityFields`
      if (entityFields.length) {
        entityFields.forEach((fieldInfo) => {
          const dynamicField = company.fields?.find((f) => f.key === fieldInfo.key);
          row[fieldInfo.label] = dynamicField ? dynamicField.value : '';
        });
      }

      return row;
    });

    // Convert JSON to CSV
    const json2csvParser = new Parser();
    return json2csvParser.parse(csvData);
  }

  async getSortedCompanyIds(model: ICompanyFilters, filterQuery: string, allRecords: boolean): Promise<IFilterResults> {
    logger.debug({ msg: 'Getting sorted company Ids' });

    const statsLeftJoin = ` LEFT JOIN stats s ON c.id = s.type_id AND s.type = 'company' AND s.key = 'successCount'`;
    const directFilterWhere = model.text ? ` AND (c.name ILIKE '%${model.text}%' OR c.website ILIKE '%${model.text}%' OR ct.first_name ILIKE '%${model.text}%' OR ct.last_name ILIKE '%${model.text}%' OR ct.email ILIKE '%${model.text}%')` : '';
    const listLeftJoin = ' JOIN list_company_idea lci ON c.id = lci.company_id JOIN list_company_idea_mapping lcim ON lci.id = lcim.idea_id AND lcim.in_shortlist = true';

    let directFilterJoin = model.text ? `${statsLeftJoin} LEFT JOIN contact_company_mapping ccm ON c.id = ccm.company_id AND ccm.is_key = true LEFT JOIN contact ct ON ccm.contact_id = ct.id ` : statsLeftJoin;
    directFilterJoin += model.lists && model.lists?.length > 0 ? listLeftJoin : '';

    const selectFields = 'c.id, c.name, c.website, c.owner_id, COALESCE(s.value, 0) AS play_run_count';
    let joinCondition = '';

    if (filterQuery.length > 0) {
      filterQuery = ` WHERE ${filterQuery}${directFilterWhere}`;
    } else if (directFilterWhere.length > 0) {
      filterQuery = ` WHERE ${directFilterWhere}`;
    }

    if (model.filters && model.filters.length > 0) {
      const { joinCondition: generatedJoinCondition, filterQueryAddition } = generateMultipleFilterConditions(model.filters, 'company_field', 'company_id');
      joinCondition = generatedJoinCondition;
      filterQuery += filterQueryAddition;
    }

    if (model.valueStats && model.valueStats.length > 1 && !(model.valueStats[0] === 0 && model.valueStats[1] === 0)) {
      if (model.valueStats[1] > 9) {
        filterQuery = filterQuery + ` AND (COALESCE(s.value, 0) >= ${model.valueStats[0]})`;
      } else {
        filterQuery = filterQuery + ` AND (COALESCE(s.value, 0) >= ${model.valueStats[0]} AND COALESCE(s.value, 0) <= ${model.valueStats[1]})`;
      }
    }
    const sortOrder = model.sortOrder === SortOrder.DESC ? SortOrder.DESC : SortOrder.ASC;
    let sortQuery: string;

    if (!model.sortKey || model.sortKey === 'company' || model.sortKey === 'name') {
      sortQuery = ` SELECT id, UPPER(fc.name) as sort_flag
                    from filtered_companies fc `;
    } else if (model.sortKey === 'website') {
      sortQuery = ` SELECT id, UPPER(fc.website) as sort_flag
                    from filtered_companies fc `;
    } else if (Object.keys(prismaFieldMapping).includes(model.sortKey)) {
      sortQuery = ` SELECT id, fc.${prismaFieldMapping[model.sortKey]} as sort_flag
                    from filtered_companies fc `;
    } else if (model.sortKey === 'accountOwner') {
      sortQuery = ` SELECT id,
                           (SELECT UPPER(u.first_name)
                            from "user" u
                            where u.id = fc.owner_id limit 1 ) as sort_flag
                    from filtered_companies fc `;
    } else if (model.sortKey === 'keyContact') {
      sortQuery = ` SELECT id,
                           (SELECT UPPER(ct.first_name)
                            from contact_company_mapping mp
                                   inner join contact ct on ct.id = mp.contact_id
                            WHERE mp.company_id = fc.id
                              and mp.is_key = true LIMIT 1 ) as sort_flag
                    from filtered_companies fc `;
    } else if (model.sortKey === 'lastActivityDate') {
      sortQuery = `SELECT id,
                          (SELECT MAX(created_at)
                           from execution ex
                           WHERE ex.entity_id = fc.id and ex.type = 'play' and entity_type = 'company') as sort_flag
                   from filtered_companies fc`;
    } else {
      sortQuery = ` SELECT id,
                           (SELECT value
                            from company_field
                            WHERE schema_field_id = (SELECT id
                                                     from entity_field
                                                     WHERE key = '${model.sortKey}' AND entity= 'company' ) AND company_id = fc.id ) as sort_flag
                    from filtered_companies fc `;
    }

    const offset = PAGE_SIZE * model.pageIndex;

    const finalQuery = `WITH filtered_companies as (SELECT ${selectFields}
                                                    from company c ${directFilterJoin}${joinCondition}${filterQuery}
                        GROUP BY c.id, c.name, c.website, c.owner_id, s.value
                          ),
                          sorted_companies as ( ${sortQuery}
                        ORDER BY sort_flag ${sortOrder}
                          )
    SELECT sc.id
    FROM sorted_companies sc
      ${allRecords ? '' : `LIMIT ${PAGE_SIZE} OFFSET ${offset}`};`;

    const finalCountQuery = `SELECT COUNT(c.id)
                             from company c ${directFilterJoin}${joinCondition}${filterQuery};`;
    logger.debug({ msg: 'Final query', finalQuery });

    const ids = (await db.$queryRaw`${Prisma.raw(finalQuery)}`) as { id: string }[];
    const rows = (await db.$queryRaw`${Prisma.raw(finalCountQuery)}`) as { count: number }[];
    return { ids: ids.map((a) => a.id), count: Number(rows[0].count) };
  }

  async getCompanyById(companyId: string): Promise<ICompanyGetResults | null> {
    return db.company.getCompanyById({ companyId, orgId: this.auth.oid });
  }

  async getFields(companyId: string): Promise<IFlattenedField[] | null> {
    if (!uuidValidate(companyId)) {
      throw new Error('Invalid company id!');
    }

    return db.company.getFields({ companyId, orgId: this.auth.oid }).then((fields: ISimpleField[] | null): IFlattenedField[] | null => {
      if (!fields) {
        return null;
      }
      return fields.map(
        (f: ISimpleField): IFlattenedField => ({
          id: f.id,
          key: f.key,
          label: f.label,
          value: f.value || '',
          dataType: f.dataType
        })
      );
    });
  }

  async getMappings(companyId: string): Promise<IFlattenedMapping[] | null> {
    if (!uuidValidate(companyId)) {
      throw new Error('Invalid company id!');
    }

    return db.company.getMappings({ companyId }).then((mappings: ICompanyMapping[] | null): IFlattenedMapping[] | null => {
      if (!mappings) {
        return null;
      }
      return mappings.map(
        (m: ICompanyMapping): IFlattenedMapping => ({
          id: m.contact.id,
          label: m.contact.firstName + ' ' + m.contact.lastName,
          value: m.contact.email,
          isKey: !!m.isKey, // @todo: Use isKey/isPrimary or key/primary
          isPrimary: !!m.isPrimary, // @todo: Use isKey/isPrimary or key/primary
          firstName: m.contact.firstName,
          lastName: m.contact.lastName,
          email: m.contact.email
        })
      );
    });
  }

  async updateKeyContact(contactId: string, companyId: string): Promise<boolean> {
    if (!uuidValidate(contactId)) {
      throw new BadRequestError('Invalid contact id!');
    }
    if (!uuidValidate(companyId)) {
      throw new BadRequestError('Invalid company id!');
    }

    return db
      .$transaction(async (trans): Promise<boolean> => {
        return trans.contact
          .findUnique({
            where: { id: contactId, orgId: this.auth.oid },
            select: { id: true }
          })
          .then(async (contact: { id: string } | null): Promise<boolean> => {
            if (!contact) {
              throw new NotFoundError('Contact not found');
            }

            return trans.companyContactMapping
              .updateMany({
                where: { companyId },
                data: { isKey: false }
              })
              .then(() =>
                trans.companyContactMapping
                  .updateMany({
                    where: { contactId, companyId },
                    data: { isKey: true }
                  })
                  .then((): boolean => true)
              );
          });
      })
      .catch((err) => {
        logger.info({ msg: 'Prisma rollback on update key contact', err });
        throw err;
      });
  }

  async getOwnerList(): Promise<IOwner[] | null> {
    return db.company
      .findMany({
        select: {
          owner: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          }
        }
      })
      .then((response) => {
        const uniqueOwnersMap = new Map<string, IOwner>();
        response.forEach((contact) => {
          if (contact.owner) {
            const { id, firstName, lastName } = contact.owner as IOwner;
            if (!uniqueOwnersMap.has(id)) {
              uniqueOwnersMap.set(id, { id, firstName, lastName });
            }
          }
        });

        const uniqueOwners = Array.from(uniqueOwnersMap.values());
        return uniqueOwners;
      });
  }

  async searchCompany({ query }): Promise<ICompany[] | null> {
    const whereClause: Prisma.CompanyWhereInput = {
      AND: [{ orgId: this.auth.oid }, { deletedAt: null }]
    };
    if (uuidValidate(query)) {
      whereClause.OR = [
        {
          id: { equals: query }
        }
      ];
    } else {
      whereClause.OR = [{ name: { contains: query, mode: 'insensitive' } }, { website: { contains: query, mode: 'insensitive' } }];
    }
    return db.company.findMany({
      where: whereClause,
      orderBy: { name: 'asc' },
      select: {
        id: true,
        name: true,
        website: true,
        entityMapping: {
          select: {
            contactId: true
          },
          orderBy: { isPrimary: 'desc' }
        }
      },
      take: 10
    });
  }

  async removeCompany(companyId: string): Promise<boolean> {
    if (!uuidValidate(companyId)) {
      throw new BadRequestError('Invalid company id!');
    }
    const findAcqwiredCompany = db.company.findFirst({
      where: {
        id: companyId,
        sourceType: 'acqwired'
      }
    });
    if (!findAcqwiredCompany) {
      throw new BadRequestError('Cannot remove CRM company. Only companies created within the application (Acqwired) can be deleted.');
    }

    return db.$transaction(async (tx): Promise<boolean> => {
      return tx.company
        .update({
          where: {
            id: companyId,
            orgId: this.auth.oid
          },
          data: { deletedAt: new Date(), deletedById: this.auth.uid }
        })
        .then(() => {
          return db.companyContactMapping
            .deleteMany({
              where: { companyId }
            })
            .then(() => true);
        })
        .catch((error) => {
          logger.error({ msg: 'Error to remove company', error });
          throw error;
        });
    });
  }

  async createMapping(contactId: string, companyId: string): Promise<ICreateCompanyMappingResponse | null> {
    if (!uuidValidate(companyId)) {
      throw new BadRequestError('Invalid company id');
    }
    if (!uuidValidate(contactId)) {
      throw new BadRequestError('Invalid contact id');
    }

    const company = await db.company.findUnique({
      where: { id: companyId, orgId: this.auth.oid },
      select: { name: true, website: true }
    });

    if (!company) {
      throw new NotFoundError('Company not found');
    }

    const existingMapping: ICreateContactMappingResponse | null = await db.companyContactMapping.findFirst({
      where: { companyId, contactId }
    });
    if (existingMapping) {
      return { ...existingMapping, name: company.name, website: company.website };
    }

    return this.createContactMapping(contactId, companyId)
      .then((mapping) => ({ ...mapping, name: company.name, website: company.website }))
      .catch((err) => {
        logger.error({ msg: 'Error creating company mapping', err });
        throw err;
      });
  }

  async saveCompany(company: ISyncData, oid: string, crmInstance: ICrmService | undefined): Promise<boolean> {
    logger.debug({ msg: '1 : Going to save company', crmId: company?.id, companyObject: company });

    if (!crmInstance) {
      logger.error({ msg: 'Failed to syn company', crmId: company?.id, e: 'CRM instance not found' });
      return false;
    }
    const ownerId = await this.getOwnerId(company.fields, oid, crmInstance.crmType);
    const { name, website } = crmInstance.getCompanyMandatoryFields(company);
    const domain = getDomainFromUrl(website);

    if (!name || !website || !domain) {
      logger.warn({
        crm: crmInstance.crmType,
        msg: 'Skipped Saving company, due to missing mandatory fields',
        missingFields: `Missing Mandatory fields: ${!name && !website ? 'name & website' : !name ? 'name' : 'website'}`,
        company: company,
        oid: oid
      });
      return false;
    }

    if (name.length > 255) {
      logger.warn({
        crm: crmInstance.crmType,
        msg: 'Skipped Saving company, due to field length exceeds the limit',
        error: `name length ${name.length} greater than limit 255`,
        company: company,
        oid: oid
      });
      return false;
    }

    if (website.length > 255) {
      logger.warn({
        crm: crmInstance.crmType,
        msg: 'Skipped Saving company, due to field length exceeds the limit',
        error: `website length ${website.length} greater than limit 255`,
        company: company,
        oid: oid
      });
      return false;
    }

    const companyWithSameDomain = await db.company.findFirst({
      where: {
        orgId: oid,
        domain: domain,
        deletedAt: null,
        crmCompany: {
          crmId: {
            not: company?.id
          }
        }
      },
      select: {
        id: true
      }
    });

    if (companyWithSameDomain) {
      logger.warn({
        crm: crmInstance.crmType,
        msg: 'Skipped Saving company, due to domain already exist',
        error: `domain ${domain} already exist`,
        company: company,
        oid: oid
      });
      return false;
    }

    const companyDetails: ICompanyDetailsParams = {
      sourceType: crmInstance.dataSourceType,
      name: name,
      website: website,
      domain: domain,
      updatedAt: new Date(),
      ownerId: ownerId,
      deletedAt: null,
      deletedById: null
    };

    return db
      .$transaction(
        (trans) =>
          crmInstance.getOrCreateCrmCompany(company, companyDetails, oid, trans).then((crmCompany) => {
            logger.debug({ msg: '2 : Found crmCompany data', crmId: company?.id, crmCompany });
            return this.updateCompany(crmCompany, companyDetails, trans).then((mainCompany) => {
              logger.debug({ msg: '3 : Saved company main data', crmId: company?.id, mainCompany });
              return this.deleteAndUpsertCompanyFields(company, crmInstance.dataSourceType, mainCompany, trans).then(() => {
                logger.debug({ msg: '4 Updated company fields', crmId: company?.id, companyId: mainCompany.id });
                return crmInstance.updateCrmCompanyFields(company, oid, trans).then(() => {
                  logger.debug({ msg: '5 Updated Crm Company fields', crmId: company?.id });
                  return true;
                });
              });
            });
          }),
        DB_TRANSACTION_SETTING
      )
      .catch((error) => {
        logger.error({ msg: 'Failed to sync company', crmId: company?.id, e: error.message });
        return false;
      });
  }

  async deleteAndUpsertCompanyFields(company: ISyncData, sourceType: DataSourceType, mainCompany: Company, trans: any): Promise<void> {
    return trans.companyField
      .deleteMany({
        where: {
          companyId: mainCompany.id,
          sourceType: sourceType
        }
      })
      .then(() => {
        return Promise.all(
          company.fields
            .filter((field: ISyncField) => field.value && field.schemaFieldId && field.isMandatory !== true)
            .map((field: ISyncField) => {
              logger.debug({
                msg: '5 Saving field for company',
                crmId: company?.id,
                field: field,
                companyId: mainCompany.id
              });

              const payload = {
                value: typeof field.value === 'object' ? JSON.stringify(field.value) : String(field.value),
                sourceType: sourceType,
                updatedAt: new Date()
              };

              return trans.companyField.upsert({
                where: {
                  companyFieldMapping: {
                    companyId: mainCompany.id,
                    schemaFieldId: field.schemaFieldId
                  }
                },
                create: {
                  companyId: mainCompany.id,
                  schemaFieldId: field.schemaFieldId,
                  ...payload
                },
                update: {
                  ...payload
                }
              });
            })
        );
      })
      .catch((error: any) => {
        logger.error({ msg: 'Failed to delete and upsert company fields', crmId: company?.id, e: error });
        throw error;
      });
  }

  async updateCompany(crmCompany: { companyId: string }, companyDetails: ICompanyDetailsParams, trans: any): Promise<Company> {
    return trans.Company.update({
      where: {
        id: crmCompany.companyId
      },
      data: companyDetails
    }).catch((error: any) => {
      logger.error({
        msg: 'Failed to update company',
        e: error.message
      });
      throw error;
    });
  }

  async getEnirchmentsFields(companyId: string): Promise<IFlattenedField[] | null> {
    return db.entityField.getEnrichmentFields({ companyId, orgId: this.auth.oid, entity: EntityType.company }).then((fields: IFlattenedField[] | null) => {
      if (!fields) {
        return null;
      }

      return fields;
    });
  }
}
