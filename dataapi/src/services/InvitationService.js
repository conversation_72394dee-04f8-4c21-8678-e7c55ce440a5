import bcrypt from 'bcryptjs';
import { logger } from '../logger';
import { db } from '../database';

export default class InvitationService {
  async checkInvitation(hash) {
    if (!/^[a-z0-9]{32}$/.test(hash)) {
      logger.info({ msg: 'Invalid invitation hash' });
      throw new Error('Invalid hash!');
    }

    let now = new Date();
    now.setHours(now.getHours() - 24);
    return db.UserInvitation.findFirst({
      where: {
        hash: hash,
        deletedAt: null
      }
    })
      .then((result) => {
        if (!result) {
          return { email: null, valid: false };
        }
        return result;
      })
      .catch((err) => {
        logger.info({ msg: 'Error in invitation check', error: err.message });
        logger.error(err);
        throw err;
      });
  }

  async isInvitationValid({ hash, email }) {
    return db.UserInvitation.findFirst({ where: { AND: [{ hash }, { email }] } })
      .then((result) => {
        if (!result) {
          throw new Error('No invitation found with this hash or email address');
        }
        return result;
      })
      .catch((err) => {
        logger.error(err);
        throw err;
      });
  }

  async convertInvitationToUser(user, invitation) {
    const salt = await bcrypt.genSalt(12);
    const password = await bcrypt.hash(user.password, salt);

    return {
      email: invitation.email,
      firstName: user.firstName,
      lastName: user.lastName,
      countryCode: user.countryCode,
      phone: user.phone,
      password: password,
      active: true,
      invitationAccepted: true
    };
  }

  async acceptInvitation(user) {
    logger.info({ msg: 'accept invitation', user: { ...user, password: 'MASKED' } });

    return this.isInvitationValid(user).then(async (invitation) => {
      const user_hash = user.hash;
      const userToUpdate = await this.convertInvitationToUser(user, invitation);
      logger.info({ msg: 'User to be updated', userToUpdate: { ...userToUpdate, password: 'MASKED' } });

      return this.updateUser(invitation.email, userToUpdate).then(async (userResult) => {
        logger.info({
          msg: 'User updated',
          result: { id: userResult.id }
        });

        const userToMessage = {
          id: userResult.id,
          firstname: userResult.firstName,
          lastname: userResult.lastName,
          orgId: invitation.organization
        };

        logger.info({
          msg: 'A new message publishing to dataapi for the user accepted invitation',
          user: userToMessage
        });

        logger.info({
          msg: 'Used invitation removing',
          hash: user_hash
        });

        return this.removeInvitation(invitation.id)
          .then((result) => {
            if (!result) {
              throw new Error('The invitation to be deleted not found!');
            }
            // @TODO: ACQ-954 we need to send a welcome e-mail here?
            return result;
          })
          .catch((err) => {
            logger.error(err.toString());
            throw err;
          });
      });
    });
  }

  async removeInvitation(invitationId) {
    return await db.UserInvitation.update({
      where: { id: invitationId },
      data: {
        deletedAt: new Date()
      }
    });
  }

  async updateUser(email, data) {
    const user = await db.User.findFirst({
      where: {
        email,
        deletedAt: null
      }
    });

    if (!user) {
      // Handle the case when the user is not found
      logger.error('User not found.');
      throw new Error('User not found.');
    }

    return await db.User.update({
      where: {
        id: user.id
      },
      data
    });
  }
}
