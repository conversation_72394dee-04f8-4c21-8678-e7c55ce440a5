import { logger } from '../logger';
import config from '../config';
import { NotFoundError } from '../lib/errors';
import { db } from '../database';
import { DataSourceType, IntegrationProvider, CrmType, CrmFieldType, EntityType } from '@prisma/client';
import { BadRequestError } from '../lib/errors';
import { CompanyMandatoryFields, ContactMandatoryFields, defaultFieldConfiguration, FieldKeys } from '../constants';
import { Promise } from 'bluebird';

import { IFilterItem, ISalesforceAuth, ICrmFields, ICrmBasicEntity, ICrmUser, IMappingField, ISalesforceServiceParam, ISyncRecordResult, IContactMandatoryFields, ICompanyMandatoryFields, ICrmListEntity, ICrmDefaultFields, IActivity, ISalesforceTask, ISalesforceEvent, CrmEntity } from '../types/Crm';
import { ICrmService } from '../lib/crm/ICrmService';
import CrmBaseService from './CrmBaseService';
import { ISyncData, ISyncFullData } from '../types/Entity';
import { SalesforceUtils } from '../lib/crm/SalesforceUtils';
import { validateFieldKey } from '../lib/utils';

export default class SalesforceService extends CrmBaseService implements ICrmService {
  sfAuth: ISalesforceAuth | null;
  dataSourceType: DataSourceType;

  // #region Constructor
  constructor({ sfAuth, auth, companyService, contactService, activityService }: ISalesforceServiceParam) {
    super({
      auth,
      companyService,
      contactService,
      activityService,
      crmType: CrmType.salesforce
    });
    this.sfAuth = sfAuth;
    this.crmInstance = this;
    this.dataSourceType = DataSourceType.salesforce;
  }

  // #endregion Constructor

  // #region Salesforce Implementation of ICrmService Interface methods

  getContactMandatoryFields(contact: ISyncData): IContactMandatoryFields {
    const crmCompanyId = this.getField<string>(contact, FieldKeys.ACCOUNT_REF_ID);
    const crmCompanyIds = crmCompanyId ? [crmCompanyId] : [];
    const firstName = this.getField<string>(contact, ContactMandatoryFields.FIRST_NAME);
    const lastName = this.getField<string>(contact, ContactMandatoryFields.LAST_NAME);
    const email = this.getField<string>(contact, ContactMandatoryFields.EMAIL);
    return { crmCompanyIds, firstName, lastName, email };
  }

  getCompanyMandatoryFields(company: ISyncData): ICompanyMandatoryFields {
    const name = this.getField<string>(company, CompanyMandatoryFields.NAME);
    const website = this.getField<string>(company, CompanyMandatoryFields.WEB_URL);
    return { name, website };
  }

  submitApiKey(_apiKey: string): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  getApiKey(): string {
    throw new Error('Method not implemented.');
  }

  getCrmLists(): Promise<ICrmListEntity[]> {
    return [];
  }

  async getCrmFields(entityType: EntityType): Promise<ICrmFields[]> {
    logger.debug({ msg: 'Reading Salesforce account custom fields' });
    this.userRoleService.validateAdminAccess();
    if (!this.sfAuth) {
      throw new BadRequestError('Salesforce not connected');
    }
    return fetch(entityType === EntityType.company ? this.sfAuth.endPoints.describeAccount : this.sfAuth.endPoints.describeContact, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.sfAuth.accessToken}`
      }
    })
      .then((res) => res.text())
      .then((txt) => JSON.parse(txt))
      .then(async (response) => {
        const allFields = response.fields;
        logger.debug({ msg: 'Got salesforce custom fields', entityType, allFields });
        const standard: ICrmFields[] = allFields
          .filter((f) => !f.custom)
          .map((f) => {
            return { label: f.label, uniqueId: f.name, key: f.name, type: f.type, source: CrmFieldType.standard };
          });
        const custom: ICrmFields[] = allFields
          .filter((f) => f.custom)
          .map((f) => {
            return { label: f.label, uniqueId: f.name, key: f.name, type: f.type, source: CrmFieldType.custom };
          });
        logger.debug({
          msg: 'Got salesforce custom fields',
          entityType,
          custom,
          standard
        });
        return standard.concat(custom);
      })
      .catch((e) => {
        logger.error({
          msg: 'Failed reading salesforce custom fields',
          entityType,
          e
        });
        return [];
      });
  }

  async getEntities(filter: IFilterItem[], entityType: EntityType): Promise<ICrmBasicEntity[]> {
    this.userRoleService.validateAdminAccess();
    const fields = await this.getMappings(entityType);
    const fieldNames = fields?.map((field) => field.uniqueId) ?? [];
    const fetchRelated = false;
    const relatedFieldNames = [];

    const result = await this.fetchSalesforceRecords({
      filter: filter,
      fieldNames,
      fetchRelated,
      relatedFieldNames,
      entityType,
      ownerId: null
    });
    logger.debug({
      msg: 'Got salesforce entities',
      entityType,
      result
    });
    return result?.[0];
  }

  async getDefaultFields(mappingType: EntityType): Promise<ICrmDefaultFields[]> {
    this.userRoleService.validateAdminAccess();
    const DEFAULT_CONTACT_OPTIONS = [
      {
        label: 'Contact ID',
        key: 'id',
        uniqueId: 'Id',
        type: 'id',
        locked: true
      },
      {
        label: 'Account ID',
        key: 'account_id',
        uniqueId: 'AccountId',
        type: 'reference',
        locked: true
      },
      {
        label: 'Owner ID',
        key: 'owner_id',
        uniqueId: 'OwnerId',
        type: 'reference',
        locked: true
      },
      {
        label: 'First Name',
        key: 'first_name',
        uniqueId: 'FirstName',
        type: 'string',
        locked: true,
        isMandatory: true
      },
      {
        label: 'Last Name',
        key: 'last_name',
        uniqueId: 'LastName',
        type: 'string',
        locked: true,
        isMandatory: true
      },
      {
        label: 'Email',
        key: 'email',
        uniqueId: 'Email',
        type: 'email',
        locked: true,
        isMandatory: true
      }
    ];

    const DEFAULT_ACCOUNT_OPTIONS = [
      {
        label: 'Account ID',
        key: 'id',
        uniqueId: 'Id',
        type: 'id',
        locked: true
      },
      {
        label: 'Owner ID',
        key: 'owner_id',
        uniqueId: 'OwnerId',
        type: 'reference',
        locked: true
      },
      {
        label: 'Company Name',
        key: 'name',
        uniqueId: 'Name',
        type: 'string',
        locked: true,
        isMandatory: true
      },
      {
        label: 'Website',
        key: 'website',
        uniqueId: 'Website',
        type: 'string',
        locked: true,
        isMandatory: true
      }
    ];

    if (mappingType === EntityType.company) {
      return DEFAULT_ACCOUNT_OPTIONS;
    }
    return DEFAULT_CONTACT_OPTIONS;
  }

  async getUsers(email: string): Promise<ICrmUser[]> {
    logger.info({ crm: this.crmType, msg: 'Attempting to fetch Salesforce User by Email', email });

    const query = `SELECT Email, Id, FirstName, LastName FROM User WHERE Email = '${email}'`;

    try {
      const records = await this.executeSalesforceQuery(query);

      if (records?.length === 1) {
        logger.info({ crm: this.crmType, msg: 'Salesforce user found', email });
        return [
          {
            exists: true,
            firstName: records[0].FirstName,
            lastName: records[0].LastName,
            email: records[0].Email,
            id: records[0].Id
          }
        ];
      } else {
        logger.info({ crm: this.crmType, msg: 'No Salesforce user found with the given email', email });
        return [{ exists: false }];
      }
    } catch (error) {
      logger.error({ crm: this.crmType, msg: 'Error occurred while fetching Salesforce user', email, error });
      throw new Error(`Error fetching Salesforce user: ${(error as Error).message}`);
    }
  }

  async revoke(): Promise<boolean> {
    const org = await db.organization.findUnique({
      where: {
        id: this.auth.oid
      }
    });
    if (!org) {
      throw new Error('Cannot find organization');
    }
    const integration = await db.orgIntegration.findFirst({
      where: {
        orgId: org.id,
        provider: IntegrationProvider.salesforce
      },
      select: {
        id: true,
        authToken: true
      }
    });
    if (!integration || !integration.authToken) {
      return false;
    }

    const { accessToken, refreshToken } = integration.authToken as { accessToken: string; refreshToken: string };

    if (accessToken) {
      await this.revokeToken(accessToken);
    }
    if (refreshToken) {
      await this.revokeToken(refreshToken);
    }

    return true;
  }

  isAuthenticated(): boolean {
    return !this.sfAuth ? false : true;
  }

  async getSyncRecords(entityType: EntityType, userSync: boolean): Promise<ISyncRecordResult> {
    const filters = await this.getFilters(entityType);
    const fields = await this.getMappings(entityType);
    const relatedFields = await this.getMappings(entityType === EntityType.company ? EntityType.contact : EntityType.company);

    if (!fields?.length) {
      logger.info({
        crm: this.crmType,
        msg: `No mapping found for organization: ${this.auth?.oid} sync type: ${entityType}`
      });
      return { mainData: null, relatedData: null, activities: null };
    }

    let syncRecords: ISyncFullData[] = [];

    // If filters exist, fetch records for each filter
    if (filters?.length > 0) {
      syncRecords = await Promise.all(
        filters.map(async (filter) =>
          this.fetchSyncRecords({
            entityType,
            filter,
            fields,
            relatedFields,
            fetchRelated: filter.fetchRelated,
            userSync
          })
        )
      );
    } else {
      logger.debug({
        crm: this.crmType,
        msg: `No filters found for organization: ${this.auth?.oid} sync type: ${entityType}`
      });
      // If no filters, fetch once without a filter
      syncRecords = [
        await this.fetchSyncRecords({
          entityType,
          filter: null,
          fields,
          relatedFields,
          fetchRelated: true, // Fetch related data since no filter is provided
          userSync
        })
      ];
    }

    const mainRecords: ISyncData[] = [];
    const relatedRecords: ISyncData[] = [];
    const mainRecordIds = new Set<string>();
    const relatedRecordIds = new Set<string>();

    // Ensure both mainData and relatedData are properly checked
    logger.debug({
      crm: this.crmType,
      msg: `Fetched Salesforce sync records: ${syncRecords.length}`
    });

    syncRecords.forEach((recordSet) => {
      if (recordSet.mainData) {
        recordSet.mainData.forEach((mainRecord) => {
          if (!mainRecordIds.has(mainRecord.id)) {
            mainRecords.push(mainRecord);
            mainRecordIds.add(mainRecord.id);
          }
        });
      }

      if (recordSet.relatedData) {
        recordSet.relatedData.forEach((relatedRecord) => {
          if (!relatedRecordIds.has(relatedRecord.id)) {
            relatedRecords.push(relatedRecord);
            relatedRecordIds.add(relatedRecord.id);
          }
        });
      }
    });

    logger.info({
      crm: this.crmType,
      msg: `Fetched Salesforce sync records: ${mainRecords.length} main, ${relatedRecords.length} related`
    });

    // Fetch activities based on collected company/contact IDs
    const activities = entityType === EntityType.company ? await this.fetchSalesforceActivities(mainRecordIds, relatedRecordIds) : await this.fetchSalesforceActivities(relatedRecordIds, mainRecordIds);

    // Return the final result
    return {
      mainData: mainRecords,
      relatedData: relatedRecords,
      activities // Include activities in the result
    };
  }

  async fetchSalesforceActivities(companyIds: Set<string>, contactIds: Set<string>): Promise<IActivity[]> {
    const BATCH_SIZE = 100;

    // Helper to split arrays into chunks
    const chunkArray = (array: string[], size: number): string[][] => {
      const result: string[][] = [];
      for (let i = 0; i < array.length; i += size) {
        result.push(array.slice(i, i + size));
      }
      return result;
    };

    // Function to execute Salesforce queries for a batch of IDs
    const fetchBatch = async (ids: string[], isCompany: boolean): Promise<{ tasks: IActivity[]; events: IActivity[] }> => {
      const idList = ids.map((id) => `'${id}'`).join(',');
      const whereClause = isCompany ? `WHERE WhatId IN (${idList})` : `WHERE WhoId IN (${idList})`;

      const taskQuery = `SELECT Id, Subject, ActivityDate, Status, Description, WhoId, WhatId, TaskSubtype FROM Task ${whereClause}`;
      const eventQuery = `SELECT Id, Subject, ActivityDate, WhoId, WhatId, Description, Location, StartDateTime, EndDateTime, IsAllDayEvent, EventSubtype FROM Event ${whereClause}`;

      // Execute both queries
      const [tasks, events]: [ISalesforceTask[], ISalesforceEvent[]] = await Promise.all([this.executeSalesforceQuery(taskQuery), this.executeSalesforceQuery(eventQuery)]);

      return {
        tasks: tasks.map(SalesforceUtils.mapSalesforceTaskToActivity),
        events: events.map(SalesforceUtils.mapSalesforceEventToActivity)
      };
    };

    // Process company and contact batches separately
    const companyIdBatches = chunkArray(Array.from(companyIds), BATCH_SIZE);
    const contactIdBatches = chunkArray(Array.from(contactIds), BATCH_SIZE);

    const taskActivities: Map<string, IActivity> = new Map(); // To ensure unique tasks by ID
    const eventActivities: Map<string, IActivity> = new Map(); // To ensure unique events by ID

    // Fetch company activities
    const companyPromises = companyIdBatches.map((batch) => fetchBatch(batch, true));
    // Fetch contact activities
    const contactPromises = contactIdBatches.map((batch) => fetchBatch(batch, false));

    const allResults = await Promise.all([...companyPromises, ...contactPromises]);

    // Separate task and event activities
    allResults.forEach(({ tasks, events }) => {
      tasks.forEach((task) => {
        if (!taskActivities.has(task.id)) {
          taskActivities.set(task.id, task); // Ensure unique tasks
        }
      });

      events.forEach((event) => {
        if (!eventActivities.has(event.id)) {
          eventActivities.set(event.id, event); // Ensure unique events
        }
      });
    });

    // Combine unique tasks and events
    const uniqueActivities = [
      ...Array.from(eventActivities.values()), // Unique events
      ...Array.from(taskActivities.values()) // Unique tasks
    ];

    logger.info({ crm: this.crmType, msg: `Fetched unique Salesforce activities: ${uniqueActivities.length}` });
    return uniqueActivities;
  }

  async executeSalesforceQuery(query: string): Promise<any> {
    if (!this.sfAuth) {
      logger.error({ crm: this.crmType, msg: 'Salesforce connection not found' });
      throw new BadRequestError('Salesforce not connected');
    }

    const url = this.queryUrl(query); // Construct the query URL

    logger.debug({ crm: this.crmType, msg: 'Generated Salesforce query URL', url });

    try {
      const response = await fetch(url, {
        method: 'GET', // Salesforce API typically uses GET for querying data
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.sfAuth.accessToken}` // Pass the access token
        }
      });

      if (!response.ok) {
        logger.error({
          crm: this.crmType,
          msg: 'Salesforce API request failed',
          status: response.status,
          statusText: response.statusText
        });
        throw new Error(`Salesforce API error: ${response.statusText}`);
      }

      const txt = await response.text(); // Get raw response text
      const data = JSON.parse(txt); // Parse response as JSON
      logger.debug({ crm: this.crmType, msg: 'Parsed response from Salesforce API', data });

      // Ensure the response contains records
      if (!data.records || !Array.isArray(data.records)) {
        logger.error({
          crm: this.crmType,
          msg: 'Salesforce query returned no records.'
        });
        return [];
      }

      return data.records;
    } catch (error) {
      logger.error({
        crm: this.crmType,
        msg: 'Error occurred during Salesforce API request',
        error: (error as Error).message
      });
      throw new Error(`Salesforce API request error: ${(error as Error).message}`);
    }
  }

  async syncDeletion(userSync: boolean = false): Promise<boolean> {
    try {
      logger.debug({
        msg: 'Starting Salesforce deleted sync for ACCOUNT',
        userSync
      });
      const companyStatus = await this.syncDeletedRecordsForEntity(userSync, CrmEntity.ACCOUNT);
      logger.debug({
        msg: 'Starting Salesforce deleted sync for CONTACT',
        userSync
      });
      const contactStatus = await this.syncDeletedRecordsForEntity(userSync, CrmEntity.CONTACT);
      logger.debug({
        msg: 'Starting Salesforce deleted sync for TASK',
        userSync
      });
      const taskStatus = await this.syncDeletedRecordsForEntity(userSync, CrmEntity.TASK);
      logger.debug({
        msg: 'Starting Salesforce deleted sync for EVENT',
        userSync
      });
      const eventStatus = await this.syncDeletedRecordsForEntity(userSync, CrmEntity.EVENT);

      return companyStatus && contactStatus && taskStatus && eventStatus;
    } catch (error) {
      logger.error({ msg: 'Failed to sync deletions', error: (error as Error).message });
      return false;
    }
  }

  // #endregion

  // #region Salesforce specific Private Methods

  createFilterQuery(filter: IFilterItem[], prefix: string) {
    const queryFields: string[] = [];

    filter?.forEach((item: IFilterItem) => {
      switch (item.condition) {
        case 'equals':
          queryFields.push(`${prefix}.${item.field}='${item.input}'`);
          break;
        case 'not-equal':
          queryFields.push(`${prefix}.${item.field}!='${item.input}'`);
          break;
        case 'less-than':
          queryFields.push(`${prefix}.${item.field}<${item.input}`);
          break;
        case 'greater-than':
          queryFields.push(`${prefix}.${item.field}>${item.input}`);
          break;
        case 'less-or-equal':
          queryFields.push(`${prefix}.${item.field}<=${item.input}`);
          break;
        case 'greater-or-equal':
          queryFields.push(`${prefix}.${item.field}>=${item.input}`);
          break;
        case 'contains':
          queryFields.push(`${prefix}.${item.field} like '%${item.input}%'`);
          break;
        case 'not-contains':
          queryFields.push(`${prefix}.${item.field} not like '%${item.input}%'`);
          break;
      }
    });
    if (queryFields.length === 0) return '';

    return encodeURIComponent(`where ${queryFields.join(' and ')}`);
  }

  queryUrl(query, useToolingApi = false) {
    query = query.replaceAll(/ /g, '+').replaceAll(/\n/g, '').replaceAll(/\t/g, '');
    if (!this.sfAuth) {
      throw new BadRequestError('Salesforce not connected');
    }
    const url = this.sfAuth.endPoints[useToolingApi ? 't_query' : 'query'] + '/?q=' + query;
    return url;
  }

  prefixFields(fields, prefix) {
    return fields.map((field) => `${prefix}.${field}`);
  }

  buildAccountQuerylUrl({ filter, fieldNames, fetchRelated, relatedFieldNames, ownerId }) {
    logger.debug({
      msg: 'Building account query url',
      data: { filter, fieldNames, fetchRelated, relatedFieldNames }
    });
    const mainFieldsList = this.prefixFields(fieldNames, 'ACCOUNT').join(', ');
    const accountFilterQuery = this.createFilterQuery(filter, 'Account');
    const ownerQuery = encodeURIComponent(`(ACCOUNT.OwnerId='${ownerId}')`);
    const filterQuery = ownerId ? (accountFilterQuery === '' ? `where ${ownerQuery}` : `${accountFilterQuery} and ${ownerQuery}`) : accountFilterQuery;

    if (fetchRelated && relatedFieldNames?.length > 0) {
      const prefixedRelatedFields = this.prefixFields(relatedFieldNames, 'CONTACT').join(', ');
      return this.queryUrl(`SELECT ${mainFieldsList}, (SELECT ${prefixedRelatedFields} FROM Contacts) from Account ${filterQuery}`);
    } else {
      return this.queryUrl(`SELECT ${mainFieldsList} from Account ${filterQuery}`);
    }
  }

  buildContactQuerylUrl({ filter, fieldNames, fetchRelated, relatedFieldNames, ownerId }) {
    logger.debug({
      msg: 'Building contact query url',
      data: { filter, fieldNames, fetchRelated, relatedFieldNames }
    });

    const ownerQuery = encodeURIComponent(`(Contact.OwnerId='${ownerId}')`);
    const mainFieldsList = this.prefixFields(fieldNames, 'CONTACT').join(', ');
    const contactFilterQuery = this.createFilterQuery(filter, 'Contact');
    const filterQuery = ownerId ? (contactFilterQuery === '' ? `where ${ownerQuery}` : `${contactFilterQuery} and ${ownerQuery}`) : contactFilterQuery;
    if (fetchRelated && relatedFieldNames?.length > 0) {
      const prefixedRelatedFields = this.prefixFields(relatedFieldNames, 'Contact.Account').join(', ');

      return this.queryUrl(`SELECT ${mainFieldsList}, ${prefixedRelatedFields} from Contact ${filterQuery} `);
    } else {
      return this.queryUrl(`SELECT ${mainFieldsList} from Contact ${filterQuery}`);
    }
  }

  parseMainRecords({ response, fieldNames, fetchRelated, relatedFieldNames, relatedFieldName }) {
    const mainRecords: { id: string }[] = [];
    const relatedRecords = [];
    for (let i = 0; i < response.records.length; i++) {
      const mainRecord = { id: response.records[i]?.Id };
      fieldNames.forEach((field) => {
        mainRecord[field] = response.records[i][field];
      });
      mainRecords.push(mainRecord);
      if (fetchRelated && response.records[i]?.[relatedFieldName]) {
        this.parseRelatedRecords({
          record: response.records[i],
          relatedRecords,
          relatedFieldNames,
          relatedFieldName
        });
      }
    }

    logger.info({ crm: this.crmType, msg: `Parsed salesforce records mainRecords: ${mainRecords?.length}, relatedRecords: ${relatedRecords?.length}` });
    return [mainRecords, relatedRecords];
  }

  parseRelatedRecords({ record, relatedRecords, relatedFieldNames, relatedFieldName = 'Contacts' }) {
    // Related record can be an array (under records) or an single object
    if (Array.isArray(record[relatedFieldName]?.records)) {
      for (let j = 0; j < record[relatedFieldName]?.records.length; j++) {
        const relatedRecord = record?.[relatedFieldName]?.records?.[j];
        if (!relatedRecord) continue;
        const newRecord = { id: relatedRecord.Id };
        relatedFieldNames.forEach((relatedField) => {
          newRecord[relatedField] = relatedRecord[relatedField];
        });
        relatedRecords.push(newRecord);
      }
    } else if (record[relatedFieldName]) {
      const newRecord = { id: record[relatedFieldName].Id };
      relatedFieldNames.forEach((relatedField) => {
        newRecord[relatedField] = record[relatedFieldName][relatedField];
      });
      relatedRecords.push(newRecord);
    }
  }

  handleSalesforceResponse({ response, fieldNames, relatedFieldNames, fetchRelated, entityType }) {
    const relatedFieldName = entityType === EntityType.contact ? 'Account' : 'Contacts';

    logger.info({ crm: this.crmType, msg: `Handling salesforce records: ${entityType}` });
    if (!response || !response.done) {
      throw new Error('Cannot read accounts');
    }
    if (response.totalSize === 0) {
      throw new NotFoundError(`No record found: ${entityType}`);
    }
    return this.parseMainRecords({
      response,
      fieldNames,
      fetchRelated,
      relatedFieldNames,
      relatedFieldName
    });
  }

  async fetchSalesforceRecords({ filter, fieldNames, fetchRelated, relatedFieldNames, entityType, ownerId }: { filter: any; fieldNames: string[]; fetchRelated: boolean; relatedFieldNames: string[]; entityType: EntityType; ownerId: string | null }) {
    logger.debug({
      msg: 'Reading Salesforce records',
      data: { filter, entityType }
    });
    const urlBuilder = entityType === EntityType.company ? this.buildAccountQuerylUrl : this.buildContactQuerylUrl;

    const url = urlBuilder.bind(this)({
      filter,
      fieldNames,
      fetchRelated,
      relatedFieldNames,
      ownerId
    });
    logger.debug({ msg: url, text: 'Salesforce query url' });
    if (!this.sfAuth) {
      throw new BadRequestError('Salesforce not connected');
    }
    return fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.sfAuth.accessToken}`
      }
    })
      .then((res) => res.text())
      .then((txt) => JSON.parse(txt))
      .then(async (response) => {
        return this.handleSalesforceResponse({
          response,
          fieldNames,
          fetchRelated,
          relatedFieldNames,
          entityType
        });
      })
      .catch((e) => {
        logger.error({ msg: 'Failed reading salesforce accounts', e: e.message });
      });
  }

  async syncDeletedRecordsForEntity(userSync: boolean, entityType: CrmEntity) {
    try {
      logger.debug({
        msg: 'Reading Salesforce deleted records',
        userSync,
        entityType
      });

      const crmIds = await this.getCrmIds(entityType, userSync);

      logger.debug({
        msg: `Got salesforce ids for ${entityType}`,
        crmIds
      });

      if (!crmIds || crmIds.length === 0) {
        return true;
      }

      const batchSize = 100;
      const totalRecords = crmIds.length;
      const batches = Math.ceil(totalRecords / batchSize);

      const processBatch = async (_, index) => {
        const start = index * batchSize;
        const end = (index + 1) * batchSize;
        const batchIds = crmIds.slice(start, end);
        await this.syncDeletedRecords(batchIds, entityType);
      };

      await Promise.map(new Array(batches), processBatch, { concurrency: 5 });

      return true;
    } catch (error) {
      logger.error({ msg: 'Failed to sync deleted records', error });
      return false;
    }
  }

  async syncDeletedRecords(crmIds: string[], entityType: CrmEntity) {
    const sfEntityKey = entityType.toString();
    const url = this.queryUrl(`SELECT ${sfEntityKey}.Id FROM ${sfEntityKey} WHERE ${sfEntityKey}.Id IN ('${crmIds.join("','")}')`);
    logger.debug({ msg: url, text: 'Salesforce delete query url' });
    if (!this.sfAuth) {
      throw new BadRequestError('Salesforce not connected');
    }
    return fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.sfAuth.accessToken}`
      }
    })
      .then((res) => res.text())
      .then((txt) => JSON.parse(txt))
      .then((response) => {
        const validIds = response.records.map((record) => record.Id);
        const validIdsSet = new Set(validIds);
        const deletedRecords = crmIds.filter((id) => !validIdsSet.has(id));
        logger.debug({
          msg: `Got salesforce deleted records : ${entityType}`,
          deletedRecords: deletedRecords
        });
        return deletedRecords;
      })
      .then(async (deletedRecords) => {
        if (deletedRecords?.length > 0) {
          await this.deleteRecords(deletedRecords, entityType);
        }
        return true;
      })
      .catch((e) => {
        logger.error({ msg: `Failed sync the deleted ${entityType}`, e });
      });
  }

  mapFields(records, fields): ISyncData[] {
    if (!records) return [];
    return records?.map((record) => {
      const mappedFields: IMappingField[] = [];

      Object.keys(record).forEach(async (originalfield) => {
        const field = fields.find((field) => field.uniqueId === originalfield);
        if (!field) return;
        mappedFields.push({
          crmFieldId: field?.uniqueId,
          fieldKey: field.key,
          value: record[originalfield],
          label: field?.label,
          schemaFieldId: field.schemaFieldId,
          locked: field?.locked,
          type: field?.type,
          isMandatory: field?.isMandatory ? true : false
        });
      });
      return {
        id: record.id,
        fields: mappedFields
      };
    });
  }

  async getMappingField({ defaultField, entityType }) {
    const schemaField = await db.entityField.findFirst({
      where: {
        orgId: this.auth.oid,
        entity: entityType,
        key: defaultField.fieldId
      },
      select: {
        id: true
      }
    });

    // Create a new object by spreading defaultField
    const clonedDefaultField = { ...defaultField };

    if (schemaField?.id) {
      // If schemaField.id exists, assign it to the clonedDefaultField
      clonedDefaultField.schemaFieldId = schemaField.id;
    } else {
      const validateResult = validateFieldKey(defaultField.fieldId);
      if (validateResult?.invalid) {
        throw new BadRequestError(validateResult.message);
      }
      const newSchemaField = await db.entityField.create({
        data: {
          organization: {
            connect: {
              id: this.auth.oid
            }
          },
          entity: entityType,
          key: defaultField.fieldId,
          label: defaultField.label,
          dataType: defaultField.type,
          config: defaultFieldConfiguration
        }
      });

      // If schemaField.id doesn't exist, assign the newSchemaField.id to the clonedDefaultField
      clonedDefaultField.schemaFieldId = newSchemaField.id;
    }

    // Return the cloned object
    return clonedDefaultField;
  }

  async fetchSyncRecords({ entityType, filter, fields, relatedFields, fetchRelated, userSync }): Promise<ISyncFullData> {
    logger.debug({ updatedRelatedFields: relatedFields });
    const relatedFieldNames = relatedFields?.length > 0 ? relatedFields?.map((field) => field.uniqueId) : [];
    logger.debug({ updatedFields: fields });
    const fieldNames = fields?.map((field) => field.uniqueId) ?? [];
    const ownerId = userSync ? await this.getOwnerId() : null;
    const records = await this.fetchSalesforceRecords({
      filter: filter?.parameters,
      fieldNames,
      fetchRelated,
      relatedFieldNames,
      entityType,
      ownerId
    });
    if (!records) return [];
    const [mainRecords, relatedRecords] = records;

    const mappedMainRecords = this.mapFields(mainRecords, fields);
    const mappedRelatedRecords = this.mapFields(relatedRecords, relatedFields);
    logger.info({
      crm: this.crmType,
      msg: `Mapped records for ${entityType}`,
      mappedMainRecords: mappedMainRecords?.length,
      mappedRelatedRecords: mappedRelatedRecords?.length
    });
    return { mainData: mappedMainRecords, relatedData: mappedRelatedRecords };
  }

  async revokeToken(token): Promise<void> {
    try {
      const params = new URLSearchParams({ token });
      const revokeUrl = new URL(`${config.salesforce.auth.authority}/revoke`);
      revokeUrl.search = params.toString();
      await fetch(revokeUrl, {
        method: 'POST',
        headers: {
          Accept: 'application/json'
        }
      }).then((_res) => logger.info({ msg: 'Salesforce token revoked' }));
    } catch (error) {
      logger.error({ msg: error });
    }
  }

  // #endregion
}
