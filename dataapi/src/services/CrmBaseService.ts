import { logger } from '../logger';
import { db } from '../database';
import { CrmFilterParameter, EntityType, CrmSetting, CrmSyncStatus, CrmType, IntegrationType, DataSourceType, CrmFieldType, CrmEntityType } from '@prisma/client';
import BaseService from './BaseService';
import { ICrmFilter, ICrmFieldMapping, IUserCrmMap, ISyncSettings, ISyncResult, IRecordSyncStatus, ICrmListMapping, IActivity, CrmEntity } from '../types/Crm';
import { CONCURRENT_TRANSACTION_COUNT, defaultFieldConfiguration, SERVER_ERROR_MESSAGE, SyncStatus, UserCrmMappingResult } from '../constants';
import { BadRequestError } from '../lib/errors';
import { Promise } from 'bluebird';
import CompanyService from './Company';
import ContactService from './Contact';
import { ISyncData, ISyncField } from '../types/Entity';
import { ICrmService } from '../lib/crm/ICrmService';
import { APIError } from '../lib';
import { IContactDetailsParams } from '../types/Contact';
import { ICompanyDetailsParams } from '../types/Company';
import ActivityService from './Activity';

import { getDomainFromUrl, validateFieldKey } from '../lib/utils';

export default class CrmBaseService extends BaseService {
  companyService: CompanyService;
  contactService: ContactService;
  activityService: ActivityService;
  crmType: CrmType;
  crmInstance: ICrmService | undefined;
  syncedRecords: IRecordSyncStatus[] = [];

  constructor({ auth, crmType, companyService, contactService, activityService }) {
    super({ auth });
    this.companyService = companyService;
    this.contactService = contactService;
    this.activityService = activityService;
    this.crmType = crmType;
  }

  async saveFilter({ id, parameters, fetchRelated }: ICrmFilter, filterType: EntityType): Promise<boolean> {
    this.userRoleService.validateAdminAccess();
    const org = await db.organization.findUnique({
      where: { id: this.auth.oid }
    });

    if (!org) {
      throw new Error('Organization not found');
    }

    if (id) {
      return db
        .$transaction(async (trans): Promise<boolean> => {
          if (fetchRelated !== null) {
            await trans.crmFilter.update({
              where: { id },
              data: {
                fetchRelated
              }
            });
          }
          await db.crmFilterParameter.deleteMany({
            where: { filterId: id }
          });
          await db.crmFilterParameter.createMany({
            data: parameters.map((param) => {
              param.filterId = id;
              return param as CrmFilterParameter;
            })
          });
          return true;
        })
        .catch((error) => {
          logger.error({ msg: 'Failed to save filter', error });
          throw new Error('Failed to save filter');
        });
    } else {
      await db.crmFilter.create({
        data: {
          fetchRelated: true,
          orgId: org.id,
          filterType,
          crmType: this.crmType,
          parameters: {
            create: [...(parameters as CrmFilterParameter[])]
          }
        }
      });
    }
    return true;
  }

  async getFilters(filterType: EntityType): Promise<ICrmFilter[]> {
    this.userRoleService.validateAdminAccess();
    const org = await db.organization.findUnique({
      where: {
        id: this.auth.oid
      }
    });
    if (!org) {
      throw new Error('Cannot find organization');
    }
    const filters = await db.crmFilter.findMany({
      where: {
        orgId: org.id,
        filterType,
        crmType: this.crmType
      },
      include: {
        parameters: true
      }
    });
    return filters;
  }

  async getMappedLists(): Promise<ICrmListMapping[]> {
    this.userRoleService.validateAdminAccess();
    const org = await db.organization.findUnique({
      where: {
        id: this.auth.oid
      }
    });
    if (!org) {
      throw new Error('Cannot find organization');
    }
    return db.crmList
      .findMany({
        where: {
          orgId: org.id,
          crmType: this.crmType
        },
        select: {
          crmId: true,
          name: true,
          entityType: true,
          ownerId: true,
          fetchRelated: true,
          mappings: {
            select: {
              id: true
            }
          }
        }
      })
      .then((lists) => {
        return lists.map((list) => {
          return {
            id: list.crmId,
            name: list.name,
            entityType: list.entityType,
            ownerId: list.ownerId,
            fetchRelated: list.fetchRelated,
            hasMapping: list.mappings.length > 0
          };
        });
      })
      .catch((error) => {
        logger.error({ msg: 'Failed to get mapped lists', error });
        throw error;
      });
  }

  async saveCrmList(list: ICrmListMapping): Promise<boolean> {
    this.userRoleService.validateAdminAccess();
    const org = await db.organization.findUnique({
      where: {
        id: this.auth.oid
      }
    });
    if (!org) {
      throw new Error('Cannot find organization');
    }
    return db.crmList
      .upsert({
        where: {
          crmId_orgId_crmType_unq: {
            orgId: org.id,
            crmId: list.id,
            crmType: this.crmType
          }
        },
        create: {
          orgId: org.id,
          crmId: list.id,
          name: list.name,
          entityType: list.entityType,
          ownerId: list.ownerId,
          fetchRelated: list.fetchRelated ?? false,
          crmType: this.crmType
        },
        update: {
          name: list.name,
          entityType: list.entityType,
          ownerId: list.ownerId,
          fetchRelated: list.fetchRelated
        }
      })
      .then(() => {
        return true;
      })
      .catch((error) => {
        logger.error({ msg: 'Failed to save CRM list', error: error.message });
        throw error;
      });
  }

  async deleteCrmList(id: string): Promise<boolean> {
    logger.info({ crm: this.crmType, msg: 'Deleting CRM list', id });

    this.userRoleService.validateAdminAccess();
    const org = await db.organization.findUnique({
      where: {
        id: this.auth.oid
      }
    });
    if (!org) {
      throw new Error('Cannot find organization');
    }

    return db.crmList
      .delete({
        where: {
          crmId_orgId_crmType_unq: {
            orgId: org.id,
            crmId: id,
            crmType: this.crmType
          }
        }
      })
      .then(() => {
        return true;
      })
      .catch((error) => {
        logger.error({ msg: 'Failed to delete CRM list', error: error.message });
        throw error;
      });
  }

  async updateCrmCompanyFields(company: ISyncData, oid: string, trans: any): Promise<void> {
    logger.debug({
      msg: 'Updating CrmCompany fields',
      crmId: company?.id,
      orgId: oid
    });
    return trans.crmCompany
      .update({
        where: {
          crmCompanyConnection: {
            crmId: company.id,
            orgId: oid,
            crmType: this.crmType
          }
        },
        data: {
          fields: company.fields
        }
      })
      .catch((error) => {
        logger.error({
          msg: 'Failed to Update CrmCompany fields',
          crmId: company?.id,
          e: error.message
        });
        throw error;
      });
  }

  async getMappings(mappingType: EntityType): Promise<ICrmFieldMapping[]> {
    try {
      this.userRoleService.validateAdminAccess();
      const mapping = await db.crmMapping.findMany({
        where: {
          orgId: this.auth.oid,
          entityType: this.getEntityType(mappingType),
          crmType: this.crmType
        },
        select: {
          id: true,
          locked: true,
          crmFieldKey: true,
          crmFieldLabel: true,
          entityField: true,
          externalId: true,
          fieldSource: true,
          listId: true
        },
        orderBy: {
          createdAt: 'asc'
        }
      });

      const transformedMapping: ICrmFieldMapping[] = mapping.map((map) => {
        return {
          id: map.id,
          locked: map.locked,
          uniqueId: map.externalId,
          entityType: map.entityField?.entity,
          key: map.entityField?.key,
          label: map.entityField?.label,
          type: map.entityField?.dataType,
          isMandatory: map.entityField?.isMandatory,
          schemaFieldId: map.entityField?.id,
          sourceId: map.listId,
          source: map.fieldSource
        };
      });
      return transformedMapping;
    } catch (error) {
      logger.error({ msg: 'Failed to get mappings', error: (error as Error).message });
      throw error;
    }
  }

  async saveMappings(query: ICrmFieldMapping[], mappingType: EntityType): Promise<boolean> {
    this.userRoleService.validateAdminAccess();
    const orgId = this.auth.oid;

    return db
      .$transaction(async (tx) => {
        // Extract new keys from the incoming query
        const newKeys = query.map((row) => row.key);

        // Soft delete entityFields that are no longer present in the new mappings
        await this.softDeleteEntityFields(mappingType, newKeys, tx);

        // Iterate over the input array and create/update mappings
        await Promise.map(query, async (row: ICrmFieldMapping) => {
          const schemaField = await tx.entityField.findFirst({
            where: {
              orgId: orgId,
              entity: mappingType,
              key: row.key
            },
            select: {
              id: true,
              label: true,
              deletedAt: true
            }
          });

          // Handle reactivation if the entityField was soft-deleted
          if (schemaField?.deletedAt) {
            await tx.entityField.update({
              where: { id: schemaField.id },
              data: {
                deletedAt: null,
                deletedById: null
              }
            });
          }
          let list: { id: string } | null = null;

          if (row.sourceId && row.source === CrmFieldType.list) {
            list = await tx.crmList.findFirst({
              where: {
                orgId: orgId,
                crmId: row.sourceId,
                crmType: this.crmType
              },
              select: {
                id: true
              }
            });
          }

          const payload = {
            organization: {
              connect: {
                id: orgId
              }
            },
            entityType: mappingType,
            locked: row.locked,
            crmFieldKey: row.key,
            crmFieldLabel: row.label,
            crmType: this.crmType,
            fieldSource: row.source,
            externalId: row.uniqueId
          };

          if (list?.id) {
            payload['list'] = {
              connect: {
                id: list.id
              }
            };
          }

          if (schemaField?.id) {
            payload['entityField'] = {
              connect: {
                id: schemaField.id
              }
            };
            if (schemaField.label !== row.label) {
              await tx.entityField.update({
                where: {
                  id: schemaField.id
                },
                data: {
                  label: row.label
                }
              });
            }
          } else {
            const validateResult = validateFieldKey(row.key);
            if (validateResult?.invalid) {
              throw new BadRequestError(validateResult.message);
            }
            payload['entityField'] = {
              create: {
                key: row.key,
                label: row.label,
                dataType: row.type,
                entity: mappingType,
                orgId: orgId,
                isMandatory: row.isMandatory,
                config: defaultFieldConfiguration
              }
            };
          }

          await tx.crmMapping.create({
            data: payload as any
          });
        });

        return true;
      })
      .catch((error) => {
        logger.error({ msg: 'Failed to save mappings', error: error.message });
        throw error;
      });
  }

  async softDeleteEntityFields(mappingType: EntityType, newKeys: string[], tx): Promise<void> {
    const orgId = this.auth.oid;
    const userId = this.auth.uid;
    // Fetch the existing crmMappings for the given orgId, entityType, and crmType
    const existingMappings = await tx.crmMapping.findMany({
      where: {
        orgId: orgId,
        entityType: this.getEntityType(mappingType),
        crmType: this.crmType
      },
      select: {
        crmFieldKey: true
      }
    });

    // Extract existing keys from the crmMapping records
    const existingKeys = existingMappings.map((mapping) => mapping.crmFieldKey);

    // Find keys that are in existing mappings but not in the new mappings
    const keysToSoftDelete = existingKeys.filter((key) => !newKeys.includes(key));

    // Soft delete the entityFields for keys that are no longer present in the new mappings
    if (keysToSoftDelete.length > 0) {
      await tx.entityField.updateMany({
        where: {
          orgId: orgId,
          entity: mappingType,
          key: { in: keysToSoftDelete },
          deletedAt: null, // Only soft delete if not already deleted
          isMandatory: false,
          isInternal: false,
          isAttachment: false
        },
        data: {
          deletedAt: new Date(),
          deletedById: userId
        }
      });
    }

    // Now delete the existing mappings for the given orgId, entityType, and crmType
    await tx.crmMapping.deleteMany({
      where: {
        orgId: orgId,
        entityType: this.getEntityType(mappingType),
        crmType: this.crmType
      }
    });
  }

  async saveSettings(settings: CrmSetting): Promise<boolean> {
    this.userRoleService.validateAdminAccess();
    const org = await db.organization.findUnique({
      where: {
        id: this.auth.oid
      }
    });
    if (!org) {
      throw new Error('Cannot find organization');
    }
    await db.crmSetting.deleteMany({
      where: { orgId: org.id, crmType: this.crmType }
    });
    settings.orgId = org.id;
    await db.crmSetting.create({
      data: {
        orgId: this.auth.oid,
        syncAccounts: settings.syncAccounts,
        syncContacts: settings.syncContacts,
        inProgress: settings.inProgress,
        crmType: this.crmType
      }
    });
    return true;
  }

  async getSettings(): Promise<CrmSetting> {
    const org = await db.organization.findUnique({
      where: {
        id: this.auth.oid
      }
    });
    if (!org) {
      throw new Error('Cannot find organization');
    }
    return await db.crmSetting.findFirst({
      where: {
        orgId: org.id,
        crmType: this.crmType
      }
    });
  }

  async getSyncStatus(): Promise<CrmSyncStatus> {
    return db.crmSyncStatus.findFirst({
      where: { orgId: this.auth.oid, crmType: this.crmType },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  async deleteFilter(id: string): Promise<boolean> {
    this.userRoleService.validateAdminAccess();
    const org = await db.organization.findUnique({
      where: {
        id: this.auth.oid
      }
    });
    if (!org) {
      throw new Error('Cannot find organization');
    }
    await db.crmFilter.deleteMany({
      where: {
        id: id,
        orgId: this.auth.oid
      }
    });
    return true;
  }

  getEntityType(mappingType: EntityType): EntityType {
    return mappingType === EntityType.company ? EntityType.company : EntityType.contact;
  }

  async getOrCreateCrmCompany(company: ISyncData, companyDetails: ICompanyDetailsParams, oid: string, trans: any): Promise<{ companyId: string }> {
    try {
      const domain = getDomainFromUrl(companyDetails.website);
      if (!domain) {
        logger.error({
          msg: 'Invalid domain',
          company: company,
          companyDetails: companyDetails,
          orgId: oid,
          crmType: this.crmType
        });
        throw new BadRequestError('Invalid domain');
      }

      // Attempt to find an existing CrmCompany
      const crmCompany = await trans.crmCompany.findUnique({
        where: {
          crmCompanyConnection: {
            crmId: company.id,
            orgId: oid,
            crmType: this.crmType
          }
        }
      });

      if (!crmCompany) {
        // Create a new CrmCompany if none exists
        const newCrmCompany = await trans.crmCompany.create({
          data: {
            fields: company.fields,
            crmId: company.id,
            orgId: oid,
            crmType: this.crmType,
            company: {
              create: {
                ...companyDetails,
                domain: domain,
                orgId: oid,
                createdById: this.auth.uid,
                createdAt: new Date()
              }
            }
          },
          select: {
            companyId: true
          }
        });
        return newCrmCompany;
      }

      return crmCompany;
    } catch (error) {
      logger.error({
        msg: 'Failed to create or find a CrmCompany',
        company: company,
        companyDetails: companyDetails,
        orgId: oid,
        crmType: this.crmType,
        error: (error as Error).message
      });
      throw error;
    }
  }

  async getOrCreateCrmContact(contact: ISyncData, oid: string, contactDetails: IContactDetailsParams, crmCompanyIds: string[], trans: any): Promise<{ contactId: string }> {
    try {
      // Attempt to find an existing CrmContact
      const existingContact = await trans.crmContact.findUnique({
        where: {
          crmContactConnection: {
            crmId: contact.id,
            orgId: oid,
            crmType: this.crmType
          }
        }
      });

      if (existingContact) {
        return existingContact;
      }

      // Create a new CrmContact if none exists
      const newCrmContact = await trans.crmContact.create({
        data: {
          field: contact.fields,
          crmId: contact.id,
          crmCompanyIds: crmCompanyIds,
          orgId: oid,
          crmType: this.crmType,
          contact: {
            create: {
              ...contactDetails,
              orgId: oid,
              createdById: this.auth.uid,
              createdAt: new Date()
            }
          }
        },
        select: {
          contactId: true
        }
      });

      return newCrmContact;
    } catch (error) {
      logger.error({
        msg: 'Failed to get or create CrmContact',
        contact: contact,
        contactDetails: contactDetails,
        orgId: oid,
        crmType: this.crmType,
        error: (error as Error).message
      });
      throw error;
    }
  }

  async updateCrmContactFields(contact: ISyncData, oid: string, crmCompanyIds: string[], trans: any): Promise<void> {
    return trans.crmContact
      .update({
        where: {
          crmContactConnection: {
            crmId: contact.id,
            orgId: oid,
            crmType: this.crmType
          }
        },
        data: {
          field: contact.fields,
          crmCompanyIds: crmCompanyIds
        }
      })
      .catch((error: Error) => {
        logger.error({
          msg: 'Failed to update CrmContact fields',
          crmId: contact?.id,
          e: error.message
        });
        throw error;
      });
  }

  async mapCrmUser(crmUserEmail: string, userId: string): Promise<UserCrmMappingResult> {
    const crmType = this.crmType;
    const { oid: orgId } = this.auth;
    if (!this.crmInstance) {
      return UserCrmMappingResult.USER_INVALID;
    }
    const crmUsers = await this.crmInstance.getUsers(crmUserEmail);

    if (!crmUsers || crmUsers.length === 0) {
      return UserCrmMappingResult.USER_INVALID;
    }

    const { email, id: crmUserId } = crmUsers[0];

    if (!email || !crmUserId) {
      return UserCrmMappingResult.USER_INVALID;
    }

    const isUsedByAnotherUser = await db.userCrmMapping.findFirst({
      where: {
        orgId: this.auth.oid,
        crmUserId: crmUserId,
        crmType: this.crmType,
        NOT: {
          userId: userId
        }
      }
    });
    if (isUsedByAnotherUser) {
      return UserCrmMappingResult.USER_UNAVAILABLE;
    }

    return db.userCrmMapping
      .upsert({
        where: {
          user_crm_mapping_unique: { userId, orgId, crmType }
        },
        update: {
          email,
          crmUserId,
          updatedAt: new Date()
        },
        create: { userId, orgId, crmUserId, email, crmType }
      })
      .then(() => {
        return UserCrmMappingResult.USER_AVAILABLE;
      })
      .catch((error) => {
        logger.error(error);
        throw new Error(error.message);
      });
  }

  async checkCrmUserEmail(crmUserEmail: string): Promise<UserCrmMappingResult> {
    if (!this.crmInstance) {
      return UserCrmMappingResult.USER_INVALID;
    }

    const crmUsers = await this.crmInstance.getUsers(crmUserEmail);
    if (!crmUsers || crmUsers.length === 0) {
      return UserCrmMappingResult.USER_INVALID;
    }
    const { email, id: crmUserId } = crmUsers[0];

    if (!email || !crmUserId) {
      return UserCrmMappingResult.USER_INVALID;
    }

    const userId = this.auth.uid;
    const isUsedByAnotherUser = await db.userCrmMapping.findFirst({
      where: {
        orgId: this.auth.oid,
        crmUserId: crmUserId,
        crmType: this.crmType,
        NOT: {
          userId: userId
        }
      }
    });
    if (isUsedByAnotherUser) {
      return UserCrmMappingResult.USER_UNAVAILABLE;
    }
    return UserCrmMappingResult.USER_AVAILABLE;
  }

  async sync(setting: ISyncSettings): Promise<ISyncResult> {
    const { syncContacts, syncAccounts, userSync } = setting;
    logger.info({
      crm: this.crmType,
      msg: 'Starting the sync procedure.',
      syncContacts,
      syncAccounts,
      userSync
    });
    if (!this.userRoleService.isAdmin() && !userSync) {
      logger.error({ msg: SERVER_ERROR_MESSAGE.Unauthorized, auth: this.auth });
      throw new APIError('Unauthorized', {
        code: 403,
        details: SERVER_ERROR_MESSAGE.Unauthorized
      });
    }

    if (!this.crmInstance) {
      logger.error({ msg: 'CRM instance not found' });
      return { status: SyncStatus.FAILED, msg: 'CRM instance not found' };
    }

    try {
      const syncSetting = await db.crmSetting.findFirst({
        where: { orgId: this.auth.oid, crmType: this.crmType },
        select: {
          inProgress: true
        }
      });

      // Check if do field mapping exist for the CRM
      const contactMappings = await this.getMappings(EntityType.contact);
      const accountMappings = await this.getMappings(EntityType.company);

      const hasContactMapping = contactMappings?.length > 0;
      const hasAccountMapping = accountMappings?.length > 0;

      if (!hasContactMapping || !hasAccountMapping) {
        return {
          status: SyncStatus.FAILED,
          msg: !hasContactMapping && !hasAccountMapping ? 'Set up Company and Contact Mappings to fetch records from your CRM account.' : !hasContactMapping ? 'Set up Contact Mappings to fetch records from your CRM account.' : 'Set up Company Mappings to fetch records from your CRM account.'
        };
      }

      if (syncSetting?.inProgress) {
        logger.warn({ crm: this.crmType, msg: 'Sync is already in progress.', orgId: this.auth.oid, uId: this.auth.uid });
        return {
          status: SyncStatus.PROGRESS,
          msg: 'Sync is already in progress.'
        };
      }

      await this.syncInProgress(true).catch((error) => {
        logger.error({ msg: 'Failed to set sync in progress', error });
        return {
          status: SyncStatus.FAILED,
          msg: 'Failed to set sync in progress'
        };
      });

      let contactSyncStatus = false;
      let accountSyncStatus = false;

      if (syncAccounts) {
        accountSyncStatus = await this.accountSync(userSync);
      }
      if (syncContacts) {
        contactSyncStatus = await this.contactSync(userSync);
      }
      const deleteSyncStatus = await this.crmInstance.syncDeletion(userSync);

      const contactsResult = !syncContacts || contactSyncStatus;
      const accountsResult = !syncAccounts || accountSyncStatus;
      const status = contactsResult && accountsResult && deleteSyncStatus;

      await db.crmSyncStatus.create({
        data: {
          orgId: this.auth.oid,
          contactSynced: syncContacts,
          accountSynced: syncAccounts,
          status,
          crmType: this.crmType
        }
      });

      if (contactsResult && accountsResult && deleteSyncStatus) {
        return {
          status: SyncStatus.COMPLETED,
          msg: ''
        };
      } else {
        return {
          status: SyncStatus.FAILED,
          msg: 'Failed to sync'
        };
      }
    } catch (error) {
      logger.error({ msg: 'Failed to sync', error: (error as Error).message });
      return { status: SyncStatus.FAILED, msg: 'Failed to sync' };
    } finally {
      await this.syncInProgress(false).catch((error) => {
        logger.error({ msg: 'Failed to unset sync progress', error });
      });
    }
  }

  getCrmEntity(entity: CrmEntityType): CrmEntity {
    switch (entity) {
      case CrmEntityType.event:
        return CrmEntity.EVENT;
      case CrmEntityType.task:
        return CrmEntity.TASK;
      case CrmEntityType.interaction:
        return CrmEntity.INTERACTION;
      default:
        return CrmEntity.INTERACTION;
    }
  }

  async saveActivities(activities: IActivity[]) {
    const { oid } = this.auth;
    logger.debug({
      msg: 'Starting to save activities',
      count: activities ? activities.length : 0
    });
    try {
      await Promise.map(
        activities,
        async (activity: IActivity) => {
          // Need to convert the $Enums.CrmEntityType to  type to uppercase
          const crmEntityType = this.getCrmEntity(activity.entityType);
          try {
            await this.activityService.saveActivity(activity, oid, this.crmType);
            logger.debug({
              msg: 'Activity saved successfully',
              crmId: activity?.id,
              orgId: oid
            });

            this.updateSyncStatus(crmEntityType, activity.id, true);
          } catch (error) {
            logger.error({
              msg: 'Failed to save activity',
              crmId: activity?.id,
              orgId: oid,
              error: (error as Error).message
            });
            this.updateSyncStatus(crmEntityType, activity.id, false, (error as Error).message);
            throw error;
          }
        },
        { concurrency: CONCURRENT_TRANSACTION_COUNT }
      );
      logger.debug({
        msg: 'Save activities completed successful.',
        count: activities ? activities.length : 0
      });
      return true;
    } catch (error) {
      logger.error({
        msg: 'Error occurred while saving activities',
        error: (error as Error).message
      });
      throw error;
    }
  }

  async saveContacts(contacts: ISyncData[]) {
    const { oid } = this.auth;
    logger.debug({
      msg: 'Starting to save contacts',
      count: contacts ? contacts.length : 0
    });

    try {
      await Promise.map(
        contacts,
        async (contact: ISyncData) => {
          try {
            await this.contactService.saveContact(contact, oid, this.crmInstance);
            logger.debug({
              msg: 'Contact saved successfully',
              crmId: contact?.id,
              orgId: oid
            });
            // Update sync status as successful
            this.updateSyncStatus(CrmEntity.CONTACT, contact.id, true);
          } catch (error) {
            logger.error({
              msg: 'Failed to save contact',
              crmId: contact?.id,
              orgId: oid,
              error: (error as Error).message
            });
            this.updateSyncStatus(CrmEntity.CONTACT, contact.id, false, (error as Error).message);
            throw error;
          }
        },
        { concurrency: CONCURRENT_TRANSACTION_COUNT }
      );
      logger.debug({
        msg: 'Save contacts completed successful.',
        count: contacts ? contacts.length : 0
      });

      return true;
    } catch (error) {
      logger.error({
        msg: 'Error occurred while saving contacts',
        error: (error as Error).message
      });
      throw error;
    }
  }

  async saveCompanies(companies: ISyncData[]) {
    const { oid } = this.auth;
    logger.debug({
      msg: 'Starting to save companies',
      count: companies.length
    });

    try {
      await Promise.map(
        companies,
        async (company: ISyncData) => {
          try {
            logger.debug({
              msg: 'Processing company',
              crmId: company?.id,
              orgId: oid
            });
            await this.companyService.saveCompany(company, oid, this.crmInstance);

            logger.debug({
              msg: 'Company saved successfully',
              crmId: company?.id,
              orgId: oid
            });

            // Update sync status as successful
            this.updateSyncStatus(CrmEntity.ACCOUNT, company.id, true);
          } catch (error) {
            logger.error({
              msg: 'Failed to save company',
              crmId: company?.id,
              orgId: oid,
              error: (error as Error).message
            });

            // Update sync status with error
            this.updateSyncStatus(CrmEntity.ACCOUNT, company.id, false, (error as Error).message);
            throw error;
          }
        },
        { concurrency: CONCURRENT_TRANSACTION_COUNT }
      );

      logger.debug({
        msg: 'Save companies completed successful.',
        count: companies.length
      });
      return true;
    } catch (error) {
      logger.error({
        msg: 'Error occurred while saving companies',
        error: (error as Error).message
      });
      throw error;
    }
  }

  async syncInProgress(status: boolean): Promise<boolean> {
    try {
      const crmSetting = await db.crmSetting.findFirst({
        where: { orgId: this.auth.oid, crmType: this.crmType }
      });
      if (!crmSetting) {
        return false;
      }
      await db.crmSetting.update({
        where: { id: crmSetting.id },
        data: {
          inProgress: status
        }
      });
      return true;
    } catch (error) {
      logger.error({ msg: 'Failed to update sync status', error });
      throw new Error('Failed to update sync status');
    }
  }

  async getUserCrmEmail(uid: string, oid: string): Promise<string | null> {
    return db.userCrmMapping
      .findUnique({
        where: {
          user_crm_mapping_unique: {
            userId: uid,
            orgId: oid,
            crmType: this.crmType
          }
        },
        select: {
          email: true
        }
      })
      .then((setting) => {
        return setting ? setting.email : null;
      })
      .catch((error) => {
        logger.error('Error in getUserCRMEmail:', error);
        throw error;
      });
  }

  async getUserCrmMapping(): Promise<IUserCrmMap | null> {
    return db.userCrmMapping
      .findFirst({
        where: {
          userId: this.auth.uid,
          orgId: this.auth.oid,
          crmType: this.crmType
        },
        select: {
          email: true,
          crmUserId: true
        }
      })
      .then((setting) => {
        return setting ? setting : null;
      })
      .catch((error) => {
        logger.error('Error in getUserCRMMapping:', error);
        throw error;
      });
  }

  async removeIntegration(integrationId: string): Promise<boolean> {
    logger.info({ crm: this.crmType, msg: 'Removing integration', integrationId });
    this.userRoleService.validateAdminAccess();
    const orgId = this.auth.oid;
    const integration = await db.orgIntegration.findFirst({
      where: {
        id: integrationId,
        orgId: orgId,
        type: IntegrationType.crm
      }
    });
    if (!integration) {
      throw new BadRequestError('Integration not found');
    }

    return db
      .$transaction(async (tx): Promise<boolean> => {
        await this.softDeleteEntityFields(EntityType.company, [], tx);
        await this.softDeleteEntityFields(EntityType.contact, [], tx);
        await tx.crmFilter.deleteMany({ where: { orgId: orgId, crmType: this.crmType } });
        await tx.crmSetting.deleteMany({ where: { orgId: orgId, crmType: this.crmType } });
        await tx.crmSyncStatus.deleteMany({ where: { orgId: orgId, crmType: this.crmType } });
        await tx.orgIntegration.delete({
          where: {
            id: integrationId
          },
          select: {
            id: true
          }
        });
        return true;
      })
      .catch((error) => {
        logger.error({ msg: 'Failed to remove integration', error });
        throw new Error('Failed to remove integration');
      });
  }

  async getOwnerId(): Promise<string | null> {
    try {
      const setting = await db.userCrmMapping.findUnique({
        where: {
          user_crm_mapping_unique: {
            userId: this.auth.uid,
            orgId: this.auth.oid,
            crmType: this.crmType
          }
        },
        select: {
          crmUserId: true
        }
      });
      return setting ? setting.crmUserId : null;
    } catch (error) {
      logger.error({ msg: 'Cannot get ownerId', error });
      throw error;
    }
  }

  async contactSync(userSync: boolean = false): Promise<boolean> {
    if (!this.crmInstance) {
      logger.error({ msg: 'CRM instance not found' });
      return false;
    }
    try {
      logger.info({ crm: this.crmType, msg: 'Syncing contacts' });
      const { mainData: contacts, relatedData: accounts, activities: activities } = await this.crmInstance.getSyncRecords(EntityType.contact, userSync);
      if (!contacts && !accounts) {
        logger.info({ crm: this.crmType, msg: 'No records found to sync' });
        return false;
      }
      logger.info({
        crm: this.crmType,
        msg: 'Completed fetching records, Started saving to db.',
        contacts: contacts.length,
        accounts: accounts.length,
        activities: activities.length
      });
      return await this.saveSyncRecords(contacts, accounts, activities);
    } catch (error) {
      logger.error({ crm: this.crmType, msg: 'Failed to sync contacts', error: (error as Error).message });
      return false;
    }
  }

  async accountSync(userSync: boolean = false): Promise<boolean> {
    if (!this.crmInstance) {
      logger.error({ crm: this.crmType, msg: 'CRM instance not found' });
      return false;
    }
    try {
      logger.info({ crm: this.crmType, msg: 'Syncing accounts' });
      const { mainData: accounts, relatedData: contacts, activities: activities } = await this.crmInstance.getSyncRecords(EntityType.company, userSync);
      if (!accounts && !contacts) {
        logger.info({ crm: this.crmType, msg: 'No records found to sync' });
        return false;
      }
      logger.info({
        crm: this.crmType,
        msg: 'Completed fetching records, Started saving to db.',
        accounts: accounts.length,
        contacts: contacts.length,
        activities: activities.length
      });

      return await this.saveSyncRecords(contacts, accounts, activities);
    } catch (error) {
      logger.error({ crm: this.crmType, msg: 'Failed to sync accounts', error: (error as Error).message });
      return false;
    }
  }

  async saveSyncRecords(contacts: ISyncData[], accounts: ISyncData[], activities: IActivity[]): Promise<boolean> {
    logger.debug({ msg: 'Saving sync records', contacts, accounts });

    try {
      const contactsResult = await this.saveContacts(contacts);
      const accountsResult = await this.saveCompanies(accounts);
      const activitiesResult = await this.saveActivities(activities);

      if (contactsResult && accountsResult && activitiesResult) {
        return await this.updateMapping(contacts);
      }

      return false;
    } catch (error) {
      logger.error({ msg: 'Failed to save sync records', error: (error as Error).message });
      throw error; // This will propagate the error to the calling function
    }
  }

  async updateMapping(contacts: ISyncData[]): Promise<boolean> {
    const { oid } = this.auth;
    if (!Array.isArray(contacts)) {
      throw new Error('Contacts should be an array.');
    }

    for (const contact of contacts) {
      try {
        await this.mapContact(contact, oid);
      } catch (error) {
        logger.error({
          msg: 'Failed to update mapping',
          error: (error as Error).message
        });
        throw error;
      }
    }

    logger.info({ crm: this.crmType, msg: 'Mapping updated successfully', count: contacts?.length });
    return true;
  }

  async mapContact(contact: ISyncData, oid: string): Promise<void> {
    try {
      const crmContact = await db.crmContact.findUnique({
        where: {
          crmContactConnection: {
            crmId: contact.id,
            orgId: oid,
            crmType: this.crmType
          }
        },
        select: {
          contactId: true,
          crmCompanyIds: true
        }
      });

      if (!crmContact?.contactId || !crmContact?.crmCompanyIds) {
        return;
      }

      const crmCompanyIds = (crmContact.crmCompanyIds as string[]).map(String);

      const crmCompanies = await db.crmCompany.findMany({
        where: {
          crmId: {
            in: crmCompanyIds
          },
          orgId: oid,
          crmType: this.crmType
        },
        select: {
          companyId: true
        }
      });

      if (!crmCompanies || crmCompanies.length === 0) {
        return;
      }

      for (const crmCompany of crmCompanies) {
        await this.contactService.createMapping(crmContact.contactId, crmCompany.companyId);
      }
    } catch (error) {
      logger.debug({
        msg: 'Failed to update mapping',
        crmId: contact?.id,
        error: (error as Error).message
      });
      throw error;
    }
  }

  protected getField<T>(data: ISyncData, fieldName: string): T | null {
    const field = data.fields.find((field: ISyncField) => field.fieldKey === fieldName)?.value;

    if (field === undefined || field === null) {
      return null;
    }

    try {
      return field as T;
    } catch (_error) {
      return null;
    }
  }

  protected updateSyncStatus(entity: CrmEntity, entityId: string, synced: boolean, error?: string) {
    const existingRecord = this.syncedRecords.find((record) => record.entity === entity && record.entityId === entityId);

    if (existingRecord) {
      // Update the existing record
      existingRecord.synced = synced;
      existingRecord.error = error;
    } else {
      // Add a new record if not found
      this.syncedRecords.push({
        entity,
        entityId,
        synced,
        error
      });
    }
  }

  protected getDataSourceType(): DataSourceType {
    switch (this.crmType) {
      case CrmType.salesforce:
        return DataSourceType.salesforce;
      case CrmType.affinity:
        return DataSourceType.affinity;
      default:
        return DataSourceType.salesforce;
    }
  }

  async deleteRecords(deletedRecords: string[], entityType: CrmEntity): Promise<boolean> {
    logger.debug({ msg: 'Deleting records', deletedRecords, entityType });
    switch (entityType) {
      case CrmEntity.ACCOUNT:
        return this.deleteCompanies(deletedRecords);
      case CrmEntity.CONTACT:
        return this.deleteContacts(deletedRecords);
      case CrmEntity.TASK:
      case CrmEntity.EVENT:
      case CrmEntity.INTERACTION:
        return this.deleteActivities(deletedRecords, entityType);
      default:
        return false;
    }
  }

  async deleteActivities(deletedRecords: string[], entityType: CrmEntity): Promise<boolean> {
    const { oid, uid } = this.auth;
    let success = true;

    logger.debug({ msg: 'Deleting activities', deletedRecords, orgId: oid });

    try {
      // Convert deletedRecords to a string of UUIDs for SQL
      const crmIds = deletedRecords.map((record) => `'${record}'`).join(',');

      const sql = `
            UPDATE "activity"
            SET "deleted_at" = NOW(),
                "deleted_by_id" = '${uid}'
            WHERE "org_id" = '${oid}'
            AND id IN (
                SELECT "activity_id"
                FROM "crm_activity"
                WHERE "entity_type" = '${entityType.toLowerCase()}'
                AND "crm_type" = '${this.getDataSourceType()}'
                AND "crm_id" IN (${crmIds})
            )
        `;

      const result = await db.$executeRawUnsafe(sql); // Use $executeRawUnsafe to run raw SQL

      logger.debug({ msg: 'Deleted activities', numberOfRecords: deletedRecords.length, result });
    } catch (error) {
      logger.error({ msg: 'Failed to delete activities', error: (error as Error).message });
      success = false;
    }

    return success;
  }

  async deleteCompanies(deletedRecords: string[]): Promise<boolean> {
    const { oid, uid } = this.auth;
    let success = true;

    logger.debug({ msg: 'Deleting companies', deletedRecords, orgId: oid });

    try {
      // Convert deletedRecords to a string of UUIDs for SQL
      const crmIds = deletedRecords.map((record) => `'${record}'`).join(',');

      const sql = `
            UPDATE "company"
            SET "deleted_at" = NOW(),
                "updated_at" = NOW(),
                "deleted_by_id" = '${uid}'
            WHERE "org_id" = '${oid}'
            AND "id" IN (
                SELECT "company_id"
                FROM "crm_company"
                WHERE "crm_id" IN (${crmIds})
            )
        `;

      const result = await db.$executeRawUnsafe(sql); // Use $executeRawUnsafe to run raw SQL

      logger.debug({ msg: 'Deleted companies', numberOfRecords: deletedRecords.length, result });
    } catch (error) {
      logger.error({ msg: 'Failed to delete companies', error });
      success = false;
    }

    return success;
  }

  async deleteContacts(deletedRecords: string[]): Promise<boolean> {
    const { oid, uid } = this.auth;
    let success = true;

    logger.debug({ msg: 'Deleting contacts', deletedRecords, orgId: oid });

    try {
      // Convert deletedRecords to a string of UUIDs for SQL
      const crmIds = deletedRecords.map((record) => `'${record}'`).join(',');

      const sql = `
            UPDATE "contact"
            SET "deleted_at" = NOW(),
                "updated_at" = NOW(),
                "deleted_by_id" = '${uid}'
            WHERE "org_id" = '${oid}'
            AND "id" IN (
                SELECT "contact_id"
                FROM "crm_contact"
                WHERE "crm_id" IN (${crmIds})
            )
        `;

      const result = await db.$executeRawUnsafe(sql); // Use $executeRawUnsafe to run raw SQL

      logger.debug({ msg: 'Deleted contacts', numberOfRecords: deletedRecords.length, result });
    } catch (error) {
      logger.error({ msg: 'Failed to delete contacts', error });
      success = false;
    }

    return success;
  }

  async getCrmIds(entityType: CrmEntity, userSync: boolean = false): Promise<string[] | null> {
    const orgId = this.auth.oid;
    const sourceType = this.getDataSourceType();
    const ownerId = userSync ? this.auth.uid : null;

    switch (entityType) {
      case CrmEntity.ACCOUNT:
        return this.getCrmCompanyIds(orgId, sourceType, ownerId);
      case CrmEntity.CONTACT:
        return this.getCrmContactIds(orgId, sourceType, ownerId);
      case CrmEntity.TASK:
      case CrmEntity.EVENT:
      case CrmEntity.INTERACTION:
        return this.getCrmActivityIds(orgId, entityType, sourceType, ownerId);
      default:
        return null;
    }
  }

  async getCrmActivityIds(orgId: string, entityType: CrmEntity, sourceType: string, ownerId: string | null): Promise<string[] | null> {
    logger.debug({ msg: 'getCrmActivityIds', orgId, sourceType, ownerId });
    try {
      // Construct SQL query to fetch all crmId values for activities
      const sql = `
            SELECT ca."crm_id"
            FROM "activity" AS a
            JOIN "crm_activity" AS ca ON a."id" = ca."activity_id"
            WHERE a."org_id" = '${orgId}'
              AND a."source_type" = 'crm'
              AND ca."crm_type" = '${sourceType}'
              AND ca."entity_type" = '${entityType.toLowerCase()}'
              AND a."deleted_at" IS NULL
              AND ca."crm_id" IS NOT NULL
        `;

      const activities = await db.$queryRawUnsafe<{ crm_id: string }[]>(sql);
      logger.debug({ msg: 'Fetched all activity crmIds', activities });
      const crmIds = activities.map((a) => a.crm_id);
      return crmIds.length === 0 ? null : crmIds;
    } catch (error) {
      logger.error({ msg: 'Error getting activity ids', error: (error as Error).message });
      throw error;
    }
  }

  async getCrmCompanyIds(orgId: string, sourceType: string, ownerId: string | null): Promise<string[] | null> {
    logger.debug({ msg: 'getCrmCompanyIds', orgId, sourceType, ownerId });

    try {
      // Construct SQL query to fetch all crmId values for companies
      const sql = `
            SELECT cc."crm_id"
            FROM "company" AS c
            JOIN "crm_company" AS cc ON c."id" = cc."company_id"
            WHERE cc."org_id" = '${orgId}'
              AND c."source_type" = '${sourceType}'
              AND c."deleted_at" IS NULL
              ${ownerId ? `AND c."owner_id" = '${ownerId}'` : ''}
              AND cc."crm_id" IS NOT NULL
        `;

      const companies = await db.$queryRawUnsafe<{ crm_id: string }[]>(sql);

      logger.debug({ msg: 'Fetched all company crmIds', companies });

      const crmIds = companies.map((c) => c.crm_id);

      return crmIds.length === 0 ? null : crmIds;
    } catch (error) {
      logger.error({ msg: 'Error getting company ids', error: (error as Error).message });
      throw error;
    }
  }

  async getCrmContactIds(orgId: string, sourceType: string, ownerId: string | null): Promise<string[] | null> {
    logger.debug({ msg: 'getCrmContactIds', orgId, sourceType, ownerId });

    try {
      // Construct SQL query to fetch all crmId values for contacts
      const sql = `
            SELECT cc."crm_id"
            FROM "contact" AS c
            JOIN "crm_contact" AS cc ON c."id" = cc."contact_id"
            WHERE cc."org_id" = '${orgId}'
              AND c."source_type" = '${sourceType}'
              AND c."deleted_at" IS NULL
              ${ownerId ? `AND c."owner_id" = '${ownerId}'` : ''}
              AND cc."crm_id" IS NOT NULL
        `;

      const contacts = await db.$queryRawUnsafe<{ crm_id: string }[]>(sql);

      logger.debug({ msg: 'Fetched all contact crmIds', contacts });

      const crmIds = contacts.map((c) => c.crm_id);

      return crmIds.length === 0 ? null : crmIds;
    } catch (error) {
      logger.error({ msg: 'Error getting contact ids', error: (error as Error).message });
      throw error;
    }
  }
}
