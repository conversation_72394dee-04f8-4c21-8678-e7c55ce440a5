import { ScoringFieldSource, EntityType, ExecutionType, ExecutionStatus, QueueTaskType, ScoringOperator, FieldDataType } from '@prisma/client';
import { db } from '../database';
import { logger } from '../logger';
import { IAuth } from '../types/Common';
import { ICreateScoringModelParams, IListScoringModelsParams, MANDATORY_FIELDS, IScoringModel, IRuleSet, IScoringFieldData } from '../types/ScoringModel';
import NotificationService from './Notification';
import { validate as uuidValidate } from 'uuid';

interface IScoringModelServiceOptions {
  auth: IAuth;
  notificationService?: NotificationService;
  writeResultsToSocket?: boolean;
}

export default class ScoringModelService {
  auth: IAuth;
  notificationService?: NotificationService;
  writeResultsToSocket: boolean;

  constructor({ auth, notificationService, writeResultsToSocket = false }: IScoringModelServiceOptions) {
    this.auth = auth;
    this.notificationService = notificationService;
    this.writeResultsToSocket = writeResultsToSocket;

    if (!auth.oid) {
      logger.error({ error: 'Invalid organization Id' });
      throw new Error('Invalid service call! Check your permissions!');
    }
  }

  private validateRuleConditions(ruleSet: IRuleSet): void {
    const { fieldData, rules } = ruleSet;
    const { fieldType } = fieldData;

    for (const rule of rules) {
      const { operator, value } = rule;

      // Validate operator is supported for the field type
      const supportedOperators = this.getSupportedOperators(fieldType);
      if (!supportedOperators.includes(operator)) {
        throw new Error(`Operator ${operator} is not supported for field type ${fieldType}`);
      }

      // Validate value matches field type
      if (operator === ScoringOperator.equals || operator === ScoringOperator.not_equals) {
        this.validateValueType(value.primary, fieldType);
      }

      // Validate secondary value for between operators
      if ((operator === ScoringOperator.between || operator === ScoringOperator.not_between) && value.secondary) {
        this.validateValueType(value.secondary, fieldType);
      }
    }
  }

  private getSupportedOperators(fieldType: FieldDataType): ScoringOperator[] {
    switch (fieldType) {
      case FieldDataType.int:
      case FieldDataType.double:
        return [ScoringOperator.equals, ScoringOperator.not_equals, ScoringOperator.greater_than, ScoringOperator.less_than, ScoringOperator.between, ScoringOperator.not_between];
      case FieldDataType.boolean:
        return [ScoringOperator.equals, ScoringOperator.not_equals];
      case FieldDataType.string:
        return [ScoringOperator.contain, ScoringOperator.not_contain, ScoringOperator.starts_with, ScoringOperator.ends_with, ScoringOperator.equals, ScoringOperator.not_equals];
      default:
        return [ScoringOperator.contain, ScoringOperator.not_contain, ScoringOperator.starts_with, ScoringOperator.ends_with, ScoringOperator.equals, ScoringOperator.not_equals];
    }
  }

  private validateValueType(value: string, fieldType: FieldDataType): void {
    switch (fieldType) {
      case FieldDataType.int:
      case FieldDataType.double:
        if (isNaN(Number(value))) {
          throw new Error(`Value ${value} is not a valid number for field type ${fieldType}`);
        }
        break;
      case FieldDataType.boolean:
        if (value.toLowerCase() !== 'true' && value.toLowerCase() !== 'false') {
          throw new Error(`Value ${value} is not a valid boolean for field type ${fieldType}`);
        }
        break;
      case FieldDataType.string:
        // String values are always valid
        break;
      case FieldDataType.array:
        // No validation needed for arrays as they are handled elsewhere or assumed valid
        break;
      default:
        throw new Error(`Unsupported field type ${fieldType}`);
    }
  }

  async createScoringModel(params: ICreateScoringModelParams): Promise<IScoringModel> {
    const { name, description, listCompanyId, ruleSets } = params;

    // Validate list company ID
    if (!listCompanyId) {
      throw new Error('Invalid list company ID');
    }

    // Validate at least one rule set
    if (!ruleSets || ruleSets.length === 0) {
      throw new Error('At least one rule set is required');
    }

    // Validate individual weights are between 0-100
    if (ruleSets.some((ruleSet) => ruleSet.weight < 0 || ruleSet.weight > 100)) {
      throw new Error('Individual rule set weights must be between 0 and 100');
    }

    // Validate scores are between 0-10
    if (ruleSets.some((ruleSet) => ruleSet.defaultScore < 0 || ruleSet.defaultScore > 10 || ruleSet.rules.some((rule) => rule.score < 0 || rule.score > 10))) {
      throw new Error('Scores must be between 0 and 10');
    }

    // Validate field data based on source type
    for (const ruleSet of ruleSets) {
      const { fieldData } = ruleSet;

      if (fieldData.sourceType === ScoringFieldSource.mandatory) {
        // For mandatory fields, only companyName and website are allowed
        if (!MANDATORY_FIELDS.includes(fieldData.fieldPath as any)) {
          throw new Error(`Invalid mandatory field: ${fieldData.fieldPath}. Only ${MANDATORY_FIELDS.join(', ')} are allowed.`);
        }
      } else if (fieldData.sourceType === ScoringFieldSource.enrichment) {
        // For enrichment fields, enrichmentId must be provided
        if (!fieldData.enrichmentId) {
          throw new Error('Enrichment ID is required for enrichment field type');
        }
      }

      // Validate rule conditions and values
      this.validateRuleConditions(ruleSet);
    }

    return db.$transaction(async (tx) => {
      // Create the scoring model
      const scoringModel = await tx.scoringModel.create({
        data: {
          name,
          description,
          listCompanyId,
          createdById: this.auth.uid
        }
      });

      // Create rule sets and their associated data
      for (const ruleSet of ruleSets) {
        // Create field data
        const fieldData = await tx.scoringFieldData.create({
          data: {
            sourceType: ruleSet.fieldData.sourceType,
            fieldType: ruleSet.fieldData.fieldType,
            fieldPath: ruleSet.fieldData.fieldPath,
            enrichmentId: ruleSet.fieldData.enrichmentId
          }
        });

        // Create rule set with fieldDataId
        const createdRuleSet = await tx.scoringRuleSet.create({
          data: {
            label: ruleSet.label,
            weight: ruleSet.weight,
            defaultScore: ruleSet.defaultScore,
            scoringModelId: scoringModel.id,
            fieldDataId: fieldData.id
          }
        });

        // Create rules
        await Promise.all(
          ruleSet.rules.map((rule) =>
            tx.scoringRule.create({
              data: {
                operator: rule.operator,
                value: rule.value,
                score: rule.score,
                ruleSetId: createdRuleSet.id
              }
            })
          )
        );
      }

      return scoringModel;
    });
  }

  async listScoringModels(params: IListScoringModelsParams): Promise<IScoringModel[]> {
    const { listCompanyId } = params;

    return db.scoringModel.findMany({
      where: {
        listCompanyId,
        deletedAt: null
      },
      include: {
        ruleSets: {
          include: {
            fieldData: true,
            rules: true
          }
        },
        createdBy: true
      }
    });
  }

  async getScoringModels(): Promise<IScoringModel[]> {
    const models = await db.scoringModel.findMany({
      where: {
        deletedAt: null
      },
      include: {
        ruleSets: {
          include: {
            fieldData: true,
            rules: true
          }
        }
      }
    });

    return models.map((model) => ({
      ...model,
      ruleSets: model.ruleSets.map((ruleSet) => ({
        id: ruleSet.id,
        createdAt: ruleSet.createdAt,
        updatedAt: ruleSet.updatedAt,
        label: ruleSet.label,
        weight: ruleSet.weight,
        defaultScore: ruleSet.defaultScore,
        scoringModelId: ruleSet.scoringModelId,
        fieldDataId: ruleSet.fieldDataId,
        fieldData: ruleSet.fieldData as IScoringFieldData,
        rules: ruleSet.rules
      })) as IRuleSet[]
    }));
  }

  async getScoringModel(id: string): Promise<IScoringModel | null> {
    if (!uuidValidate(id)) {
      throw new Error('Invalid scoring model ID');
    }

    const model = await db.scoringModel.findUnique({
      where: { id },
      include: {
        ruleSets: {
          include: {
            fieldData: true,
            rules: true
          }
        }
      }
    });

    if (!model) {
      throw new Error('Scoring model not found');
    }

    return model;
  }

  async updateScoringModel(id: string, params: ICreateScoringModelParams): Promise<IScoringModel> {
    if (!uuidValidate(id)) {
      throw new Error('Invalid scoring model ID');
    }

    // Get existing scoring model
    const existingModel = await db.scoringModel.findUnique({
      where: { id },
      include: {
        ruleSets: {
          include: {
            fieldData: true,
            rules: true
          }
        }
      }
    });

    if (!existingModel) {
      throw new Error('Scoring model not found');
    }

    const { name, description, ruleSets } = params;

    // Validate at least one rule set
    if (!ruleSets || ruleSets.length === 0) {
      throw new Error('At least one rule set is required');
    }

    // Validate individual weights are between 0-100
    if (ruleSets.some((ruleSet) => ruleSet.weight < 0 || ruleSet.weight > 100)) {
      throw new Error('Individual rule set weights must be between 0 and 100');
    }

    // Validate scores are between 0-10
    if (ruleSets.some((ruleSet) => ruleSet.defaultScore < 0 || ruleSet.defaultScore > 10 || ruleSet.rules.some((rule) => rule.score < 0 || rule.score > 10))) {
      throw new Error('Scores must be between 0 and 10');
    }

    // Validate field data based on source type
    for (const ruleSet of ruleSets) {
      const { fieldData } = ruleSet;

      if (fieldData.sourceType === ScoringFieldSource.mandatory) {
        // For mandatory fields, only companyName and website are allowed
        if (!MANDATORY_FIELDS.includes(fieldData.fieldPath as any)) {
          throw new Error(`Invalid mandatory field: ${fieldData.fieldPath}. Only ${MANDATORY_FIELDS.join(', ')} are allowed.`);
        }
      } else if (fieldData.sourceType === ScoringFieldSource.enrichment) {
        // For enrichment fields, enrichmentId must be provided
        if (!fieldData.enrichmentId) {
          throw new Error('Enrichment ID is required for enrichment field type');
        }
      }

      // Validate rule conditions and values
      this.validateRuleConditions(ruleSet);
    }

    return db.$transaction(async (tx) => {
      // Update the scoring model
      const scoringModel = await tx.scoringModel.update({
        where: { id },
        data: {
          name,
          description,
          updatedById: this.auth.uid,
          updatedAt: new Date()
        }
      });

      // Get existing rule set IDs
      const existingRuleSetIds = existingModel.ruleSets.map((rs) => rs.id);
      const updatedRuleSetIds: string[] = [];

      // Update or create rule sets
      for (const ruleSet of ruleSets) {
        let ruleSetId: string;
        let fieldDataId: string;

        // Handle field data first
        if (ruleSet.fieldData.id) {
          // Check if field data exists
          const existingFieldData = await tx.scoringFieldData.findUnique({
            where: { id: ruleSet.fieldData.id }
          });

          if (existingFieldData) {
            // Update existing field data
            fieldDataId = ruleSet.fieldData.id;
            const updateData: any = {
              sourceType: ruleSet.fieldData.sourceType,
              fieldType: ruleSet.fieldData.fieldType,
              fieldPath: ruleSet.fieldData.fieldPath
            };

            // Handle enrichmentId based on source type
            if (ruleSet.fieldData.sourceType === ScoringFieldSource.enrichment) {
              // For enrichment fields, enrichmentId is required
              updateData.enrichmentId = ruleSet.fieldData.enrichmentId;
            } else {
              // For mandatory fields, set enrichmentId to null
              updateData.enrichmentId = null;
            }

            await tx.scoringFieldData.update({
              where: { id: ruleSet.fieldData.id },
              data: updateData
            });
          } else {
            // Create new field data if it doesn't exist
            const newFieldData = await tx.scoringFieldData.create({
              data: {
                sourceType: ruleSet.fieldData.sourceType,
                fieldType: ruleSet.fieldData.fieldType,
                fieldPath: ruleSet.fieldData.fieldPath,
                enrichmentId: ruleSet.fieldData.sourceType === ScoringFieldSource.enrichment ? ruleSet.fieldData.enrichmentId : null
              }
            });
            fieldDataId = newFieldData.id;
          }
        } else {
          // Create new field data
          const newFieldData = await tx.scoringFieldData.create({
            data: {
              sourceType: ruleSet.fieldData.sourceType,
              fieldType: ruleSet.fieldData.fieldType,
              fieldPath: ruleSet.fieldData.fieldPath,
              enrichmentId: ruleSet.fieldData.sourceType === ScoringFieldSource.enrichment ? ruleSet.fieldData.enrichmentId : null
            }
          });
          fieldDataId = newFieldData.id;
        }

        if (ruleSet.id && existingRuleSetIds.includes(ruleSet.id)) {
          // Update existing rule set
          ruleSetId = ruleSet.id;
          await tx.scoringRuleSet.update({
            where: { id: ruleSet.id },
            data: {
              label: ruleSet.label,
              weight: ruleSet.weight,
              defaultScore: ruleSet.defaultScore,
              fieldDataId
            }
          });
        } else {
          // Create new rule set with the new field data ID
          const newRuleSet = await tx.scoringRuleSet.create({
            data: {
              label: ruleSet.label,
              weight: ruleSet.weight,
              defaultScore: ruleSet.defaultScore,
              scoringModelId: id,
              fieldDataId
            }
          });
          ruleSetId = newRuleSet.id;
        }
        updatedRuleSetIds.push(ruleSetId);

        // Handle rules
        const existingRules = existingModel.ruleSets.find((rs) => rs.id === ruleSetId)?.rules || [];
        const existingRuleIds = existingRules.map((r) => r.id);
        const updatedRuleIds: string[] = [];

        for (const rule of ruleSet.rules) {
          let ruleId: string;
          if (rule.id && existingRuleIds.includes(rule.id)) {
            // Update existing rule
            ruleId = rule.id;
            await tx.scoringRule.update({
              where: { id: rule.id },
              data: {
                operator: rule.operator,
                value: rule.value,
                score: rule.score
              }
            });
          } else {
            // Create new rule
            const newRule = await tx.scoringRule.create({
              data: {
                operator: rule.operator,
                value: rule.value,
                score: rule.score,
                ruleSetId
              }
            });
            ruleId = newRule.id;
          }
          updatedRuleIds.push(ruleId);
        }

        // Delete rules that were not included in the update
        await tx.scoringRule.deleteMany({
          where: {
            ruleSetId,
            id: {
              notIn: updatedRuleIds
            }
          }
        });
      }

      // Delete rule sets that were not included in the update
      // The database will handle cascading deletion of rules and field data
      await tx.scoringRuleSet.deleteMany({
        where: {
          scoringModelId: id,
          id: {
            notIn: updatedRuleSetIds
          }
        }
      });

      return scoringModel;
    });
  }

  async deleteScoringModel(id: string): Promise<void> {
    if (!uuidValidate(id)) {
      throw new Error('Invalid scoring model ID');
    }

    const model = await db.scoringModel.findUnique({ where: { id } });
    if (!model) {
      throw new Error('Scoring model not found');
    }

    await db.scoringModel.delete({ where: { id } });
  }

  async executeScoring({ scoringModelId, entityType, entityIds, options = {} }: { scoringModelId: string; entityType: EntityType; entityIds: string[]; options?: Record<string, any> }): Promise<{ entityId: string; executionId: string }[]> {
    // Validate scoring model ID
    if (!uuidValidate(scoringModelId)) {
      throw new Error('Invalid scoring model ID');
    }

    // Validate scoring model exists
    const scoringModel = await db.scoringModel.findUnique({
      where: { id: scoringModelId }
    });

    if (!scoringModel) {
      throw new Error('Scoring model not found');
    }

    const listId = scoringModel.listCompanyId;

    const firebaseUpdates: object = {};
    // Create executions for each entity
    const executions = await Promise.all(
      entityIds.map(async (entityId) => {
        // Create execution record
        const execution = await db.execution.create({
          data: {
            orgId: this.auth.oid,
            type: ExecutionType.scoring,
            typeId: scoringModelId,
            entityType,
            entityId,
            status: ExecutionStatus.pending,
            statusMessage: 'Scoring job queued',
            createdById: this.auth.uid,
            createdAt: new Date(),
            scheduledAt: new Date()
          }
        });

        // Create queue task for worker processing
        await db.queueTask.create({
          data: {
            executionId: execution.id,
            type: QueueTaskType.scoring, // Using enrichment as it's the closest type available
            status: ExecutionStatus.pending,
            createdAt: new Date(),
            scheduledAt: new Date(),
            data: {
              entityId,
              entityType,
              scoringModelId,
              executionId: execution.id,
              orgId: this.auth.oid,
              uid: this.auth.uid,
              options,
              isSilent: options.silent
            }
          }
        });

        if (this.notificationService && !options.silent) {
          firebaseUpdates[`ideas/${entityId}/scores/${scoringModelId}`] = {
            entityId,
            scoringModelId,
            executionId: execution.id,
            status: 'processing',
            statusMessage: '',
            data: {},
            details: {}
          };
          firebaseUpdates[`newExecutions/${execution.id}`] = {
            type: 'score',
            entityId,
            scoringModelId,
            executionId: execution.id,
            time: new Date().toISOString()
          };
        }

        return {
          entityId,
          executionId: execution.id
        };
      })
    );

    if (Object.keys(firebaseUpdates).length && entityType === EntityType.idea && this.writeResultsToSocket && this.notificationService) {
      logger.debug({ msg: 'Pushing updates to firebase:', count: Object.keys(firebaseUpdates).length });
      await this.notificationService.updateData(`/organizations/${this.auth.oid}/lists/${listId}`, firebaseUpdates);
    }

    return executions;
  }

  /**
   * Fetch a scoring execution by executionId
   */
  async getScoringExecution(executionId: string) {
    if (!uuidValidate(executionId)) {
      throw new Error('Invalid execution id');
    }

    const execution = await db.execution.findUnique({
      where: {
        id: executionId,
        type: ExecutionType.scoring
      },
      include: {
        scoringResults: true,
        queueTasks: true
      }
    });

    return execution;
  }
}
