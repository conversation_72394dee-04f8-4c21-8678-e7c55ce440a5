import { logger } from '../logger';
import { db } from '../database';
import { IAuth } from '../types/Common';
import { Activity, ActivitySource, ActivityType, ChannelEmail, EmailMessageStatus, EmailStatus, EmailType, ExecutionType, Prisma } from '@prisma/client';
import InputJsonValue = Prisma.InputJsonValue;
import { EmailData } from '../types/Entity';
import config from '../config';

interface IEmailPayload {
  userId?: string;
  orgId?: string;
  from: string;
  to: string;
  cc?: string[];
  bcc?: string[];
  template: string;
  data: object;
  subject: string;
  scheduledAt?: string;
}

interface IChannelEmail {
  id?: string;
  contactId: string;
  companyId: string;
  type: EmailType;
  status: EmailStatus;
  cc: string[];
  subject: string;
  body: string;
  to: string;
}

interface IChannelEmailPayload {
  id: string;
  orgId: string;
  contactId: string | null;
  companyId: string | null;
  type: EmailType;
  status: EmailStatus;
  errorMessage?: string | null;
  cc: string[];
  subject: string;
  body: string;
  createdById?: string | null;
  createdAt: Date;
  updatedAt?: Date | null;
  deletedAt?: Date | null;
  cancelledAt?: Date | null;
  scheduledAt?: Date | null;
  executionId: string | null;
}

export default class EmailService {
  auth: IAuth;

  constructor({ auth }: { auth: IAuth }) {
    this.auth = auth;
  }

  async createEmail(body: IEmailPayload): Promise<boolean> {
    logger.debug({ message: 'Creating email', body });

    return db.emailMessage
      .create({
        data: {
          userId: this.auth?.uid || null,
          orgId: this.auth?.oid || null,
          from: body.from,
          to: body.to,
          cc: body.cc || [],
          bcc: body.bcc || [],
          template: body.template,
          data: body.data as InputJsonValue,
          subject: body.subject,
          scheduledAt: body.scheduledAt || new Date(),
          status: EmailMessageStatus.raw
        }
      })
      .then((): boolean => true)
      .catch((err: Error): boolean => {
        logger.error({ msg: 'Failed to create email message', error: err.message });
        return false;
      });
  }

  async upsertChannelEmail(data: IChannelEmail): Promise<IChannelEmailPayload | null> {
    const currentTime = new Date();
    const scheduledAt = this.scheduleTime(currentTime);

    const channelEmailData = {
      contactId: data.contactId,
      companyId: data.companyId,
      type: data.type,
      status: data.status,
      cc: data.cc,
      subject: data.subject,
      body: data.body,
      orgId: this.auth?.oid || '',
      createdById: this.auth?.uid,
      errorMessage: '',
      scheduledAt: data.status === EmailStatus.scheduled ? scheduledAt : null,
      from: this.auth?.email || '',
      to: data.to
    };

    return await db
      .$transaction(async (trans) => {
        let result;

        if (data.id) {
          let channelEmailDataUpdate: object;
          if (data.status === EmailStatus.scheduled) {
            channelEmailDataUpdate = {
              updatedAt: currentTime,
              ...channelEmailData
            };
          } else {
            channelEmailDataUpdate = {
              updatedAt: currentTime
            };
          }

          result = await trans.channelEmail.update({
            where: { id: data.id },
            data: channelEmailDataUpdate
          });
        } else {
          result = await trans.channelEmail.create({
            data: {
              companyId: channelEmailData.companyId,
              contactId: channelEmailData.contactId,
              orgId: channelEmailData.orgId,
              type: channelEmailData.type,
              status: channelEmailData.status,
              cc: channelEmailData.cc,
              subject: channelEmailData.subject,
              body: channelEmailData.body,
              createdById: channelEmailData.createdById,
              errorMessage: channelEmailData.errorMessage,
              scheduledAt: channelEmailData.scheduledAt,
              from: channelEmailData.from,
              to: channelEmailData.to
            }
          });
        }

        // Handle activity creation/update for scheduled or completed status
        if ([EmailStatus.scheduled, EmailStatus.sent].includes(result.status)) {
          await this.upsertActivity(result, trans); // Pass prisma transaction context
        }

        return result;
      })
      .catch((error: Error) => {
        logger.error({ msg: 'Failed to upsert channel email', error });
        throw error;
      });
  }

  async updateChannelEmailStatus(id: string, status: EmailStatus): Promise<EmailData> {
    const currentTime = new Date();
    const scheduledAt = this.scheduleTime(currentTime);

    return await db
      .$transaction(async (trans) => {
        const result = await trans.channelEmail.update({
          where: {
            id,
            orgId: this.auth?.oid,
            deletedAt: null
          },
          data: {
            updatedAt: currentTime,
            status,
            scheduledAt: status === EmailStatus.scheduled ? scheduledAt : null
          },
          include: {
            contact: true
          }
        });

        if (status === EmailStatus.scheduled) {
          await this.upsertActivity(result, trans); // Pass prisma transaction context
        }

        return result;
      })
      .catch((error) => {
        logger.error({ msg: 'Failed to update channel email status', error });
        throw error;
      });
  }

  async upsertActivity(emailData: ChannelEmail, trans): Promise<Activity | null> {
    try {
      // Create or update activity
      const activityData = {
        orgId: this.auth?.oid || '',
        source: ActivitySource.acqwired,
        type: ActivityType.email,
        deletedAt: null,
        participants: {
          create: [
            {
              contactId: emailData.contactId,
              companyId: emailData.companyId
            }
          ]
        }
      };

      let activity;
      if (emailData.activityId) {
        // Try to find the existing activity
        activity = await trans.activity.findFirst({
          where: {
            id: emailData.activityId,
            orgId: this.auth?.oid || '',
            deletedAt: null
          }
        });

        if (!activity) {
          throw new Error('Activity not found');
        }
      } else {
        // Create new activity if no activityId exists
        activity = await trans.activity.create({
          data: activityData
        });

        // Update ChannelEmail with the newly created activityId
        await trans.channelEmail.update({
          where: { id: emailData.id },
          data: { activityId: activity.id }
        });
      }

      return activity;
    } catch (error) {
      logger.error({ msg: 'Failed to upsert activity', error });
      throw error;
    }
  }

  async getEmails(): Promise<EmailData[] | any> {
    logger.debug({ message: 'get email' });
    return db.channelEmail
      .findMany({
        where: {
          createdById: this.auth?.uid,
          status: {
            notIn: [EmailStatus.cancelled, EmailStatus.sent, EmailStatus.scheduled]
          }
        },
        include: {
          company: {
            select: {
              entityMapping: {
                where: { isKey: true },
                select: {
                  contact: {
                    select: {
                      firstName: true,
                      lastName: true,
                      email: true
                    }
                  }
                }
              }
            }
          },
          contact: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          },
          execution: {
            where: { type: ExecutionType.play },
            select: {
              typeId: true
            }
          }
        },
        orderBy: { createdAt: 'asc' }
      })
      .then(async (res): Promise<EmailData[]> => {
        const playIds = res.map((data) => (data.execution ? data.execution.typeId : '')).filter(Boolean);

        const plays = await db.play.findMany({
          where: { id: { in: playIds } }
        });

        return res.map((item) => {
          return { ...item, play: plays.find((play) => play.id === item.execution?.typeId) } as EmailData;
        });
      })
      .catch((error: unknown) => {
        logger.error({ msg: 'Failed to create email message', error });
        return false;
      });
  }

  scheduleTime = (currentTime: Date) => {
    const scheduleOffsetSeconds = config.scheduleOffsetSeconds;
    const scheduleOffsetSecondsTime = scheduleOffsetSeconds * 1000;
    return new Date(currentTime.getTime() + scheduleOffsetSecondsTime);
  };
}
