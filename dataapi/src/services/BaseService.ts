import { logger } from '../logger';
import { IAuth } from '../types/Common';
import UserRoleService from './UserRoleService';

export default class BaseService {
  auth: IAuth;
  userRoleService: UserRoleService;
  constructor({ auth }) {
    this.auth = auth;
    this.userRoleService = new UserRoleService(auth);

    if (!auth.oid) {
      logger.error({ error: 'Invalid organization Id' });
      throw new Error('Invalid service call! Check your permissions!');
    }
  }
}
