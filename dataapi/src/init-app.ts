import express from 'express';
import cors from 'cors';
import { connect } from './database';
import { logger, httpLogger } from './logger';
import {
  OrganizationRouter,
  BackOfficeRouter,
  CrmRouter,
  UserRouter,
  ContactRouter,
  CompanyRouter,
  PlayRouter,
  EnrichmentRouter,
  EntityRouter,
  TemplateVariableRouter,
  EmailRouter,
  NotificationsRouter,
  IntegrationRouter,
  ListCompanyRouter,
  AuthRouter,
  ActivityRouter,
  ScoringModelRouter
} from './routers';
import { APIError } from './lib';
import { Auth, PrepareCrmService } from './middleware';

export default async (app: any) => {
  app.use((req: any, _res: any, next: any) => {
    req.url = req.url.replace('/api/v1', '');
    next();
  });

  connect()
    .then(() => {
      logger.info('Data API service connected to db...');
    })
    .catch((e) => {
      logger.error(e);
      process.exit(1);
    });

  app.use(httpLogger);
  app.use(express.json({ limit: '10mb' }));
  app.use(cors());

  app.use((_req: any, res: any, next: any) => {
    res.set('Cache-Control', 'no-store');
    res.set('Pragma', 'no-cache');
    next();
  });

  app.all(['/healthcheck', '/'], (_req: any, res: any) => {
    res.send({
      service: 'dataapi',
      uptime: process.uptime(),
      message: 'OK',
      timestamp: Date.now()
    });
  });

  app.use('/auth', AuthRouter());
  app.use(Auth());
  app.use('/user', UserRouter());
  app.use('/organization', OrganizationRouter());
  app.use('/backoffice', BackOfficeRouter());
  app.use('/contact', ContactRouter());
  app.use('/company', CompanyRouter());
  app.use('/play', PlayRouter());
  app.use('/enrichment', EnrichmentRouter());
  app.use('/entity', EntityRouter());
  app.use('/template-variable', TemplateVariableRouter());
  app.use('/email', EmailRouter());
  app.use('/notifications', NotificationsRouter());
  app.use('/integration', IntegrationRouter());
  app.use('/list-company', ListCompanyRouter());
  app.use('/activity', ActivityRouter());
  app.use('/scoring-model', ScoringModelRouter());
  // PrepareCrmService is a middleware only for crm, not an error!
  app.use('/crm', PrepareCrmService());
  app.use('/crm', CrmRouter());

  app.use((err: any, _req: any, res: any, _next: any) => {
    const e = new APIError('Server Error', {
      name: err.name || null,
      details: err.message || null
    });
    logger.error(e);
    res.status(err.code || 401).json(e);
  });
};
