import { FieldConfigAutoRunFrequency, FieldConfigReconciliationRule, IStoredFieldConfig } from './types/Field';

enum FieldKeys {
  OWNER_ID = 'owner_id',
  ACCOUNT_REF_ID = 'account_id',
  ORG_IDS = 'organization_ids'
}

enum CompanyMandatoryFields {
  NAME = 'name',
  WEB_URL = 'website'
}

enum ContactMandatoryFields {
  FIRST_NAME = 'first_name',
  LAST_NAME = 'last_name',
  EMAIL = 'email',
  UPDATED_AT = 'updated_at',
  OWNER = 'owner_id',
  CREATED_AT = 'created_at'
}

enum EnrichmentMandatoryFields {
  CREATED_BY = 'created_by_id'
}
enum SalesforceEntityKey {
  CONTACT = 'CONTACT',
  ACCOUNT = 'ACCOUNT'
}

enum SyncStatus {
  INVALID = 'invalid',
  COMPLETED = 'completed',
  FAILED = 'failed',
  PROGRESS = 'progress'
}

enum EmailTemplate {
  INVITE_ORGANIZATION = 'organization-invitation',
  INVITE_USER = 'user-invitation',
  RESET_PASSWORD = 'reset-password'
}

enum UserCrmMappingResult {
  USER_INVALID = 'USER_INVALID',
  USER_UNAVAILABLE = 'USER_UNAVAILABLE',
  USER_AVAILABLE = 'USER_AVAILABLE'
}

enum SERVER_ERROR_MESSAGE {
  Unauthorized = 'Your roles does not accept this operation!'
}

const PAGE_SIZE = 100;

const CONCURRENT_TRANSACTION_COUNT = 10;

const DB_TRANSACTION_SETTING = { timeout: 10000, maxWait: 5000 };

export const prismaFieldMapping = {
  orgId: 'org_id',
  deletedAt: 'deleted_at',
  firstName: 'first_name',
  lastName: 'last_name',
  email: 'email',
  updatedAt: 'updated_at',
  ownerId: 'owner_id',
  playRunCount: 'play_run_count',
  sourceType: 'source_type',
  playRuns: 'play_run_count',
  createdBy: 'created_by_id',
  createdAt: 'created_at',
  llmEngine: 'llm_engine',
  name: 'name',
  website: 'website',
  acqwiredProvidedPlays: 'acqwired_provided_plays'
};

export const defaultFieldConfiguration: IStoredFieldConfig = {
  reconciliation: FieldConfigReconciliationRule.UseLatest,
  runOnFieldCall: {
    enabled: true,
    runFor: {
      allRows: false,
      autoRun: FieldConfigAutoRunFrequency.Never
    }
  }
};

export { FieldKeys, CompanyMandatoryFields, ContactMandatoryFields, EnrichmentMandatoryFields, SyncStatus, PAGE_SIZE, CONCURRENT_TRANSACTION_COUNT, DB_TRANSACTION_SETTING, SalesforceEntityKey, EmailTemplate, UserCrmMappingResult, SERVER_ERROR_MESSAGE };
