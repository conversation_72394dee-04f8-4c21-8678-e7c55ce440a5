import crypto from 'crypto';
import { logger } from '../logger';
import { getDomain } from 'tldts';
import { Request } from 'express';

const isValidPassword = (p: string): boolean => {
  // no whitespace, must include a number, a lowercase and uppercase letter, not less than 5 char
  return /^(?!.*\s)(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).{5,}$/.test(p);
};

const isValidInviteHash = (h: string): boolean => {
  // must be lowercase or number and 32 char
  return /^[a-z0-9]{32}$/.test(h);
};

const isValidEmail = (e: string): boolean => {
  return /^.+@.+$/.test(e);
};

const isValidPhoneNumber = (e: string): boolean => {
  return /^[0-9]{7,11}$/.test(e);
};

const generateKeyPair = (): string => {
  return crypto.randomBytes(32).toString('hex');
};

const extractReqToken = (req: Request): string => {
  const parseCookies = (cookieString: string | undefined): Record<string, string> | null => {
    if (cookieString) {
      return cookieString.split('; ').reduce(
        (acc, cookie) => {
          const [name, value] = cookie.split('=');
          acc[name] = value;
          return acc;
        },
        {} as Record<string, string>
      );
    }
    return null;
  };

  const cookies = parseCookies(req.headers.cookie);

  // Try to get app-specific access tokens first, then fallback to generic
  const accessToken = cookies?.admin_accessToken || cookies?.console_accessToken || cookies?.accessToken;

  const token = accessToken ?? req.headers['authorization'];
  if (!token) {
    logger.info({ msg: 'Unauthorized request, missing or invalid token.' });
    throw new Error('No token found, please re-login');
  }

  if (!accessToken && !token.startsWith('Bearer ')) {
    logger.info({ msg: 'Unauthorized request, the token is not Bearer type' });
    throw new Error('Unauthorized! Invalid token syntax, please re-login');
  }

  return token.startsWith('Bearer ') ? token.split('Bearer ')[1] : token;
};

const getDomainFromUrl = (url: string | null | undefined): string | null => {
  return url ? getDomain(url) : null;
};

const formatKey = (label: string): string => {
  // Convert to lowercase and replace unsupported characters with '_'
  let fieldKey = label.toLowerCase().replace(/[^a-z0-9_]/g, '_'); // Replace non-supported characters with '_'

  // Ensure the name does not start with a number
  if (/^[0-9]/.test(fieldKey)) {
    fieldKey = `_${fieldKey}`;
  }

  return fieldKey.replace(/_+/g, '_'); // Replace multiple underscores with a single underscore
};

const validateFieldKey = (fieldId: string): { invalid: boolean; message: string } => {
  const alphaNumericRegex = /^[_a-z][a-z0-9_]*$/;
  if (!alphaNumericRegex.test(fieldId)) {
    return {
      invalid: true,
      message: 'Field Id can start with an underscore (_) or a lowercase letter, but cannot start with a number. It can only contain lowercase letters, numbers, and underscores (_).'
    };
  }
  return {
    invalid: false,
    message: ''
  };
};

export { isValidPassword, isValidInviteHash, isValidEmail, isValidPhoneNumber, generateKeyPair, extractReqToken, getDomainFromUrl, validateFieldKey, formatKey };
