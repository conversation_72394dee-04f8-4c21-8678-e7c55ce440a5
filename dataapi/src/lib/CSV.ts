import <PERSON> from 'papaparse';
import { ICSVObject, ICSVOptions, IEntityDataForCsv } from '../types/CSV';
import { logger } from '../logger';
import { ServerError } from './errors';
import { createObjectCsvStringifier } from 'csv-writer';

const defaultOptions: ICSVOptions = {
  acceptMissingData: false,
  acceptRowLengthMismatch: true,
  separator: ','
};

export default class CSV {
  private readonly options: ICSVOptions;
  private content: string = '';
  private headers: string[] = [];
  private exportData: IEntityDataForCsv[] = [];

  constructor(options?: ICSVOptions) {
    this.options = options ? { ...defaultOptions, ...options } : defaultOptions;
  }

  export(data: IEntityDataForCsv[]): Promise<string> {
    if (data.length === 0) {
      throw new ServerError('No data to export');
    }

    return new Promise<string>((resolve, _reject) => {
      this.exportData = data;
      this.headers = Object.keys(this.exportData[0]);

      logger.debug({ msg: 'Exporting CSV', headers: this.headers, count: this.exportData.length });
      const csvWriter = createObjectCsvStringifier({ header: this.headers });
      resolve(this.headers.join(this.options.separator) + '\n' + csvWriter.stringifyRecords(this.exportData as unknown as object[]));
    });
  }

  load(content: string) {
    this.content = content;
    logger.debug({ msg: 'CSV content loaded', length: this.content.length });
    return this;
  }

  private stripBOM(content: string): string {
    if (content.charCodeAt(0) === 0xFEFF) {
      return content.slice(1);
    }
    return content;
  }

  clear(): CSV {
    this.content = this.content.trim();
    this.content = this.stripBOM(this.content);
    return this;
  }

  parse(): Promise<ICSVObject[]> {
    logger.debug({ msg: 'Parsing CSV', options: this.options });
    return new Promise((resolve, reject) => {
      Papa.parse<ICSVObject>(this.content, {
        header: true, // Automatically detects headers
        skipEmptyLines: true, // Skips empty lines
        delimiter: this.options.separator, // Handles custom separators
        complete: (result) => {
          if (result.errors.length > 0) {
            logger.error({ msg: 'CSV parsing errors', errors: result.errors });
            reject(new ServerError('Failed to parse CSV', result.errors));
          } else {
            logger.debug({ msg: 'CSV parsed successfully', rows: result.data.length });
            resolve(result.data);
          }
        },
        error: (error) => {
          logger.error({ msg: 'CSV parsing failed', error });
          reject(error);
        }
      });
    });
  }

  countLines(content: string): number {
    return content.split(/\r\n|\r|\n/).filter(line => line.length > 0).length;
  }
}
