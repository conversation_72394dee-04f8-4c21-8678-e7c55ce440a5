import { logger } from '../logger';
import config from '../config';
import { db } from '../database';

export default class Office365 {
  auth;
  token;

  constructor({ auth }) {
    if (!auth) {
      throw new Error('Authorization failed');
    }
    this.auth = auth;
  }

  async validateToken() {
    const integrations = await db.organization.findFirst({
      where: {
        id: this.auth.oid
      },
      select: {
        id: true,
        user: {
          where: {
            id: this.auth.uid
          },
          select: {
            id: true,
            integration: true
          }
        }
      }
    });

    logger.info({ msg: 'Got integrations', integrations });

    const integration = integrations?.user[0]?.integration?.find((i) => i.provider === 'office365');
    if (!integration) {
      return null;
    }
    if (new Date(integration.aTokenExpiresAt) > new Date()) {
      return integration.accessToken;
    }
    if (new Date(integration.rTokenExpiresAt) < new Date()) {
      return false;
    }

    // @todo Get a new access token with refresh token here
    return false;

    /*
    const org = await OrganizationModel.findOne({ _id: new ObjectId(this.auth.oid) });
    if (!org) {
      throw new Error('Cannot find organization');
    }
    const user = org.users.find(u => u._id.toString() === this.auth.uid);
    if (!user) {
      throw new Error('Cannot find user');
    }
    let integration = user.integrations.find(i => i.provider === 'office365');
    if (!integration) {
      return null;
    }
    if (new Date(integration.aTokenExpiresAt) > new Date()) {
      return integration.accessToken;
    }
    if (new Date(integration.rTokenExpiresAt) < new Date()) {
      return false;
    }
    const result = await this.refreshToken(integration.refreshToken);
    if (!result || !result.access_token) {
      return false;
    }
    const account = JSON.parse(Buffer.from(result.access_token.split('.')[1], 'base64'));
    integration.aTokenExpiresAt = new Date(account.exp * 1000);
    integration.accessToken = result.access_token;
    integration.refreshedAt = new Date();
    const save = await org.save();
    if (save) {
      return integration.accessToken;
    }
    return false;

     */
  }

  async refreshToken(refresh_token) {
    logger.info({ msg: 'Trying to refresh Office365 token' });

    const postHeaders = new Headers();
    postHeaders.append('Content-Type', 'application/x-www-form-urlencoded');

    const postData = new URLSearchParams();
    postData.append('client_id', config.office365.auth.clientId);
    postData.append('scope', 'offline_access openid Mail.Read Mail.Send');
    postData.append('redirect_uri', config.office365.auth.redirectUri);
    postData.append('grant_type', 'refresh_token');
    postData.append('client_secret', config.office365.auth.clientSecret);
    postData.append('refresh_token', refresh_token);

    return fetch(`${config.office365.auth.authority}/oauth2/v2.0/token`, {
      method: 'POST',
      headers: postHeaders,
      body: postData,
      redirect: 'follow'
    })
      .then(async (response) => JSON.parse(await response.text()))
      .catch((_error) => false);
  }
}
