import { getAuth } from 'firebase-admin/auth';
import { logger } from '../../logger';
import admin from 'firebase-admin';
import config from '../../config';
import { IAuth } from '../../types/Common';
import { INotificationTokenResponse } from '../../types/Notification';
import { GoogleAuth } from 'google-auth-library';
import { ServerError } from '../errors';
import { getDatabase } from 'firebase-admin/database';

let app: any = null;

class FirebaseLibrary {
  auth: IAuth;

  constructor({ auth }) {
    this.auth = auth;
  }

  async initializeAppWithGoogleClient() {
    if (!app) {
      const auth = new GoogleAuth({
        scopes: ['https://www.googleapis.com/auth/cloud-platform']
      });
      // @ts-expect-error We need this line behind the scenes
      const _client = await auth.getClient();
      app = admin.initializeApp({
        credential: admin.credential.applicationDefault(),
        databaseURL: config.firebase.databaseUrl
      });
    }
  }

  async initializeAppWithEnv() {
    if (!app) {
      app = admin.initializeApp({
        credential: admin.credential.cert(config.firebase.serviceAccount as admin.ServiceAccount),
        databaseURL: config.firebase.databaseUrl
      });
    }
  }

  async initialize(): Promise<void> {
    if (config.envName === 'prod' || config.envName === 'dev') {
      return this.initializeAppWithGoogleClient();
    }

    if (config.envName === 'local') {
      return this.initializeAppWithEnv();
    }

    const msg: string = `Invalid environment (envName: ${config.envName} env: ${config.env}) for Firebase initialization.`;
    throw new ServerError(msg);
  }

  async createCustomToken(): Promise<INotificationTokenResponse> {
    const fbAuth = getAuth();
    logger.info({ msg: 'FirebaseService initialized', fbAuth });
    const { uid, oid } = this.auth;

    return fbAuth.createCustomToken(uid, { oid }).then((token) => {
      logger.info({ msg: 'Token generated', token });
      return { uid, oid, token };
    });
  }

  async writeData(dbPath: string, data: object) {
    const dbRef = admin.database().ref(dbPath);
    return dbRef
      .transaction((currentData) => {
        return { ...currentData, ...data };
      })
      .then(() => true)
      .catch((error) => {
        logger.error({ msg: 'Error writing data to database', error, data });
        throw error;
      });
  }

  async updateData(dbPath: string, data: object) {
    logger.info({ msg: 'Updating data in Firebase database', dbPath, data });

    const dbRef = getDatabase().ref(dbPath);
    return dbRef
      .update(data)
      .then(() => true)
      .catch((error) => {
        logger.error({ msg: 'Error updating data in Firebase database', error, dbPath, data });
        throw error;
      });
  }
}

export { FirebaseLibrary };
