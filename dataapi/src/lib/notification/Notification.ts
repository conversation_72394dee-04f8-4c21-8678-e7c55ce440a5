import { IAuth } from '../../types/Common';
import { ServerError } from '../errors';
import { FirebaseLibrary } from './Firebase';
import { INotificationTokenResponse } from '../../types/Notification';

enum NotificationProviderType {
  Firebase = 'firebase'
}

interface INotificationProviderProps {
  auth: IAuth;
  type: NotificationProviderType;
  libraries: {
    firebase?: FirebaseLibrary;
  };
}

class NotificationProvider {
  library: FirebaseLibrary;
  auth: IAuth;
  libraries: any;
  type: NotificationProviderType.Firebase;

  constructor({ auth, libraries, type = NotificationProviderType.Firebase }: INotificationProviderProps) {
    this.auth = auth;
    this.libraries = libraries;
    this.type = type;
    this.library = this.libraries.firebase;
  }

  async init() {
    if (this.type === NotificationProviderType.Firebase) {
      if (!this.libraries.firebase) {
        throw new ServerError('Firebase library not provided');
      }
      await this.library.initialize();
    } else {
      throw new ServerError('Invalid notification provider type');
    }
  }

  async createCustomToken(): Promise<INotificationTokenResponse> {
    return this.library.createCustomToken();
  }

  async writeData(db: string, data: object) {
    return this.library.writeData(db, data);
  }

  async updateData(db: string, data: object) {
    return this.library.updateData(db, data);
  }
}

export { NotificationProvider, NotificationProviderType };
