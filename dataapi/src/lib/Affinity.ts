import { IAuth } from '../types/Common';
import { logger } from '../logger';
import { db } from '../database';
import { IntegrationProvider, OrgIntegration, Organization } from '@prisma/client';
import { IAffinityAuth } from '../types/Crm';

export default class Affinity {
  private auth: IAuth;

  constructor({ auth }: { auth: IAuth }) {
    if (!auth) {
      throw new Error('Authorization failed');
    }
    this.auth = auth;
  }

  async validateToken(): Promise<IAffinityAuth | null > {
    const org: Organization | null = await db.organization.findUnique({ where: { id: this.auth.oid } });
    if (!org) {
      throw new Error('Cannot find organization');
    }

    const integration: OrgIntegration | null = await db.orgIntegration.findFirst({
      where: { orgId: org.id, provider: IntegrationProvider.affinity }
    });
    if (!integration) {
      return null;
    }

    if (integration.expiresAt && new Date(integration.expiresAt) > new Date()) {
      return this.getAffinityAuth(integration);
    } else {
      logger.debug({ msg: 'Affinity token is expired', integration });
      return null;
    }
  }

  getAffinityAuth(integration: OrgIntegration): IAffinityAuth | null {
    if (!integration?.authToken || !integration?.data) {
      return null;
    }

    const { accessToken } = integration.authToken as { accessToken: string };

    if (!accessToken) {
      return null;
    }

    return {
      accessToken
    };
  }
}
