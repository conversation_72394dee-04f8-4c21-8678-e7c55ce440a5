import { Response } from 'express';
import APIError from '../APIError';

const GetErrorResponse = (response: Response, message?: string, error?: any, code = 500) => {
  const responseCode: number = error && typeof error['code'] === 'number' ? error['code'] : code;

  return response.status(responseCode).json(
    new APIError(message || 'Server Error', {
      details: error?.toString()
    })
  );
};

export { GetErrorResponse };
