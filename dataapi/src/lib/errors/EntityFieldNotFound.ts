export interface EntityFieldNotFoundDetails {
  name?: string,
  details?: Error | null,
  msg?: string
  param: string | any
}

export default class EntityFieldNotFound extends Error {
  constructor(message: string, data?: EntityFieldNotFoundDetails) {
    super(message);
    Object.assign(this, {
      name: "EntityFieldNotFound",
      details: null,
      time: new Date().toISOString(),
      ...data,
    });
  }
}

