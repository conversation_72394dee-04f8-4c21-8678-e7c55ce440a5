import { IAuth } from '../types/Common';
import { logger } from '../logger';
import config from '../config';
import { db } from '../database';
import { IntegrationProvider, OrgIntegration, Organization } from '@prisma/client';
import { ISalesforceAuth } from '../types/Crm';

export default class Salesforce {
  private auth: IAuth;

  constructor({ auth }: { auth: IAuth }) {
    if (!auth) {
      throw new Error('Authorization failed');
    }
    this.auth = auth;
  }

  async validateToken(): Promise<ISalesforceAuth | null > {
    const org: Organization | null = await db.organization.findUnique({ where: { id: this.auth.oid } });
    if (!org) {
      throw new Error('Cannot find organization');
    }

    const integration: OrgIntegration | null = await db.orgIntegration.findFirst({
      where: { orgId: org.id, provider: IntegrationProvider.salesforce }
    });
    if (!integration) {
      return null;
    }

    if (integration.expiresAt && new Date(integration.expiresAt) > new Date()) {
      const { aTokenExpiresAt } = integration.data as { aTokenExpiresAt: string };
      if (aTokenExpiresAt && new Date(aTokenExpiresAt) < new Date()) {
        logger.debug({ msg: 'Salesforce token is expired', integration });
        const updatedIntegration = await this.refreshToken(integration);
        return updatedIntegration ? this.getSalesforceAuth(updatedIntegration) : null;
      }
      return this.getSalesforceAuth(integration);
    } else {
      logger.debug({ msg: 'Salesforce token is expired', integration });
      return null;
    }
  }

  getSalesforceAuth(integration: OrgIntegration): ISalesforceAuth | null {
    if (!integration?.authToken || !integration?.data) {
      return null;
    }

    const { accessToken } = integration.authToken as { accessToken: string };
    const { instanceUrl } = integration.data as { instanceUrl: string };
    const apiVersion = config.salesforce.version;

    if (!accessToken || !instanceUrl) {
      return null;
    }

    return {
      accessToken,
      instanceUrl,
      endPoints: {
        query: `${instanceUrl}/services/data/v${apiVersion}/query`,
        t_query: `${instanceUrl}/services/data/v${apiVersion}/tooling/query`,
        describeAccount: `${instanceUrl}/services/data/v${apiVersion}/sobjects/Account/describe`,
        describeContact: `${instanceUrl}/services/data/v${apiVersion}/sobjects/Contact/describe`
      }
    };
  }

  async refreshToken(integration: OrgIntegration): Promise<OrgIntegration | null> {
    const refresh_token = (integration.authToken as { refreshToken: string }).refreshToken;
    logger.info({ msg: 'Trying to refresh Salesforce token', refresh_token });

    const postHeaders = new Headers();
    postHeaders.append('Content-Type', 'application/x-www-form-urlencoded');

    const postData = new URLSearchParams();
    postData.append('refresh_token', refresh_token);
    postData.append('grant_type', 'refresh_token');
    postData.append('client_id', config.salesforce.auth.clientId ?? '');
    postData.append('client_secret', config.salesforce.auth.clientSecret ?? '');

    try {
      const response = await fetch(`${config.salesforce.auth.authority}/token`, {
        method: 'POST',
        headers: postHeaders,
        body: postData,
        redirect: 'follow'
      });
      const refresh_response = await response.json();
      logger.debug({ msg: 'SF Refresh token response', refresh_response });

      const { access_token } = refresh_response;
      if (!access_token) {
        throw new Error('Invalid Salesforce access token');
      }

      const aTokenExpiresAt = new Date();
      aTokenExpiresAt.setHours(aTokenExpiresAt.getHours() + 1);

      const token = { ...(integration.authToken as object), accessToken: access_token };
      const data = {
        ...(integration.data as object),
        accessToken: access_token,
        aTokenExpiresAt: aTokenExpiresAt,
        refreshedAt: new Date()
      };

      const updatedIntegration = await db.orgIntegration.update({
        where: { id: integration.id },
        data: {
          authToken: token,
          data,
          updatedAt: new Date()
        }
      });

      logger.debug({ msg: 'Token refreshed', updatedIntegration });
      return updatedIntegration;
    } catch (error: any) {
      logger.error({ msg: 'Refresh token failed', error: error.message });
      return null;
    }
  }
}
