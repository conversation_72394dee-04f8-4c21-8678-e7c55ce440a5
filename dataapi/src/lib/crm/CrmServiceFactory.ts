import Salesforce from '../Salesforce';
import SalesforceService from '../../services/Salesforce';
import { ICrmService } from './ICrmService';
import { CompanyService, ContactService } from '../../services';
import { CrmAccessError, UnprocessableContentError } from '../errors';
import { CrmType } from '@prisma/client';
import Affinity from '../Affinity';
import AffinityService from '../../services/Affinity';
import ActivityService from '../../services/Activity';
import { IAuth } from '../../types/Common';

export default class CrmServiceFactory {
  static async getCrmService(type: CrmType, auth: IAuth, forceAuth: boolean): Promise<ICrmService> {
    const companyService = new CompanyService({ auth });
    const contactService = new ContactService({ auth });
    const activityService = new ActivityService({ auth });
    switch (type) {
      case CrmType.salesforce: {
        const salesforce = new Salesforce({ auth });

        const sfAuth = await salesforce.validateToken();
        if (forceAuth && !sfAuth) {
          throw new CrmAccessError('Cannot validate Salesforce access token', {
            details: 'Salesforce token is expired or invalid',
            code: 401
          });
        }

        return new SalesforceService({ sfAuth, auth, companyService, contactService, activityService });
      }
      case CrmType.affinity: {
        const affinity = new Affinity({ auth });

        const affAuth = await affinity.validateToken();
        if (forceAuth && !affAuth) {
          throw new CrmAccessError('Cannot validate Salesforce access token', {
            details: 'Salesforce token is expired or invalid',
            code: 401
          });
        }

        return new AffinityService({ affAuth, auth, companyService, contactService, activityService });
      }
      // Add cases for other CRM types here
      default:
        throw new UnprocessableContentError('Unsupported CRM type');
    }
  }
}
