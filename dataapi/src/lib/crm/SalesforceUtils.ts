import { ActivityStatus, ActivityType, CrmEntityType } from '@prisma/client';
import { IActivity, ISalesforceEvent, ISalesforceTask} from '../../types/Crm';

export class SalesforceUtils {
  // Method to map Salesforce Task to internal IActivity type
  static mapSalesforceTaskToActivity(task: ISalesforceTask): IActivity {
    return {
      id: task.Id,
      entityType: CrmEntityType.task,
      title: task.Subject,
      type: SalesforceUtils.mapTaskSubtypeToActivityType(task.TaskSubtype), // Map TaskSubtype to ActivityType
      content: task.Description || '',
      dueDate: task.ActivityDate ? new Date(task.ActivityDate) : new Date(),
      status: SalesforceUtils.mapSalesforceTaskStatusToActivityStatus(task.Status),
      participant: [ { contactId: task.WhoId, companyId: task.WhatId } ], // Map WhoId and WhatId to contactId and companyId respectively
      crmActivity: task
    };
  }

  // Method to map Salesforce Event to internal IActivity type
  static mapSalesforceEventToActivity(event: ISalesforceEvent): IActivity {
    return {
      id: event.Id,
      entityType: CrmEntityType.event,
      title: event.Subject,
      type: ActivityType.other,
      content: event.Description || '',
      location: event.Location || '',
      dueDate: event.ActivityDate ? new Date(event.ActivityDate) : event.EndDateTime ? new Date(event.EndDateTime) : null,
      startDateTime: event.StartDateTime ? new Date(event.StartDateTime) : null,
      endDateTime: event.EndDateTime ? new Date(event.EndDateTime) : null,
      isAllDayEvent: event.IsAllDayEvent,
      status: ActivityStatus.unknown,
      participant: [ { contactId: event.WhoId, companyId: event.WhatId } ], // Map WhoId and WhatId to contactId and companyId respective
      crmActivity: event
    };
  }

  // Helper method to map Salesforce TaskSubtype to internal ActivityType with case-insensitive check
  private static mapTaskSubtypeToActivityType(taskSubtype: string | null): ActivityType {
    // Convert taskSubtype to lowercase for case-insensitive comparison
    switch (taskSubtype?.toLowerCase()) {
      case 'call':
        return ActivityType.call;
      case 'email':
        return ActivityType.email;
      case 'meeting':
        return ActivityType.f2f; // Assuming this represents face-to-face meetings
      case 'task':
        return ActivityType.other;
      default:
        return ActivityType.other; // Default to 'other' if no specific mapping is found
    }
  }

  // Private method to map Salesforce Task status to internal ActivityStatus
  private static mapSalesforceTaskStatusToActivityStatus(status: string): ActivityStatus {
    switch (status) {
      case 'Completed':
        return ActivityStatus.completed;
      case 'In Progress':
        return ActivityStatus.scheduled;
      case 'Cancelled':
        return ActivityStatus.cancelled;
      case 'Deferred':
        return ActivityStatus.missed;
      default:
        return ActivityStatus.unknown;
    }
  }
}
