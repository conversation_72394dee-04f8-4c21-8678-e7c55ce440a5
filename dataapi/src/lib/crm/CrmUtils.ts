import { CrmType, IntegrationProvider } from '@prisma/client';

const integrationProviderToCrmTypeMap: { [key in IntegrationProvider]?: CrmType } = {
  [IntegrationProvider.salesforce]: CrmType.salesforce,
  [IntegrationProvider.affinity]: CrmType.affinity

  // Add more mappings as needed
};

function getCrmTypeForProvider(provider: IntegrationProvider): CrmType | null {
  return integrationProviderToCrmTypeMap[provider] ?? null;
}

function getCrmTitle(crmType: CrmType): string {
  switch (crmType) {
    case CrmType.salesforce:
      return 'Salesforce';
    case CrmType.affinity:
      return 'Affinity';
    // Add more cases as needed
    default:
      return 'Unknown';
  }
}

export const CrmUtils = {
  getCrmTypeForProvider,
  getCrmTitle
};
