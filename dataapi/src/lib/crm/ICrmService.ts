import { CrmSetting, CrmSyncStatus, CrmType, DataSourceType, EntityType } from '@prisma/client';
import { UserCrmMappingResult } from '../../constants';
import { ICompanyMandatoryFields, IContactMandatoryFields, ICrmBasicEntity, ICrmDefaultFields, ICrmFieldMapping, ICrmFields, ICrmFilter, ICrmListEntity, ICrmListMapping, ICrmUser, IFilterItem, ISyncRecordResult, ISyncResult, ISyncSettings, IUserCrmMap } from '../../types/Crm';
import { ISyncData } from '../../types/Entity';
import { ICompanyDetailsParams } from '../../types/Company';
import { IContactDetailsParams } from '../../types/Contact';

export interface ICrmService {
  getContactMandatoryFields(contact: ISyncData): IContactMandatoryFields;
  getCompanyMandatoryFields(company: ISyncData): ICompanyMandatoryFields;
  getCrmFields(entityType: EntityType): Promise<ICrmFields[]>;
  getEntities(filter: IFilterItem[], entityType: EntityType): Promise<ICrmBasicEntity[]>;
  getUsers(email: string): Promise<ICrmUser[]>;
  saveFilter({ id, parameters, fetchRelated }: ICrmFilter, filterType: EntityType): Promise<boolean>;
  getFilters(filterType: EntityType): Promise<ICrmFilter[]>;
  getMappings(mappingType: EntityType): Promise<ICrmFieldMapping[]>;
  getDefaultFields(mappingType: EntityType): Promise<ICrmDefaultFields[]>;
  saveMappings(query: ICrmFieldMapping[], mappingType: EntityType): Promise<boolean>;
  saveSettings(settings: CrmSetting): Promise<boolean>;
  getSettings(): Promise<CrmSetting>;
  getSyncStatus(): Promise<CrmSyncStatus>;
  sync(setting: ISyncSettings): Promise<ISyncResult>;
  revoke(): Promise<boolean>;
  deleteFilter(id: string): Promise<boolean>;
  isAuthenticated(): boolean;
  getApiKey(): string;
  submitApiKey(apiKey: string): Promise<boolean>;
  checkCrmUserEmail(crmUserEmail: string): Promise<UserCrmMappingResult>;
  mapCrmUser(crmUserEmail: string, userId: string): Promise<UserCrmMappingResult>;
  getUserCrmMapping(): Promise<IUserCrmMap | null>;
  getUserCrmEmail(uid: string, oid: string): Promise<string | null>;
  removeIntegration(integrationId: string): Promise<boolean>;
  getOrCreateCrmCompany(company: ISyncData, companyDetails: ICompanyDetailsParams, oid: string, trans: any): Promise<{ companyId: string }>;
  updateCrmCompanyFields(company: ISyncData, oid: string, trans: any): Promise<void>;
  getOrCreateCrmContact(contact: ISyncData, oid: string, contactDetails: IContactDetailsParams, crmCompanyIds: string[], trans: any): Promise<{ contactId: string }>;
  updateCrmContactFields(contact: ISyncData, oid: string, crmCompanyIds: string[], trans: any): Promise<void>;
  getSyncRecords(entityType: EntityType, userSync: boolean): Promise<ISyncRecordResult>;
  syncDeletion(userSync: boolean): Promise<boolean>;
  dataSourceType: DataSourceType;
  crmType: CrmType;
  getCrmLists(): Promise<ICrmListEntity[]>;
  getMappedLists(): Promise<ICrmListMapping[]>;
  saveCrmList(list: ICrmListMapping): Promise<boolean>;
  deleteCrmList(id: string): Promise<boolean>;
}
