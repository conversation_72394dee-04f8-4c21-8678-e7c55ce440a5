import { logger } from '../logger';

export default class APIResponse<T> {
  constructor(data: T) {
    if (data instanceof Error) {
      logger.error(data);
      this.responseError(data);
    } else {
      this.responseSuccess(data);
    }
  }

  responseError(data: Error) {
    if (data.name === 'APIError') {
      return Object.assign(this, {
        errors: [
          {
            name: data.name,
            msg: data.message,
            details: data.stack
          }
        ]
      });
    }
  }

  responseSuccess(data: T) {
    return Object.assign(this, { data });
  }
}
