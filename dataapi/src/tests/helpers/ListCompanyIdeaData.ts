import { db } from '../../database';
import { v7 as uuid } from 'uuid';
import { EntityType, ExecutionStatus, ExecutionType, EnrichmentType } from '@prisma/client';
import { mockAuth } from './ModelData';
import { TestDBManager } from './DBHelpers';

export interface TestContext {
  auth: typeof mockAuth;
  testListCompanyId: string;
  intentionId: string;
}

export async function createTestSource(listCompanyId: string, intentionId: string) {
  const intentionSource = await db.intentionSource.findFirst({
    where: {
      intentionId,
    }
  });

  if (!intentionSource) {
    throw new Error('Intention source not found');
  }

  return db.listSource.create({
    data: {
      listCompanyId,
      intentionSourceId: intentionSource.id,
      status: 'completed',
      parameters: {},
      createdAt: new Date()
    }
  });
}

export async function createTestMappings(ideaIds: string[], sourceId: string, listCompanyId: string) {
  // Create source idea mappings
  await db.listCompanySourceIdeaMapping.createMany({
    data: ideaIds.map((ideaId) => ({
      listSourceId: sourceId,
      listCompanyIdeaId: ideaId,
      metadata: {}
    }))
  });

  // Create list company idea mappings
  await db.listCompanyIdeaMapping.createMany({
    data: ideaIds.map((ideaId) => ({
      listCompanyId,
      listCompanyIdeaId: ideaId,
      inShortlist: false
    }))
  });
}

export async function createTestIdea(context: TestContext, companyName: string, website: string | null = null, companyId: string | null = null) {
  return db.listCompanyIdea.create({
    data: {
      orgId: context.auth.oid,
      companyName,
      website,
      companyId
    }
  });
}

export async function createTestEnrichment(context: TestContext, entityType: EntityType, enrichmentType: EnrichmentType = EnrichmentType.llm) {
  const config = await db.enrichmentConfig.findFirst({
    where: {
      entityType,
      enrichmentType: enrichmentType
    }
  });

  if (!config) {
    throw new Error('Enrichment config not found');
  }

  return db.enrichment.create({
    data: {
      name: 'Test Enrichment',
      configId: config.id,
      orgId: context.auth.oid,
      entityType,
      createdById: context.auth.uid,
      createdAt: new Date(),
      updatedAt: new Date(),
      listCompanyId: context.testListCompanyId,
      data: { testEnrichment: 'testValue' }
    }
  });
}

export async function createTestExecution(context: TestContext, entityId: string, entityType: EntityType, type: ExecutionType, typeId: string) {
  return db.execution.create({
    data: {
      createdAt: new Date(),
      orgId: context.auth.oid,
      createdById: context.auth.uid,
      status: ExecutionStatus.completed,
      entityId,
      entityType,
      type,
      typeId
    }
  });
}

export async function createTestScoringModel(context: TestContext) {
  return db.scoringModel.create({
    data: {
      name: 'Test Scoring Model',
      createdById: context.auth.uid,
      createdAt: new Date(),
      updatedAt: new Date(),
      listCompanyId: context.testListCompanyId
    }
  });
}

export async function createTestEnrichmentResult(entityId: string, enrichmentId: string, executionId: string) {
  return db.enrichmentResult.create({
    data: {
      entityType: EntityType.idea,
      entityId,
      enrichmentId,
      value: { testEnrichment: 'testValue' },
      executionId,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
}

export async function createTestScoringResult(entityId: string, scoringModelId: string, executionId: string) {
  return db.scoringResult.create({
    data: {
      entityId,
      scoringModelId,
      executionId,
      details: { testScoring: 'testValue' },
      totalScore: 85
    }
  });
}

export async function setupTestContext() {
  const auth = { ...mockAuth };
  const intention = await db.intention.findFirst({
    where: {
      name: 'AI tools'
    }
  });

  if (!intention) {
    throw new Error('Intention not found');
  }

  const dbManager = new TestDBManager(auth);
  const testListCompanyId = await dbManager.createListData();

  return {
    auth,
    intentionId: intention.id,
    testListCompanyId
  };
}

export async function createTestIdeasWithStatuses(context: TestContext, source: any) {
  // Create a company
  const company = await db.company.create({
    data: {
      name: 'Test Company C',
      website: 'www.example-c.com',
      orgId: context.auth.oid,
      domain: 'example-c.com',
      sourceType: 'acqwired'
    }
  });

  // Create ideas with different statuses
  const idea1 = await createTestIdea(context, 'Test Company C', 'www.example-c.com', company.id);
  const idea2 = await createTestIdea(context, 'Test Company D', null);
  const idea3 = await createTestIdea(context, 'Test Company E', 'www.example-e.com');

  // Create source idea mappings
  await createTestMappings([idea1.id, idea2.id, idea3.id], source.id, context.testListCompanyId);

  return { idea1, idea2, idea3 };
}

export async function createTestIdeasWithShortlist(context: TestContext, source: any) {
  // Create test ideas
  const idea1 = await createTestIdea(context, 'Test Company F', 'www.example-f.com');
  const idea2 = await createTestIdea(context, 'Test Company G', 'www.example-g.com');

  // Create source idea mappings
  await createTestMappings([idea1.id, idea2.id], source.id, context.testListCompanyId);

  // Update shortlist status
  await db.listCompanyIdeaMapping.update({
    where: {
      listCompanyIdeaId_listCompanyId: {
        listCompanyIdeaId: idea1.id,
        listCompanyId: context.testListCompanyId
      }
    },
    data: { inShortlist: true }
  });

  return { idea1, idea2 };
}

export async function createTestIdeasWithScoring(context: TestContext, source: any) {
  // Create test ideas
  const idea1 = await createTestIdea(context, 'Test Company 1', 'www.example1.com');
  const idea2 = await createTestIdea(context, 'Test Company 2', 'www.example2.com');

  // Create source idea mappings
  await createTestMappings([idea1.id, idea2.id], source.id, context.testListCompanyId);

  // Create scoring model and related data
  const scoringModel = await createTestScoringModel(context);
  const execution1 = await createTestExecution(context, idea1.id, EntityType.idea, ExecutionType.scoring, scoringModel.id);
  const execution2 = await createTestExecution(context, idea2.id, EntityType.idea, ExecutionType.scoring, scoringModel.id);
  await createTestScoringResult(idea1.id, scoringModel.id, execution1.id);
  await createTestScoringResult(idea2.id, scoringModel.id, execution2.id);

  return { idea1, idea2, scoringModel };
}

export async function createTestIdeasWithEnrichment(context: TestContext, source: any) {
  // Create test idea with all possible data
  const idea = await createTestIdea(context, `Test Company ${uuid()}`, 'www.testcompany.com');

  // Create source idea mapping
  await db.listCompanySourceIdeaMapping.create({
    data: {
      listCompanyIdeaId: idea.id,
      listSourceId: source.id,
      metadata: { testMetadata: 'testValue' }
    }
  });

  // Create list company idea mapping
  await db.listCompanyIdeaMapping.create({
    data: {
      listCompanyId: context.testListCompanyId,
      listCompanyIdeaId: idea.id,
      inShortlist: false
    }
  });

  // Create enrichment and related data
  const enrichment = await createTestEnrichment(context, EntityType.idea);
  const execution = await createTestExecution(context, idea.id, EntityType.idea, ExecutionType.enrichment, enrichment.id);
  const enrichmentResult = await createTestEnrichmentResult(idea.id, enrichment.id, execution.id);

  return { idea, enrichment, enrichmentResult };
}
