import { db } from '../../database';
import { ListCompanyStatus } from '@prisma/client';
import { IAuth } from '../../types/Common';
import { logger } from '../../logger';
import { DBManager } from './DBManager';

export class TestDBManager {
  private testListCompanyId: string | null = null;

  constructor(private auth: IAuth) {}

  async createListData(options: { name?: string; status?: ListCompanyStatus } = {}): Promise<string> {
    const listCompanyId = await this.createListCompany(options);
    await this.createListCompanyIdea(listCompanyId);
    await this.createTestEnrichment(listCompanyId);
    return listCompanyId;
  }

  async createTestEnrichment(listCompanyId: string): Promise<string> {
    const enrichment = await db.enrichment.create({
      data: {
        name: 'Test Enrichment',
        orgId: this.auth.oid,
        data: {},
        isPrimary: false,
        createdById: this.auth.uid,
        configId: '86762c91-ca01-4449-be68-275d3e2d9181', // Using LLM Prompt config ID from fixtures
        entityType: 'company',
        listCompanyId: listCompanyId
      }
    });
    return enrichment.id;
  }

  async createListCompany(options: { name?: string; status?: ListCompanyStatus } = {}) {
    try {
      // Connect to the database
      await db.$connect();
      logger.info('Connected to database successfully');

      // Create a test list company for our tests
      logger.info('Creating test list company data...');
      const testOrg = await db.organization.findFirst();

      if (!testOrg) {
        throw new Error('No organization found in database. Please ensure seed data exists.');
      }

      // Update auth with real organization ID
      this.auth.oid = testOrg.id;

      // Create a test list company with provided options
      const testListCompany = await db.listCompany.create({
        data: {
          name: options.name || 'Test Company',
          orgId: testOrg.id,
          status: options.status || ListCompanyStatus.completed,
          createdById: this.auth.uid
        }
      });

      this.testListCompanyId = testListCompany.id;
      this.auth.lid = testListCompany.id;
      logger.info(`Created test list company with ID: ${this.testListCompanyId}`);

      return this.testListCompanyId;
    } catch (error) {
      logger.error({ error }, 'Error in test setup');
      throw error;
    }
  }

  async createListCompanyIdea(listCompanyId: string): Promise<void> {
    await db.listCompanyIdea.create({
      data: {
        orgId: this.auth.oid,
        fields: {},
        listCompanyIdeaMapping: {
          create: {
            listCompanyId: listCompanyId,
            inShortlist: false
          }
        }
      }
    });
  }

  async cleanupTestData() {
    try {
      logger.info('Cleaning up test data...');

      // Reset the database to its initial state
      await DBManager.resetDB();
      logger.info('Database reset complete');
    } catch (error) {
      logger.error({ error }, 'Error cleaning up test data');
    } finally {
      // Disconnect from the database
      await db.$disconnect();
      logger.info('Disconnected from database');
    }
  }
}
