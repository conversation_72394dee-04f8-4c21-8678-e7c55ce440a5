import { spawn } from 'child_process';
import { logger } from '../../logger';
import { db } from '../../database';
import { resolve } from 'path';

export class DBManager {
  static async resetDB(): Promise<void> {
    try {
      // First ensure our Prisma connection is closed
      await db.$disconnect();
      logger.info('Disconnected from database');

      // Get and log the current working directory
      const currentDir = process.cwd();
      logger.info({ currentDir }, 'Current working directory');

      // Get and log the target directory - we're in dataapi, so go up one level
      const targetDir = resolve(currentDir, '..');
      logger.info({ targetDir }, 'Target directory for make command');

      // Create a promise to handle the spawn process
      const resetPromise = new Promise<void>((resolve, reject) => {
        const makeProcess = spawn('make', ['reset-testdb'], {
          stdio: 'pipe',
          shell: true,
          cwd: targetDir
        });

        // Handle stdout
        makeProcess.stdout.on('data', (data) => {
          logger.info(data.toString());
        });

        // Handle stderr
        makeProcess.stderr.on('data', (data) => {
          logger.error(data.toString());
        });

        // Handle process completion
        makeProcess.on('close', (code) => {
          if (code === 0) {
            resolve();
          } else {
            reject(new Error(`make reset-testdb exited with code ${code}`));
          }
        });

        // Handle process errors
        makeProcess.on('error', (error) => {
          reject(error);
        });
      });

      // Wait for the reset process to complete
      await resetPromise;

      // Reconnect to the database after reset
      await db.$connect();
      logger.info('Reconnected to database after reset');
    } catch (error) {
      logger.error({ error }, 'Failed to reset database');
      throw new Error(`Failed to reset database: ${error}`);
    }
  }
}
