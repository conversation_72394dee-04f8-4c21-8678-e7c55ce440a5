import { db } from '../../database';
import { v7 as uuid } from 'uuid';
import { ListSourceStatus, ListSourceType } from '@prisma/client';
import { mockAuth } from './ModelData';

export interface SourceTestContext {
  auth: typeof mockAuth;
  testListCompanyId: string;
  intentionId: string;
  intentionSourceId: string;
}

export async function setupSourceTestContext(): Promise<SourceTestContext> {
  const auth = { ...mockAuth };

  // Create test list company
  const listCompany = await db.listCompany.create({
    data: {
      name: `Test List Company ${uuid()}`,
      orgId: auth.oid,
      createdById: auth.uid,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });

  // Create test intention
  const intention = await db.intention.create({
    data: {
      name: `Test Intention ${uuid()}`,
      orderBy: 1,
      createdAt: new Date()
    }
  });

  // Create intention source
  const intentionSource = await db.intentionSource.create({
    data: {
      name: `Test Source ${uuid()}`,
      orderBy: 1,
      intentionId: intention.id,
      sourceType: ListSourceType.resource,
      parameters: {},
      createdAt: new Date()
    }
  });

  return {
    auth,
    testListCompanyId: listCompany.id,
    intentionId: intention.id,
    intentionSourceId: intentionSource.id
  };
}

export async function createTestSource(context: SourceTestContext, sourceType: ListSourceType, parameters: Record<string, any> = {}, status: ListSourceStatus = ListSourceStatus.processing) {
  return db.listSource.create({
    data: {
      listCompanyId: context.testListCompanyId,
      intentionSourceId: context.intentionSourceId,
      status,
      parameters,
      createdAt: new Date()
    }
  });
}

export async function createTestCompanies(context: SourceTestContext, count: number = 3) {
  const companies = [];
  for (let i = 0; i < count; i++) {
    const company = await db.company.create({
      data: {
        name: `Test Company ${i + 1} ${uuid()}`,
        website: `https://test-company-${i + 1}.com`,
        domain: `test-company-${i + 1}.com`,
        sourceType: 'acqwired',
        orgId: context.auth.oid
      }
    });
    companies.push(company);
  }
  return companies;
}

export async function createTestListCompanyIdeas(context: SourceTestContext, count: number = 3) {
  const ideas = [];
  for (let i = 0; i < count; i++) {
    const idea = await db.listCompanyIdea.create({
      data: {
        orgId: context.auth.oid,
        companyName: `Idea Company ${i + 1} ${uuid()}`,
        website: `https://idea-company-${i + 1}.com`,
        domain: `idea-company-${i + 1}.com`
      }
    });
    ideas.push(idea);
  }
  return ideas;
}

export function createMockCSVData(includeInvalid: boolean = false) {
  const validData = [
    { name: 'CSV Company A', website: 'https://csv-a.com', industry: 'Tech' },
    { name: 'CSV Company B', website: 'https://csv-b.com', industry: 'Finance' },
    { name: 'CSV Company C', website: 'https://csv-c.com', industry: 'Healthcare' }
  ];

  if (includeInvalid) {
    validData.push(
      { name: '', website: '', industry: 'Invalid' }, // Empty row
      { name: 'CSV Company D', website: 'https://csv-a.com', industry: 'Tech' } // Duplicate domain
    );
  }

  return validData;
}

export function createMockGoogleMapsData() {
  return [
    {
      displayName: { text: 'Maps Restaurant A' },
      websiteUri: 'https://maps-restaurant-a.com',
      location: { latitude: 40.7128, longitude: -74.006 },
      types: ['restaurant', 'food']
    },
    {
      displayName: { text: 'Maps Restaurant B' },
      websiteUri: 'https://maps-restaurant-b.com',
      location: { latitude: 40.7589, longitude: -73.9851 },
      types: ['restaurant', 'food']
    },
    {
      displayName: { text: 'Maps Restaurant C' },
      websiteUri: null, // No website
      location: { latitude: 40.7505, longitude: -73.9934 },
      types: ['restaurant', 'food']
    }
  ];
}

export function createMockOceanData() {
  return {
    companies: [
      {
        company: {
          name: 'Ocean Company A',
          rootUrl: 'https://ocean-a.com',
          domain: 'ocean-a.com'
        },
        metadata: {
          employees: 100,
          revenue: '10M',
          industry: 'Software'
        }
      },
      {
        company: {
          name: 'Ocean Company B',
          rootUrl: 'https://ocean-b.com',
          domain: 'ocean-b.com'
        },
        metadata: {
          employees: 250,
          revenue: '25M',
          industry: 'Hardware'
        }
      }
    ]
  };
}

export function createMockSourceScrubData() {
  return [
    {
      name: 'SourceScrub Company A',
      website: 'https://sourcescrub-a.com',
      domain: 'sourcescrub-a.com',
      industry: 'Software',
      employees: 150,
      revenue: '15M'
    },
    {
      name: 'SourceScrub Company B',
      website: 'https://sourcescrub-b.com',
      domain: 'sourcescrub-b.com',
      industry: 'Hardware',
      employees: 75,
      revenue: '8M'
    },
    {
      name: 'SourceScrub Company C',
      website: 'https://sourcescrub-a.com', // Duplicate domain
      domain: 'sourcescrub-a.com',
      industry: 'Software',
      employees: 200,
      revenue: '20M'
    }
  ];
}

export function createMockExaData() {
  return [
    {
      title: 'Exa Company A',
      url: 'https://exa-a.com',
      text: 'Leading AI company specializing in machine learning',
      score: 0.95,
      publishedDate: '2024-01-15'
    },
    {
      title: 'Exa Company B',
      url: 'https://exa-b.com',
      text: 'Innovative startup in artificial intelligence',
      score: 0.87,
      publishedDate: '2024-01-10'
    }
  ];
}

export async function verifySourceCreationResult(
  sourceId: string,
  expectedCounts: {
    totalProcessed: number;
    successCount: number;
    skippedCount: number;
    failureCount: number;
  }
) {
  // Verify source status
  const source = await db.listSource.findUnique({
    where: { id: sourceId }
  });

  if (!source) {
    throw new Error(`Source ${sourceId} not found`);
  }

  // Verify source mappings count
  const sourceMappings = await db.listCompanySourceIdeaMapping.count({
    where: { listSourceId: sourceId }
  });

  // Verify list mappings count
  const listMappings = await db.listCompanyIdeaMapping.count({
    where: { listCompanyId: source.listCompanyId }
  });

  return {
    source,
    sourceMappingsCount: sourceMappings,
    listMappingsCount: listMappings
  };
}

export async function cleanupSourceTestData(context: SourceTestContext) {
  // Clean up in reverse order of dependencies
  await db.listCompanySourceIdeaMapping.deleteMany({
    where: {
      listSource: {
        listCompanyId: context.testListCompanyId
      }
    }
  });

  await db.listCompanyIdeaMapping.deleteMany({
    where: {
      listCompanyId: context.testListCompanyId
    }
  });

  await db.listCompanyIdea.deleteMany({
    where: {
      orgId: context.auth.oid
    }
  });

  await db.listSource.deleteMany({
    where: {
      listCompanyId: context.testListCompanyId
    }
  });

  await db.company.deleteMany({
    where: {
      orgId: context.auth.oid
    }
  });

  await db.intentionSource.deleteMany({
    where: {
      intentionId: context.intentionId
    }
  });

  await db.intention.deleteMany({
    where: {
      id: context.intentionId
    }
  });

  await db.listCompany.deleteMany({
    where: {
      id: context.testListCompanyId
    }
  });
}

export function createCSVBuffer(data: Array<Record<string, any>>, headers?: string[]): Buffer {
  if (data.length === 0) {
    return Buffer.from('');
  }

  const csvHeaders = headers || Object.keys(data[0]);
  const csvContent = [csvHeaders.join(','), ...data.map((row) => csvHeaders.map((header) => row[header] || '').join(','))].join('\n');

  return Buffer.from(csvContent);
}

export function createMockMulterFile(buffer: Buffer, originalname: string = 'test.csv', mimetype: string = 'text/csv'): Express.Multer.File {
  return {
    buffer,
    originalname,
    mimetype,
    fieldname: 'file',
    encoding: '7bit',
    size: buffer.length,
    destination: '',
    filename: '',
    path: '',
    stream: null as any
  };
}
