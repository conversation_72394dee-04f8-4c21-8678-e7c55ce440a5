import { assert } from 'chai';
import sinon from 'sinon';
import { db } from '../../database';
import { TestDBManager } from '../helpers/DBHelpers';
import { mockAuth } from '../helpers/ModelData';
import { FieldDataType, ScoringFieldSource, ScoringOperator, EntityType, ExecutionType, ExecutionStatus, QueueTaskType } from '@prisma/client';
import { v7 as uuid } from 'uuid';
import scoringModelRouter from '../../routers/ScoringModel';
import { Request, Response, RequestHandler } from 'express';
import { IAuth } from '../../types/Common';

interface MockRequest extends Partial<Request> {
  auth: typeof mockAuth;
  body: Record<string, unknown>;
  params: Record<string, string>;
  query: Record<string, string>;
}

interface MockResponse extends Partial<Response> {
  status: sinon.SinonStub;
  json: sinon.SinonStub;
}

describe('ScoringModel Router', () => {
  let dbManager: TestDBManager;
  let auth: typeof mockAuth;
  let testListCompanyId: string;
  let testScoringModelId: string;
  let handlers: Record<string, RequestHandler>;
  let req: MockRequest;
  let res: MockResponse;
  let next: sinon.SinonStub;

  before(async () => {
    auth = { ...mockAuth };
    dbManager = new TestDBManager(auth);
    testListCompanyId = await dbManager.createListData();
  });
  after(async function () {
    this.timeout(30000); // Increase timeout to 30 seconds for cleanup
    await dbManager.cleanupTestData();
  });

  beforeEach(() => {
    const router = scoringModelRouter();
    const findHandler = (path: string, method: string): RequestHandler => {
      const layer = router.stack.find((l) => {
        const route = l.route;
        return route?.path === path && route?.stack[0]?.method === method;
      });
      return (layer?.route?.stack[0]?.handle ?? (() => {})) as RequestHandler;
    };

    handlers = {
      post: findHandler('/', 'post'),
      get: findHandler('/', 'get'),
      getById: findHandler('/:id', 'get'),
      put: findHandler('/:id', 'put'),
      delete: findHandler('/:id', 'delete'),
      execute: findHandler('/execute', 'post'),
      getExecution: findHandler('/execution/:executionId', 'get')
    };

    req = {
      auth,
      body: {},
      params: {},
      query: {}
    };
    res = {
      status: sinon.stub().returnsThis(),
      json: sinon.stub().returnsThis()
    };
    next = sinon.stub();
  });

  beforeEach(async () => {
    // Create a test scoring model for update/delete operations
    const scoringModel = await db.scoringModel.create({
      data: {
        name: 'Test Model',
        description: 'Test Description',
        listCompanyId: testListCompanyId,
        createdById: auth.uid,
        ruleSets: {
          create: [
            {
              label: 'Test Rule Set',
              weight: 100,
              defaultScore: 5,
              fieldData: {
                create: {
                  sourceType: ScoringFieldSource.mandatory,
                  fieldType: FieldDataType.string,
                  fieldPath: 'companyName'
                }
              },
              rules: {
                create: [
                  {
                    operator: ScoringOperator.equals,
                    value: { primary: 'Test Company' },
                    score: 5
                  }
                ]
              }
            }
          ]
        }
      }
    });
    testScoringModelId = scoringModel.id;
  });

  afterEach(async () => {
    // Clean up test data after each test
    await db.scoringModel.deleteMany({
      where: { listCompanyId: testListCompanyId }
    });
  });

  describe('POST /scoring-model', () => {
    it('should create a new scoring model', async () => {
      req.body = {
        name: 'New Test Model',
        description: 'New Test Description',
        listCompanyId: testListCompanyId,
        ruleSets: [
          {
            label: 'New Rule Set',
            weight: 100,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'New Company' },
                score: 5
              }
            ]
          }
        ]
      };

      await handlers.post(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 201);
      assert.exists(res.json.args[0][0].id);
      assert.equal(res.json.args[0][0].name, 'New Test Model');
      assert.equal(res.json.args[0][0].description, 'New Test Description');
    });

    it('should return 400 for invalid listCompanyId', async () => {
      req.body = {
        name: 'New Test Model',
        description: 'New Test Description',
        listCompanyId: 'invalid-uuid',
        ruleSets: []
      };

      await handlers.post(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 400);
      assert.include(res.json.args[0][0].message, 'Invalid listCompanyId');
    });

    it('should return 400 for missing required fields', async () => {
      req.body = {
        name: 'New Test Model',
        description: 'New Test Description'
      };

      await handlers.post(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 400);
      assert.include(res.json.args[0][0].message, 'Missing required fields');
    });
  });

  describe('GET /scoring-model', () => {
    it('should list scoring models for a list company', async () => {
      req.query = { listCompanyId: testListCompanyId };

      await handlers.get(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 200);
      assert.isArray(res.json.args[0][0]);
      assert.isAbove(res.json.args[0][0].length, 0);
      assert.equal(res.json.args[0][0][0].name, 'Test Model');
    });

    it('should return 400 for invalid listCompanyId', async () => {
      req.query = { listCompanyId: 'invalid-uuid' };

      await handlers.get(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 400);
      assert.include(res.json.args[0][0].message, 'Invalid listCompanyId');
    });
  });

  describe('GET /scoring-model/:id', () => {
    it('should get a single scoring model', async () => {
      req.params = { id: testScoringModelId };

      await handlers.getById(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 200);
      assert.equal(res.json.args[0][0].id, testScoringModelId);
      assert.equal(res.json.args[0][0].name, 'Test Model');
      assert.exists(res.json.args[0][0].ruleSets);
      assert.equal(res.json.args[0][0].ruleSets.length, 1);
    });

    it('should return 400 for invalid UUID', async () => {
      req.params = { id: 'invalid-uuid' };

      await handlers.getById(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 400);
      assert.include(res.json.args[0][0].message, 'Invalid scoring model ID');
    });

    it('should return 404 for non-existent model', async () => {
      req.params = { id: uuid() };

      await handlers.getById(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 404);
      assert.include(res.json.args[0][0].message, 'Scoring model not found');
    });
  });

  describe('PUT /scoring-model/:id', () => {
    it('should update a scoring model', async () => {
      req.params = { id: testScoringModelId };
      req.body = {
        name: 'Updated Test Model',
        description: 'Updated Test Description',
        listCompanyId: testListCompanyId,
        ruleSets: [
          {
            label: 'Updated Rule Set',
            weight: 100,
            defaultScore: 6,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Updated Company' },
                score: 8
              }
            ]
          }
        ]
      };

      await handlers.put(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 200);
      assert.equal(res.json.args[0][0].id, testScoringModelId);
      assert.equal(res.json.args[0][0].name, 'Updated Test Model');
      assert.equal(res.json.args[0][0].description, 'Updated Test Description');

      // Verify the update in the database
      const updatedModel = await db.scoringModel.findUnique({
        where: { id: testScoringModelId },
        include: {
          ruleSets: {
            include: {
              fieldData: true,
              rules: true
            }
          }
        }
      });

      assert.equal(updatedModel?.name, 'Updated Test Model');
      assert.equal(updatedModel?.ruleSets[0].label, 'Updated Rule Set');
      const ruleValue = updatedModel?.ruleSets[0].rules[0].value as { primary: string };
      assert.equal(ruleValue.primary, 'Updated Company');
    });

    it('should return 400 for invalid UUID', async () => {
      req.params = { id: 'invalid-uuid' };
      req.body = {
        name: 'Updated Test Model',
        description: 'Updated Test Description',
        listCompanyId: testListCompanyId,
        ruleSets: []
      };

      await handlers.put(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 400);
      assert.include(res.json.args[0][0].message, 'Invalid scoring model ID');
    });

    it('should return 404 for non-existent model', async () => {
      req.params = { id: uuid() };
      req.body = {
        name: 'Updated Test Model',
        description: 'Updated Test Description',
        listCompanyId: testListCompanyId,
        ruleSets: []
      };

      await handlers.put(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 404);
      assert.include(res.json.args[0][0].message, 'Scoring model not found');
    });

    it('should return 400 for invalid rule set weights', async () => {
      req.params = { id: testScoringModelId };
      req.body = {
        name: 'Updated Test Model',
        description: 'Updated Test Description',
        listCompanyId: testListCompanyId,
        ruleSets: [
          {
            label: 'Updated Rule Set',
            weight: 150, // Invalid weight
            defaultScore: 6,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Updated Company' },
                score: 8
              }
            ]
          }
        ]
      };

      await handlers.put(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 400);
      assert.include(res.json.args[0][0].message, 'Individual rule set weights must be between 0 and 100');
    });
  });

  describe('DELETE /scoring-model/:id', () => {
    it('should delete a scoring model', async () => {
      req.params = { id: testScoringModelId };

      await handlers.delete(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 204);

      const deletedModel = await db.scoringModel.findUnique({ where: { id: testScoringModelId } });
      assert.isNull(deletedModel);
    });

    it('should return 404 for non-existent model', async () => {
      req.params = { id: uuid() };

      await handlers.delete(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 404);
      assert.include(res.json.args[0][0].message, 'Scoring model not found');
    });

    it('should return 400 for invalid UUID', async () => {
      req.params = { id: 'invalid-uuid' };

      await handlers.delete(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 400);
      assert.include(res.json.args[0][0].message, 'Invalid scoring model ID');
    });
  });

  describe('POST /scoring-model/execute', () => {
    let testEntityIds: string[];

    beforeEach(() => {
      // Create mock entity IDs for testing
      testEntityIds = [uuid(), uuid()];

      // Mock the executeScoring method on ScoringModelService
      sinon.stub(handlers, 'execute').callsFake(async (req: Request, res: Response) => {
        // Return mock execution data
        const executions = (req.body.entityIds as string[]).map((entityId: string) => ({
          entityId,
          executionId: uuid()
        }));

        return res.json(executions);
      });
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should execute scoring model for provided entity IDs', async () => {
      req.body = {
        scoringModelId: testScoringModelId,
        entityType: EntityType.idea,
        entityIds: testEntityIds,
        options: { silent: false }
      };

      await handlers.execute(req as Request, res as Response, next);

      assert.isTrue(res.json.called);
      const executions = res.json.args[0][0];
      assert.isArray(executions);
      assert.equal(executions.length, testEntityIds.length);
      assert.equal(executions[0].entityId, testEntityIds[0]);
      assert.property(executions[0], 'executionId');
    });

    it('should return 400 for invalid scoring model ID', async () => {
      // Restore the stub to test validation
      (handlers.execute as sinon.SinonStub).restore();

      req.body = {
        scoringModelId: 'invalid-id',
        entityType: EntityType.idea,
        entityIds: testEntityIds
      };

      await handlers.execute(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 400);
      assert.include(res.json.args[0][0].message, 'Invalid scoring model id');
    });

    it('should return 400 for missing entity IDs', async () => {
      // Restore the stub to test validation
      (handlers.execute as sinon.SinonStub).restore();

      req.body = {
        scoringModelId: testScoringModelId,
        entityType: EntityType.idea,
        entityIds: []
      };

      await handlers.execute(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 400);
      assert.include(res.json.args[0][0].message, 'Invalid entity ids');
    });

    it('should return 400 for invalid entity type', async () => {
      // Restore the stub to test validation
      (handlers.execute as sinon.SinonStub).restore();

      req.body = {
        scoringModelId: testScoringModelId,
        entityType: 'invalid-type',
        entityIds: testEntityIds
      };

      await handlers.execute(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 400);
      assert.include(res.json.args[0][0].message, 'Invalid entity type');
    });

    it('should return 401 if user is not authenticated', async () => {
      // Restore the stub to test validation
      (handlers.execute as sinon.SinonStub).restore();

      req.auth = undefined as unknown as IAuth;
      req.body = {
        scoringModelId: testScoringModelId,
        entityType: EntityType.idea,
        entityIds: testEntityIds
      };

      await handlers.execute(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 401);
      assert.include(res.json.args[0][0].message, 'Unauthorized');
    });
  });

  describe('GET /scoring-model/execution/:executionId', () => {
    let testExecutionId: string;

    beforeEach(async () => {
      // Create a test execution
      const execution = await db.execution.create({
        data: {
          orgId: auth.oid,
          type: ExecutionType.scoring,
          typeId: testScoringModelId,
          entityType: EntityType.idea,
          entityId: uuid(),
          status: ExecutionStatus.pending,
          statusMessage: 'Scoring job queued',
          createdById: auth.uid
        }
      });
      testExecutionId = execution.id;

      // Create a queue task
      await db.queueTask.create({
        data: {
          executionId: testExecutionId,
          type: QueueTaskType.scoring,
          status: ExecutionStatus.pending,
          data: {
            entityId: execution.entityId,
            entityType: execution.entityType,
            scoringModelId: testScoringModelId,
            executionId: testExecutionId,
            orgId: auth.oid,
            uid: auth.uid
          }
        }
      });
    });

    it('should get a single scoring execution', async () => {
      req.params = { executionId: testExecutionId };

      await handlers.getExecution(req as Request, res as Response, next);

      // If res.status wasn't called, Express uses the default status of 200
      if (res.status.called) {
        assert.equal(res.status.args[0][0], 200);
      }

      // Verify json was called
      assert.isTrue(res.json.called);

      // Get the first call arguments to res.json
      const [responseData] = res.json.firstCall.args;

      // Verify response structure
      assert.exists(responseData);
      assert.exists(responseData.data);

      // Check the execution data
      const execution = responseData.data;
      assert.equal(execution.id, testExecutionId);
      assert.equal(execution.type, ExecutionType.scoring);
      assert.equal(execution.status, ExecutionStatus.pending);
      assert.equal(execution.typeId, testScoringModelId);
      assert.equal(execution.orgId, auth.oid);
      assert.equal(execution.createdById, auth.uid);

      // Check queue tasks
      assert.exists(execution.queueTasks);
      assert.isArray(execution.queueTasks);
      assert.isAtLeast(execution.queueTasks.length, 1);
      assert.equal(execution.queueTasks[0].type, QueueTaskType.scoring);
      assert.equal(execution.queueTasks[0].status, ExecutionStatus.pending);
    });

    it('should return 400 for invalid UUID', async () => {
      req.params = { executionId: 'invalid-uuid' };

      await handlers.getExecution(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 400);
      assert.include(res.json.args[0][0].message, 'Invalid execution id');
    });

    it('should return 404 for non-existent execution', async () => {
      req.params = { executionId: uuid() };

      await handlers.getExecution(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 404);
      assert.include(res.json.args[0][0].message, 'Execution not found');
    });

    it('should return 401 for unauthorized request', async () => {
      req.auth = undefined as unknown as IAuth;
      req.params = { executionId: testExecutionId };

      await handlers.getExecution(req as Request, res as Response, next);

      assert.equal(res.status.args[0][0], 401);
      assert.include(res.json.args[0][0].message, 'Unauthorized');
    });
  });
});
