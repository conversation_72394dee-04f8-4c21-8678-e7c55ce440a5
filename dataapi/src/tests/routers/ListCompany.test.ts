import { assert } from 'chai';
import sinon from 'sinon';
import { db } from '../../database';
import { TestDBManager } from '../helpers/DBHelpers';
import { mockAuth } from '../helpers/ModelData';
import { ListSourceStatus, ListSourceType } from '@prisma/client';
import { v7 as uuid } from 'uuid';
import listCompanyRouter from '../../routers/ListCompany';
import { Request, Response, RequestHandler } from 'express';

interface MockRequest extends Partial<Request> {
  auth: typeof mockAuth;
  body: Record<string, unknown>;
  params: Record<string, string>;
  query: Record<string, string>;
  file?: Express.Multer.File;
}

interface MockResponse extends Partial<Response> {
  status: sinon.SinonStub;
  json: sinon.SinonStub;
}

describe('ListCompany Router', () => {
  let dbManager: TestDBManager;
  let auth: typeof mockAuth;
  let testListCompanyId: string;
  let intentionId: string;
  let handlers: Record<string, RequestHandler>;
  let req: MockRequest;
  let res: MockResponse;
  let next: sinon.SinonStub;

  before(async () => {
    auth = { ...mockAuth };
    dbManager = new TestDBManager(auth);
    testListCompanyId = await dbManager.createListData();

    // Create test intention and intention source
    const intention = await db.intention.create({
      data: {
        name: 'Test Intention',
        orderBy: 1,
        createdAt: new Date()
      }
    });
    intentionId = intention.id;

    await db.intentionSource.create({
      data: {
        name: 'Test Source',
        orderBy: 1,
        intentionId,
        sourceType: ListSourceType.resource,
        parameters: {},
        createdAt: new Date()
      }
    });
  });

  after(async function () {
    this.timeout(30000);
    await dbManager.cleanupTestData();
  });

  beforeEach(() => {
    const router = listCompanyRouter();
    const findHandler = (path: string, method: string): RequestHandler => {
      const layer = router.stack.find((l) => {
        const route = l.route;
        return route?.path === path && route?.stack[0]?.method === method;
      });
      return (layer?.route?.stack[0]?.handle ?? (() => {})) as RequestHandler;
    };

    handlers = {
      createSource: findHandler('/source', 'post'),
      getSourceStatus: findHandler('/source/:sourceId/status', 'get'),
      getSources: findHandler('/sources', 'get'),
      getIdeas: findHandler('/ideas', 'get')
    };

    req = {
      auth,
      body: {},
      params: {},
      query: {},
      file: undefined
    };
    res = {
      status: sinon.stub().returnsThis(),
      json: sinon.stub().returnsThis()
    };
    next = sinon.stub();
  });

  describe('POST /source - CSV Source Creation', () => {
    it('should create CSV source successfully', async () => {
      req.body = {
        listCompanyId: testListCompanyId,
        intentionId,
        sourceType: ListSourceType.resource,
        parameters: {
          columns: [
            { column: 'name', value: 'name' },
            { column: 'website', value: 'website' }
          ]
        }
      };

      // Mock file upload
      req.file = {
        buffer: Buffer.from('name,website\nTest Company,https://test.com'),
        originalname: 'test.csv',
        mimetype: 'text/csv'
      } as Express.Multer.File;

      await handlers.createSource(req as Request, res as Response, next);

      assert.isTrue(res.status.calledWith(200));
      assert.isTrue(res.json.calledOnce);

      const response = res.json.getCall(0).args[0];
      assert.isTrue(response.success);
      assert.isString(response.sourceId);
      assert.equal(response.sourceResult.totalProcessed, 1);
      assert.equal(response.sourceResult.successCount, 1);
    });

    it('should handle CSV with validation errors', async () => {
      req.body = {
        listCompanyId: testListCompanyId,
        intentionId,
        sourceType: ListSourceType.resource,
        parameters: {
          columns: [
            { column: 'name', value: 'name' },
            { column: 'website', value: 'website' }
          ]
        }
      };

      // Mock file with invalid data
      req.file = {
        buffer: Buffer.from('name,website\n,\nValid Company,https://valid.com'),
        originalname: 'test.csv',
        mimetype: 'text/csv'
      } as Express.Multer.File;

      await handlers.createSource(req as Request, res as Response, next);

      assert.isTrue(res.status.calledWith(200));
      const response = res.json.getCall(0).args[0];
      assert.isTrue(response.success);
      assert.equal(response.sourceResult.totalProcessed, 2);
      assert.equal(response.sourceResult.successCount, 1); // Only valid company
      assert.equal(response.sourceResult.skippedCount, 1); // Empty row
    });

    it('should handle missing file', async () => {
      req.body = {
        listCompanyId: testListCompanyId,
        intentionId,
        sourceType: ListSourceType.resource,
        parameters: {}
      };
      // No file attached

      await handlers.createSource(req as Request, res as Response, next);

      assert.isTrue(res.status.calledWith(400));
      const response = res.json.getCall(0).args[0];
      assert.isFalse(response.success);
      assert.include(response.message, 'file');
    });
  });

  describe('POST /source - Google Maps Source Creation', () => {
    it('should create Google Maps source successfully', async () => {
      req.body = {
        listCompanyId: testListCompanyId,
        intentionId,
        sourceType: ListSourceType.googleMaps,
        parameters: {
          query: 'restaurants near NYC',
          location: 'New York, NY'
        }
      };

      // Mock Google Maps API response
      const mockPlaces = [
        {
          displayName: { text: 'Test Restaurant' },
          websiteUri: 'https://test-restaurant.com',
          location: { latitude: 40.7128, longitude: -74.006 }
        }
      ];

      // Note: In real implementation, you'd stub the actual Google Maps service
      sinon.stub().resolves(mockPlaces);

      await handlers.createSource(req as Request, res as Response, next);

      assert.isTrue(res.status.calledWith(200));
      const response = res.json.getCall(0).args[0];
      assert.isTrue(response.success);
      assert.isString(response.sourceId);
    });

    it('should handle missing required parameters', async () => {
      req.body = {
        listCompanyId: testListCompanyId,
        intentionId,
        sourceType: ListSourceType.googleMaps,
        parameters: {} // Missing query and location
      };

      await handlers.createSource(req as Request, res as Response, next);

      assert.isTrue(res.status.calledWith(400));
      const response = res.json.getCall(0).args[0];
      assert.isFalse(response.success);
      assert.include(response.message.toLowerCase(), 'parameter');
    });
  });

  describe('POST /source - Company Filter Source Creation', () => {
    it('should create Company Filter source successfully', async () => {
      // Create test companies first
      await db.company.create({
        data: {
          name: 'Filter Test Company',
          website: 'https://filter-test.com',
          domain: 'filter-test.com',
          sourceType: 'acqwired',
          orgId: auth.oid
        }
      });

      req.body = {
        listCompanyId: testListCompanyId,
        intentionId,
        sourceType: ListSourceType.companyFilter,
        parameters: {
          filters: {
            industry: 'Technology'
          }
        }
      };

      await handlers.createSource(req as Request, res as Response, next);

      assert.isTrue(res.status.calledWith(200));
      const response = res.json.getCall(0).args[0];
      assert.isTrue(response.success);
      assert.isString(response.sourceId);
    });
  });

  describe('GET /source/:sourceId/status', () => {
    it('should return source status', async () => {
      // Create a test source
      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.completed,
          parameters: {},
          createdAt: new Date()
        }
      });

      req.params = { sourceId: source.id };

      await handlers.getSourceStatus(req as Request, res as Response, next);

      assert.isTrue(res.status.calledWith(200));
      const response = res.json.getCall(0).args[0];
      assert.equal(response.status, ListSourceStatus.completed);
      assert.equal(response.sourceId, source.id);
    });

    it('should handle non-existent source', async () => {
      req.params = { sourceId: uuid() };

      await handlers.getSourceStatus(req as Request, res as Response, next);

      assert.isTrue(res.status.calledWith(404));
      const response = res.json.getCall(0).args[0];
      assert.isFalse(response.success);
      assert.include(response.message, 'not found');
    });
  });

  describe('GET /sources', () => {
    it('should return list of sources', async () => {
      // Create test sources
      await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.completed,
          parameters: {},
          createdAt: new Date()
        }
      });

      req.query = { listCompanyId: testListCompanyId };

      await handlers.getSources(req as Request, res as Response, next);

      assert.isTrue(res.status.calledWith(200));
      const response = res.json.getCall(0).args[0];
      assert.isArray(response.sources);
      assert.isAtLeast(response.sources.length, 1);
    });

    it('should handle missing listCompanyId', async () => {
      req.query = {}; // Missing listCompanyId

      await handlers.getSources(req as Request, res as Response, next);

      assert.isTrue(res.status.calledWith(400));
      const response = res.json.getCall(0).args[0];
      assert.isFalse(response.success);
      assert.include(response.message, 'listCompanyId');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid listCompanyId', async () => {
      req.body = {
        listCompanyId: 'invalid-id',
        intentionId,
        sourceType: ListSourceType.resource,
        parameters: {}
      };

      await handlers.createSource(req as Request, res as Response, next);

      assert.isTrue(res.status.calledWith(400));
      const response = res.json.getCall(0).args[0];
      assert.isFalse(response.success);
    });

    it('should handle invalid intentionId', async () => {
      req.body = {
        listCompanyId: testListCompanyId,
        intentionId: 'invalid-id',
        sourceType: ListSourceType.resource,
        parameters: {}
      };

      await handlers.createSource(req as Request, res as Response, next);

      assert.isTrue(res.status.calledWith(400));
      const response = res.json.getCall(0).args[0];
      assert.isFalse(response.success);
    });

    it('should handle unsupported source type', async () => {
      req.body = {
        listCompanyId: testListCompanyId,
        intentionId,
        sourceType: 'unsupported-type',
        parameters: {}
      };

      await handlers.createSource(req as Request, res as Response, next);

      assert.isTrue(res.status.calledWith(400));
      const response = res.json.getCall(0).args[0];
      assert.isFalse(response.success);
      assert.include(response.message, 'source type');
    });
  });
});
