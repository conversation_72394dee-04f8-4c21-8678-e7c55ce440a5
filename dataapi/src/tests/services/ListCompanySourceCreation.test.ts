import { assert } from 'chai';
import sinon from 'sinon';
import { db } from '../../database';
import { TestDBManager } from '../helpers/DBHelpers';
import { mockAuth } from '../helpers/ModelData';
import { ListSourceStatus, ListSourceType } from '@prisma/client';
import ListCompanyService from '../../services/ListCompany';
import NotificationService from '../../services/Notification';

describe('ListCompany Source Creation', () => {
  let dbManager: TestDBManager;
  let auth: typeof mockAuth;
  let testListCompanyId: string;
  let intentionId: string;
  let listCompanyService: ListCompanyService;

  before(async () => {
    auth = { ...mockAuth };
    dbManager = new TestDBManager(auth);
    testListCompanyId = await dbManager.createListData();

    // Create test intention and intention source
    const intention = await db.intention.create({
      data: {
        name: 'Test Intention',
        orderBy: 1,
        createdAt: new Date()
      }
    });
    intentionId = intention.id;

    await db.intentionSource.create({
      data: {
        name: 'Test Source',
        orderBy: 1,
        intentionId,
        sourceType: ListSourceType.resource,
        parameters: {},
        createdAt: new Date()
      }
    });

    listCompanyService = new ListCompanyService({
      auth,
      notificationService: new NotificationService({ auth })
    });
  });

  after(async function () {
    this.timeout(30000);
    await dbManager.cleanupTestData();
  });

  describe('CSV Source Creation', () => {
    it('should create CSV source with proper counting logic', async () => {
      // Create test CSV data with some duplicates
      const csvData = [
        { name: 'Company A', website: 'https://www.companya.com', industry: 'Tech' },
        { name: 'Company B', website: 'https://www.companyb.com', industry: 'Finance' },
        { name: 'Company C', website: 'https://www.companya.com', industry: 'Tech' }, // Duplicate domain
        { name: 'Company D', website: 'https://www.companyd.com', industry: 'Healthcare' },
        { name: '', website: '', industry: 'Invalid' } // Invalid row
      ];

      // Mock CSV parsing
      const csvContent = csvData.map((row) => `${row.name},${row.website},${row.industry}`).join('\n');
      const csvBuffer = Buffer.from(`name,website,industry\n${csvContent}`);

      // Create list source
      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.inProgress,
          parameters: {
            columns: [
              { column: 'name', value: 'name' },
              { column: 'website', value: 'website' },
              { column: 'industry', value: 'industry' }
            ]
          },
          createdAt: new Date()
        }
      });

      // Mock file processing
      const mockRows = csvData.map((row) => ({
        name: row.name,
        website: row.website,
        industry: row.industry
      }));

      // Test the CSV processing logic
      const result = await listCompanyService.createCSVListIdeasForSource(source, undefined, mockRows);

      // Verify results
      assert.isTrue(result.success);
      assert.equal(result.totalProcessed, 5);
      assert.equal(result.successCount, 3); // Company A, B, D (C is duplicate, empty row skipped)
      assert.equal(result.skippedCount, 2); // Duplicate domain + invalid row
      assert.equal(result.failureCount, 0);

      // Verify database state
      const createdIdeas = await db.listCompanyIdea.findMany({
        where: { orgId: auth.oid }
      });

      // Should have 3 unique companies (A, B, D)
      const uniqueDomains = new Set(createdIdeas.map((idea) => idea.domain).filter(Boolean));
      assert.equal(uniqueDomains.size, 3);

      // Verify source mappings
      const sourceMappings = await db.listCompanySourceIdeaMapping.findMany({
        where: { listSourceId: source.id }
      });
      assert.equal(sourceMappings.length, 3); // Only successful records should be mapped

      // Verify list mappings
      const listMappings = await db.listCompanyIdeaMapping.findMany({
        where: { listCompanyId: testListCompanyId }
      });
      assert.equal(listMappings.length, 3);
    });

    it('should handle CSV with all duplicate domains', async () => {
      // Create initial company
      await db.listCompanyIdea.create({
        data: {
          orgId: auth.oid,
          companyName: 'Existing Company',
          website: 'https://www.existing.com',
          domain: 'existing.com'
        }
      });

      // Create CSV with all duplicate domains
      const csvData = [
        { name: 'Company X', website: 'https://www.existing.com' },
        { name: 'Company Y', website: 'https://existing.com' },
        { name: 'Company Z', website: 'http://www.existing.com' }
      ];

      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.inProgress,
          parameters: {
            columns: [
              { column: 'name', value: 'name' },
              { column: 'website', value: 'website' }
            ]
          },
          createdAt: new Date()
        }
      });

      const result = await listCompanyService.createCSVListIdeasForSource(source, undefined, csvData);

      // All should be skipped as duplicates
      assert.isTrue(result.success);
      assert.equal(result.totalProcessed, 3);
      assert.equal(result.successCount, 0);
      assert.equal(result.skippedCount, 3);
      assert.equal(result.failureCount, 0);
    });
  });

  describe('Google Maps Source Creation', () => {
    it('should create Google Maps source with proper counting', async () => {
      // Mock Google Maps places data
      const mockPlaces = [
        {
          displayName: { text: 'Restaurant A' },
          websiteUri: 'https://www.restaurant-a.com',
          location: { latitude: 40.7128, longitude: -74.006 }
        },
        {
          displayName: { text: 'Restaurant B' },
          websiteUri: 'https://www.restaurant-b.com',
          location: { latitude: 40.7589, longitude: -73.9851 }
        },
        {
          displayName: { text: 'Restaurant C' },
          websiteUri: null, // No website
          location: { latitude: 40.7505, longitude: -73.9934 }
        }
      ];

      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.inProgress,
          parameters: {
            query: 'restaurants near NYC',
            location: 'New York, NY'
          },
          createdAt: new Date()
        }
      });

      // Mock the Google Maps service call
      const googleMapsStub = sinon.stub(listCompanyService as any, 'callGoogleMapsAPI').resolves(mockPlaces);

      try {
        const result = await listCompanyService.createGoogleMapsListIdeasForSource(source);

        assert.isTrue(result.success);
        assert.equal(result.totalProcessed, 3);
        assert.equal(result.successCount, 3); // All places should be processed
        assert.equal(result.skippedCount, 0);
        assert.equal(result.failureCount, 0);

        // Verify database state
        const createdIdeas = await db.listCompanyIdea.findMany({
          where: {
            orgId: auth.oid,
            companyName: { in: ['Restaurant A', 'Restaurant B', 'Restaurant C'] }
          }
        });
        assert.equal(createdIdeas.length, 3);
      } finally {
        googleMapsStub.restore();
      }
    });
  });

  describe('Company Filter Source Creation', () => {
    it('should create Company Filter source with proper counting', async () => {
      // Create test companies in the main Company table
      const company1 = await db.company.create({
        data: {
          name: 'Filter Company A',
          website: 'https://www.filter-a.com',
          domain: 'filter-a.com',
          sourceType: 'acqwired',
          orgId: auth.oid
        }
      });

      await db.company.create({
        data: {
          name: 'Filter Company B',
          website: 'https://www.filter-b.com',
          domain: 'filter-b.com',
          sourceType: 'acqwired',
          orgId: auth.oid
        }
      });

      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.inProgress,
          parameters: {
            filters: {
              industry: 'Technology'
            }
          },
          createdAt: new Date()
        }
      });

      // Mock the company filter query
      const filterStub = sinon.stub(db.company, 'findMany').resolves([company1]);

      try {
        const result = await listCompanyService.createCompanyFilterListIdeasForSource(source);

        assert.isTrue(result.success);
        assert.equal(result.totalProcessed, 1);
        assert.equal(result.successCount, 1);
        assert.equal(result.skippedCount, 0);
        assert.equal(result.failureCount, 0);

        // Verify the correct company was processed
        const sourceMappings = await db.listCompanySourceIdeaMapping.findMany({
          where: { listSourceId: source.id },
          include: { listCompanyIdea: true }
        });

        assert.equal(sourceMappings.length, 1);
        assert.equal(sourceMappings[0].listCompanyIdea.companyId, company1.id);
      } finally {
        filterStub.restore();
      }
    });
  });

  describe('Ocean.io Source Creation', () => {
    it('should create Ocean.io source with proper counting', async () => {
      // Mock Ocean.io API response
      const mockOceanResponse = {
        companies: [
          {
            company: {
              name: 'Ocean Company A',
              rootUrl: 'https://www.ocean-a.com'
            },
            metadata: { employees: 100, revenue: '10M' }
          },
          {
            company: {
              name: 'Ocean Company B',
              rootUrl: 'https://www.ocean-b.com'
            },
            metadata: { employees: 50, revenue: '5M' }
          }
        ]
      };

      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.inProgress,
          parameters: {
            query: 'technology companies',
            filters: { employees: '50-200' }
          },
          createdAt: new Date()
        }
      });

      // Mock the Ocean.io service call
      const oceanStub = sinon.stub(listCompanyService as any, 'callOceanAPI').resolves(mockOceanResponse);

      try {
        const result = await listCompanyService.createOceanIoListIdeasForSource(source);

        assert.isTrue(result.success);
        assert.equal(result.totalProcessed, 2);
        assert.equal(result.successCount, 2);
        assert.equal(result.skippedCount, 0);
        assert.equal(result.failureCount, 0);

        // Verify database state
        const createdIdeas = await db.listCompanyIdea.findMany({
          where: {
            orgId: auth.oid,
            companyName: { in: ['Ocean Company A', 'Ocean Company B'] }
          }
        });
        assert.equal(createdIdeas.length, 2);

        // Verify metadata is stored
        const sourceMappings = await db.listCompanySourceIdeaMapping.findMany({
          where: { listSourceId: source.id }
        });
        assert.equal(sourceMappings.length, 2);
        assert.isObject(sourceMappings[0].metadata);
      } finally {
        oceanStub.restore();
      }
    });

    it('should handle Ocean.io API errors gracefully', async () => {
      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.processing,
          parameters: { query: 'invalid query' },
          createdAt: new Date()
        }
      });

      // Mock API error
      const oceanStub = sinon.stub(listCompanyService as any, 'callOceanAPI').rejects(new Error('API Error'));

      try {
        const result = await listCompanyService.createOceanListIdeasForSource(source);

        assert.isFalse(result.success);
        assert.equal(result.totalProcessed, 0);
        assert.equal(result.successCount, 0);
        assert.equal(result.skippedCount, 0);
        assert.equal(result.failureCount, 0);
        assert.isArray(result.failures);
        assert.equal(result.failures.length, 1);
        assert.include(result.failures[0].reason, 'API Error');
      } finally {
        oceanStub.restore();
      }
    });
  });

  describe('SourceScrub Source Creation', () => {
    it('should create SourceScrub source with proper counting', async () => {
      // Mock SourceScrub API response
      const mockSourceScrubResponse = [
        {
          name: 'SourceScrub Company A',
          website: 'https://www.sourcescrub-a.com',
          industry: 'Software',
          employees: 150
        },
        {
          name: 'SourceScrub Company B',
          website: 'https://www.sourcescrub-b.com',
          industry: 'Hardware',
          employees: 75
        },
        {
          name: 'SourceScrub Company C',
          website: 'https://www.sourcescrub-a.com', // Duplicate domain
          industry: 'Software',
          employees: 200
        }
      ];

      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.processing,
          parameters: {
            searchTerm: 'software companies',
            location: 'San Francisco'
          },
          createdAt: new Date()
        }
      });

      // Mock the SourceScrub service call
      const sourceScrubStub = sinon.stub(listCompanyService as any, 'callSourceScrubAPI').resolves(mockSourceScrubResponse);

      try {
        const result = await listCompanyService.createSourceScrubListIdeasForSource(source);

        assert.isTrue(result.success);
        assert.equal(result.totalProcessed, 3);
        assert.equal(result.successCount, 2); // A and B (C is duplicate)
        assert.equal(result.skippedCount, 1); // C is duplicate
        assert.equal(result.failureCount, 0);

        // Verify only unique domains were created
        const createdIdeas = await db.listCompanyIdea.findMany({
          where: {
            orgId: auth.oid,
            companyName: { in: ['SourceScrub Company A', 'SourceScrub Company B', 'SourceScrub Company C'] }
          }
        });
        assert.equal(createdIdeas.length, 2); // Only A and B
      } finally {
        sourceScrubStub.restore();
      }
    });
  });

  describe('Exa Source Creation', () => {
    it('should create Exa source with proper counting', async () => {
      // Mock Exa API response
      const mockExaResponse = [
        {
          title: 'Exa Company A',
          url: 'https://www.exa-a.com',
          text: 'Leading AI company',
          score: 0.95
        },
        {
          title: 'Exa Company B',
          url: 'https://www.exa-b.com',
          text: 'Machine learning startup',
          score: 0.87
        }
      ];

      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.processing,
          parameters: {
            query: 'AI companies',
            numResults: 10
          },
          createdAt: new Date()
        }
      });

      // Mock the Exa service call
      const exaStub = sinon.stub(listCompanyService as any, 'callExaAPI').resolves(mockExaResponse);

      try {
        const result = await listCompanyService.createExaListIdeasForSource(source);

        assert.isTrue(result.success);
        assert.equal(result.totalProcessed, 2);
        assert.equal(result.successCount, 2);
        assert.equal(result.skippedCount, 0);
        assert.equal(result.failureCount, 0);

        // Verify database state
        const createdIdeas = await db.listCompanyIdea.findMany({
          where: {
            orgId: auth.oid,
            companyName: { in: ['Exa Company A', 'Exa Company B'] }
          }
        });
        assert.equal(createdIdeas.length, 2);
      } finally {
        exaStub.restore();
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle database errors gracefully', async () => {
      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.processing,
          parameters: {},
          createdAt: new Date()
        }
      });

      // Mock database error
      const dbStub = sinon.stub(db.listCompanyIdea, 'create').rejects(new Error('Database connection failed'));

      try {
        const csvData = [{ name: 'Test Company', website: 'https://test.com' }];
        const result = await listCompanyService.createResourceListIdeasForSource(source, csvData);

        assert.isTrue(result.success);
        assert.equal(result.totalProcessed, 1);
        assert.equal(result.successCount, 0);
        assert.equal(result.skippedCount, 0);
        assert.equal(result.failureCount, 1);
        assert.isArray(result.failures);
        assert.equal(result.failures.length, 1);
      } finally {
        dbStub.restore();
      }
    });

    it('should handle empty source data', async () => {
      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.processing,
          parameters: {},
          createdAt: new Date()
        }
      });

      const result = await listCompanyService.createResourceListIdeasForSource(source, []);

      assert.isTrue(result.success);
      assert.equal(result.totalProcessed, 0);
      assert.equal(result.successCount, 0);
      assert.equal(result.skippedCount, 0);
      assert.equal(result.failureCount, 0);
    });

    it('should update source status correctly on completion', async () => {
      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.processing,
          parameters: {},
          createdAt: new Date()
        }
      });

      const csvData = [{ name: 'Status Test Company', website: 'https://status-test.com' }];
      await listCompanyService.createResourceListIdeasForSource(source, csvData);

      // Verify source status was updated
      const updatedSource = await db.listSource.findUnique({
        where: { id: source.id }
      });

      assert.equal(updatedSource?.status, ListSourceStatus.completed);
      assert.isString(updatedSource?.reason);
    });
  });
});
