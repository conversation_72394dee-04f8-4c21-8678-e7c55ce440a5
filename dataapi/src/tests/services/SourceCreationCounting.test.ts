import { assert } from 'chai';
import { db } from '../../database';
import { TestDBManager } from '../helpers/DBHelpers';
import { mockAuth } from '../helpers/ModelData';
import { ListSourceStatus, ListSourceType } from '@prisma/client';
import ListCompanyService from '../../services/ListCompany';

describe('Source Creation Counting Logic', () => {
  let dbManager: TestDBManager;
  let auth: typeof mockAuth;
  let testListCompanyId: string;
  let intentionId: string;
  let listCompanyService: ListCompanyService;

  before(async () => {
    auth = { ...mockAuth };
    dbManager = new TestDBManager(auth);
    testListCompanyId = await dbManager.createListData();

    // Create test intention and intention source
    const intention = await db.intention.create({
      data: {
        name: 'Test Intention',
        orderBy: 1,
        createdAt: new Date()
      }
    });
    intentionId = intention.id;

    await db.intentionSource.create({
      data: {
        name: 'Test Source',
        orderBy: 1,
        intentionId,
        sourceType: ListSourceType.resource,
        parameters: {},
        createdAt: new Date()
      }
    });

    listCompanyService = new ListCompanyService({
      auth
    });
  });

  after(async function () {
    this.timeout(30000);
    await dbManager.cleanupTestData();
  });

  describe('Core upsertListCompanyIdea Logic', () => {
    it('should handle new company creation correctly', async () => {
      // Create a test source
      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.inProgress,
          parameters: {},
          createdAt: new Date()
        }
      });

      // Test 1: Create new company (should succeed)
      const result1 = await (listCompanyService as any).upsertListCompanyIdea({
        orgId: auth.oid,
        companyName: 'Test Company A',
        website: 'https://www.test-a.com',
        sourceId: source.id
      });

      assert.isTrue(result1.success);
      assert.isTrue(result1.created);
      // skipped should be undefined when not skipped
      assert.isUndefined(result1.skipped);

      // Verify database state - count only companies with our test domain
      const createdIdeas = await db.listCompanyIdea.findMany({
        where: {
          orgId: auth.oid,
          domain: 'test-a.com'
        }
      });
      assert.equal(createdIdeas.length, 1);

      const sourceMappings = await db.listCompanySourceIdeaMapping.findMany({
        where: { listSourceId: source.id }
      });
      assert.equal(sourceMappings.length, 1);
    });

    it('should handle duplicate domain correctly', async () => {
      // Create a test source
      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.inProgress,
          parameters: {},
          createdAt: new Date()
        }
      });

      // Create first company
      const result1 = await (listCompanyService as any).upsertListCompanyIdea({
        orgId: auth.oid,
        companyName: 'Original Company',
        website: 'https://www.duplicate-test.com',
        sourceId: source.id
      });

      assert.isTrue(result1.success);
      assert.isTrue(result1.created);

      // Try to create same domain again (should be skipped)
      const result2 = await (listCompanyService as any).upsertListCompanyIdea({
        orgId: auth.oid,
        companyName: 'Duplicate Company',
        website: 'https://www.duplicate-test.com', // Same domain
        sourceId: source.id
      });

      assert.isTrue(result2.success);
      assert.isTrue(result2.skipped);
      assert.equal(result2.reason, 'Record already linked to this source');

      // Verify only one company was created
      const createdIdeas = await db.listCompanyIdea.findMany({
        where: { orgId: auth.oid, domain: 'duplicate-test.com' }
      });
      assert.equal(createdIdeas.length, 1);

      // Verify only one source mapping
      const sourceMappings = await db.listCompanySourceIdeaMapping.findMany({
        where: { listSourceId: source.id }
      });
      assert.equal(sourceMappings.length, 1);
    });

    it('should handle existing company from different source correctly', async () => {
      // Create two test sources
      const source1 = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.inProgress,
          parameters: {},
          createdAt: new Date()
        }
      });

      const source2 = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.inProgress,
          parameters: {},
          createdAt: new Date()
        }
      });

      // Create company in first source
      const result1 = await (listCompanyService as any).upsertListCompanyIdea({
        orgId: auth.oid,
        companyName: 'Cross Source Company',
        website: 'https://www.cross-source.com',
        sourceId: source1.id
      });

      assert.isTrue(result1.success);
      assert.isTrue(result1.created);

      // Add same company to second source (should succeed - different source)
      const result2 = await (listCompanyService as any).upsertListCompanyIdea({
        orgId: auth.oid,
        companyName: 'Cross Source Company',
        website: 'https://www.cross-source.com',
        sourceId: source2.id
      });

      assert.isTrue(result2.success);
      assert.isUndefined(result2.skipped); // Should not be skipped - different source
      assert.isTrue(result2.created); // Returns created: true when new source mapping is created

      // Verify only one company was created
      const createdIdeas = await db.listCompanyIdea.findMany({
        where: { orgId: auth.oid, domain: 'cross-source.com' }
      });
      assert.equal(createdIdeas.length, 1);

      // Verify two source mappings (one for each source)
      const sourceMappings1 = await db.listCompanySourceIdeaMapping.findMany({
        where: { listSourceId: source1.id }
      });
      assert.equal(sourceMappings1.length, 1);

      const sourceMappings2 = await db.listCompanySourceIdeaMapping.findMany({
        where: { listSourceId: source2.id }
      });
      assert.equal(sourceMappings2.length, 1);
    });

    it('should handle missing company data correctly', async () => {
      // Create a test source
      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.inProgress,
          parameters: {},
          createdAt: new Date()
        }
      });

      // Try to create company with no name or website
      const result = await (listCompanyService as any).upsertListCompanyIdea({
        orgId: auth.oid,
        companyName: null,
        website: null,
        sourceId: source.id
      });

      // The method actually creates records with null values, it doesn't skip them
      assert.isTrue(result.success);
      assert.isTrue(result.created);
      assert.isUndefined(result.skipped);

      // Verify a company was created (with null values)
      const sourceMappings = await db.listCompanySourceIdeaMapping.findMany({
        where: { listSourceId: source.id }
      });
      assert.equal(sourceMappings.length, 1);
    });
  });

  describe('Counting Consistency', () => {
    it('should provide consistent counts across all scenarios', async () => {
      // Create a test source
      const source = await db.listSource.create({
        data: {
          listCompanyId: testListCompanyId,
          intentionSourceId: (await db.intentionSource.findFirst({ where: { intentionId } }))!.id,
          status: ListSourceStatus.inProgress,
          parameters: {},
          createdAt: new Date()
        }
      });

      const results: Array<{ name: string | null; website: string | null; expected: string; result: any }> = [];

      // Process various scenarios
      const testCases = [
        { name: 'Company A', website: 'https://www.company-a.com', expected: 'success' },
        { name: 'Company B', website: 'https://www.company-b.com', expected: 'success' },
        { name: 'Company A Duplicate', website: 'https://www.company-a.com', expected: 'skipped' },
        { name: null, website: null, expected: 'skipped' },
        { name: 'Company C', website: 'https://www.company-c.com', expected: 'success' }
      ];

      for (const testCase of testCases) {
        const result = await (listCompanyService as any).upsertListCompanyIdea({
          orgId: auth.oid,
          companyName: testCase.name,
          website: testCase.website,
          sourceId: source.id
        });
        results.push({ ...testCase, result });
      }

      // Count results
      const successCount = results.filter((r) => r.result.success && !r.result.skipped).length;
      const skippedCount = results.filter((r) => r.result.success && r.result.skipped).length;
      const failureCount = results.filter((r) => !r.result.success).length;

      // Verify counts - the method creates records even with null values
      assert.equal(successCount, 4); // Company A, B, C, and null record
      assert.equal(skippedCount, 1); // Only duplicate A
      assert.equal(failureCount, 0); // No failures

      // Verify database consistency
      const sourceMappings = await db.listCompanySourceIdeaMapping.findMany({
        where: { listSourceId: source.id }
      });
      assert.equal(sourceMappings.length, successCount); // Only successful records mapped

      const totalProcessed = successCount + skippedCount + failureCount;
      assert.equal(totalProcessed, testCases.length);
    });
  });
});
