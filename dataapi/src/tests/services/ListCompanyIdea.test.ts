import { assert } from 'chai';
import { db } from '../../database';
import { TestDBManager } from '../helpers/DBHelpers';
import { v7 as uuid } from 'uuid';
import { EntityType, ExecutionType } from '@prisma/client';
import { logger } from '../../logger';
import { ICompanyIdeaStatus } from '../../types/ListCompanyIdea';
import { TestContext, createTestSource, createTestMappings, createTestIdea, createTestExecution, createTestScoringModel, setupTestContext, createTestIdeasWithStatuses, createTestIdeasWithShortlist, createTestIdeasWithScoring, createTestIdeasWithEnrichment } from '../helpers/ListCompanyIdeaData';
import { SortOrder } from '../../types/Common';

describe('ListCompanyIdea', () => {
  let dbManager: TestDBManager;
  let context: TestContext;

  before(async () => {
    context = await setupTestContext();
    dbManager = new TestDBManager(context.auth);
    logger.info({ msg: 'Test context setup complete', context });
  });

  after(async function () {
    this.timeout(30000); // Increase timeout to 30 seconds for cleanup
    await dbManager.cleanupTestData();
  });

  describe('List Company Ideas with Required Data', () => {
    it('should return all required data including enrichment results', async () => {
      // Create test source
      const source = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create test idea with enrichment
      const { idea, enrichmentResult } = await createTestIdeasWithEnrichment(context, source);

      // Get filtered ideas
      const result = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0
      });

      // Verify result structure
      assert.isTrue(result.hasRecords);
      assert.equal(result.listCompanyIdeas.length, 1);
      assert.equal(result.totalRecords, 1);
      assert.equal(result.totalPages, 1);

      // Verify idea data
      const returnedIdea = result.listCompanyIdeas[0];
      assert.equal(returnedIdea.id, idea.id);
      assert.equal(returnedIdea.orgId, idea.orgId);
      assert.equal(returnedIdea.companyName, idea.companyName);
      assert.equal(returnedIdea.website, idea.website);
      assert.equal(returnedIdea.status, ICompanyIdeaStatus.Idea);
      assert.isFalse(returnedIdea.inShortlist);

      // Verify sources
      assert.isArray(returnedIdea.sources);
      assert.equal(returnedIdea.sources.length, 1);
      const returnedSource = returnedIdea.sources[0];
      assert.equal(returnedSource.id, source.id);
      assert.equal(returnedSource.listCompanyId, source.listCompanyId);
      assert.equal(returnedSource.status, source.status);
      assert.equal(returnedSource.intentionSourceId, source.intentionSourceId);

      // Verify enrichment results
      assert.isArray(returnedIdea.enrichmentResults);
      assert.equal(returnedIdea.enrichmentResults.length, 1);
      const returnedEnrichment = returnedIdea.enrichmentResults[0];
      assert.equal(returnedEnrichment.id, enrichmentResult.id);
      assert.equal(returnedEnrichment.enrichmentId, enrichmentResult.enrichmentId);
      assert.deepEqual(returnedEnrichment.value, enrichmentResult.value);
    });

    it('should handle missing optional data gracefully', async () => {
      // Create test source
      const source = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create test idea with minimal data
      const idea = await createTestIdea(context, `Minimal Company ${uuid()}`, 'www.minimal.com');

      // Create source idea mapping
      await db.listCompanySourceIdeaMapping.create({
        data: {
          listCompanyIdeaId: idea.id,
          listSourceId: source.id,
          metadata: {}
        }
      });

      // Create list company idea mapping
      await db.listCompanyIdeaMapping.create({
        data: {
          listCompanyId: context.testListCompanyId,
          listCompanyIdeaId: idea.id,
          inShortlist: false
        }
      });

      // Get filtered ideas
      const result = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0
      });

      // Verify result structure
      assert.isTrue(result.hasRecords);
      assert.equal(result.listCompanyIdeas.length, 1);
      assert.equal(result.totalRecords, 1);
      assert.equal(result.totalPages, 1);

      // Verify idea data
      const returnedIdea = result.listCompanyIdeas[0];
      assert.equal(returnedIdea.id, idea.id);
      assert.equal(returnedIdea.orgId, idea.orgId);
      assert.equal(returnedIdea.companyName, idea.companyName);
      assert.equal(returnedIdea.website, idea.website);
      assert.isNull(returnedIdea.domain);
      assert.isNull(returnedIdea.fields);
      assert.equal(returnedIdea.status, ICompanyIdeaStatus.Idea);
      assert.isFalse(returnedIdea.inShortlist);

      // Verify sources
      assert.isArray(returnedIdea.sources);
      assert.equal(returnedIdea.sources.length, 1);

      // Verify optional data is handled gracefully
      assert.isArray(returnedIdea.enrichmentResults);
      assert.equal(returnedIdea.enrichmentResults.length, 0);
      assert.isArray(returnedIdea.scoringResults);
      assert.equal(returnedIdea.scoringResults.length, 0);
    });
  });

  describe('List Company Ideas with Filter Combinations', () => {
    it('should filter by text search on company name and website', async () => {
      // Create test source
      const source = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create test ideas with different names and websites
      const idea1 = await createTestIdea(context, 'Test Company A', 'www.example-a.com');
      const idea2 = await createTestIdea(context, 'Test Company B', 'www.example-b.com');

      // Create source idea mappings
      await createTestMappings([idea1.id, idea2.id], source.id, context.testListCompanyId);

      // Test search by company name
      const nameResult = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0,
        text: 'Company A'
      });
      assert.equal(nameResult.listCompanyIdeas.length, 1);
      assert.equal(nameResult.listCompanyIdeas[0].id, idea1.id);

      // Test search by website
      const websiteResult = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0,
        text: 'example-b'
      });
      assert.equal(websiteResult.listCompanyIdeas.length, 1);
      assert.equal(websiteResult.listCompanyIdeas[0].id, idea2.id);
    });

    it('should filter by multiple record statuses', async () => {
      // Create test source
      const source = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create test ideas with different statuses
      const { idea1, idea2, idea3 } = await createTestIdeasWithStatuses(context, source);

      // Test filtering by multiple statuses
      const result = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0,
        recordStatus: [ICompanyIdeaStatus.InAcqwired, ICompanyIdeaStatus.Invalid]
      });

      assert.equal(result.listCompanyIdeas.length, 2);
      const ideaIds = result.listCompanyIdeas.map((idea) => idea.id);
      assert.include(ideaIds, idea1.id); // InAcqwired
      assert.include(ideaIds, idea2.id); // Invalid
      assert.notInclude(ideaIds, idea3.id); // Idea
    });

    it('should filter by multiple shortlist statuses', async () => {
      // Create test source
      const source = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create test ideas with shortlist status
      const { idea1, idea2 } = await createTestIdeasWithShortlist(context, source);

      // Test filtering by multiple shortlist statuses
      const result = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0,
        inShortlist: ['true', 'false']
      });

      assert.equal(result.listCompanyIdeas.length, 2);
      const ideaIds = result.listCompanyIdeas.map((idea) => idea.id);
      assert.include(ideaIds, idea1.id);
      assert.include(ideaIds, idea2.id);
    });
  });

  describe('List Company Ideas with Pagination', () => {
    it('should handle pagination correctly', async () => {
      // Create test source
      const source = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create multiple test ideas
      const ideas = await Promise.all(Array.from({ length: 5 }, (_, i) => createTestIdea(context, `Test Company ${i + 1} ${uuid()}`, `www.example${i + 1}.com`)));

      // Create source idea mappings
      await createTestMappings(
        ideas.map((idea) => idea.id),
        source.id,
        context.testListCompanyId
      );

      // Test first page
      const firstPage = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 2,
        pageIndex: 0
      });

      assert.equal(firstPage.listCompanyIdeas.length, 2);
      assert.equal(firstPage.totalRecords, 5);
      assert.equal(firstPage.totalPages, 3);

      // Test second page
      const secondPage = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 2,
        pageIndex: 1
      });

      assert.equal(secondPage.listCompanyIdeas.length, 2);
      assert.equal(secondPage.totalRecords, 5);
      assert.equal(secondPage.totalPages, 3);
    });
  });

  describe('List Company Ideas with Scoring Results', () => {
    it('should return ideas with scoring results', async () => {
      // Create test source
      const source = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create test ideas with scoring results
      const { idea1, idea2 } = await createTestIdeasWithScoring(context, source);

      // Get filtered ideas
      const result = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0
      });

      // Verify results
      assert.isTrue(result.hasRecords);
      assert.equal(result.listCompanyIdeas.length, 2);

      // Verify first idea has scoring result
      const firstIdea = result.listCompanyIdeas.find((idea) => idea.id === idea1.id);
      assert.exists(firstIdea);
      assert.isArray(firstIdea?.scoringResults);
      assert.equal(firstIdea?.scoringResults.length, 1);
      assert.equal(firstIdea?.scoringResults[0].totalScore.toString(), '85');

      // Verify second idea has scoring result
      const secondIdea = result.listCompanyIdeas.find((idea) => idea.id === idea2.id);
      assert.exists(secondIdea);
      assert.isArray(secondIdea?.scoringResults);
      assert.equal(secondIdea?.scoringResults.length, 1);
      assert.equal(secondIdea?.scoringResults[0].totalScore.toString(), '85');
    });
  });

  describe('List Company Ideas with Sorting', () => {
    it('should sort by company name', async () => {
      // Create test source
      const source = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create test ideas with different names
      const idea1 = await createTestIdea(context, 'Zebra Company', 'www.zebra.com');
      const idea2 = await createTestIdea(context, 'Apple Company', 'www.apple.com');
      const idea3 = await createTestIdea(context, 'Banana Company', 'www.banana.com');

      // Create source idea mappings
      await createTestMappings([idea1.id, idea2.id, idea3.id], source.id, context.testListCompanyId);

      // Test ascending sort
      const ascResult = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0,
        sortKey: 'companyName',
        sortOrder: SortOrder.ASC
      });

      assert.equal(ascResult.listCompanyIdeas.length, 3);
      assert.equal(ascResult.listCompanyIdeas[0].companyName, 'Apple Company');
      assert.equal(ascResult.listCompanyIdeas[1].companyName, 'Banana Company');
      assert.equal(ascResult.listCompanyIdeas[2].companyName, 'Zebra Company');

      // Test descending sort
      const descResult = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0,
        sortKey: 'companyName',
        sortOrder: SortOrder.DESC
      });

      assert.equal(descResult.listCompanyIdeas.length, 3);
      assert.equal(descResult.listCompanyIdeas[0].companyName, 'Zebra Company');
      assert.equal(descResult.listCompanyIdeas[1].companyName, 'Banana Company');
      assert.equal(descResult.listCompanyIdeas[2].companyName, 'Apple Company');
    });

    it('should sort by website', async () => {
      // Create test source
      const source = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create test ideas with different websites
      const idea1 = await createTestIdea(context, 'Company A', 'www.zebra.com');
      const idea2 = await createTestIdea(context, 'Company B', 'www.apple.com');
      const idea3 = await createTestIdea(context, 'Company C', 'www.banana.com');

      // Create source idea mappings
      await createTestMappings([idea1.id, idea2.id, idea3.id], source.id, context.testListCompanyId);

      // Test ascending sort
      const ascResult = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0,
        sortKey: 'website',
        sortOrder: SortOrder.ASC
      });

      assert.equal(ascResult.listCompanyIdeas.length, 3);
      assert.equal(ascResult.listCompanyIdeas[0].website, 'www.apple.com');
      assert.equal(ascResult.listCompanyIdeas[1].website, 'www.banana.com');
      assert.equal(ascResult.listCompanyIdeas[2].website, 'www.zebra.com');

      // Test descending sort
      const descResult = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0,
        sortKey: 'website',
        sortOrder: SortOrder.DESC
      });

      assert.equal(descResult.listCompanyIdeas.length, 3);
      assert.equal(descResult.listCompanyIdeas[0].website, 'www.zebra.com');
      assert.equal(descResult.listCompanyIdeas[1].website, 'www.banana.com');
      assert.equal(descResult.listCompanyIdeas[2].website, 'www.apple.com');
    });

    it('should sort by scoring results', async () => {
      // Create test source
      const source = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create test ideas with unique names
      const idea1 = await createTestIdea(context, `Company A ${uuid()}`, 'www.company-a.com');
      const idea2 = await createTestIdea(context, `Company B ${uuid()}`, 'www.company-b.com');
      const idea3 = await createTestIdea(context, `Company C ${uuid()}`, 'www.company-c.com');

      // Create source idea mappings
      await createTestMappings([idea1.id, idea2.id, idea3.id], source.id, context.testListCompanyId);

      // Create scoring model and related data
      const scoringModel = await createTestScoringModel(context);
      const execution1 = await createTestExecution(context, idea1.id, EntityType.idea, ExecutionType.scoring, scoringModel.id);
      const execution2 = await createTestExecution(context, idea2.id, EntityType.idea, ExecutionType.scoring, scoringModel.id);
      const execution3 = await createTestExecution(context, idea3.id, EntityType.idea, ExecutionType.scoring, scoringModel.id);

      // Create scoring results with different scores
      await db.scoringResult.create({
        data: {
          entityId: idea1.id,
          scoringModelId: scoringModel.id,
          executionId: execution1.id,
          details: { testScoring: 'testValue' },
          totalScore: 85
        }
      });
      await db.scoringResult.create({
        data: {
          entityId: idea2.id,
          scoringModelId: scoringModel.id,
          executionId: execution2.id,
          details: { testScoring: 'testValue' },
          totalScore: 95
        }
      });
      await db.scoringResult.create({
        data: {
          entityId: idea3.id,
          scoringModelId: scoringModel.id,
          executionId: execution3.id,
          details: { testScoring: 'testValue' },
          totalScore: 75
        }
      });

      // Test ascending sort
      const ascResult = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0,
        sortKey: `scoringResults.${scoringModel.id}`,
        sortOrder: SortOrder.ASC
      });

      assert.equal(ascResult.listCompanyIdeas.length, 3);
      assert.equal(ascResult.listCompanyIdeas[0].id, idea3.id); // Lowest score
      assert.equal(ascResult.listCompanyIdeas[1].id, idea1.id); // Middle score
      assert.equal(ascResult.listCompanyIdeas[2].id, idea2.id); // Highest score

      // Test descending sort
      const descResult = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0,
        sortKey: `scoringResults.${scoringModel.id}`,
        sortOrder: SortOrder.DESC
      });

      assert.equal(descResult.listCompanyIdeas.length, 3);
      assert.equal(descResult.listCompanyIdeas[0].id, idea2.id); // Highest score
      assert.equal(descResult.listCompanyIdeas[1].id, idea1.id); // Middle score
      assert.equal(descResult.listCompanyIdeas[2].id, idea3.id); // Lowest score
    });
  });

  describe('List Company Ideas with Error Cases', () => {
    it('should handle invalid organization ID', async () => {
      try {
        await db.listCompanyIdea.getFilteredListCompanyIdeas({
          orgId: '',
          listCompanyId: context.testListCompanyId,
          listSourceIds: [],
          pageSize: 10,
          pageIndex: 0
        });
        assert.fail('Should have thrown an error');
      } catch (error) {
        assert.equal((error as Error).message, 'Invalid organization id.');
      }
    });

    it('should handle invalid list company ID', async () => {
      try {
        await db.listCompanyIdea.getFilteredListCompanyIdeas({
          orgId: context.auth.oid,
          listCompanyId: '',
          listSourceIds: [],
          pageSize: 10,
          pageIndex: 0
        });
        assert.fail('Should have thrown an error');
      } catch (error) {
        assert.equal((error as Error).message, 'Invalid list company id');
      }
    });

    it('should handle empty source IDs', async () => {
      const result = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [],
        pageSize: 10,
        pageIndex: 0
      });

      assert.isFalse(result.hasRecords);
      assert.equal(result.listCompanyIdeas.length, 0);
      assert.equal(result.totalRecords, 0);
      assert.equal(result.totalPages, 0);
    });
  });

  describe('List Company Ideas with Multiple Sources', () => {
    it('should return ideas from multiple sources', async () => {
      // Create multiple test sources
      const source1 = await createTestSource(context.testListCompanyId, context.intentionId);
      const source2 = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create test ideas
      const idea1 = await createTestIdea(context, `Company A ${uuid()}`, 'www.company-a.com');
      const idea2 = await createTestIdea(context, `Company B ${uuid()}`, 'www.company-b.com');

      // Create mappings for different sources
      await createTestMappings([idea1.id], source1.id, context.testListCompanyId);
      await createTestMappings([idea2.id], source2.id, context.testListCompanyId);

      // Get ideas from both sources
      const result = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source1.id, source2.id],
        pageSize: 10,
        pageIndex: 0
      });

      assert.isTrue(result.hasRecords);
      assert.equal(result.listCompanyIdeas.length, 2);
      assert.equal(result.totalRecords, 2);
      assert.equal(result.totalPages, 1);

      // Verify both ideas are returned
      const ideaIds = result.listCompanyIdeas.map((idea) => idea.id);
      assert.include(ideaIds, idea1.id);
      assert.include(ideaIds, idea2.id);
    });
  });

  describe('List Company Ideas with Combined Filters and Sorting', () => {
    it('should apply filters and sorting together', async () => {
      // Create test source
      const source = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create test ideas with different statuses and names
      const idea1 = await createTestIdea(context, `Zebra Company ${uuid()}`, 'www.zebra.com');
      const idea2 = await createTestIdea(context, `Apple Company ${uuid()}`, null);
      const idea3 = await createTestIdea(context, `Banana Company ${uuid()}`, 'www.banana.com');

      // Create source idea mappings
      await createTestMappings([idea1.id, idea2.id, idea3.id], source.id, context.testListCompanyId);

      // Update shortlist status
      await db.listCompanyIdeaMapping.update({
        where: {
          listCompanyIdeaId_listCompanyId: {
            listCompanyIdeaId: idea1.id,
            listCompanyId: context.testListCompanyId
          }
        },
        data: { inShortlist: true }
      });

      // Test combined filters and sorting
      const result = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 10,
        pageIndex: 0,
        text: 'Company',
        inShortlist: ['true'],
        recordStatus: [ICompanyIdeaStatus.Idea],
        sortKey: 'companyName',
        sortOrder: SortOrder.ASC
      });

      assert.isTrue(result.hasRecords);
      assert.equal(result.listCompanyIdeas.length, 1);
      assert.equal(result.totalRecords, 1);
      assert.equal(result.totalPages, 1);
      assert.equal(result.listCompanyIdeas[0].id, idea1.id);
    });
  });

  describe('List Company Ideas with Pagination Edge Cases', () => {
    it('should handle empty page', async () => {
      // Create test source
      const source = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create test ideas
      const idea1 = await createTestIdea(context, `Company A ${uuid()}`, 'www.company-a.com');
      const idea2 = await createTestIdea(context, `Company B ${uuid()}`, 'www.company-b.com');

      // Create source idea mappings
      await createTestMappings([idea1.id, idea2.id], source.id, context.testListCompanyId);

      // Request page beyond available data
      const result = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 1,
        pageIndex: 2
      });

      assert.isTrue(result.hasRecords);
      assert.equal(result.listCompanyIdeas.length, 0);
      assert.equal(result.totalRecords, 2);
      assert.equal(result.totalPages, 2);
    });

    it('should handle page size larger than total records', async () => {
      // Create test source
      const source = await createTestSource(context.testListCompanyId, context.intentionId);

      // Create test idea
      const idea = await createTestIdea(context, `Company A ${uuid()}`, 'www.company-a.com');

      // Create source idea mapping
      await createTestMappings([idea.id], source.id, context.testListCompanyId);

      // Request with large page size
      const result = await db.listCompanyIdea.getFilteredListCompanyIdeas({
        orgId: context.auth.oid,
        listCompanyId: context.testListCompanyId,
        listSourceIds: [source.id],
        pageSize: 100,
        pageIndex: 0
      });

      assert.isTrue(result.hasRecords);
      assert.equal(result.listCompanyIdeas.length, 1);
      assert.equal(result.totalRecords, 1);
      assert.equal(result.totalPages, 1);
    });
  });
});
