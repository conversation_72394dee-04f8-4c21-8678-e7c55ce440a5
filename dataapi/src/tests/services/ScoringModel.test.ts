import { assert } from 'chai';
import ScoringModelService from '../../services/ScoringModel';
import { db } from '../../database';
import { FieldDataType, ScoringFieldSource, ScoringOperator, EntityType, ExecutionStatus, ExecutionType, QueueTaskType } from '@prisma/client';
import { TestDBManager } from '../helpers/DBHelpers';
import { mockAuth } from '../helpers/ModelData';
import { v7 as uuid } from 'uuid';
import sinon from 'sinon';
import { NotificationService } from '../../services';

describe('ScoringModel', () => {
  let scoringModel: ScoringModelService;
  let dbManager: TestDBManager;
  let auth: typeof mockAuth;
  let testListCompanyId: string;
  let testEnrichmentId: string;

  before(async () => {
    auth = { ...mockAuth };
    dbManager = new TestDBManager(auth);
    testListCompanyId = await dbManager.createListData();
    const enrichment = await db.enrichment.findFirst({
      where: { orgId: auth.oid }
    });
    testEnrichmentId = enrichment?.id || '';
  });

  after(async function () {
    this.timeout(30000); // Increase timeout to 30 seconds for cleanup
    await dbManager.cleanupTestData();
  });

  beforeEach(() => {
    scoringModel = new ScoringModelService({ auth });
  });

  describe('Validation', () => {
    describe('Rule Set Weights', () => {
      it('should validate that rule set weights sum to 100', async () => {
        const params = createValidScoringModelParams(testListCompanyId);
        const result = await scoringModel.createScoringModel(params);
        assert.exists(result.id);
      });

      it('should reject rule sets with weights not summing to 100', async () => {
        const params = createInvalidWeightParams(testListCompanyId);
        try {
          await scoringModel.createScoringModel(params);
          assert.fail('Should have thrown an error');
        } catch (error: unknown) {
          assert.instanceOf(error, Error);
          if (error instanceof Error) {
            assert.include(error.message, 'Individual rule set weights must be between 0 and 100');
          }
        }
      });
    });

    describe('Rule Set Requirements', () => {
      it('should reject empty rule sets', async () => {
        const params = createEmptyRuleSetsParams(testListCompanyId);
        try {
          await scoringModel.createScoringModel(params);
          assert.fail('Should have thrown an error');
        } catch (error: unknown) {
          assert.instanceOf(error, Error);
          if (error instanceof Error) {
            assert.include(error.message, 'At least one rule set is required');
          }
        }
      });
    });

    describe('Score Ranges', () => {
      it('should validate score ranges', async () => {
        const params = createInvalidScoreParams(testListCompanyId);
        try {
          await scoringModel.createScoringModel(params);
          assert.fail('Should have thrown an error');
        } catch (error: unknown) {
          assert.instanceOf(error, Error);
          if (error instanceof Error) {
            assert.include(error.message, 'Scores must be between 0 and 10');
          }
        }
      });
    });

    describe('Field Validation', () => {
      it('should validate field existence and types', async () => {
        const params = createInvalidFieldParams(testListCompanyId);
        try {
          await scoringModel.createScoringModel(params);
          assert.fail('Should have thrown an error');
        } catch (error: unknown) {
          assert.instanceOf(error, Error);
          if (error instanceof Error) {
            assert.include(error.message, 'Invalid mandatory field');
          }
        }
      });

      it('should handle different field types correctly', async () => {
        const params = createDifferentFieldTypesParams(testListCompanyId);
        const result = await scoringModel.createScoringModel(params);
        assert.exists(result.id);
      });
    });
  });

  describe('Data Type Validation', () => {
    it('should validate numeric field conditions', async () => {
      const params = {
        name: 'Numeric Field Test',
        description: 'Testing numeric field validation',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Numeric Rules',
            weight: 100,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.enrichment,
              fieldType: FieldDataType.int,
              fieldPath: 'revenue',
              enrichmentId: testEnrichmentId
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: '1000' },
                score: 10
              },
              {
                operator: ScoringOperator.between,
                value: { primary: '1000', secondary: '2000' },
                score: 8
              }
            ]
          })
        ]
      };

      const result = await scoringModel.createScoringModel(params);
      assert.exists(result.id);
    });

    it('should reject invalid numeric values', async () => {
      const params = {
        name: 'Invalid Numeric Test',
        description: 'Testing invalid numeric values',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Invalid Numeric Rules',
            weight: 100,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.enrichment,
              fieldType: FieldDataType.int,
              fieldPath: 'revenue',
              enrichmentId: testEnrichmentId
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'not-a-number' },
                score: 10
              }
            ]
          })
        ]
      };

      try {
        await scoringModel.createScoringModel(params);
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'is not a valid number');
        }
      }
    });

    it('should validate boolean field conditions', async () => {
      const params = {
        name: 'Boolean Field Test',
        description: 'Testing boolean field validation',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Boolean Rules',
            weight: 100,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.enrichment,
              fieldType: FieldDataType.boolean,
              fieldPath: 'isPublic',
              enrichmentId: testEnrichmentId
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'true' },
                score: 10
              }
            ]
          })
        ]
      };

      const result = await scoringModel.createScoringModel(params);
      assert.exists(result.id);
    });

    it('should reject invalid boolean values', async () => {
      const params = {
        name: 'Invalid Boolean Test',
        description: 'Testing invalid boolean values',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Invalid Boolean Rules',
            weight: 100,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.enrichment,
              fieldType: FieldDataType.boolean,
              fieldPath: 'isPublic',
              enrichmentId: testEnrichmentId
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'not-a-boolean' },
                score: 10
              }
            ]
          })
        ]
      };

      try {
        await scoringModel.createScoringModel(params);
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'is not a valid boolean');
        }
      }
    });

    it('should validate string field conditions', async () => {
      const params = {
        name: 'String Field Test',
        description: 'Testing string field validation',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'String Rules',
            weight: 100,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Test Company' },
                score: 10
              },
              {
                operator: ScoringOperator.contain,
                value: { primary: 'Test' },
                score: 8
              }
            ]
          })
        ]
      };

      const result = await scoringModel.createScoringModel(params);
      assert.exists(result.id);
    });

    it('should reject unsupported operators for field type', async () => {
      const params = {
        name: 'Unsupported Operator Test',
        description: 'Testing unsupported operators',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Invalid Operator Rules',
            weight: 100,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.enrichment,
              fieldType: FieldDataType.boolean,
              fieldPath: 'isPublic',
              enrichmentId: testEnrichmentId
            },
            rules: [
              {
                operator: ScoringOperator.contain, // contain is not supported for boolean
                value: { primary: 'true' },
                score: 10
              }
            ]
          })
        ]
      };

      try {
        await scoringModel.createScoringModel(params);
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'is not supported for field type');
        }
      }
    });

    it('should validate between operator values', async () => {
      const params = {
        name: 'Between Operator Test',
        description: 'Testing between operator validation',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Between Rules',
            weight: 100,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.enrichment,
              fieldType: FieldDataType.int,
              fieldPath: 'revenue',
              enrichmentId: testEnrichmentId
            },
            rules: [
              {
                operator: ScoringOperator.between,
                value: { primary: '1000', secondary: '2000' },
                score: 10
              }
            ]
          })
        ]
      };

      const result = await scoringModel.createScoringModel(params);
      assert.exists(result.id);
    });

    it('should reject invalid between operator values', async () => {
      const params = {
        name: 'Invalid Between Test',
        description: 'Testing invalid between values',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Invalid Between Rules',
            weight: 100,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.enrichment,
              fieldType: FieldDataType.int,
              fieldPath: 'revenue',
              enrichmentId: testEnrichmentId
            },
            rules: [
              {
                operator: ScoringOperator.between,
                value: { primary: '1000', secondary: 'not-a-number' },
                score: 10
              }
            ]
          })
        ]
      };

      try {
        await scoringModel.createScoringModel(params);
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'is not a valid number');
        }
      }
    });
  });

  describe('Create Scoring Model', () => {
    it('should create and list scoring models', async () => {
      const params = createValidScoringModelParams(testListCompanyId);
      const created = await scoringModel.createScoringModel(params);
      assert.exists(created.id);

      const models = await scoringModel.listScoringModels({ listCompanyId: params.listCompanyId });
      assert.isArray(models);
      const foundModel = models.find((model) => model.id === created.id);
      assert.exists(foundModel?.id);
    });

    it('should handle multiple rules within a rule set', async () => {
      const params = {
        name: 'Multi-Rule Test Model',
        description: 'Testing multiple rules in a rule set',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Multi-Rule Set',
            weight: 100,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Test Company' },
                score: 10
              },
              {
                operator: ScoringOperator.not_equals,
                value: { primary: 'Wrong Company' },
                score: 8
              },
              {
                operator: ScoringOperator.contain,
                value: { primary: 'Test' },
                score: 6
              }
            ]
          })
        ]
      };

      const result = await scoringModel.createScoringModel(params);
      assert.exists(result.id);
    });

    it('should handle numeric field comparisons', async () => {
      const params = {
        name: 'Numeric Field Test Model',
        description: 'Testing numeric field comparisons',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Numeric Rules',
            weight: 100,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.enrichment,
              fieldType: FieldDataType.int,
              fieldPath: 'revenue',
              enrichmentId: testEnrichmentId
            },
            rules: [
              {
                operator: ScoringOperator.greater_than,
                value: { primary: '1000000' },
                score: 10
              },
              {
                operator: ScoringOperator.less_than,
                value: { primary: '500000' },
                score: 3
              }
            ]
          })
        ]
      };

      const result = await scoringModel.createScoringModel(params);
      assert.exists(result.id);
    });

    it('should handle boolean field comparisons', async () => {
      const params = {
        name: 'Boolean Field Test Model',
        description: 'Testing boolean field comparisons',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Boolean Rules',
            weight: 100,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.enrichment,
              fieldType: FieldDataType.boolean,
              fieldPath: 'isPublic',
              enrichmentId: testEnrichmentId
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'true' },
                score: 8
              },
              {
                operator: ScoringOperator.equals,
                value: { primary: 'false' },
                score: 2
              }
            ]
          })
        ]
      };

      const result = await scoringModel.createScoringModel(params);
      assert.exists(result.id);
    });

    it('should handle multiple rule sets with different weights', async () => {
      const params = {
        name: 'Multi-RuleSet Test Model',
        description: 'Testing multiple rule sets with different weights',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Company Name Rules',
            weight: 40,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Test Company' },
                score: 10
              }
            ]
          }),
          createTestRuleSet({
            label: 'Website Rules',
            weight: 30,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'website'
            },
            rules: [
              {
                operator: ScoringOperator.contain,
                value: { primary: 'example.com' },
                score: 10
              }
            ]
          }),
          createTestRuleSet({
            label: 'Revenue Rules',
            weight: 30,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.enrichment,
              fieldType: FieldDataType.int,
              fieldPath: 'revenue',
              enrichmentId: testEnrichmentId
            },
            rules: [
              {
                operator: ScoringOperator.greater_than,
                value: { primary: '1000000' },
                score: 10
              }
            ]
          })
        ]
      };

      const result = await scoringModel.createScoringModel(params);
      assert.exists(result.id);
    });

    it('should handle edge cases for scores and weights', async () => {
      const params = {
        name: 'Edge Cases Test Model',
        description: 'Testing edge cases for scores and weights',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Min Score',
            weight: 50,
            defaultScore: 0,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Test Company' },
                score: 0
              }
            ]
          }),
          createTestRuleSet({
            label: 'Max Score',
            weight: 50,
            defaultScore: 10,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'website'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'www.example.com' },
                score: 10
              }
            ]
          })
        ]
      };

      const result = await scoringModel.createScoringModel(params);
      assert.exists(result.id);
    });
  });

  describe('Get Single Scoring Model', () => {
    it('should get a single scoring model', async () => {
      const params = createValidScoringModelParams(testListCompanyId);
      const created = await scoringModel.createScoringModel(params);
      assert.exists(created.id);

      const retrieved = await scoringModel.getScoringModel(created.id);
      if (!retrieved) {
        assert.fail('Retrieved model should not be null');
        return;
      }
      assert.equal(retrieved.name, params.name);
      assert.equal(retrieved.description, params.description);
      assert.equal(retrieved.ruleSets?.length, params.ruleSets.length);
    });

    it('should throw error for invalid UUID', async () => {
      try {
        await scoringModel.getScoringModel('invalid-uuid');
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'Invalid scoring model ID');
        }
      }
    });

    it('should throw error for non-existent model', async () => {
      try {
        await scoringModel.getScoringModel('00000000-0000-0000-0000-000000000000');
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'Scoring model not found');
        }
      }
    });
  });

  describe('Update Scoring Model', () => {
    it('should update a scoring model', async () => {
      const params = createValidScoringModelParams(testListCompanyId);
      const created = await scoringModel.createScoringModel(params);
      assert.exists(created.id);

      const updateParams = {
        name: 'Updated Model Name',
        description: 'Updated Description',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Updated Rule Set',
            weight: 60,
            defaultScore: 6,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Updated Company' },
                score: 8
              }
            ]
          })
        ]
      };

      const updated = await scoringModel.updateScoringModel(created.id, updateParams);
      assert.exists(updated.id);
      assert.equal(updated.name, updateParams.name);
      assert.equal(updated.description, updateParams.description);

      const retrieved = await scoringModel.getScoringModel(created.id);
      if (!retrieved || !retrieved.ruleSets || retrieved.ruleSets.length === 0) {
        assert.fail('Retrieved model should have rule sets');
        return;
      }
      assert.equal(retrieved.ruleSets[0].label, updateParams.ruleSets[0].label);
      assert.equal(retrieved.ruleSets[0].weight, updateParams.ruleSets[0].weight);
      assert.equal(retrieved.ruleSets[0].defaultScore, updateParams.ruleSets[0].defaultScore);
      assert.equal(retrieved.ruleSets[0].rules[0].value.primary, updateParams.ruleSets[0].rules[0].value.primary);
      assert.equal(retrieved.ruleSets[0].rules[0].score, updateParams.ruleSets[0].rules[0].score);
    });

    it('should throw error for data type validation failure for int', async () => {
      const params = createValidScoringModelParams(testListCompanyId);
      const created = await scoringModel.createScoringModel(params);
      assert.exists(created.id);

      const updateParams = {
        name: 'Updated Model Name',
        description: 'Updated Description',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Updated Rule Set',
            weight: 60,
            defaultScore: 6,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.int,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Updated Company' },
                score: 8
              }
            ]
          })
        ]
      };

      try {
        await scoringModel.updateScoringModel(created.id, updateParams);
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'Value Updated Company is not a valid');
        }
      }
    });

    it('should throw error for data type validation failure for boolean', async () => {
      const params = createValidScoringModelParams(testListCompanyId);
      const created = await scoringModel.createScoringModel(params);
      assert.exists(created.id);

      const updateParams = {
        name: 'Updated Model Name',
        description: 'Updated Description',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Updated Rule Set',
            weight: 60,
            defaultScore: 6,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.boolean,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Updated Company' },
                score: 8
              }
            ]
          })
        ]
      };

      try {
        await scoringModel.updateScoringModel(created.id, updateParams);
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'Value Updated Company is not a valid boolean');
        }
      }
    });

    it('should add new rule set when updating', async () => {
      const params = createValidScoringModelParams(testListCompanyId);
      const created = await scoringModel.createScoringModel(params);
      assert.exists(created.id);

      const updateParams = {
        name: 'Updated Model Name',
        description: 'Updated Description',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Updated Rule Set',
            weight: 60,
            defaultScore: 6,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Updated Company' },
                score: 8
              }
            ]
          }),
          createTestRuleSet({
            label: 'New Rule Set',
            weight: 40,
            defaultScore: 5,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'website'
            },
            rules: [
              {
                operator: ScoringOperator.contain,
                value: { primary: 'example.com' },
                score: 10
              }
            ]
          })
        ]
      };

      const updated = await scoringModel.updateScoringModel(created.id, updateParams);
      assert.exists(updated.id);

      const retrieved = await scoringModel.getScoringModel(created.id);
      assert.equal(retrieved?.ruleSets?.length, 2);
      assert.equal(retrieved?.ruleSets?.[1]?.label, 'New Rule Set');
      assert.equal(retrieved?.ruleSets?.[1]?.weight, 40);
      assert.equal(retrieved?.ruleSets?.[1]?.rules?.[0]?.value.primary, 'example.com');
    });

    it('should remove rule set when not included in update', async () => {
      const params = createValidScoringModelParams(testListCompanyId);
      const created = await scoringModel.createScoringModel(params);
      assert.exists(created.id);

      const updateParams = {
        name: 'Updated Model Name',
        description: 'Updated Description',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Updated Rule Set',
            weight: 100,
            defaultScore: 6,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Updated Company' },
                score: 8
              }
            ]
          })
        ]
      };

      const updated = await scoringModel.updateScoringModel(created.id, updateParams);
      assert.exists(updated.id);

      const retrieved = await scoringModel.getScoringModel(created.id);
      assert.equal(retrieved?.ruleSets?.length, 1);
    });

    it('should throw error for invalid UUID', async () => {
      try {
        await scoringModel.updateScoringModel('invalid-uuid', createValidScoringModelParams(testListCompanyId));
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'Invalid scoring model ID');
        }
      }
    });

    it('should throw error for non-existent model', async () => {
      try {
        await scoringModel.updateScoringModel('00000000-0000-0000-0000-000000000000', createValidScoringModelParams(testListCompanyId));
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'Scoring model not found');
        }
      }
    });

    it('should validate rule set weights', async () => {
      const params = createValidScoringModelParams(testListCompanyId);
      const created = await scoringModel.createScoringModel(params);
      assert.exists(created.id);

      const updateParams = {
        name: 'Updated Model Name',
        description: 'Updated Description',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Updated Rule Set',
            weight: 150,
            defaultScore: 6,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Updated Company' },
                score: 8
              }
            ]
          })
        ]
      };

      try {
        await scoringModel.updateScoringModel(created.id, updateParams);
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'Individual rule set weights must be between 0 and 100');
        }
      }
    });

    it('should validate scores', async () => {
      const params = createValidScoringModelParams(testListCompanyId);
      const created = await scoringModel.createScoringModel(params);
      assert.exists(created.id);

      const updateParams = {
        name: 'Updated Model Name',
        description: 'Updated Description',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Updated Rule Set',
            weight: 100,
            defaultScore: 15,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'companyName'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Updated Company' },
                score: 8
              }
            ]
          })
        ]
      };

      try {
        await scoringModel.updateScoringModel(created.id, updateParams);
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'Scores must be between 0 and 10');
        }
      }
    });

    it('should validate field data', async () => {
      const params = createValidScoringModelParams(testListCompanyId);
      const created = await scoringModel.createScoringModel(params);
      assert.exists(created.id);

      const updateParams = {
        name: 'Updated Model Name',
        description: 'Updated Description',
        listCompanyId: testListCompanyId,
        ruleSets: [
          createTestRuleSet({
            label: 'Updated Rule Set',
            weight: 100,
            defaultScore: 6,
            fieldData: {
              sourceType: ScoringFieldSource.mandatory,
              fieldType: FieldDataType.string,
              fieldPath: 'invalidField'
            },
            rules: [
              {
                operator: ScoringOperator.equals,
                value: { primary: 'Updated Company' },
                score: 8
              }
            ]
          })
        ]
      };

      try {
        await scoringModel.updateScoringModel(created.id, updateParams);
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'Invalid mandatory field');
        }
      }
    });
  });

  describe('Delete Scoring Model', () => {
    it('should delete a scoring model', async () => {
      const params = createValidScoringModelParams(testListCompanyId);
      const created = await scoringModel.createScoringModel(params);
      assert.exists(created.id);

      await scoringModel.deleteScoringModel(created.id);

      const deletedModel = await db.scoringModel.findUnique({ where: { id: created.id } });
      assert.isNull(deletedModel);
    });

    it('should throw error for non-existent model', async () => {
      try {
        await scoringModel.deleteScoringModel('00000000-0000-0000-0000-000000000000');
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'Scoring model not found');
        }
      }
    });

    it('should throw error for invalid UUID', async () => {
      try {
        await scoringModel.deleteScoringModel('invalid-uuid');
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'Invalid scoring model ID');
        }
      }
    });
  });

  describe('Execute Scoring', () => {
    let notificationServiceStub: sinon.SinonStubbedInstance<NotificationService>;
    let testEntityIds: string[];

    beforeEach(async () => {
      // Create test entities
      testEntityIds = [uuid(), uuid()];
      await db.listCompanyIdea.create({
        data: {
          id: testEntityIds[0],
          companyName: 'Test Company',
          website: 'www.example.com',
          orgId: auth.oid
        }
      });

      // Create a mock notification service
      notificationServiceStub = sinon.createStubInstance(NotificationService);
      (notificationServiceStub.writeData as sinon.SinonStub) = sinon.stub().resolves(true);

      // Create service with notification
      scoringModel = new ScoringModelService({
        auth,
        notificationService: notificationServiceStub as unknown as NotificationService,
        writeResultsToSocket: true
      });
    });

    afterEach(async () => {
      // Clean up test entities
      await db.listCompanyIdea.deleteMany({
        where: { id: { in: testEntityIds } }
      });

      // Clean up executions and queue tasks
      await db.execution.deleteMany({
        where: { entityId: { in: testEntityIds } }
      });

      await db.queueTask.deleteMany({
        where: {
          data: {
            path: ['entityId'],
            equals: testEntityIds[0]
          }
        }
      });

      sinon.restore();
    });

    it('should create executions and queue tasks', async () => {
      // Create a valid scoring model first
      const params = createValidScoringModelParams(testListCompanyId);
      const createdModel = await scoringModel.createScoringModel(params);
      assert.exists(createdModel.id);

      // Execute scoring
      const executions = await scoringModel.executeScoring({
        scoringModelId: createdModel.id,
        entityType: EntityType.idea,
        entityIds: testEntityIds,
        options: { silent: false }
      });

      // Verify executions were created
      assert.isArray(executions);
      assert.equal(executions.length, testEntityIds.length);

      // Check first execution
      const firstExecution = executions[0];
      assert.property(firstExecution, 'entityId');
      assert.property(firstExecution, 'executionId');
      assert.isTrue(testEntityIds.includes(firstExecution.entityId));

      // Verify in database
      const dbExecution = await db.execution.findUnique({
        where: { id: firstExecution.executionId }
      });

      assert.exists(dbExecution);
      if (dbExecution) {
        assert.equal(dbExecution.type, ExecutionType.scoring);
        assert.equal(dbExecution.status, ExecutionStatus.pending);
        assert.equal(dbExecution.entityId, firstExecution.entityId);
      }

      // Verify queue task was created
      const queueTasks = await db.queueTask.findMany({
        where: {
          executionId: firstExecution.executionId,
          type: QueueTaskType.scoring,
          status: ExecutionStatus.pending,
          data: {
            path: ['entityId'],
            equals: firstExecution.entityId
          }
        }
      });

      assert.isAtLeast(queueTasks.length, 1);

      // Check that notification service was called
      assert.isTrue((notificationServiceStub.writeData as sinon.SinonStub).called);
    });

    it('should handle silent execution option', async () => {
      // Create a valid scoring model first
      const params = createValidScoringModelParams(testListCompanyId);
      const createdModel = await scoringModel.createScoringModel(params);

      // Execute scoring with silent option
      const executions = await scoringModel.executeScoring({
        scoringModelId: createdModel.id,
        entityType: EntityType.idea,
        entityIds: testEntityIds,
        options: { silent: true }
      });

      // Verify executions were created
      assert.isArray(executions);
      assert.equal(executions.length, testEntityIds.length);

      // Notification service should not be called for silent execution
      assert.isFalse((notificationServiceStub.writeData as sinon.SinonStub).called);
    });

    it('should throw error for invalid scoring model ID', async () => {
      try {
        await scoringModel.executeScoring({
          scoringModelId: 'invalid-uuid',
          entityType: EntityType.idea,
          entityIds: testEntityIds
        });
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'Invalid scoring model ID');
        }
      }
    });

    it('should throw error for non-existent scoring model', async () => {
      try {
        await scoringModel.executeScoring({
          scoringModelId: uuid(), // Random non-existent ID
          entityType: EntityType.idea,
          entityIds: testEntityIds
        });
        assert.fail('Should have thrown an error');
      } catch (error: unknown) {
        assert.instanceOf(error, Error);
        if (error instanceof Error) {
          assert.include(error.message, 'Scoring model not found');
        }
      }
    });

    describe('Get Scoring Execution', () => {
      it('should fetch execution status by ID', async () => {
        // Create a valid scoring model first
        const params = createValidScoringModelParams(testListCompanyId);
        const createdModel = await scoringModel.createScoringModel(params);

        // Execute scoring
        const executions = await scoringModel.executeScoring({
          scoringModelId: createdModel.id,
          entityType: EntityType.idea,
          entityIds: [testEntityIds[0]],
          options: { silent: true }
        });

        // Fetch execution
        const executionId = executions[0].executionId;
        const execution = await scoringModel.getScoringExecution(executionId);

        // Verify execution details
        assert.exists(execution);
        if (execution) {
          assert.equal(execution.id, executionId);
          assert.equal(execution.entityId, testEntityIds[0]);
          assert.equal(execution.type, ExecutionType.scoring);
          assert.equal(execution.status, ExecutionStatus.pending);
          assert.equal(execution.typeId, createdModel.id);
          assert.equal(execution.orgId, auth.oid);
          assert.equal(execution.createdById, auth.uid);
        }
      });

      it('should return null for non-existent execution', async () => {
        const execution = await scoringModel.getScoringExecution(uuid());
        assert.isNull(execution);
      });

      it('should throw error for invalid UUID', async () => {
        try {
          await scoringModel.getScoringExecution('invalid-uuid');
          assert.fail('Should have thrown an error');
        } catch (error: unknown) {
          assert.instanceOf(error, Error);
          if (error instanceof Error) {
            assert.include(error.message, 'Invalid execution id');
          }
        }
      });

      it('should include scoring results and queue tasks when available', async () => {
        // Create a valid scoring model first
        const params = createValidScoringModelParams(testListCompanyId);
        const createdModel = await scoringModel.createScoringModel(params);

        // Execute scoring
        const executions = await scoringModel.executeScoring({
          scoringModelId: createdModel.id,
          entityType: EntityType.idea,
          entityIds: [testEntityIds[0]],
          options: { silent: true }
        });

        // Fetch execution
        const executionId = executions[0].executionId;
        const execution = await scoringModel.getScoringExecution(executionId);

        // Verify execution includes related data
        assert.exists(execution);
        if (execution) {
          assert.property(execution, 'scoringResults');
          assert.property(execution, 'queueTasks');
          assert.isArray(execution.queueTasks);
          assert.isAtLeast(execution.queueTasks.length, 1);
          assert.equal(execution.queueTasks[0].type, QueueTaskType.scoring);
          assert.equal(execution.queueTasks[0].status, ExecutionStatus.pending);
        }
      });
    });
  });
});

const createValidScoringModelParams = (listCompanyId: string) => ({
  name: 'Test Scoring Model',
  description: 'Test Description',
  listCompanyId,
  ruleSets: [
    {
      id: '00000000-0000-0000-0000-000000000000',
      createdAt: new Date(),
      updatedAt: null,
      label: 'Test Rule Set',
      weight: 100,
      defaultScore: 5,
      scoringModelId: '00000000-0000-0000-0000-000000000000',
      fieldDataId: '00000000-0000-0000-0000-000000000000',
      fieldData: {
        id: '00000000-0000-0000-0000-000000000000',
        createdAt: new Date(),
        updatedAt: null,
        sourceType: ScoringFieldSource.mandatory,
        fieldType: FieldDataType.string,
        fieldPath: 'companyName',
        enrichmentId: null
      },
      rules: [
        {
          id: '00000000-0000-0000-0000-000000000000',
          createdAt: new Date(),
          updatedAt: null,
          operator: ScoringOperator.equals,
          value: { primary: 'Test Company' },
          score: 5,
          ruleSetId: '00000000-0000-0000-0000-000000000000'
        }
      ]
    }
  ]
});

const createInvalidWeightParams = (listCompanyId: string) => ({
  name: 'Test Scoring Model',
  description: 'Test Description',
  listCompanyId,
  ruleSets: [
    {
      id: '00000000-0000-0000-0000-000000000000',
      createdAt: new Date(),
      updatedAt: null,
      label: 'Test Rule Set',
      weight: 150, // Invalid weight
      defaultScore: 5,
      scoringModelId: '00000000-0000-0000-0000-000000000000',
      fieldDataId: '00000000-0000-0000-0000-000000000000',
      fieldData: {
        id: '00000000-0000-0000-0000-000000000000',
        createdAt: new Date(),
        updatedAt: null,
        sourceType: ScoringFieldSource.mandatory,
        fieldType: FieldDataType.string,
        fieldPath: 'companyName',
        enrichmentId: null
      },
      rules: [
        {
          id: '00000000-0000-0000-0000-000000000000',
          createdAt: new Date(),
          updatedAt: null,
          operator: ScoringOperator.equals,
          value: { primary: 'Test Company' },
          score: 5,
          ruleSetId: '00000000-0000-0000-0000-000000000000'
        }
      ]
    }
  ]
});

const createEmptyRuleSetsParams = (listCompanyId: string) => ({
  name: 'Test Scoring Model',
  description: 'Test Description',
  listCompanyId,
  ruleSets: []
});

const createInvalidScoreParams = (listCompanyId: string) => ({
  name: 'Test Scoring Model',
  description: 'Test Description',
  listCompanyId,
  ruleSets: [
    {
      id: '00000000-0000-0000-0000-000000000000',
      createdAt: new Date(),
      updatedAt: null,
      label: 'Test Rule Set',
      weight: 100,
      defaultScore: 15, // Invalid score
      scoringModelId: '00000000-0000-0000-0000-000000000000',
      fieldDataId: '00000000-0000-0000-0000-000000000000',
      fieldData: {
        id: '00000000-0000-0000-0000-000000000000',
        createdAt: new Date(),
        updatedAt: null,
        sourceType: ScoringFieldSource.mandatory,
        fieldType: FieldDataType.string,
        fieldPath: 'companyName',
        enrichmentId: null
      },
      rules: [
        {
          id: '00000000-0000-0000-0000-000000000000',
          createdAt: new Date(),
          updatedAt: null,
          operator: ScoringOperator.equals,
          value: { primary: 'Test Company' },
          score: 15, // Invalid score
          ruleSetId: '00000000-0000-0000-0000-000000000000'
        }
      ]
    }
  ]
});

const createInvalidFieldParams = (listCompanyId: string) => ({
  name: 'Test Scoring Model',
  description: 'Test Description',
  listCompanyId,
  ruleSets: [
    {
      id: '00000000-0000-0000-0000-000000000000',
      createdAt: new Date(),
      updatedAt: null,
      label: 'Test Rule Set',
      weight: 100,
      defaultScore: 5,
      scoringModelId: '00000000-0000-0000-0000-000000000000',
      fieldDataId: '00000000-0000-0000-0000-000000000000',
      fieldData: {
        id: '00000000-0000-0000-0000-000000000000',
        createdAt: new Date(),
        updatedAt: null,
        sourceType: ScoringFieldSource.mandatory,
        fieldType: FieldDataType.string,
        fieldPath: 'invalidField', // Invalid field path
        enrichmentId: null
      },
      rules: [
        {
          id: '00000000-0000-0000-0000-000000000000',
          createdAt: new Date(),
          updatedAt: null,
          operator: ScoringOperator.equals,
          value: { primary: 'Test Company' },
          score: 5,
          ruleSetId: '00000000-0000-0000-0000-000000000000'
        }
      ]
    }
  ]
});

const createDifferentFieldTypesParams = (listCompanyId: string) => ({
  name: 'Types Test Model',
  description: 'Testing different field types',
  listCompanyId,
  ruleSets: [
    {
      id: '00000000-0000-0000-0000-000000000001',
      createdAt: new Date(),
      updatedAt: null,
      label: 'Numeric Field',
      weight: 50,
      defaultScore: 5,
      scoringModelId: '00000000-0000-0000-0000-000000000000',
      fieldDataId: '00000000-0000-0000-0000-000000000001',
      fieldData: {
        id: '00000000-0000-0000-0000-000000000001',
        createdAt: new Date(),
        updatedAt: null,
        sourceType: ScoringFieldSource.mandatory,
        fieldType: FieldDataType.int,
        fieldPath: 'companyName',
        enrichmentId: null
      },
      rules: [
        {
          id: '00000000-0000-0000-0000-000000000001',
          createdAt: new Date(),
          updatedAt: null,
          operator: ScoringOperator.greater_than,
          value: { primary: '1000000' },
          score: 10,
          ruleSetId: '00000000-0000-0000-0000-000000000001'
        }
      ]
    },
    {
      id: '00000000-0000-0000-0000-000000000002',
      createdAt: new Date(),
      updatedAt: null,
      label: 'Boolean Field',
      weight: 50,
      defaultScore: 5,
      scoringModelId: '00000000-0000-0000-0000-000000000000',
      fieldDataId: '00000000-0000-0000-0000-000000000002',
      fieldData: {
        id: '00000000-0000-0000-0000-000000000002',
        createdAt: new Date(),
        updatedAt: null,
        sourceType: ScoringFieldSource.mandatory,
        fieldType: FieldDataType.boolean,
        fieldPath: 'website',
        enrichmentId: null
      },
      rules: [
        {
          id: '00000000-0000-0000-0000-000000000002',
          createdAt: new Date(),
          updatedAt: null,
          operator: ScoringOperator.equals,
          value: { primary: 'true' },
          score: 5,
          ruleSetId: '00000000-0000-0000-0000-000000000002'
        }
      ]
    }
  ]
});

// Helper function to create a valid rule set
const createTestRuleSet = (params: {
  label: string;
  weight: number;
  defaultScore: number;
  fieldData: {
    sourceType: ScoringFieldSource;
    fieldType: FieldDataType;
    fieldPath: string;
    enrichmentId?: string | null;
  };
  rules: Array<{
    operator: ScoringOperator;
    value: { primary: string; secondary?: string };
    score: number;
  }>;
}) => {
  const id = uuid();
  return {
    id,
    createdAt: new Date(),
    updatedAt: null,
    label: params.label,
    weight: params.weight,
    defaultScore: params.defaultScore,
    scoringModelId: uuid(),
    fieldDataId: uuid(),
    fieldData: {
      id: uuid(),
      createdAt: new Date(),
      updatedAt: null,
      sourceType: params.fieldData.sourceType,
      fieldType: params.fieldData.fieldType,
      fieldPath: params.fieldData.fieldPath,
      enrichmentId: params.fieldData.enrichmentId || null
    },
    rules: params.rules.map((rule) => ({
      id: uuid(),
      createdAt: new Date(),
      updatedAt: null,
      operator: rule.operator,
      value: rule.value,
      score: rule.score,
      ruleSetId: id
    }))
  };
};
