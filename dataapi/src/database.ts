import { logger } from './logger';
import process from 'node:process';
import { PrismaClient } from '@prisma/client';
import { PlayExtension, EntityExtension, EnrichmentExtension, ExecutionExtension, UserExtension, UserCalendarExtension, UserInvitationExtension, EmailMessageExtension, StatsExtension } from './models/';
import ListCompanyExtension from './models/ListCompany';
import ListCompanyIdeaExtension from './models/ListCompanyIdea';

// Get the appropriate database URL based on environment
const dbUrl = process.env.NODE_ENV === 'test' || process.env.TEST === 'true' ? process.env.TEST_DATABASE_URL : process.env.DATABASE_URL;

if (!dbUrl) {
  logger.error('No database URL found. Please set DATABASE_URL or TEST_DATABASE_URL');
  process.exit(1);
}

const db = new PrismaClient({
  datasources: {
    db: {
      url: dbUrl
    }
  }
})
  .$extends(PlayExtension)
  .$extends(EnrichmentExtension)
  .$extends(EntityExtension)
  .$extends(ExecutionExtension)
  .$extends(UserExtension)
  .$extends(UserCalendarExtension)
  .$extends(UserInvitationExtension)
  .$extends(EmailMessageExtension)
  .$extends(StatsExtension)
  .$extends(ListCompanyExtension)
  .$extends(ListCompanyIdeaExtension);

const connect = async () => {
  logger.info({ msg: 'Data API Connecting to DB...' });
  return db
    .$connect()
    .then(() => {
      logger.info({ msg: 'Data API service connected to DB.' });
    })
    .catch((error) => {
      logger.error({ msg: 'Failed to connect to DB', error });
      process.exit(1);
    });
};

const disconnect = async () => {
  logger.info({ msg: 'Data API disconnecting from DB...' });
  return db
    .$disconnect()
    .then(() => {
      logger.info({ msg: 'Data API service disconnected from DB.' });
    })
    .catch((error) => {
      logger.error({ msg: 'Failed to disconnect from DB', error });
      process.exit(1);
    });
};

export { db, connect, disconnect };
