import pinoHttp from 'pino-http';
import pino from 'pino';
import config from './config';
import fs from 'fs';
import path from 'path';

/**
 * In case you need pretty print log files dont add any dependency to logger
 * use jq, i.e:
 * tail -f file | jq -nR -c 'inputs|fromjson?' |jq 'with_entries( select(.key|contains("msg") ) )'
 * tail -f file | jq -nR -c 'inputs|fromjson?' |jq 'with_entries( select(.key|contains("req") | not ) )'
 * tail -f file | jq -nR -c 'inputs|fromjson?' |jq .
 */

// Mapping Pino levels to GCP Logging severity levels
const levelToSeverity = {
  trace: 'DEBUG',
  debug: 'DEBUG',
  info: 'INFO',
  warn: 'WARNING',
  error: 'ERROR',
  fatal: 'CRITICAL'
};

// Ensure logs directory exists
if (config.logdir) {
  const logDir = path.resolve(config.logdir);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
}

const streamsList: any[] = [
  {
    level: 'trace',
    stream: process.stdout
  }
];

// Add file stream if logdir is configured
if (config.logdir) {
  const logFilePath = path.join(config.logdir, 'dataapi.log');
  streamsList.push({
    level: 'trace',
    stream: fs.createWriteStream(logFilePath, { flags: 'a' })
  });
}

const streams = pino.multistream(streamsList);

const errorSerializer = (err: unknown) => {
  if (err instanceof Error) {
    return {
      message: err.message ? err.message.trim().split('\n') : undefined,
      stack: err.stack ? err.stack.split('\n') : undefined,
      data: { ...err }
    };
  }
  return err;
};

const logger = pino(
  {
    level: config.logLevel,
    timestamp: () => `,"time":"${new Date().toLocaleTimeString()}"`,
    formatters: {
      level(label) {
        return { severity: levelToSeverity[label] || 'DEFAULT' };
      }
    },
    serializers: { error: errorSerializer }
  },
  streams
);

const httpLogger = pinoHttp({
  logger: logger,
  stream: streams,
  autoLogging: {
    ignore: (req: any) => {
      return (req.url === '/healthcheck' && config.env === 'development') || (req.url === '/' && req.headers['user-agent'] === 'kube-probe/1.29' && req.headers['host'].startsWith('10.'));
    }
  },
  redact: {
    paths: ['req.headers.cookie'],
    remove: true
  },
  customSuccessMessage: function (req, res) {
    return `${res.statusCode} ${req.method} ${req.headers.host} ${req.url}`;
  },

  customErrorMessage: function (_req: any, res: any, _err: any) {
    return 'request errored with status code: ' + res.statusCode;
  },

  customReceivedMessage: function (req: any, _res: any) {
    return 'request received: ' + req.method + ' ' + req.url;
  }
});

export { logger, httpLogger };
