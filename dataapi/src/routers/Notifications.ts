import { Response, Router } from 'express';
import { UnauthorizedError } from '../lib/errors';
import { logger } from '../logger';
import { IAcqRequest } from '../types/Common';
import { FirebaseLibrary } from '../lib/notification/Firebase';
import { NotificationProvider, NotificationProviderType } from '../lib/notification/Notification';
import { NotificationService } from '../services';
import { INotificationTokenResponse } from '../types/Notification';
import { ServerError } from '../lib/errors';

export default () => {
  const router: Router = Router({ mergeParams: true });

  router.get('/token', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    logger.info({ msg: 'Generate firebase token request', auth });
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    try {
      const firebaseLib = new FirebaseLibrary({ auth });
      const notificationProvider = new NotificationProvider({
        auth,
        libraries: { firebase: firebaseLib },
        type: NotificationProviderType.Firebase
      });
      await notificationProvider.init();
      const service = new NotificationService({ auth, provider: notificationProvider });

      return service
        .createCustomToken()
        .then((tokenResponse: INotificationTokenResponse) => {
          return res.status(200).json(tokenResponse);
        })
        .catch((error) => {
          logger.error({ msg: 'Error generating token', error: error.message });
          return res.status(500).json({ error: 'Error generating token' });
        });
    } catch (err: any) {
      logger.error({ msg: err.message });
      res.status(503).json(new ServerError(err.message));
    }
  });

  return router;
};
