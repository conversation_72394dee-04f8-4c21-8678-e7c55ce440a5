export { default as OrganizationRouter } from './Organization';
export { default as BackOfficeRouter } from './BackOffice';
export { default as CrmRouter } from './Crm';
export { default as UserRouter } from './User';
export { default as ContactRouter } from './Contact';
export { default as CompanyRouter } from './Company';
export { default as PlayRouter } from './Play';
export { default as EnrichmentRouter } from './Enrichment';
export { default as EntityRouter } from './Entity';
export { default as TemplateVariableRouter } from './TemplateVariable';
export { default as EmailRouter } from './Email';
export { default as NotificationsRouter } from './Notifications';
export { default as IntegrationRouter } from './Integration';
export { default as ListCompanyRouter } from './ListCompany';
export { default as AuthRouter } from './Auth';
export { default as ActivityRouter } from './Activity';
export { default as ScoringModelRouter } from './ScoringModel';
