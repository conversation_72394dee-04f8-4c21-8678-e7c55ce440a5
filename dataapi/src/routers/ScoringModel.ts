import { Response, Router } from 'express';
import { BadRequestError, ServerError, UnauthorizedError } from '../lib/errors';
import { logger } from '../logger';
import { IAcqRequest } from '../types/Common';
import { validate as uuidValidate } from 'uuid';
import ScoringModelService from '../services/ScoringModel';
import { EntityType } from '@prisma/client';
import { FirebaseLibrary } from '../lib/notification/Firebase';
import { NotificationProvider, NotificationProviderType } from '../lib/notification/Notification';
import { ListService, NotificationService } from '../services';

export default () => {
  const router = Router({ mergeParams: true });

  router.post('/', async (req: IAcqRequest, res: Response) => {
    const { auth, body } = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Create scoring model request', uid: auth.uid });

    const { name, description, listCompanyId, ruleSets } = body;

    // Validate required fields
    if (!name || !listCompanyId || !ruleSets || !Array.isArray(ruleSets)) {
      return res.status(400).json(new BadRequestError('Missing required fields'));
    }

    // Validate listCompanyId is a valid UUID
    if (!uuidValidate(listCompanyId)) {
      return res.status(400).json(new BadRequestError('Invalid listCompanyId'));
    }

    const scoringModelService = new ScoringModelService({ auth });

    try {
      const scoringModel = await scoringModelService.createScoringModel({
        name,
        description,
        listCompanyId,
        ruleSets
      });

      logger.info({ msg: 'Created scoring model', scoringModel });
      return res.status(201).json(scoringModel);
    } catch (error) {
      logger.error({ msg: 'Create scoring model error', error });
      if (error instanceof BadRequestError) {
        return res.status(400).json(error);
      }
      return res.status(500).json(new ServerError('Unable to create scoring model'));
    }
  });

  router.get('/', async (req: IAcqRequest, res: Response) => {
    const { auth, query } = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'List scoring models request', uid: auth.uid });

    const { listCompanyId } = query;

    // Validate listCompanyId is provided and is a valid UUID
    if (!listCompanyId || typeof listCompanyId !== 'string' || !uuidValidate(listCompanyId)) {
      return res.status(400).json(new BadRequestError('Invalid listCompanyId'));
    }

    const scoringModelService = new ScoringModelService({ auth });

    try {
      const scoringModels = await scoringModelService.listScoringModels({
        listCompanyId
      });

      logger.info({ msg: 'Listed scoring models', count: scoringModels.length });
      return res.status(200).json(scoringModels);
    } catch (error) {
      logger.error({ msg: 'List scoring models error', error });
      return res.status(500).json(new ServerError('Unable to list scoring models'));
    }
  });

  router.get('/:id', async (req: IAcqRequest, res: Response) => {
    const { auth, params } = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Get scoring model request', uid: auth.uid, id: params.id });

    const { id } = params;

    // Validate ID is a valid UUID
    if (!uuidValidate(id)) {
      return res.status(400).json(new BadRequestError('Invalid scoring model ID'));
    }

    const scoringModelService = new ScoringModelService({ auth });

    try {
      const scoringModel = await scoringModelService.getScoringModel(id);
      logger.info({ msg: 'Retrieved scoring model', id });
      return res.status(200).json(scoringModel);
    } catch (error) {
      logger.error({ msg: 'Get scoring model error', error });
      if (error instanceof Error) {
        if (error.message === 'Scoring model not found') {
          return res.status(404).json(new BadRequestError(error.message));
        }
        return res.status(400).json(new BadRequestError(error.message));
      }
      return res.status(500).json(new ServerError('Unable to get scoring model'));
    }
  });

  router.put('/:id', async (req: IAcqRequest, res: Response) => {
    const { auth, params, body } = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Update scoring model request', uid: auth.uid, id: params.id });

    const { id } = params;
    const { name, description, ruleSets, listCompanyId } = body;

    // Validate ID is a valid UUID
    if (!uuidValidate(id)) {
      return res.status(400).json(new BadRequestError('Invalid scoring model ID'));
    }

    // Validate required fields
    if (!name || !ruleSets || !Array.isArray(ruleSets) || !listCompanyId) {
      return res.status(400).json(new BadRequestError('Missing required fields'));
    }

    const scoringModelService = new ScoringModelService({ auth });

    try {
      const scoringModel = await scoringModelService.updateScoringModel(id, {
        name,
        description,
        ruleSets,
        listCompanyId
      });
      logger.info({ msg: 'Updated scoring model', id });
      return res.status(200).json(scoringModel);
    } catch (error) {
      logger.error({ msg: 'Update scoring model error', error });
      if (error instanceof Error) {
        if (error.message === 'Scoring model not found') {
          return res.status(404).json(new BadRequestError(error.message));
        }
        return res.status(400).json(new BadRequestError(error.message));
      }
      return res.status(500).json(new ServerError('Unable to update scoring model'));
    }
  });

  router.delete('/:id', async (req: IAcqRequest, res: Response) => {
    const { auth, params } = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Delete scoring model request', uid: auth.uid, id: params.id });

    const { id } = params;

    // Validate ID is a valid UUID
    if (!uuidValidate(id)) {
      return res.status(400).json(new BadRequestError('Invalid scoring model ID'));
    }

    const scoringModelService = new ScoringModelService({ auth });

    try {
      await scoringModelService.deleteScoringModel(id);
      logger.info({ msg: 'Deleted scoring model', id });
      return res.status(204).json({ message: 'Scoring model deleted successfully' });
    } catch (error) {
      logger.error({ msg: 'Delete scoring model error', error });
      if (error instanceof Error) {
        if (error.message === 'Scoring model not found') {
          return res.status(404).json(new BadRequestError(error.message));
        }
        if (error.message === 'Invalid scoring model ID') {
          return res.status(400).json(new BadRequestError(error.message));
        }
        return res.status(500).json(new ServerError('Unable to delete scoring model'));
      }
      return res.status(500).json(new ServerError('Unable to delete scoring model'));
    }
  });

  router.post('/execute', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { scoringModelId, entityType, options, ideasFilter }
    }: IAcqRequest = req;
    let { entityIds: entityIds }: IAcqRequest['body'] = req.body;
    logger.debug({ msg: 'Execute scoring request', scoringModelId, entityType, entityIds, options });
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    if (!uuidValidate(scoringModelId)) {
      return res.status(400).json(new BadRequestError('Invalid scoring model id'));
    }

    if (ideasFilter && Object.keys(ideasFilter).length > 0) {
      const listService: ListService = new ListService({ auth });
      entityIds = await listService.getListCompanyIdeaIdsByFilters(ideasFilter);
    }

    if (!Array.isArray(entityIds) || entityIds.length === 0) {
      return res.status(400).json(new BadRequestError('Invalid entity ids'));
    }

    for (let i = 0; i < entityIds.length; i++) {
      if (!uuidValidate(entityIds[i])) {
        return res.status(400).json(new BadRequestError('Invalid entity id'));
      }
    }

    // Check if the entityType is a valid EntityType enum value
    const validEntityTypes = Object.values(EntityType);
    if (!validEntityTypes.includes(entityType as EntityType)) {
      return res.status(400).json(new BadRequestError('Invalid entity type'));
    }

    try {
      const firebaseLib = new FirebaseLibrary({ auth });
      const notificationProvider = new NotificationProvider({
        auth,
        libraries: { firebase: firebaseLib },
        type: NotificationProviderType.Firebase
      });
      await notificationProvider.init();
      const notificationService = new NotificationService({ auth, provider: notificationProvider });
      const scoringModelService = new ScoringModelService({
        auth,
        notificationService,
        writeResultsToSocket: true
      });

      return scoringModelService
        .executeScoring({ scoringModelId, entityType: entityType as EntityType, entityIds, options })
        .then((executions: { entityId: string; executionId: string }[]) => {
          logger.info({ msg: 'Scoring execution queued.', executions });
          return res.json(executions);
        })
        .catch((err: Error) => {
          logger.error({ msg: 'Unable to execute scoring', err });
          return res.status(500).json(new ServerError('Unable to execute scoring'));
        });
    } catch (error) {
      logger.error({ msg: 'Execute scoring error', error });
      return res.status(500).json(new ServerError('Unable to execute scoring'));
    }
  });

  router.get('/execution/:executionId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { executionId }
    }: IAcqRequest = req;
    logger.info({ msg: 'Reading single scoring execution request' });

    if (!uuidValidate(executionId)) {
      return res.status(400).json(new BadRequestError('Invalid execution id'));
    }

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const scoringModelService = new ScoringModelService({ auth });
    return scoringModelService
      .getScoringExecution(executionId)
      .then((execution) => {
        if (!execution) {
          logger.info({ msg: 'No execution found' });
          return res.status(404).json(new BadRequestError('Execution not found'));
        }
        logger.info({ msg: 'Read scoring execution', execution });
        return res.json({ data: execution });
      })
      .catch((err) => {
        logger.error({ msg: 'Read single scoring execution error', err });
        return res.status(500).json(new ServerError('Unable to read scoring execution'));
      });
  });

  return router;
};
