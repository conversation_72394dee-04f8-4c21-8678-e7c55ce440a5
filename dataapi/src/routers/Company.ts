import { Router, Response } from 'express';
import { APIResponse, APIError } from '../lib';
import { logger } from '../logger';
import { NotFoundError, UnauthorizedError } from '../lib/errors';
import { CompanyService } from '../services';
import { IAcqRequest } from '../types/Common';
import { ICompanyFilters, ICompanyGetResults, IFlattenedMapping, IGetFilteredCompanyResult } from '../types/Company';
import { ICompany, ICreateCompanyMappingResponse, IOwner } from '../types/Entity';

export default () => {
  const router: Router = Router({ mergeParams: true });

  router.post('/get-companies', async (req: IAcqRequest, res: Response) => {
    const { auth, body: postBody }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Get companies request', postBody });

    const service: CompanyService = new CompanyService({ auth });
    const filters: ICompanyFilters = postBody as ICompanyFilters;

    return service
      .getFilteredCompanies(filters, false)
      .then((companies: IGetFilteredCompanyResult | null) => {
        if (!companies) {
          return res.status(404).json(new NotFoundError('Companies not found'));
        }
        return res.status(200).json(companies);
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get companies', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/details/:companyId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { companyId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.debug({ msg: 'Get company details request', companyId });

    const service: CompanyService = new CompanyService({ auth });

    return service
      .getCompanyDetailsById(companyId)
      .then((company) => {
        if (!company) {
          return res.status(404).json(new NotFoundError('Company not found'));
        }
        return res.status(200).json(company);
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get company', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/export', async (req: IAcqRequest, res: Response) => {
    const { auth, body: postBody }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service = new CompanyService({ auth });
    const filters = postBody;
    logger.info({ msg: 'Export companies request', postBody });
    try {
      // Get companies based on filters
      const companies = await service.getFilteredCompanies(filters, true);

      if (!companies || !companies.entityList) {
        return res.status(404).json(new NotFoundError('Companies not found'));
      }

      // Generate CSV data
      const csv = await service.prepareCompaniesCsvData(companies);

      // Send the CSV file as a response
      res.header('Content-Type', 'text/csv');
      res.attachment('companies.csv');
      res.send(csv);
    } catch (error) {
      return res.status(500).json(
        new APIError('Unable to export companies to CSV', {
          details: (error as Error).message
        })
      );
    }
  });

  router.get('/owners-list', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: CompanyService = new CompanyService({ auth });

    return service
      .getOwnerList()
      .then((owners: IOwner[] | null) => {
        if (!owners) {
          return res.status(404).json(new NotFoundError('Owners not found'));
        }
        return res.status(200).json(owners);
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get owners list', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/:id', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { id }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: CompanyService = new CompanyService({ auth });

    return service
      .getCompanyById(id)
      .then((company: ICompanyGetResults | null) => {
        if (!company) {
          return res.status(404).json(new NotFoundError('Company not found'));
        }
        return res.status(200).json(new APIResponse(company));
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get company', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/fields/:companyId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { companyId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: CompanyService = new CompanyService({ auth });

    return service
      .getFields(companyId)
      .then((result) => res.json(new APIResponse(result)))
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get company fields', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/mappings/:companyId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { companyId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: CompanyService = new CompanyService({ auth });

    return service
      .getMappings(companyId)
      .then((mappings: IFlattenedMapping[] | null) => {
        return res.json(new APIResponse(mappings || []));
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get company mappings', {
            details: err.toString()
          })
        );
      });
  });

  router.put('/set-key', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { contactId, companyId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: CompanyService = new CompanyService({ auth });

    return service
      .updateKeyContact(contactId, companyId)
      .then((result: boolean) => {
        if (!result) {
          return res.status(400).json(new NotFoundError('Set key company not found'));
        }
        return res.status(200).json(result);
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError('Failed to set key contact', {
            details: error.toString()
          })
        );
      });
  });

  router.post('/search', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { query }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: CompanyService = new CompanyService({ auth });

    return service
      .searchCompany({ query })
      .then((companies: ICompany[] | null) => {
        if (!companies) {
          return res.status(404).json(new NotFoundError('Search companies not found'));
        }
        return res.status(200).json(new APIResponse(companies));
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get companies', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/create-mapping', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { contactId, companyId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: CompanyService = new CompanyService({ auth });

    return service
      .createMapping(contactId, companyId)
      .then((result: ICreateCompanyMappingResponse | null) => {
        return res.status(201).json(new APIResponse(result));
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to create mapping', {
            details: err.toString()
          })
        );
      });
  });

  router.delete('/delete/:companyId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { companyId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    const service: CompanyService = new CompanyService({ auth });

    return service
      .removeCompany(companyId)
      .then((result: boolean) => {
        if (result) {
          return res.status(200).json(true);
        }
        return res.status(500).json(false);
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError('Failed to remove company', {
            details: error.toString()
          })
        );
      });
  });

  router.post('/remove-company', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    const service: CompanyService = new CompanyService({ auth });

    return service
      .removeCompany(body)
      .then((result: boolean) => {
        return res.status(201).json(new APIResponse(result));
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError('Failed to remove company', {
            details: error.toString()
          })
        );
      });
  });

  router.get('/get-enrichments-fields/:companyId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { companyId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: CompanyService = new CompanyService({ auth });

    return service
      .getEnirchmentsFields(companyId)
      .then((response) => res.json(new APIResponse(response)))
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get company fields', {
            details: err.toString()
          })
        );
      });
  });

  return router;
};
