import { Response, Router } from 'express';
import { BadRequestError, ConflictError, ServerError, UnauthorizedError } from '../lib/errors';
import { logger } from '../logger';
import EntityService from '../services/Entity';
import { IAcqRequest } from '../types/Common';
import { APIError, APIResponse } from '../lib';
import { Company, CompanyContactMapping, Contact, EntityType } from '@prisma/client';
import { GetErrorResponse } from '../lib/errors/GetErrorResponse';
import { IContactCompanyEntity, IFilterFields, IResponseStatus } from '../types/Entity';
import { ICreateContactParams } from '../types/Contact';
import { ICompanyCreateWithContactId } from '../types/Company';
import { IEntitySearchResult } from '../types/Entity';

export default () => {
  const router = Router({ mergeParams: true });

  router.get('/fields', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    logger.info({ msg: 'Get entities request', uid: auth?.uid });
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    const entityService: EntityService = new EntityService({ auth });

    return entityService
      .getAllFields()
      .then((fields) => {
        logger.info({ msg: 'Got entity list' });
        return res.status(200).json(fields);
      })
      .catch((err) => {
        logger.error({ msg: 'Get entity list error', err });
        return res.status(500).json(new ServerError('Unable to read entity fields'));
      });
  });

  router.put('/set-config', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest & { body: { fieldId: string; config: object } } = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    const entityService: EntityService = new EntityService({ auth });

    const { fieldId, config }: { fieldId: string; config: object } = body;

    return entityService
      .setFieldConfig({ fieldId, config })
      .then((result: boolean) => {
        return res.status(200).json(result);
      })
      .catch((err: Error) => {
        return res.status(500).json(new ServerError('Unable to set field config', { details: err.toString() }));
      });
  });

  router.post('/field', async (req: IAcqRequest, res: Response) => {
    const { auth, body: fieldData }: IAcqRequest = req;
    logger.info({ msg: 'Create field request', uid: auth?.uid });
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    const entityService: EntityService = new EntityService({ auth });

    const label = fieldData.label;
    const entityType = fieldData.entityType;
    const dataType = fieldData.dataType;

    return entityService
      .createField({ label, entityType, dataType })
      .then((field) => {
        logger.debug({ msg: 'Field created', orgId: auth.oid, field: label });
        return res.status(201).json(field);
      })
      .catch((err) => {
        logger.error({ msg: 'Create field error', err });
        return res.status(500).json(new ServerError(err.message));
      });
  });

  router.put('/company/field', async (req: IAcqRequest, res: Response) => {
    const { auth, body: fieldData }: IAcqRequest = req;
    logger.debug({ msg: 'Update field request', uid: auth?.uid });
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    const entityService: EntityService = new EntityService({ auth });

    const entityType = EntityType.company;
    const entityId = fieldData.entityId;
    const fieldId = fieldData.fieldId;
    const value = fieldData.value;

    return entityService
      .updateFieldData({ entityType, entityId, fieldId, value })
      .then((result: boolean) => {
        logger.debug({ msg: 'Field updated', orgId: auth.oid, fieldId, value });
        return res.status(200).json(result);
      })
      .catch((err) => {
        logger.error({ msg: 'Update field error', err });
        return res.status(500).json(new ServerError('Unable to update entity field'));
      });
  });

  router.put('/contact/field', async (req: IAcqRequest, res: Response) => {
    const { auth, body: fieldData }: IAcqRequest = req;
    logger.debug({ msg: 'Update field request', uid: auth?.uid });
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    const entityService: EntityService = new EntityService({ auth });

    const entityType = EntityType.contact;
    const entityId = fieldData.entityId;
    const fieldId = fieldData.fieldId;
    const value = fieldData.value;

    return entityService
      .updateFieldData({ entityType, entityId, fieldId, value })
      .then((result: boolean) => {
        logger.debug({ msg: 'Field updated', orgId: auth.oid, fieldId, value });
        return res.status(200).json(result);
      })
      .catch((err) => {
        logger.error({ msg: 'Update field error', err });
        return res.status(500).json(new ServerError('Unable to update entity field'));
      });
  });

  router.post('/contact', async (req: IAcqRequest, res: Response) => {
    const { auth, body: contactData }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: EntityService = new EntityService({ auth });

    return service
      .createContact(contactData as ICreateContactParams)
      .then((contact: Contact | null) => {
        if (!contact) {
          return GetErrorResponse(res, 'Unable to create new contact');
        }
        return res.status(201).json(new APIResponse(contact));
      })
      .catch((error) => GetErrorResponse(res, 'Unable to create new contact', error));
  });

  router.post('/company', async (req: IAcqRequest, res: Response) => {
    const { auth, body: companyData }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: EntityService = new EntityService({ auth });

    return service
      .createCompany(companyData as ICompanyCreateWithContactId)
      .then((company: Company | null) => {
        if (!company) {
          return GetErrorResponse(res, 'Unable to create new company');
        }
        return res.status(201).json(new APIResponse(company));
      })
      .catch((error) => GetErrorResponse(res, 'Unable to create new company', error));
  });

  router.put('/company', async (req: IAcqRequest, res: Response) => {
    const { auth, body: companyData }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: EntityService = new EntityService({ auth });

    return service
      .modifyCompany(companyData as ICompanyCreateWithContactId)
      .then((company: Company | null) => {
        if (!company) {
          return GetErrorResponse(res, 'Unable to update company');
        }
        return res.status(201).json(new APIResponse(company));
      })
      .catch((error) => GetErrorResponse(res, 'Unable to update company', error));
  });

  router.put('/contact', async (req: IAcqRequest, res: Response) => {
    const { auth, body: contactData }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: EntityService = new EntityService({ auth });

    return service
      .modifyContact(contactData as ICreateContactParams)
      .then((contact: Contact | null) => {
        if (!contact) {
          return GetErrorResponse(res, 'Unable to update contact');
        }
        return res.status(201).json(new APIResponse(contact));
      })
      .catch((error) => GetErrorResponse(res, 'Unable to update contact', error));
  });

  router.post('/', async (req: IAcqRequest, res: Response) => {
    const { auth, body: companyContactData }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: EntityService = new EntityService({ auth });

    return service
      .createCompanyContactEntity(companyContactData as IContactCompanyEntity)
      .then((contact: CompanyContactMapping | null) => {
        if (!contact) {
          return res.status(500).json(new APIError('Unable to create new entity'));
        }
        return res.status(201).json(new APIResponse(contact));
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError('Unable to create new entity', {
            details: error.toString()
          })
        );
      });
  });

  router.post('/search', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest & { body: { query: string } } = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: EntityService = new EntityService({ auth });

    return service
      .searchEntity(body)
      .then((entities: IEntitySearchResult[]) => {
        if (!entities) {
          return res.status(500).json(new APIError('Unable to query entities'));
        }
        return res.status(200).json(new APIResponse(entities));
      })
      .catch((error: Error) => {
        return res.status(500).json(
          new APIError('Unable to query entities', {
            details: error.toString()
          })
        );
      });
  });

  router.put('/mapping-company', async (req: IAcqRequest, res: Response) => {
    const { auth, body: companyContactData }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: EntityService = new EntityService({ auth });
    return service
      .updateCompanyContactEntity(companyContactData as IContactCompanyEntity)
      .then((entity: CompanyContactMapping | null) => {
        if (!entity) {
          return res.status(500).json(new APIError('Unable to create new entity'));
        }
        return res.status(201).json(new APIResponse(entity));
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError('Unable to create new entity', {
            details: error.toString()
          })
        );
      });
  });

  router.put('/mapping-contact', async (req: IAcqRequest, res: Response) => {
    const { auth, body: companyContactData }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: EntityService = new EntityService({ auth });

    return service
      .updateContactCompanyEntity(companyContactData as IContactCompanyEntity)
      .then((entity: CompanyContactMapping | null) => {
        if (!entity) {
          return res.status(500).json(new APIError('Unable to create new entity'));
        }
        return res.status(201).json(new APIResponse(entity));
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError('Unable to create new entity', {
            details: error.toString()
          })
        );
      });
  });

  router.post('/check-valid-entity', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: EntityService = new EntityService({ auth });

    return service
      .checkValidEntity(body)
      .then((response) => res.json(response))
      .catch((err) => {
        return res.status(400).json(
          new APIError('Unable to get entity', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/check-ai-generated', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: EntityService = new EntityService({ auth });

    return service
      .checkAiGenerated(body)
      .then((entity: IResponseStatus) => {
        return res.status(201).json(new APIResponse(entity));
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError('Unable to create new entity', {
            details: error.toString()
          })
        );
      });
  });

  router.get('/filters-fields', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: EntityService = new EntityService({ auth });

    return service
      .getFilterFields()
      .then((filters: IFilterFields[]) => {
        return res.json(new APIResponse(filters));
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError('Unable to create new entity', {
            details: error.toString()
          })
        );
      });
  });

  router.post('/create', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    const service: EntityService = new EntityService({ auth });
    return service
      .createEntity(body)
      .then((entity: any) => {
        return res.status(201).json(entity);
      })
      .catch((error) => {
        logger.error({ msg: 'Create entity error', error });
        if (error instanceof ConflictError) {
          return res.status(409).json(error);
        } else {
          return res.status(500).json(
            new APIError('Unable to create new entity', {
              details: error.toString()
            })
          );
        }
      });
  });

  router.put('/update', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    const service: EntityService = new EntityService({ auth });
    return service
      .updateEntity(body)
      .then((result: boolean) => {
        return res.status(200).json(result);
      })
      .catch((error) => {
        logger.error({ msg: 'Update entity error', error });
        if (error instanceof ConflictError) {
          return res.status(409).json(error);
        } else if (error instanceof BadRequestError) {
          return res.status(400).json(error);
        } else {
          return res.status(500).json(
            new APIError('Unable to update entity', {
              details: error.toString()
            })
          );
        }
      });
  });

  return router;
};
