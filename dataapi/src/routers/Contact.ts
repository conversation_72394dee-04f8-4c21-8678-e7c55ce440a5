import { Router, Response } from 'express';
import { APIResponse, APIError } from '../lib';
import { ContactService } from '../services';
import { NotFoundError, UnauthorizedError } from '../lib/errors';
import { IAcqRequest } from '../types/Common';
import { IContact, IOwner } from '../types/Entity';
import { IFlattenedField, IFlattenedMapping } from '../types/Company';
import { IGetEmailFieldsResult, IGetFilteredContactResult, ISetEmailFieldResult } from '../types/Contact';
import { logger } from '../logger';

export default () => {
  const router: Router = Router({ mergeParams: true });

  router.post('/get-contacts', async (req: IAcqRequest, res: Response) => {
    const { auth, body: query }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ContactService = new ContactService({ auth });
    return service
      .getContacts(query)
      .then((contacts: IGetFilteredContactResult | null) => {
        if (!contacts) {
          return res.status(404).json(new NotFoundError('Contacts not found'));
        }
        return res.status(200).json(contacts);
      })
      .catch((err) => {
        return res.status(400).json(
          new APIError('Unable to get contacts', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/details/:contactId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { contactId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.debug({ msg: 'Get contact details request', contactId });

    const service: ContactService = new ContactService({ auth });

    return service
      .getContactDetailsById(contactId)
      .then((contact) => {
        if (!contact) {
          return res.status(404).json(new NotFoundError('Contact not found'));
        }
        return res.status(200).json(contact);
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get contact', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/fields/:contactId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { contactId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ContactService = new ContactService({ auth });

    return service
      .getFields(contactId)
      .then((result: IFlattenedField[] | null) => res.json(new APIResponse(result)))
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get contact fields', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/mappings/:contactId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { contactId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ContactService = new ContactService({ auth });

    return service
      .getMappings(contactId)
      .then((mappings: IFlattenedMapping[] | null) => {
        return res.status(200).json(new APIResponse(mappings || []));
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get contact mappings', {
            details: err.toString()
          })
        );
      });
  });

  router.delete('/delete/:contactId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { contactId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ContactService = new ContactService({ auth });

    return service
      .deleteContact(contactId)
      .then((result: boolean) => {
        if (!result) {
          return res.status(500).json(new APIError('Unable to delete contact'));
        }
        return res.status(200).json(new APIResponse(result));
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError('Unable to to delete contact', {
            details: error.toString()
          })
        );
      });
  });

  router.put('/set-primary', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { contactId, companyId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ContactService = new ContactService({ auth });

    return service
      .updatePrimaryCompany(contactId, companyId)
      .then((result: boolean) => {
        if (!result) {
          return res.status(500).json(new APIError('Unable to set primary company'));
        }
        return res.status(200).json(result);
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError('Unable to set primary company', {
            details: error.toString()
          })
        );
      });
  });

  router.post('/search', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { query }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ContactService = new ContactService({ auth });

    return service
      .searchContact({ query })
      .then((contacts: IContact[] | null) => {
        if (!contacts || contacts.length < 1) {
          return res.status(200).json([]);
        }
        return res.status(200).json(contacts);
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get contact', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/create-mapping', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { contactId, companyId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ContactService = new ContactService({ auth });

    return service
      .createMapping(contactId, companyId)
      .then(() => {
        return res.status(201).json(new APIResponse(true));
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to create mapping', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/remove-contact', async (req, res) => {
    const {
      auth,
      body: { id }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ContactService = new ContactService({ auth });
    return service
      .removeContact(id)
      .then((result: boolean | null) => {
        return res.status(201).json(new APIResponse(result));
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError('Failed to remove contact', {
            details: error.toString()
          })
        );
      });
  });

  router.get('/email-fields', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ContactService = new ContactService({ auth });

    return service
      .getEmailFields()
      .then((fields: IGetEmailFieldsResult[] | null) => {
        if (!fields) {
          return res.status(404).json(new NotFoundError('Email fields not found'));
        }
        return res.status(200).json(new APIResponse(fields));
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get email fields', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/set-email-field', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { contactId, schemaFieldId, label, value }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ContactService = new ContactService({ auth });
    return service
      .setEmailField({ contactId, schemaFieldId, label, value })
      .then((result: ISetEmailFieldResult | null) => {
        if (!result) {
          return res.status(500).json(new APIError('Unable to set email field'));
        }
        return res.status(201).json(new APIResponse(result));
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError('Unable to set email field', {
            details: error.toString()
          })
        );
      });
  });

  router.post('/set-default-email', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { contactId, fieldKey }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ContactService = new ContactService({ auth });
    return service
      .setDefaultEmail({ contactId, fieldKey })
      .then((result) => {
        if (!result) {
          return res.status(500).json(new APIError('Unable to set default email'));
        }
        return res.status(201).json(new APIResponse(result));
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError('Unable to set default email', {
            details: error.toString()
          })
        );
      });
  });

  router.get('/owners-list', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    const service: ContactService = new ContactService({ auth });

    return service
      .getOwnersList()
      .then((owners: IOwner[] | null) => {
        if (!owners) {
          return res.status(404).json(new NotFoundError('Owners not found'));
        }
        return res.status(200).json(owners);
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get owners list', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/:contactId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { contactId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ContactService = new ContactService({ auth });

    return service
      .getContactById(contactId)
      .then((contact: IContact | null) => {
        if (!contact) {
          return res.status(404).json(new NotFoundError('Contact not found'));
        }
        return res.json(new APIResponse(contact));
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get contact', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/get-enrichments-fields/:contactId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { contactId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ContactService = new ContactService({ auth });

    return service
      .getEnirchmentsFields(contactId)
      .then((response) => res.json(new APIResponse(response)))
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get company fields', {
            details: err.toString()
          })
        );
      });
  });

  return router;
};
