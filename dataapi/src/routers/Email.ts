import { Router, Response } from 'express';
import { EmailService } from '../services';
import { IAcqRequest } from '../types/Common';
import { APIError, APIResponse } from '../lib';
import { NotFoundError, UnauthorizedError } from '../lib/errors';
import { EmailData } from '../types/Entity';

export default () => {
  const router: Router = Router({ mergeParams: true });

  router.post('/send-to-email', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service = new EmailService({ auth });

    return await service
      .upsertChannelEmail(req.body)
      .then((result) => {
        res.json(new APIResponse(result));
      })
      .catch((err) => {
        return res.status(422).json(
          new APIError(err.toString(), {
            details: err.toString()
          })
        );
      });
  });

  router.post('/send-email-to-my-self', async (_req: IAcqRequest, res: Response) => {
    return res.json(new APIResponse(true));
  });

  router.post('/update-email-status', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { id, status }
    }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: EmailService = new EmailService({ auth });

    return service
      .updateChannelEmailStatus(id, status)
      .then((email: EmailData) => {
        if (!email) {
          return res.status(404).json(new NotFoundError('email not found'));
        }
        return res.json(new APIResponse(email));
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get emails', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/pending', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: EmailService = new EmailService({ auth });

    return service
      .getEmails()
      .then((email: EmailData[] | []) => {
        if (!email) {
          return res.status(404).json(new NotFoundError('email not found'));
        }
        return res.json(new APIResponse(email));
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to get emails', {
            details: err.toString()
          })
        );
      });
  });

  return router;
};
