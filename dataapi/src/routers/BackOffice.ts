import { Router, Response } from 'express';
import { APIError, APIResponse, CSV } from '../lib';
import { BackOfficeService, ExporterService, ImporterService, OrganizationService } from '../services';
import { logger } from '../logger';
import { IAcqRequest, IAuth, InviteOrgRequest } from '../types/Common';
import { BadRequestError, UnauthorizedError } from '../lib/errors';
import multer from 'multer';
import { ICSVObject, IEntityDataForCsv } from '../types/CSV';
import EntityService from '../services/Entity';
import { IEntityDataToWrite, IImporterResponse } from '../types/Importer';
import { EntityType, OrgType, MembershipType, ContactPurpose, IntegrationProvider, IntegrationType, OrgStatus, OrgTier } from '@prisma/client';

const storage = multer.memoryStorage();
const upload = multer({ storage: storage });
const MAXIMUM_ALLOWED_IMPORT_LINES = 10000;

export default (): Router => {
  const router: Router = Router({ mergeParams: true });

  // Get organization creation form data (enums for dropdowns)
  router.get('/organization-form-data', async (req: IAcqRequest, res: Response) => {
    const auth: IAuth | null | undefined = req.auth;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    try {
      const formData = {
        orgTypes: Object.values(OrgType),
        membershipTypes: Object.values(MembershipType),
        contactPurposes: Object.values(ContactPurpose),
        integrationProviders: Object.values(IntegrationProvider),
        integrationTypes: Object.values(IntegrationType),
        orgStatuses: Object.values(OrgStatus),
        orgTiers: Object.values(OrgTier)
      };

      return res.json(new APIResponse(formData));
    } catch (err) {
      return res.status(500).json(
        new APIError('Server Error: Unable to fetch form data', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  router.post('/invite-organization', async (req: IAcqRequest, res: Response) => {
    const auth: IAuth | null | undefined = req.auth;
    const createOrgRequest: InviteOrgRequest = req.body;
    logger.info({ msg: 'Organization invitation request received', createOrgRequest });

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const organizationService = new OrganizationService({ auth });
    const service: BackOfficeService = new BackOfficeService({ auth, organizationService });
    return service
      .createOrgAndInvitation(createOrgRequest)
      .then((result) => {
        return res.json(new APIResponse(result));
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Server Error: Unable to invite Organization ', {
            details: err instanceof Error ? err.message : 'Unknown error occurred'
          })
        );
      });
  });

  router.get('/organizations', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    try {
      logger.info({ request: 'Get all organizations', email: auth.email });

      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      return service
        .getAllOrganizations()
        .then((result) => {
          return res.json(new APIResponse(result));
        })
        .catch((err) => {
          logger.error({ error: 'Get organizations failed', msg: err.toString() });
          return res.status(500).json(
            new APIError('Get organizations failed', {
              details: err instanceof Error ? err.message : 'Unknown error occurred'
            })
          );
        });
    } catch (err) {
      return res.status(500).json(
        new APIError('Get organizations failed', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  router.post('/import-csv', upload.single('file'), async (req: IAcqRequest, res: Response) => {
    if (!req.auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    if (!req.file) {
      return res.status(400).json(new BadRequestError('No file uploaded'));
    }

    const auth: IAuth = req.auth;
    const csvContent: string = req.file.buffer.toString('utf8').trim();
    const { crop, entityType, identifier }: { crop: string; entityType: EntityType; identifier: string } = req.body;

    if (entityType !== EntityType.company && entityType !== EntityType.contact) {
      return res.status(400).json(new BadRequestError('Invalid entity type'));
    }

    const csvLibrary: CSV = new CSV();
    const lineCount: number = csvLibrary.countLines(csvContent);
    if (lineCount > MAXIMUM_ALLOWED_IMPORT_LINES + 1) {
      logger.error({ msg: 'CSV file is too large', lineCount, allowed: MAXIMUM_ALLOWED_IMPORT_LINES });
      return res.status(413).json(
        new APIError(`CSV file is too large (Max ${MAXIMUM_ALLOWED_IMPORT_LINES} lines allowed)`, {
          details: `CSV file is too large (Max ${MAXIMUM_ALLOWED_IMPORT_LINES} lines allowed)`
        })
      );
    }

    const entityService: EntityService = new EntityService({ auth });
    const importerService: ImporterService = new ImporterService({ auth, entityType, entityService });
    const cropIfNeeded: boolean = crop === 'true' || crop === '1';

    return csvLibrary
      .load(csvContent)
      .clear()
      .parse()
      .then(async (result: ICSVObject[]) => {
        return importerService
          .prepareData(result, identifier)
          .then(async (data: IEntityDataToWrite[]) => {
            return importerService
              .createEntities(data, cropIfNeeded)
              .then((result: IImporterResponse) => {
                return res.status(201).json(new APIResponse(result));
              })
              .catch((error) => {
                logger.error({ msg: 'Importer error during data write', error });
                return res.status(500).json(new APIError('Unable to import CSV', { details: 'Write error' }));
              });
          })
          .catch((error) => {
            logger.error({ msg: 'Importer error during data prepare', error });
            return res.status(400).json(new APIError('Unable to import CSV', { details: 'Data error' }));
          });
      })
      .catch((error) => {
        logger.error({ msg: 'Importer error during CSV parse', error });
        return res.status(400).json(new APIError('Unable to import CSV', { details: 'Parse error' }));
      });
  });

  router.post('/export-csv', async (req: IAcqRequest, res: Response) => {
    if (!req.auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const auth: IAuth = req.auth;
    const { fields, entityType }: { fields: string[]; entityType: EntityType } = req.body;

    const csvLibrary: CSV = new CSV();
    const entityService: EntityService = new EntityService({ auth });
    const exporterService: ExporterService = new ExporterService({ auth, fields, entityType, entityService });

    return exporterService
      .prepareEntityData()
      .then((result: IEntityDataForCsv[]) => {
        logger.debug({ msg: 'Exporting data', entityType, fields, count: result.length });
        if (result.length === 0) {
          return res.status(404).json(new APIError('No data to export'));
        }
        return csvLibrary
          .export(result)
          .then((csvContent: string) => {
            return res.setHeader('Content-Disposition', 'attachment; filename="companies.csv"').setHeader('content-type', 'text/csv').status(200).send(csvContent);
          })
          .catch((error) => {
            logger.error({ msg: 'Exporter error during CSV export', error });
            return res.status(500).json(new APIError('Unable to export CSV', { details: 'Export error' }));
          });
      })
      .catch((error) => {
        logger.error({ msg: 'Exporter error during data prepare', error });
        return res.status(500).json(new APIError('Unable to export CSV', { details: 'Data prepare error' }));
      });
  });

  router.get('/organization/:orgId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { orgId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    try {
      logger.info({ request: 'Get organization details', orgId, email: auth.email });

      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      return service
        .getOrganizationById({ orgId, admin: true })
        .then((result) => {
          if (!result) {
            logger.error({ error: 'Unable to get organization' });
            return res.status(400).json(new APIError('Unable to get organization'));
          }
          return res.json(new APIResponse(result));
        })
        .catch((err) => {
          logger.error({ error: 'Get organization failed', msg: err.toString() });
          return res.status(500).json(
            new APIError('Get organization failed', {
              details: err instanceof Error ? err.message : 'Unknown error occurred'
            })
          );
        });
    } catch (err) {
      return res.status(500).json(
        new APIError('Get organization failed', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  router.put('/organization/:orgId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { orgId },
      body: data
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    try {
      logger.info({ request: 'Update organization', orgId, email: auth.email });

      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      return service
        .updateOrganization({ orgId, data })
        .then((result) => {
          if (!result) {
            logger.error({ error: 'Unable to update organization' });
            return res.status(400).json(new APIError('Unable to update organization'));
          }
          return res.json(new APIResponse(result));
        })
        .catch((err) => {
          logger.error({ error: 'Update organization failed', msg: err.toString() });
          return res.status(500).json(
            new APIError('Update organization failed', {
              details: err instanceof Error ? err.message : 'Unknown error occurred'
            })
          );
        });
    } catch (err) {
      return res.status(500).json(
        new APIError('Update organization failed', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  // Address Management Routes
  router.post('/organization/:orgId/addresses', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { orgId },
      body: address
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.addOrganizationAddress({ orgId, address });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to add address', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  router.put('/organization/addresses/:addressId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { addressId },
      body: address
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.updateOrganizationAddress({ addressId, address });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to update address', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  router.delete('/organization/addresses/:addressId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { addressId }
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.deleteOrganizationAddress({ addressId });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to delete address', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  // Contact Management Routes
  router.post('/organization/:orgId/contacts', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { orgId },
      body: contact
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.addOrganizationContact({ orgId, contact });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to add contact', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  router.put('/organization/contacts/:contactId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { contactId },
      body: contact
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.updateOrganizationContact({ contactId, contact });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to update contact', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  router.delete('/organization/contacts/:contactId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { contactId }
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.deleteOrganizationContact({ contactId });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to delete contact', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  // Integration Management Routes
  router.post('/organization/:orgId/integrations', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { orgId },
      body: integration
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.addOrganizationIntegration({ orgId, integration, userId: auth.uid });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to add integration', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  router.put('/organization/integrations/:integrationId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { integrationId },
      body: integration
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.updateOrganizationIntegration({ integrationId, integration });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to update integration', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  router.delete('/organization/integrations/:integrationId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { integrationId }
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.deleteOrganizationIntegration({ integrationId });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to delete integration', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  // Credit Management Routes
  router.get('/organization/:orgId/credits', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { orgId }
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.getOrganizationCredits({ orgId });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to get organization credits', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  router.get('/organization/:orgId/credit-purchases', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { orgId },
      query: { page = '1', limit = '10' }
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.getCreditPurchases({
        orgId,
        page: parseInt(page as string),
        limit: parseInt(limit as string)
      });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to get credit purchases', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  router.get('/organization/:orgId/credit-usage', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { orgId },
      query: { page = '1', limit = '10' }
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.getCreditUsage({
        orgId,
        page: parseInt(page as string),
        limit: parseInt(limit as string)
      });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to get credit usage', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  router.get('/organization/:orgId/credit-transactions', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { orgId },
      query: { page = '1', limit = '10' }
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.getCreditTransactions({
        orgId,
        page: parseInt(page as string),
        limit: parseInt(limit as string)
      });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to get credit transactions', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  router.post('/organization/:orgId/credit-purchase', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { orgId },
      body: creditPurchase
    }: IAcqRequest = req;
    if (!auth) return res.status(401).json(new UnauthorizedError('Unauthorized'));

    try {
      const organizationService = new OrganizationService({ auth });
      const service = new BackOfficeService({ auth, organizationService });

      const result = await service.addCreditPurchase({ orgId, creditPurchase, userId: auth.uid });
      return res.json(new APIResponse(result));
    } catch (err) {
      return res.status(500).json(
        new APIError('Failed to add credit purchase', {
          details: err instanceof Error ? err.message : 'Unknown error occurred'
        })
      );
    }
  });

  return router;
};
