import { Router, Response } from 'express';
import { IAcqRequest } from '../types/Common';
import { APIError } from '../lib';
import { BadRequestError, NotFoundError, ServerError, UnauthorizedError } from '../lib/errors';
import { logger } from '../logger';
import { CompanyService, ContactService, NotificationService, PlayService, TemplateService, TemplateVariableService } from '../services';
import { validate as uuidValidate } from 'uuid';
import { IPlay, IPlaybookResponse, IPlaySaveRequest } from '../types/Play';
import { FirebaseLibrary } from '../lib/notification/Firebase';
import { NotificationProvider, NotificationProviderType } from '../lib/notification/Notification';
import { LlmProvider } from '@prisma/client';

export default () => {
  const router: Router = Router({ mergeParams: true });

  router.get('/', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Get list of plays request', uid: auth?.uid });

    const companyService: CompanyService = new CompanyService({ auth });
    const contactService: ContactService = new ContactService({ auth });
    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });

    const play: PlayService = new PlayService({
      auth,
      companyService,
      contactService,
      templateVariablesService
    });

    return play
      .getPlays()
      .then((plays?: IPlay[]) => {
        logger.info({ msg: 'Plays found', numberOfPlays: plays?.length });
        return res.json(plays);
      })
      .catch((err) => {
        logger.error({ msg: 'Read play list error', err });
        return res.status(500).json(new APIError('Unable to read plays'));
      });
  });

  router.post('/playbook', async (req: IAcqRequest, res: Response) => {
    const { auth, body: query }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Get list of play books request', uid: auth?.uid });

    const companyService: CompanyService = new CompanyService({ auth });
    const contactService: ContactService = new ContactService({ auth });
    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });

    const play: PlayService = new PlayService({
      auth,
      companyService,
      contactService,
      templateVariablesService
    });

    return play
      .getPlaybook(query)
      .then((plays: IPlaybookResponse) => {
        // logger.info({ msg: 'Play Books found', numberOfPlays: plays?.playbooks?.length });
        return res.json(plays);
      })
      .catch((err) => {
        logger.error({ msg: 'Read play books list error', err });
        return res.status(500).json(new APIError('Unable to read play books'));
      });
  });

  router.post('/', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Saving play request', uid: auth?.uid });

    const templateService: TemplateService = new TemplateService();
    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });
    const companyService: CompanyService = new CompanyService({ auth });
    const contactService: ContactService = new ContactService({ auth });

    const play: PlayService = new PlayService({
      auth,
      templateService,
      templateVariablesService,
      companyService,
      contactService
    });

    const playRequest: IPlaySaveRequest = req.body as IPlaySaveRequest;

    return play
      .savePlay(playRequest)
      .then((play: IPlay) => {
        logger.info({ msg: 'Save play', play });
        return res.json({ data: play });
      })
      .catch((err) => {
        logger.error({ msg: 'Save play error', err });
        return res.status(err.code || 500).json(new ServerError(err.message || 'Unable to save play'));
      });
  });

  router.get('/draft/:llmEngine', async (req: IAcqRequest, res: Response) => {
    const { auth, params }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Reading draft play request' });

    const companyService: CompanyService = new CompanyService({ auth });
    const contactService: ContactService = new ContactService({ auth });
    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });

    const play: PlayService = new PlayService({
      auth,
      companyService,
      contactService,
      templateVariablesService
    });
    const llmValue = params?.llmEngine ? (params.llmEngine as LlmProvider) : undefined;

    return play
      .getDraft(llmValue)
      .then((play) => {
        if (!play) {
          logger.info({ msg: 'No play found' });
          return res.status(404).json(new NotFoundError('Play not found'));
        }
        logger.info({ msg: 'Read play', play });
        return res.json({ data: play });
      })
      .catch((err) => {
        logger.error({ msg: 'Read single play error', err });
        return res.status(500).json(new APIError('Unable to read play'));
      });
  });

  router.delete('/draft', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Reading draft play request' });

    const companyService: CompanyService = new CompanyService({ auth });
    const contactService: ContactService = new ContactService({ auth });
    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });

    const play: PlayService = new PlayService({
      auth,
      companyService,
      contactService,
      templateVariablesService
    });

    return play
      .removeDraft()
      .then((play) => {
        if (!play) {
          logger.info({ msg: 'No play found' });
          return res.status(404).json(new NotFoundError('Play not found'));
        }
        logger.info({ msg: 'Read play', play });
        return res.json({ data: play });
      })
      .catch((err) => {
        logger.error({ msg: 'Read single play error', err });
        return res.status(500).json(new APIError('Unable to read play'));
      });
  });

  router.delete('/:playId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { playId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Delete play request' });

    if (!uuidValidate(playId)) {
      return res.status(400).json(new BadRequestError('Invalid play id'));
    }

    const companyService: CompanyService = new CompanyService({ auth });
    const contactService: ContactService = new ContactService({ auth });
    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });

    const play: PlayService = new PlayService({
      auth,
      companyService,
      contactService,
      templateVariablesService
    });

    return play
      .delete(playId)
      .then((result) => {
        if (!result) {
          logger.info({ msg: 'Delete play failed' });
          return res.status(404).json(new NotFoundError('Play not found'));
        }
        logger.info({ msg: 'Delete play' });
        return res.json({ data: true });
      })
      .catch((err) => {
        logger.error({ msg: 'Delete play error', err });
        return res.status(500).json(new APIError('Unable to read play'));
      });
  });

  router.get('/playbook-users', async (req: IAcqRequest, res: Response) => {
    const { auth } = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Getting playbook users list', uid: auth?.uid });

    const companyService: CompanyService = new CompanyService({ auth });
    const contactService: ContactService = new ContactService({ auth });
    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });

    const play: PlayService = new PlayService({
      auth,
      companyService,
      contactService,
      templateVariablesService
    });
    return play
      .getPlaybookUsers()
      .then((users) => {
        logger.info({ msg: 'Users found', numberOfUsers: users?.length });
        return res.json(users);
      })
      .catch((err) => {
        logger.error({ msg: 'getPlaybookUsers list error', err });
        return res.status(500).json(new APIError('Unable to get users'));
      });
  });

  router.get('/:playId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { playId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Reading single play request', playId });

    if (!uuidValidate(playId)) {
      return res.status(400).json(new BadRequestError('Invalid play id'));
    }

    const companyService: CompanyService = new CompanyService({ auth });
    const contactService: ContactService = new ContactService({ auth });
    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });

    const play: PlayService = new PlayService({
      auth,
      companyService,
      contactService,
      templateVariablesService
    });

    return play
      .getPlay({ playId })
      .then((play) => {
        if (!play) {
          logger.info({ msg: 'No play found' });
          return res.status(404).json(new NotFoundError('Play not found'));
        }
        logger.info({ msg: 'Read play', play });
        return res.json({ data: play });
      })
      .catch((err) => {
        logger.error({ msg: 'Read single play error', err });
        return res.status(500).json(new APIError('Unable to read play'));
      });
  });

  router.post('/execute', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { playId, entityType, targetIds, executionId, channelEmailId, entityId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({
      msg: 'Execute play request',
      uid: auth?.uid,
      playId,
      entityType,
      targetIds
    });

    if (!uuidValidate(playId)) {
      return res.status(400).json(new BadRequestError('Invalid play id'));
    }

    const firebaseLib = new FirebaseLibrary({ auth });
    const notificationProvider = new NotificationProvider({
      auth,
      libraries: { firebase: firebaseLib },
      type: NotificationProviderType.Firebase
    });
    await notificationProvider.init();
    const notificationService = new NotificationService({ auth, provider: notificationProvider });
    const templateService: TemplateService = new TemplateService();
    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });
    const companyService: CompanyService = new CompanyService({ auth });
    const contactService: ContactService = new ContactService({ auth });
    const play: PlayService = new PlayService({
      auth,
      companyService,
      contactService,
      templateService,
      templateVariablesService,
      notificationService,
      writeResultsToSocket: true
    });

    return play
      .executePlay({ playId, entityType, targetIds, executionId, channelEmailId, entityId })
      .then((response) => {
        return res.status(201).json(response);
      })
      .catch((err) => {
        logger.error({ msg: 'Play execution error', err });
        return res.status(500).json(new ServerError('Unable to execute play'));
      });
  });

  router.post('/execution/cancel', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { executionId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({
      msg: 'Cancel play execution request',
      uid: auth?.uid,
      executionId
    });

    if (!uuidValidate(executionId)) {
      return res.status(400).json(new BadRequestError('Invalid play id'));
    }

    const firebaseLib = new FirebaseLibrary({ auth });
    const notificationProvider = new NotificationProvider({
      auth,
      libraries: { firebase: firebaseLib },
      type: NotificationProviderType.Firebase
    });
    await notificationProvider.init();
    const notificationService = new NotificationService({ auth, provider: notificationProvider });
    const templateService: TemplateService = new TemplateService();
    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });
    const companyService: CompanyService = new CompanyService({ auth });
    const contactService: ContactService = new ContactService({ auth });
    const play: PlayService = new PlayService({
      auth,
      companyService,
      contactService,
      templateService,
      templateVariablesService,
      notificationService,
      writeResultsToSocket: true
    });

    return play
      .cancelPlayExecution({ executionId })
      .then((response) => {
        return res.status(201).json(response);
      })
      .catch((err) => {
        logger.error({ msg: 'Play execution error', err });
        return res.status(500).json(new ServerError('Unable to cancel play'));
      });
  });

  router.get('/execution/:executionId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { executionId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Reading single play execution request' });

    if (!uuidValidate(executionId)) {
      return res.status(400).json(new BadRequestError('Invalid execution id'));
    }

    const companyService: CompanyService = new CompanyService({ auth });
    const contactService: ContactService = new ContactService({ auth });
    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });

    const play: PlayService = new PlayService({
      auth,
      companyService,
      contactService,
      templateVariablesService
    });

    return play
      .getPlayExecution({ executionId })
      .then((play) => {
        if (!play) {
          logger.info({ msg: 'No play execution found' });
          return res.status(404).json(new NotFoundError('Play execution not found'));
        }
        logger.info({ msg: 'Read play execution', play });
        return res.json({ data: play });
      })
      .catch((err) => {
        logger.error({ msg: 'Read single play execution error', err });
        return res.status(500).json(new APIError('Unable to read play execution'));
      });
  });

  router.post('/duplicate/:playId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { playId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Duplicate play request', playId });

    if (!uuidValidate(playId)) {
      return res.status(400).json(new BadRequestError('Invalid play id'));
    }

    const companyService: CompanyService = new CompanyService({ auth });
    const contactService: ContactService = new ContactService({ auth });
    const templateService: TemplateService = new TemplateService();
    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });

    const play: PlayService = new PlayService({
      auth,
      companyService,
      contactService,
      templateService,
      templateVariablesService
    });

    return play
      .duplicatePlay(playId)
      .then((play) => {
        if (!play) {
          return res.status(404).json(new NotFoundError('Play not found'));
        }
        return res.json({ data: play });
      })
      .catch((err) => {
        logger.error({ msg: 'Duplicate play error', err });
        return res.status(500).json(new APIError('Unable to duplicate play'));
      });
  });

  router.delete('/remove/:playId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { playId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Remove play request', playId });

    if (!uuidValidate(playId)) {
      return res.status(400).json(new BadRequestError('Invalid play id'));
    }

    const companyService: CompanyService = new CompanyService({ auth });
    const contactService: ContactService = new ContactService({ auth });
    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });

    const play: PlayService = new PlayService({
      auth,
      companyService,
      contactService,
      templateVariablesService
    });

    return play
      .remove(playId)
      .then(() => res.json({ data: true }))
      .catch((err) => {
        logger.error({ msg: 'Duplicate play error', err });
        return res.status(500).json(new APIError('Unable to duplicate play'));
      });
  });

  return router;
};
