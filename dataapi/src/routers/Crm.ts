import { Router, Request, Response } from 'express';
import { APIError, APIResponse } from '../lib';
import { logger } from '../logger';
import { IAcqCrmRequest } from '../types/Common';
import { UnauthorizedError } from '../lib/errors';
import { ICrmBasicEntity, ICrmDefaultFields, ICrmFieldMapping, ICrmFields, ICrmFilter, ICrmListEntity, ICrmListMapping, ICrmUser, IUserCrmMap } from '../types/Crm';
import { CrmSetting, CrmSyncStatus } from '@prisma/client';
import { UserCrmMappingResult } from '../constants';

export default (): Router => {
  const router: Router = Router({ mergeParams: true });

  router.get('/entity-fields', async (req: Request, res: Response) => {
    const { auth, crmService, entityType }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Read Salesforce fields', user: auth.uid });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }

    if (!entityType) {
      return res.status(400).json(new APIError('Missing crmType parameter'));
    }

    return crmService
      .getCrmFields(entityType)
      .then((result: ICrmFields[]) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to read Salesforce token', {
            details: err.toString(),
            code: errorCode
          })
        );
      });
  });

  router.post('/entities', async (req: Request, res: Response) => {
    const { auth, crmService, entityType }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Read Salesforce contacts request', user: auth.uid });
    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }
    if (!entityType) {
      return res.status(400).json(new APIError('Missing crmType parameter'));
    }

    return crmService
      .getEntities(req.body, entityType)
      .then((result: ICrmBasicEntity[]) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to get Salesforce contacts', {
            details: err.toString(),
            code: errorCode
          })
        );
      });
  });

  router.post('/users', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }
    logger.info({ msg: 'Read Salesforce users request', user: auth.uid });
    const email = req.body.email;
    if (!email) {
      return res.status(400).json(new APIError('Missing email parameter'));
    }

    return crmService
      .getUsers(email)
      .then((result: ICrmUser[]) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        return res.status(500).json(
          new APIError('Unable to get Salesforce users', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/crm-filter', async (req: Request, res: Response) => {
    const { auth, crmService, entityType }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({
      msg: 'Add Crm filter request',
      user: auth.uid
    });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }
    if (!entityType) {
      return res.status(400).json(new APIError('Missing crmType parameter'));
    }

    return crmService
      .saveFilter(req.body, entityType)
      .then((result: boolean) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to set Salesforce contacts filter', {
            details: err.toString(),
            code: errorCode
          })
        );
      });
  });

  router.get('/crm-filters', async (req: Request, res: Response) => {
    const { auth, crmService, entityType }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({
      msg: 'Get Salesforce contacts filter request',
      user: auth.uid
    });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }
    if (!entityType) {
      return res.status(400).json(new APIError('Missing crmType parameter'));
    }

    return crmService
      .getFilters(entityType)
      .then((result: ICrmFilter[]) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to set Salesforce contacts filter', {
            details: err.toString(),
            code: errorCode
          })
        );
      });
  });

  router.get('/entity-mappings', async (req: Request, res: Response) => {
    const { auth, crmService, entityType }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({
      msg: 'Get crm mappings request',
      user: auth.uid
    });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }
    if (!entityType) {
      return res.status(400).json(new APIError('Missing crmType parameter'));
    }

    return crmService
      .getMappings(entityType)
      .then((result: ICrmFieldMapping[]) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to process crm mapping request', {
            details: err.toString(),
            code: errorCode
          })
        );
      });
  });

  router.get('/default-fields', async (req: Request, res: Response) => {
    const { auth, crmService, entityType }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({
      msg: 'Get default mappings request',
      user: auth.uid
    });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }
    if (!entityType) {
      return res.status(400).json(new APIError('Missing crmType parameter'));
    }

    return crmService
      .getDefaultFields(entityType)
      .then((result: ICrmDefaultFields[]) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to get default mappings', {
            details: err.toString(),
            code: errorCode
          })
        );
      });
  });

  router.post('/entity-mappings', async (req: Request, res: Response) => {
    const { auth, crmService, entityType }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({
      msg: 'Add Salesforce contacts mappings request',
      user: auth.uid
    });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }
    if (!entityType) {
      return res.status(400).json(new APIError('Missing crmType parameter'));
    }

    return crmService
      .saveMappings(req.body, entityType)
      .then((result: boolean) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        logger.error({ msg: 'Unable to set Salesforce contacts mapping', error: err });
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to set Salesforce contacts mapping', {
            details: err.toString(),
            code: errorCode
          })
        );
      });
  });

  router.post('/settings', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Set Crm settings request', user: auth.uid });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }

    return crmService
      .saveSettings({ ...req.body })
      .then((result: boolean) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to set Crm settings', {
            details: err.toString(),
            code: errorCode
          })
        );
      });
  });

  router.get('/settings', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Get Crm settings request', user: auth.uid });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }

    return crmService
      .getSettings()
      .then((result: CrmSetting) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        return res.status(500).json(
          new APIError('Unable to get Crm settings', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/sync-status', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Sync Salesforce contacts request', user: auth.uid });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }

    return crmService
      .getSyncStatus()
      .then((result: CrmSyncStatus) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        return res.status(500).json(
          new APIError('Unable to sync Salesforce contacts', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/sync', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Sync Salesforce contacts request', user: auth.uid });
    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }
    return crmService
      .sync(req.body)
      .then((result: any) => {
        return res.json(new APIResponse(result));
      })
      .catch((err: any) => {
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to sync Salesforce contacts', {
            details: err.toString(),
            code: errorCode
          })
        );
      });
  });

  router.post('/revoke', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Revoke Salesforce token request', user: auth.uid });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }

    return crmService
      .revoke()
      .then((result: boolean) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        return res.status(500).json(
          new APIError('Unable to revoke Salesforce tokens', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/api-key', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'get api key request', user: auth.uid });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }
    const key = crmService.getApiKey();
    logger.debug({ msg: 'get api key request success' });
    return res.status(200).json(new APIResponse(key));
  });

  router.post('/validate', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'validate Salesforce token request', user: auth.uid });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }
    const isAuthenticated = crmService.isAuthenticated();
    logger.debug({ msg: 'validate Salesforce token response', isAuthenticated });
    return res.status(200).json(new APIResponse(isAuthenticated));
  });

  router.post('/submit-key', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Submit crm key request', user: auth.uid });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }
    const apiKey = req.body.apiKey;
    if (!apiKey) {
      return res.status(400).json(new APIError('Missing api key parameter'));
    }
    return crmService
      .submitApiKey(apiKey)
      .then((result: boolean) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to Submit crm key ', {
            details: err.toString(),
            code: errorCode
          })
        );
      });
  });

  router.delete('/filter/:id', async (req: Request, res: Response) => {
    const {
      params: { id }
    } = req;
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({
      msg: 'Delete Salesforce filter request',
      user: auth.uid
    });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }

    return crmService
      .deleteFilter(id)
      .then((result: any) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to delete Salesforce filter', {
            details: err.toString(),
            code: errorCode
          })
        );
      });
  });

  router.post('/check-crm-user', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Check CRM user request', user: auth.uid });
    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }

    const { email } = req.body;
    if (!email) {
      return res.status(400).json(new APIError('Missing email parameter'));
    }

    return crmService
      .checkCrmUserEmail(email)
      .then((result: UserCrmMappingResult) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        return res.status(500).json(
          new APIError('Unable to check CRM user request', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/map-crm-user', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Map crm user request', user: auth.uid });
    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }
    const { email } = req.body;
    if (!email) {
      return res.status(400).json(new APIError('Missing email parameter'));
    }

    return crmService
      .mapCrmUser(email, auth.uid)
      .then((result: UserCrmMappingResult) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        return res.status(500).json(
          new APIError('Unable to map crm user request', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/crm-settings', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Get Crm settings', user: auth.uid });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }

    return crmService
      .getUserCrmMapping()
      .then((result: IUserCrmMap | null) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        return res.status(500).json(
          new APIError('Unable to get Crm settings', {
            details: err.toString()
          })
        );
      });
  });

  router.delete('/disconnect/:id', async (req: Request, res: Response) => {
    const {
      params: { id: integrationId }
    } = req;
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Disconnect crm request', user: auth.uid });
    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }
    if (!integrationId) {
      return res.status(400).json(new APIError('Missing integration id parameter'));
    }

    return crmService
      .removeIntegration(integrationId)
      .then((result: boolean) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        return res.status(500).json(
          new APIError('Unable to disconnect crm', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/crm-lists', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Get crm lists request', user: auth.uid });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }

    return crmService
      .getCrmLists()
      .then((result: ICrmListEntity[]) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        return res.status(500).json(
          new APIError('Unable to get crm lists', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/mapped-lists', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Get mapped lists request', user: auth.uid });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }

    return crmService
      .getMappedLists()
      .then((result: ICrmListMapping[]) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        return res.status(500).json(
          new APIError('Unable to get mapped lists', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/crm-list', async (req: Request, res: Response) => {
    const { auth, crmService }: IAcqCrmRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Save crm list request', user: auth.uid });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }

    const { list } = req.body;
    if (!list) {
      return res.status(400).json(new APIError('Missing list data'));
    }

    return crmService
      .saveCrmList(list)
      .then((result: boolean) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        return res.status(500).json(
          new APIError('Unable to save crm list', {
            details: err.toString()
          })
        );
      });
  });

  router.delete('/crm-list/:id', async (req: Request, res: Response) => { 
    const {
      params: { id }
    } = req;
    const { auth, crmService }: IAcqCrmRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Delete crm list request', user: auth.uid });

    if (!crmService) {
      return res.status(400).json(new APIError('Missing CRM service'));
    }

    return crmService
      .deleteCrmList(id)
      .then((result: boolean) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err: any) => {
        return res.status(500).json(
          new APIError('Unable to delete crm list', {
            details: err.toString()
          })
        );
      });
  });

  return router;
};
