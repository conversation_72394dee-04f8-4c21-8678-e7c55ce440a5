import { Router, Response } from 'express';
import { IAcqRequest } from '../types/Common';
import { APIError } from '../lib';
import { BadRequestError, ConflictError, NotFoundError, ServerError, UnauthorizedError } from '../lib/errors';
import { logger } from '../logger';
import { validate as uuidValidate } from 'uuid';
import EnrichmentService from '../services/Enrichment';
import { EntityType } from '@prisma/client';
import { NotificationService, TemplateService, TemplateVariableService } from '../services';
import { FirebaseLibrary } from '../lib/notification/Firebase';
import { NotificationProvider, NotificationProviderType } from '../lib/notification/Notification';
import EntityService from '../services/Entity';
import { IExecution } from '../types/Execution';

export default () => {
  const router = Router({ mergeParams: true });

  router.post('/list', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const { filters, pagination } = body;
    logger.info({ msg: filters });

    logger.info({ msg: 'Get enrichments request', uid: auth.uid });

    const enrichment: EnrichmentService = new EnrichmentService({ auth });

    return enrichment
      .getEnrichments({ filters, pagination })
      .then((enrichments) => {
        logger.info({ msg: 'Got enrichment list' });
        return res.json(enrichments);
      })
      .catch((err) => {
        logger.error({ msg: 'Get enrichment list error', err });
        return res.status(500).json(new ServerError('Unable to read enrichments'));
      });
  });

  router.get('/configs', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Get enrichment config request', uid: auth.uid });

    const enrichment: EnrichmentService = new EnrichmentService({ auth });

    return enrichment
      .getEnrichmentConfigs()
      .then((configurations) => {
        logger.info({ msg: 'Got enrichment config list' });
        return res.status(200).json({ configurations });
      })
      .catch((err) => {
        logger.error({ msg: 'Get enrichment config list error', err });
        return res.status(500).json(new ServerError('Unable to read enrichment configs'));
      });
  });

  router.get('/:listId/list', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { listId }
    }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Get enrichments request', uid: auth.uid, listId });

    const enrichment: EnrichmentService = new EnrichmentService({ auth });

    return enrichment
      .getListEnrichments({ listId })
      .then((enrichments) => {
        logger.info({ msg: 'Got enrichment list' });
        return res.json(enrichments);
      })
      .catch((err) => {
        logger.error({ msg: 'Get enrichment list error', err });
        return res.status(500).json(new ServerError('Unable to read enrichments'));
      });
  });

  router.get('/idea/configs', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Get enrichment config request', uid: auth.uid });

    const enrichment: EnrichmentService = new EnrichmentService({ auth });

    return enrichment
      .getListEnrichmentConfigs()
      .then((configurations) => {
        logger.info({ msg: 'Got enrichment config list' });
        return res.status(200).json({ configurations });
      })
      .catch((err) => {
        logger.error({ msg: 'Get enrichment config list error', err });
        return res.status(500).json(new ServerError('Unable to read enrichment configs'));
      });
  });

  router.get('/executions', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Get all executions request', uid: auth.uid });

    const enrichment: EnrichmentService = new EnrichmentService({ auth });

    return enrichment
      .getExecutions()
      .then((executions: IExecution[]) => {
        logger.info({ msg: 'Got executions list' });
        return res.status(200).json(executions);
      })
      .catch((err) => {
        logger.error({ msg: 'Get enrichment executions list error', err });
        return res.status(500).json(new ServerError('Unable to read enrichments executions'));
      });
  });

  router.post('/', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Save enrichment request', uid: auth.uid });

    const templateService: TemplateService = new TemplateService();
    const templateVariableService: TemplateVariableService = new TemplateVariableService({ auth });
    const enrichment: EnrichmentService = new EnrichmentService({
      auth,
      templateService,
      templateVariableService
    });

    let enrichmentData; // :IEnrichmentData...
    try {
      enrichmentData = req.body; // as IEnrichmentData...
    } catch (err) {
      logger.error({ msg: 'Invalid enrichment data', err });
      return res.status(400).json(new BadRequestError('Invalid enrichment data'));
    }

    return enrichment
      .saveEnrichment(enrichmentData)
      .then((enrichments) => {
        logger.info({ msg: 'Enrichment saved' });
        return res.status(201).json(enrichments);
      })
      .catch((err) => {
        logger.error({ msg: 'Unable to save enrichment', err });
        if (err instanceof ConflictError) {
          return res.status(409).json(err);
        }
        return res.status(500).json(new ServerError('Unable to save enrichment'));
      });
  });

  router.put('/', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'update enrichment request', uid: auth.uid });

    const enrichment: EnrichmentService = new EnrichmentService({ auth });

    return enrichment
      .updateEnrichment(req.body)
      .then((enrichments) => {
        logger.info({ msg: 'Enrichment updates' });
        return res.status(200).json({ data: enrichments });
      })
      .catch((err) => {
        logger.error({ msg: 'Unable to update enrichment', err });
        if (err instanceof ConflictError) {
          return res.status(409).json(err);
        }
        return res.status(500).json(new ServerError('Unable to update enrichment'));
      });
  });

  router.put('/column', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'update enrichment column request', uid: auth.uid });

    const enrichment: EnrichmentService = new EnrichmentService({ auth });

    return enrichment
      .updateEnrichmentColumn(req.body)
      .then((result) => {
        logger.info({ msg: 'Enrichment column updates' });
        return res.status(200).json({ data: result });
      })
      .catch((err) => {
        logger.error({ msg: 'Unable to update enrichment column', err });
        if (err instanceof ConflictError) {
          return res.status(409).json(err);
        }
        return res.status(500).json(new ServerError('Unable to update enrichment column'));
      });
  });

  router.post('/execute', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { enrichmentId, entityType, entityFilters, options }
    }: IAcqRequest = req;
    let { entityIds: entityIds }: IAcqRequest['body'] = req.body;
    logger.debug({ msg: 'Execute enrichment request', enrichmentId, entityType, entityIds, entityFilters, options });

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    if (!uuidValidate(enrichmentId)) {
      return res.status(400).json(new BadRequestError('Invalid enrichment id'));
    }

    let executeByFilters = false;
    if (entityFilters && typeof entityFilters === 'object') {
      executeByFilters = true;
    } else {
      if (!Array.isArray(entityIds) || entityIds.length === 0) {
        return res.status(400).json(new BadRequestError('Invalid entity ids'));
      }
      for (let i = 0; i < entityIds.length; i++) {
        if (!uuidValidate(entityIds[i])) {
          return res.status(400).json(new BadRequestError('Invalid entity id'));
        }
      }
    }
    try {
      if (!(entityType as EntityType)) {
        return res.status(400).json(new BadRequestError('Invalid entity type'));
      }
    } catch (_error) {
      return res.status(400).json(new BadRequestError('Invalid entity type'));
    }

    const firebaseLib = new FirebaseLibrary({ auth });
    const notificationProvider = new NotificationProvider({
      auth,
      libraries: { firebase: firebaseLib },
      type: NotificationProviderType.Firebase
    });
    await notificationProvider.init();
    const notificationService = new NotificationService({ auth, provider: notificationProvider });
    const templateService: TemplateService = new TemplateService();
    const templateVariableService: TemplateVariableService = new TemplateVariableService({ auth });
    const enrichmentService: EnrichmentService = new EnrichmentService({
      auth,
      notificationService,
      writeResultsToSocket: true,
      templateService,
      templateVariableService
    });

    if (executeByFilters) {
      const entityService: EntityService = new EntityService({ auth });
      entityIds = await entityService.getEntityIdsByFilters({ entityType: entityType as EntityType, filters: entityFilters });

      logger.debug({ msg: 'Entity ids by filters', count: entityIds.length });
      if (entityIds.length === 0) {
        return res.status(404).json(new NotFoundError('No entities found by filters'));
      }
    }

    return enrichmentService
      .executeEnrichment({ enrichmentId, entityType: entityType as EntityType, entityIds, silent: executeByFilters, options })
      .then((executions: { entityId: string; executionId: string }[]) => {
        if (executeByFilters) {
          logger.info({ msg: 'Filtered enrichment execution queued.', count: executions.length });
          return res.json({ count: executions.length });
        }
        logger.info({ msg: 'Enrichment execution queued.', executions });
        return res.json(executions);
      })
      .catch((error: Error) => {
        logger.error({ msg: 'Unable to execute enrichment', error });
        return res.status(500).json(new ServerError('Unable to execute enrichment'));
      });
  });

  router.get('/execution/:executionId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { executionId }
    }: IAcqRequest = req;
    logger.info({ msg: 'Reading single enrichment execution request' });

    if (!uuidValidate(executionId)) {
      return res.status(400).json(new BadRequestError('Invalid execution id'));
    }

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const enrichment: EnrichmentService = new EnrichmentService({ auth });
    return enrichment
      .getEnrichmentExecution(executionId)
      .then((execution) => {
        if (!execution) {
          logger.info({ msg: 'No execution found' });
          return res.status(404).json(new NotFoundError('Execution not found'));
        }
        logger.info({ msg: 'Read enrichment execution', execution });
        return res.json({ data: execution });
      })
      .catch((err) => {
        logger.error({ msg: 'Read single enrichment execution error', err });
        return res.status(500).json(new APIError('Unable to read enrichment execution'));
      });
  });

  router.get('/:enrichmentId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { enrichmentId }
    }: IAcqRequest = req;
    logger.info({ msg: 'Reading single enrichment request' });

    if (!uuidValidate(enrichmentId)) {
      return res.status(400).json(new BadRequestError('Invalid enrichment id'));
    }

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const enrichment: EnrichmentService = new EnrichmentService({ auth });
    return enrichment
      .getEnrichment({ enrichmentId })
      .then((enrichment) => {
        if (!enrichment) {
          logger.info({ msg: 'No enrichment found' });
          return res.status(404).json(new NotFoundError('Enrichment not found'));
        }
        logger.info({ msg: 'Read enrichment', enrichment });
        return res.json({ data: enrichment });
      })
      .catch((err) => {
        logger.error({ msg: 'Read single enrichment error', err });
        return res.status(500).json(new APIError('Unable to read enrichment'));
      });
  });

  router.delete('/:enrichmentId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { enrichmentId }
    }: IAcqRequest = req;
    logger.info({ msg: 'Delete enrichment request' });

    if (!uuidValidate(enrichmentId)) {
      return res.status(400).json(new BadRequestError('Invalid enrichment id'));
    }

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const enrichment: EnrichmentService = new EnrichmentService({ auth });
    return enrichment
      .deleteEnrichment({ enrichmentId })
      .then((result) => {
        if (!result) {
          logger.info({ msg: 'No enrichment found' });
          return res.status(404).json(new NotFoundError('Enrichment not found'));
        }
        logger.info({ msg: 'Enrichment deleted' });
        return res.status(200).json(result);
      })
      .catch((err) => {
        logger.error({ msg: 'Delete enrichment error', err });
        return res.status(500).json(new APIError('Unable to delete enrichment'));
      });
  });

  router.post('/assign', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const enrichment: EnrichmentService = new EnrichmentService({ auth });
    const { fieldId, enrichmentId } = body;

    return enrichment
      .assignEnrichmentToField({ fieldId, enrichmentId })
      .then((result) => {
        return res.status(200).json(result);
      })
      .catch((err: Error) => {
        return res.status(500).json(new APIError('Unable to assign enrichment', { details: err.toString() }));
      });
  });

  router.post('/unassign', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const enrichment: EnrichmentService = new EnrichmentService({ auth });
    const { fieldId, enrichmentId } = body;

    return enrichment
      .unassignEnrichmentFromField({ fieldId, enrichmentId })
      .then((result: boolean) => {
        return res.status(200).json(result);
      })
      .catch((err: Error) => {
        return res.status(500).json(new APIError('Unable to unassign enrichment', { details: err.toString() }));
      });
  });

  router.put('/set-primary', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const enrichment: EnrichmentService = new EnrichmentService({ auth });
    const { fieldId, enrichmentId } = body;

    return enrichment
      .setPrimaryEnrichmentForField({ fieldId, enrichmentId })
      .then((result: boolean) => {
        return res.status(200).json(result);
      })
      .catch((err: Error) => {
        return res.status(500).json(new APIError('Unable to set primary enrichment', { details: err.toString() }));
      });
  });

  router.put('/set-color', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const enrichment: EnrichmentService = new EnrichmentService({ auth });
    const { enrichmentId, color } = body;

    return enrichment
      .setColor({ enrichmentId, color })
      .then((result: boolean) => {
        return res.status(200).json(result);
      })
      .catch((err: Error) => {
        return res.status(500).json(new APIError('Unable to set primary enrichment', { details: err.toString() }));
      });
  });

  router.post('/tag-find', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const enrichment: EnrichmentService = new EnrichmentService({ auth });

    return enrichment
      .tagFind({ hint: body.hint })
      .then((result) => {
        return res.status(200).json(result);
      })
      .catch((err: Error) => {
        return res.status(500).json(new APIError('Unable to execute request', { details: err.toString() }));
      });
  });

  router.post('/generate-prompt', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const enrichment: EnrichmentService = new EnrichmentService({ auth });

    return enrichment
      .generatePrompt({ prompt: body.prompt })
      .then((result) => {
        return res.status(200).json(result);
      })
      .catch((err: Error) => {
        return res.status(500).json(new APIError('Unable to generate a prompt', { details: err.toString() }));
      });
  });

  return router;
};
