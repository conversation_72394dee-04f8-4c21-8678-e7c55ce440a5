import { Response, Router } from 'express';
import { UnauthorizedError } from '../lib/errors';
import { logger } from '../logger';
import { IAcqRequest } from '../types/Common';
import { APIError } from '../lib';
import ActivityService from '../services/Activity';

export default () => {
  const router = Router({ mergeParams: true });

  router.post('/', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body
    }: IAcqRequest = req;
    logger.info({ msg: 'Save activities request', uid: auth?.uid });
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ActivityService = new ActivityService({ auth });
    return service.createActivity(body)
      .then((activities) => {
        return res.json(activities);
      })
      .catch((err: unknown) => {
        return res.status(500).json(
          new APIError('Unable to save activities', {
            details: (err as any).toString()
          })
        );
      });
  });

  router.put('/', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;
    logger.info({ msg: 'update activities request', uid: auth?.uid });
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ActivityService = new ActivityService({ auth });
    return service.updateParticipantAndActivity(body)
      .then((activities) => {
        return res.json(activities);
      })
      .catch((err: any) => {
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to update activities', {
            details: err.details,
            code: errorCode
          })
        );
      });
  });

  router.get('/participant/:id', async (req: IAcqRequest, res: Response) => {
    const { auth, params: { id } }: IAcqRequest = req;
    logger.info({ msg: 'get activities request', uid: auth?.uid });
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ActivityService = new ActivityService({ auth });
    return service.getParticipant(id)
      .then((activities) => {
        return res.json(activities);
      })
      .catch((err: unknown) => {
        return res.status(500).json(
          new APIError('Unable to get activities', {
            details: (err as any).toString()
          })
        );
      });
  });

  router.delete('/:id', async (req: IAcqRequest, res: Response) => {
    const { auth, params: { id } }: IAcqRequest = req;
    logger.info({ msg: 'delete activities request', uid: auth?.uid });
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service: ActivityService = new ActivityService({ auth });
    return service.deleteParticipant(id)
      .then((activities) => {
        return res.json(activities);
      })
      .catch((err: any) => {
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to delete activities', {
            details: err.details,
            code: errorCode
          })
        );
      });
  });

  return router;
};
