import { Response, Router } from 'express';
import { BadRequestError, ConflictError, ServerError, UnauthorizedError } from '../lib/errors';
import { logger } from '../logger';
import { ListService, NotificationService } from '../services';
import { IAcqRequest } from '../types/Common';
import { upload } from '../upload';
import { validate as uuidValidate } from 'uuid';
import { FirebaseLibrary } from '../lib/notification/Firebase';
import { NotificationProvider, NotificationProviderType } from '../lib/notification/Notification';
import { EntityType } from '@prisma/client';
import { IFilterSampleData } from '../types/ListCompanyIdea';

export default () => {
  const router = Router({ mergeParams: true });

  router.post('/idea/export', async (req: IAcqRequest, res: Response) => {
    const { auth, body: filters }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Export list company ideas request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });
    return listService
      .exportListCompanyIdeas(filters)
      .then((csvContent) => {
        res.header('Content-Type', 'text/csv');
        res.attachment('list-ideas.csv');
        res.setHeader('Content-Disposition', 'attachment; filename=list-ideas.csv');
        return res.status(200).send(csvContent);
      })
      .catch((err) => {
        logger.error({ msg: 'Export list company ideas error', err });
        return res.status(500).json(new ServerError('Unable to export list company ideas'));
      });
  });

  router.get('/intentions', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Get intentions request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });

    return listService
      .getIntentions()
      .then((intentions) => {
        logger.info({ msg: 'Got intentions list' });
        return res.status(200).json(intentions);
      })
      .catch((err) => {
        logger.error({ msg: 'Get intentions error', err });
        return res.status(500).json(new ServerError('Unable to get intentions'));
      });
  });

  router.post('/list', async (req: IAcqRequest, res: Response) => {
    const { auth, body: query }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'List company request', uid: auth.uid });
    const listService: ListService = new ListService({ auth });

    return listService
      .getFilteredListCompanies(query)
      .then((listCompanies) => {
        logger.info({ msg: 'List ListCompany', listCompanies });
        return res.status(200).json(listCompanies);
      })
      .catch((err) => {
        logger.error({ msg: 'List ListCompany error', err });
        return res.status(500).json(new ServerError('Unable to list ListCompany'));
      });
  });

  router.post('/google-maps-samples', async (req: IAcqRequest, res: Response) => {
    const { auth, body: query }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'List company request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });

    return listService
      .getGoogleMapsSamples(query)
      .then((listCompanies) => {
        logger.info({ msg: 'List ListCompany' });
        return res.status(200).json(listCompanies);
      })
      .catch((err) => {
        logger.error({ msg: 'List ListCompany error', err });
        return res.status(500).json(new ServerError('Unable to list ListCompany'));
      });
  });

  router.get('/:listCompanyId/complete-confirmation', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { listCompanyId }
    }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'ListCompany complete confirmation request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });
    return listService
      .getCompleteConfirmation(listCompanyId)
      .then((listCompanyIdeas) => {
        logger.info({ msg: 'ListCompany complete confirmation' });
        return res.status(200).json(listCompanyIdeas);
      })
      .catch((err) => {
        logger.error({ msg: 'ListCompany complete confirmation error', err });
        return res.status(500).json(new ServerError('Unable to get ListCompany complete confirmation'));
      });
  });

  router.post('/idea/list', async (req: IAcqRequest, res: Response) => {
    const { auth, body: query }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'List company idea request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });
    return listService
      .getFilteredListCompanyIdeas(query)
      .then((listCompanyIdeas) => {
        logger.info({ msg: 'List ListCompanyIdea', listCompanyIdeas });
        return res.status(200).json(listCompanyIdeas);
      })
      .catch((err) => {
        logger.error({ msg: 'List ListCompanyIdea error', err });
        return res.status(500).json(new ServerError('Unable to list ListCompanyIdea'));
      });
  });

  router.post('/filter', async (req: IAcqRequest, res: Response) => {
    const { auth, body: query }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'List company idea request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });
    return listService
      .getFilterSamples(query)
      .then((listCompanyIdeas: IFilterSampleData[]) => {
        logger.info({ msg: 'List ListCompanyIdea' });
        return res.status(200).json(listCompanyIdeas);
      })
      .catch((err) => {
        logger.error({ msg: 'List ListCompanyIdea error', err });
        return res.status(500).json(new ServerError('Unable to list ListCompanyIdea'));
      });
  });

  router.put('/idea/shortlist', async (req: IAcqRequest, res: Response) => {
    const { auth, body: query }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Shortlist list company idea request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });
    if (query.ideaFilters && Object.keys(query.ideaFilters).length > 0) {
      query.ideaIds = await listService.getListCompanyIdeaIdsByFilters(query.ideaFilters);
    }
    return listService
      .shortlistListCompanyIdea(query)
      .then((listCompanyIdeas) => {
        logger.info({ msg: 'Shortlist ListCompanyIdea' });
        return res.status(200).json(listCompanyIdeas);
      })
      .catch((err) => {
        logger.error({ msg: 'Shortlist ListCompanyIdea error', err });
        return res.status(500).json(new ServerError('Unable to shortlist ListCompanyIdea'));
      });
  });

  router.post('/idea', async (req: IAcqRequest, res: Response) => {
    const { auth, body: body }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Update list company idea request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });
    return listService
      .updateListCompanyIdea(body)
      .then((listCompanyIdea) => {
        logger.info({ msg: 'Update ListCompanyIdea' });
        return res.status(200).json(listCompanyIdea);
      })
      .catch((error) => {
        logger.error({ msg: 'Update ListCompanyIdea error', error });
        if (error instanceof ConflictError) {
          return res.status(409).json(error);
        } else if (error instanceof BadRequestError) {
          return res.status(400).json(error);
        } else {
          return res.status(500).json(new ServerError('Unable to update ListCompanyIdea'));
        }
      });
  });

  router.patch('/name', async (req: IAcqRequest, res: Response) => {
    const { auth, body: body }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const { listId, name } = body;

    logger.debug({ msg: 'Update list name request', uid: auth.uid, listId, name });

    const firebaseLib = new FirebaseLibrary({ auth });
    const notificationProvider = new NotificationProvider({
      auth,
      libraries: { firebase: firebaseLib },
      type: NotificationProviderType.Firebase
    });
    await notificationProvider.init();
    const notificationService = new NotificationService({ auth, provider: notificationProvider });

    const listService: ListService = new ListService({ auth, notificationService });
    return listService
      .updateListName({ listId, name })
      .then((result: boolean) => {
        if (result) {
          logger.debug({ msg: 'Update list name success' });
          return res.status(200).json(true);
        }
        logger.error({ msg: 'Update list name failed' });
        return res.status(500).json(false);
      })
      .catch((error: Error) => {
        logger.error({ msg: 'Update list name error', error });
        return res.status(500).json(false);
      });
  });

  router.delete('/source/:sourceId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { sourceId }
    }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Delete ListSource request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });
    return listService
      .deleteSource(sourceId)
      .then(() => {
        logger.info({ msg: 'Delete ListSource' });
        return res.status(200).json();
      })
      .catch((err) => {
        logger.error({ msg: 'Delete ListSource error', err });
        return res.status(500).json(new ServerError('Unable to delete ListSource'));
      });
  });

  router.get('/users', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Get ListCompany users request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });

    return listService
      .getUsers()
      .then((users) => {
        logger.info({ msg: 'Get ListCompany users' });
        return res.status(200).json(users);
      })
      .catch((err) => {
        logger.error({ msg: 'Get ListCompany users error', err });
        return res.status(500).json(new ServerError('Unable to get ListCompany users'));
      });
  });

  router.get('/:listCompanyId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { listCompanyId }
    }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Get ListCompany request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });

    return listService
      .getListCompany(listCompanyId)
      .then((listCompany) => {
        logger.info({ msg: 'Get ListCompany', listCompany });
        return res.status(200).json(listCompany);
      })
      .catch((err) => {
        logger.error({ msg: 'Get ListCompany error', err });
        return res.status(500).json(new ServerError('Unable to get ListCompany'));
      });
  });

  router.post('/source', upload.single('file'), async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Create ListSource request', uid: auth.uid });

    const firebaseLib = new FirebaseLibrary({ auth });
    const notificationProvider = new NotificationProvider({
      auth,
      libraries: { firebase: firebaseLib },
      type: NotificationProviderType.Firebase
    });
    await notificationProvider.init();
    const notificationService = new NotificationService({ auth, provider: notificationProvider });

    const listService: ListService = new ListService({ auth, notificationService });
    const { listCompanyId, intentionSourceId, source } = req.body;
    const parsedSource = typeof source === 'string' ? JSON.parse(source) : source;
    return listService
      .createSource(listCompanyId, intentionSourceId, parsedSource, req.file)
      .then((result) => {
        logger.info({
          msg: 'Created ListSource',
          sourceResult: result.sourceResult
            ? {
              success: result.sourceResult.success,
              totalProcessed: result.sourceResult.totalProcessed,
              successCount: result.sourceResult.successCount,
              failureCount: result.sourceResult.failureCount,
              skippedCount: result.sourceResult.skippedCount
            }
            : null
        });
        return res.status(201).json({
          createdListId: result.createdList?.id,
          listSource: result.createdSource,
          sourceResult: result.sourceResult
        });
      })
      .catch((err) => {
        logger.error({ msg: 'Create ListSource error', err });
        return res.status(500).json(new ServerError('Unable to create ListSource'));
      });
  });

  router.delete('/:listCompanyId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { listCompanyId }
    }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Delete ListCompany request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });

    return listService
      .deleteListCompany(listCompanyId)
      .then((listCompany) => {
        logger.info({ msg: 'Delete ListCompany' });
        return res.status(200).json(listCompany);
      })
      .catch((err) => {
        logger.error({ msg: 'Delete ListCompany error', err });
        return res.status(500).json(new ServerError('Unable to delete ListCompany'));
      });
  });

  router.patch('/:listCompanyId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { listCompanyId }
    }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Update ListCompany request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });

    return listService
      .updateListCompany(listCompanyId, req.body)
      .then((listCompany) => {
        logger.info({ msg: 'Update ListCompany' });
        return res.status(200).json(listCompany);
      })
      .catch((err) => {
        logger.error({ msg: 'Update ListCompany error', err });
        return res.status(500).json(new ServerError('Unable to update ListCompany'));
      });
  });

  router.post('/idea/delete', async (req: IAcqRequest, res: Response) => {
    const { auth, body }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Delete ListCompanyIdea request', uid: auth.uid });

    const listService: ListService = new ListService({ auth });

    if (body.ideaFilters && Object.keys(body.ideaFilters).length > 0) {
      body.listCompanyIdeaIds = await listService.getListCompanyIdeaIdsByFilters(body.ideaFilters);
    }

    return listService
      .deleteListCompanyIdea(body)
      .then((listCompany) => {
        logger.info({ msg: 'Delete ListCompanyIdea' });
        return res.status(200).json(listCompany);
      })
      .catch((err) => {
        logger.error({ msg: 'Delete ListCompanyIdea error', err });
        return res.status(500).json(new ServerError('Unable to delete ListCompanyIdea'));
      });
  });

  router.post('/:listCompanyId/complete', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { listCompanyId }
    }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Complete list company request', uid: auth.uid });
    const listService: ListService = new ListService({ auth });

    return listService
      .completeListCompany(listCompanyId)
      .then((listCompany) => {
        logger.info({ msg: 'Complete ListCompany' });
        return res.status(200).json(listCompany);
      })
      .catch((err) => {
        logger.error({ msg: 'Complete ListCompany error', err });
        return res.status(500).json(new ServerError('Unable to complete ListCompany'));
      });
  });

  router.post('/execute', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { typeId, entityType, entityId }
    }: IAcqRequest = req;
    logger.info({ msg: 'Execute list company request', typeId, entityType, entityId });

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    if (!uuidValidate(entityId)) {
      return res.status(400).json(new BadRequestError('Invalid list company id'));
    }

    const firebaseLib = new FirebaseLibrary({ auth });
    const notificationProvider = new NotificationProvider({
      auth,
      libraries: { firebase: firebaseLib },
      type: NotificationProviderType.Firebase
    });
    await notificationProvider.init();
    const notificationService = new NotificationService({ auth, provider: notificationProvider });
    const listCompanyService: ListService = new ListService({ auth, notificationService });

    return listCompanyService
      .execute({ typeId, entityType: entityType as EntityType, entityId })
      .then((execution: { entityId: string; executionId: string }) => {
        logger.info({ msg: 'List company execution queued.', executions: execution });
        return res.json([execution]);
      })
      .catch((err: Error) => {
        logger.error({ msg: 'Unable to execute list company', err });
        return res.status(500).json(new ServerError('Unable to execute list company'));
      });
  });

  router.post('/auto-complete', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { query, enableOceanIoSuggestions }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Auto complete list company request', uid: auth.uid });
    const listService: ListService = new ListService({ auth });

    return listService
      .getAutoCompleteSuggestions({ query, enableOceanIoSuggestions })
      .then((listCompanies) => {
        logger.info({ msg: 'Auto complete ListCompany', listCompanies });
        return res.status(200).json(listCompanies);
      })
      .catch((err) => {
        logger.error({ msg: 'Auto complete ListCompany error', err });
        return res.status(500).json(new ServerError('Unable to auto complete ListCompany'));
      });
  });

  router.post('/ocean/preview', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { domains }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Ocean preview list company request', uid: auth.uid });
    const listService: ListService = new ListService({ auth });

    return listService
      .getOceanPreview(domains)
      .then((listCompanies) => {
        logger.info({ msg: 'Ocean preview ListCompany', listCompanies });
        return res.status(200).json(listCompanies);
      })
      .catch((err) => {
        logger.error({ msg: 'Ocean preview ListCompany error', err });
        return res.status(500).json(new ServerError('Unable to ocean preview ListCompany'));
      });
  });

  router.post('/exa/preview', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { query }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Exa preview list company request', uid: auth.uid });
    const listService: ListService = new ListService({ auth });

    return listService
      .getExaPreview(query)
      .then((listCompanies) => {
        logger.info({ msg: 'Exa preview ListCompany', listCompanies });
        return res.status(200).json(listCompanies);
      })
      .catch((err) => {
        logger.error({ msg: 'Exa preview ListCompany error', err });
        return res.status(500).json(new ServerError('Unable to Exa preview ListCompany'));
      });
  });

  router.post('/source-scrub/preview', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { domains, maxResultCount }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'SourceScrub preview list company request', uid: auth.uid });
    const listService: ListService = new ListService({ auth });

    return listService
      .getSourceScrubPreview({ domains, maxResultCount })
      .then((listCompanies) => {
        logger.info({ msg: 'SourceScrub preview ListCompany', listCompanies });
        return res.status(200).json(listCompanies);
      })
      .catch((err) => {
        logger.error({ msg: 'SourceScrub preview ListCompany error', err });
        return res.status(500).json(new ServerError('Unable to SourceScrub preview ListCompany'));
      });
  });

  router.post('/source-scrub-sources/preview', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { filters }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'SourceScrub sources preview list company request', uid: auth.uid });
    const listService: ListService = new ListService({ auth });

    return listService
      .getSourceScrubSourcesPreview(filters)
      .then((listCompanies) => {
        logger.info({ msg: 'SourceScrub sources preview ListCompany', listCompanies });
        return res.status(200).json(listCompanies);
      })
      .catch((err) => {
        logger.error({ msg: 'SourceScrub sources preview ListCompany error', err });
        return res.status(500).json(new ServerError('Unable to SourceScrub preview ListCompany'));
      });
  });

  router.post('/source-scrub-companies/preview', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { filters, maxResultCount }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'SourceScrub companies preview list company request', uid: auth.uid });
    const listService: ListService = new ListService({ auth });

    return listService
      .getSourceScrubCompaniesPreview({ filters, maxResultCount })
      .then((listCompanies) => {
        logger.info({ msg: 'SourceScrub companies preview ListCompany', listCompanies });
        return res.status(200).json(listCompanies);
      })
      .catch((err) => {
        logger.error({ msg: 'SourceScrub companies preview ListCompany error', err });
        return res.status(500).json(new ServerError('Unable to SourceScrub preview ListCompany'));
      });
  });

  return router;
};
