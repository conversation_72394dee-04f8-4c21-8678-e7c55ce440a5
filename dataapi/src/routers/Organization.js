import { Router } from 'express';
import { APIResponse, APIError } from '../lib';
import { logger } from '../logger';
import { OrganizationService, EmailService, NotificationService } from '../services';
import { NotificationProvider, NotificationProviderType } from '../lib/notification/Notification';
import { FirebaseLibrary } from '../lib/notification/Firebase';

export default () => {
  const router = new Router({ mergeParams: true });

  router.get('/', async ({ auth }, res) => {
    logger.info({ request: 'Get my organization', email: auth.email });

    const organizationService = new OrganizationService({ auth });
    const orgId = auth.oid;

    if (!orgId) {
      return res.status(401).json(new APIError('Unauthorized'));
    }
    const isAdmin = auth.scope.includes('admin');

    return organizationService
      .getOrganizationById({ orgId, admin: isAdmin })
      .then((result) => {
        if (!result) {
          logger.error({ error: 'Unable to get organization' });
          return res.status(500).json(new APIError('Unable to get organization'));
        }
        return res.status(200).json(result);
      })
      .catch((err) => {
        logger.error({ error: 'Get organization failed', msg: err.toString() });
        return res.status(500).json(new APIError('Get organization failed'));
      });
  });

  router.put('/role', async (req, res) => {
    const {
      auth,
      body: { userId, role }
    } = req;
    const firebaseLib = new FirebaseLibrary({ auth });
    const notificationProvider = new NotificationProvider({
      auth,
      libraries: { firebase: firebaseLib },
      type: NotificationProviderType.Firebase
    });
    await notificationProvider.init();
    const notificationService = new NotificationService({ auth, provider: notificationProvider });
    const service = new OrganizationService({ auth, notificationService });

    if (!auth.scope.includes('admin')) {
      return res.status(401).json(new APIError('Unauthorized'));
    }

    return service
      .setUserRole({ userId, role })
      .then((result) => {
        return res.json(new APIResponse(result));
      })
      .catch((err) => {
        return res.status(400).json(
          new APIError('Unable to update role', {
            details: err.toString()
          })
        );
      });
  });

  router.put('/userStatus', async (req, res) => {
    const {
      auth,
      body: { userId, status }
    } = req;

    const firebaseLib = new FirebaseLibrary({ auth });
    const notificationProvider = new NotificationProvider({
      auth,
      libraries: { firebase: firebaseLib },
      type: NotificationProviderType.Firebase
    });
    await notificationProvider.init();
    const notificationService = new NotificationService({ auth, provider: notificationProvider });
    const service = new OrganizationService({ auth, notificationService });

    if (!auth.scope.includes('admin')) {
      return res.status(401).json(new APIError('Unauthorized'));
    }

    return service
      .setUserStatus({ userId, status })
      .then((result) => {
        return res.json(new APIResponse(result));
      })
      .catch((err) => {
        return res.status(400).json(
          new APIError('Unable to update user status', {
            details: err.toString()
          })
        );
      });
  });

  router.put('/useSystemLLMSetting', async (req, res) => {
    const {
      auth,
      body: { id, status }
    } = req;

    if (!auth.scope.includes('admin')) {
      return res.status(401).json(new APIError('Unauthorized'));
    }

    const service = new OrganizationService({ auth });

    return service
      .setUseSystemLLMSetting({ id, status })
      .then((result) => {
        return res.json(new APIResponse(result));
      })
      .catch((err) => {
        return res.status(400).json(
          new APIError('Unable to update system default llm setting', {
            details: err.toString()
          })
        );
      });
  });

  router.put('/llmSetting', async (req, res) => {
    const {
      auth,
      body: { settings }
    } = req;

    if (!auth.scope.includes('admin')) {
      return res.status(401).json(new APIError('Unauthorized'));
    }

    const service = new OrganizationService({ auth });

    return service
      .updateLLMSettings(settings)
      .then((result) => {
        return res.json(new APIResponse(result));
      })
      .catch((err) => {
        return res.status(400).json(
          new APIError('Unable to update llm settings', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/active-crm', async (req, res) => {
    const { auth } = req;
    const service = new OrganizationService({ auth });

    return service
      .getActiveCrm()
      .then((result) => {
        return res.status(200).json(new APIResponse(result));
      })
      .catch((err) => {
        logger.error({ error: 'Unable to get active crm', msg: err.toString() });
        return res.status(400).json(
          new APIError('Unable to get crm type', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/integrations', async (req, res) => {
    const { auth } = req;

    const service = new OrganizationService({ auth });

    return service
      .getIntegrations()
      .then((result) => {
        return res.json(new APIResponse(result));
      })
      .catch((err) => {
        return res.status(400).json(
          new APIError('Unable to get integrations', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/system-roles', async ({ auth, params: { orgId } }, res) => {
    logger.info({ request: 'Get my organization', orgId, email: auth.email });

    const org = new OrganizationService({ auth });

    return org
      .getSystemRoles()
      .then((result) => {
        if (!result) {
          logger.error({ error: 'Unable to get system roles' });
          return res.status(400).json(new APIError('Unable to get system roles'));
        }
        return res.status(200).json(result);
      })
      .catch((err) => {
        logger.error({ error: 'Get system roles failed', msg: err.toString() });
        return res.status(500).json(new APIError('Get system roles failed'));
      });
  });

  router.post('/create-invitation', async ({ auth, body: { users } }, res) => {
    const service = new OrganizationService({ auth });

    service
      .createInvitations(users)
      .then((result) => {
        if (!result) {
          throw new Error('No result found in service response');
        }
        res.json(new APIResponse({ users: result }));
      })
      .catch((err) => {
        return res.status(422).json(
          new APIError(err.toString(), {
            details: err.toString()
          })
        );
      });
  });

  router.post('/update-user', async (req, res) => {
    try {
      const user = req.body.user;
      const auth = req.auth;

      const org = new OrganizationService({ auth });
      return org
        .updateUser(user)
        .then((result) => {
          return res.json(new APIResponse({ result }));
        })
        .catch((error) => {
          return res.status(400).json(
            new APIError(error.details, {
              code: error.code,
              details: error.details
            })
          );
        });
    } catch (e) {
      return res.status(500).json(e);
    }
  });

  router.delete('/user/:userId', async (req, res) => {
    const {
      auth,
      params: { userId }
    } = req;

    if (!auth.scope.includes('admin')) {
      return res.status(401).json(new APIError('Unauthorized'));
    }

    const firebaseLib = new FirebaseLibrary({ auth });
    const notificationProvider = new NotificationProvider({
      auth,
      libraries: { firebase: firebaseLib },
      type: NotificationProviderType.Firebase
    });
    await notificationProvider.init();
    const notificationService = new NotificationService({ auth, provider: notificationProvider });
    const organizationService = new OrganizationService({ auth, notificationService });

    return organizationService
      .deleteUser({ userId })
      .then((result) => {
        return res.status(200).json(result);
      })
      .catch((error) => {
        return res.status(500).json(
          new APIError(error.details, {
            code: error.code,
            details: error.details
          })
        );
      });
  });

  router.post('/update-invitation', async (req, res) => {
    const {
      auth,
      body: { users }
    } = req;
    const service = new OrganizationService({ auth });

    return service
      .updateInvitation(users)
      .then((result) => {
        return res.json(new APIResponse({ result }));
      })
      .catch((err) => {
        return res.status(400).json(
          new APIError('Unable to update invitation', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/integration', async (req, res) => {
    const { auth, body: data } = req;
    logger.info({ msg: 'New integration request', user: auth.uid, data });
    const service = new OrganizationService({ auth });

    return service
      .createIntegration(data)
      .then((result) => {
        return res.status(201).json(new APIResponse(result));
      })
      .catch((err) => {
        const errorCode = err.code ? err.code : 500;
        return res.status(errorCode).json(
          new APIError('Unable to add integration', {
            details: err.toString(),
            code: errorCode
          })
        );
      });
  });

  router.get('/invitations', async ({ auth }, res) => {
    const service = new OrganizationService({ auth });
    return service
      .getInvitations()
      .then((invitationResult) => {
        return res.json(invitationResult);
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Server Error', {
            code: 500,
            details: err.toString()
          })
        );
      });
  });

  router.post('/invite-users', async ({ auth, body: { users, organization } }, res) => {
    const emailService = new EmailService({ auth });
    const service = new OrganizationService({ auth, emailService });
    return service
      .inviteUsersWithEmail(users, organization)
      .then((result) => {
        return res.json(
          new APIResponse({
            invitedUsers: result
          })
        );
      })
      .catch((error) => {
        const errorCode = error.code ? error.code : 500;
        return res.status(errorCode).json(
          new APIError('Server Error', {
            code: errorCode,
            details: error.toString()
          })
        );
      });
  });

  router.delete('/invitation/:userId', async ({ auth, params: { userId } }, res) => {
    logger.info({ msg: 'cancel invitation request received', userId });
    return new OrganizationService({ auth })
      .cancelInvitation(userId)
      .then((r) => {
        return res.json({ deletedAt: r.deletedAt });
      })
      .catch((err) => {
        throw err;
      });
  });

  router.get('/:orgId', async ({ auth, params: { orgId } }, res) => {
    try {
      logger.info({ request: 'Get my organization', orgId, email: auth.email });

      if (orgId !== auth.oid) {
        throw new Error('Invalid organization ID!', orgId, auth);
      }

      const org = new OrganizationService({ auth });

      return org
        .getMyOrganization()
        .then((result) => {
          if (!result) {
            logger.error({ error: 'Unable to get organization' });
            return res.status(400).json(new APIError('Unable to get organization'));
          }
          return res.json(new APIResponse(result));
        })
        .catch((err) => {
          logger.error({ error: 'Get organization failed', msg: err.toString() });
        });
    } catch (err) {
      return res.status(500).json(
        new APIError('Get organization failed', {
          details: err.toString()
        })
      );
    }
  });

  return router;
};
