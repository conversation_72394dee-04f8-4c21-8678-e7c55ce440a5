import { Router, Response } from 'express';
import { APIResponse, APIError } from '../lib';
import { logger } from '../logger';
import { TokenService, EmailService } from '../services';
import { isValidPassword } from '../lib/utils';
import { IAcqRequest, IAuth } from '../types/Common';
import { JwtPayload } from 'jsonwebtoken';
import jwt from 'jsonwebtoken';
import DeviceDetector from 'device-detector-js';
import { isValidEmail, isValidInviteHash, isValidPhoneNumber } from '../lib/utils';
import { InvitationService } from '../services';
import config from '../config';
import AuthService from '../services/AuthService';

export default () => {
  const router: Router = Router({ mergeParams: true });

  router.post('/signin', async (req: IAcqRequest, res: Response) => {
    const { email, password, appSource } = req.body;

    if (!isValidEmail(email) || !isValidPassword(password)) {
      return res.status(403).json(
        new APIError('No user or invalid credentials', {
          details: 'User not found or invalid credentials'
        })
      );
    }

    // Validate app source
    const validAppSources = ['console', 'admin'];
    const detectedAppSource = appSource || 'console'; // Default to console for backward compatibility

    if (!validAppSources.includes(detectedAppSource)) {
      logger.info({ msg: 'Invalid application source', appSource: detectedAppSource });
      return res.status(400).json(
        new APIError('Invalid application source', {
          details: 'Application source must be either console or admin'
        })
      );
    }

    const service = new AuthService();
    return service
      .authenticate({ email, password })
      .then(async (user) => {
        if (!user) {
          throw new Error('No user found with given credentials!');
        }

        // Check super admin permission for admin app access
        if (detectedAppSource === 'admin') {
          const hasAdminAccess = await service.checkSuperAdminAccess(user.id);
          if (!hasAdminAccess) {
            logger.info({ msg: 'Super admin access required for this application', user: { ...user, appSource: detectedAppSource } });
            return res.status(403).json(
              new APIError('Access denied', {
                details: 'Super admin access required for this application'
              })
            );
          }
        }

        const deviceDetector = new DeviceDetector();
        const userAgent = req.headers['user-agent'];
        (user as typeof user & { ip?: string }).ip = req.ip;
        (user as typeof user & { device }).device = deviceDetector.parse(userAgent || '');
        (user as typeof user & { appSource?: string }).appSource = detectedAppSource;

        logger.info({ msg: 'Authenticated.', user: { ...user, appSource: detectedAppSource } });

        return service
          .authorize(user, detectedAppSource)
          .then((result) => {
            logger.info({ msg: 'Authorized.', result });
            const { accessToken, refreshToken } = result;
            res.json(
              new APIResponse({
                accessToken,
                refreshToken,
                expiresIn: config.jwtExpAccess,
                refreshExpiresIn: config.jwtExpRefresh,
                appSource: detectedAppSource
              })
            );
          })
          .catch((err) => {
            logger.error({ msg: 'Authorization error!', err });
            res.status(err.code || 400).json(
              new APIError('Unable to login', {
                details: err.toString()
              })
            );
          });
      })
      .catch((err) => {
        res.status(err.code || 400).json(
          new APIError('Unable to login!', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/refresh', async (req: IAcqRequest, res: Response) => {
    try {
      // TODO: we are checking if it is a valid token,
      // however we need to validate the client device, aud and iss of the signed token..

      const appSource = req.body?.appSource || 'console'; // Get app source from request

      // Extract refresh token from cookies or body
      let refreshToken = req.body?.refreshToken;
      if (!refreshToken && req.headers.cookie) {
        const cookies = req.headers.cookie.split(';').reduce((acc, cookie) => {
          const [key, value] = cookie.trim().split('=');
          acc[key] = value;
          return acc;
        }, {});

        // Try app-specific cookie names
        refreshToken = cookies[`${appSource}_refreshToken`] || cookies['refreshToken'];
      }

      if (!refreshToken) {
        throw new Error('No refresh token provided');
      }

      const decoded = jwt.decode(refreshToken) as JwtPayload;
      const userId = decoded?.uid;

      if (!userId) {
        throw new Error('Invalid refresh token');
      }

      const service = new AuthService();
      const user = await service.isUserExistsById({ userId: userId });

      if (!user) {
        throw new Error('User not found');
      }

      const data: { ip?: string; device?: any; [key: string]: any } = await TokenService.verifyRefreshToken(refreshToken, user?.uniqueKey, appSource);
      const deviceDetector = new DeviceDetector();
      data.ip = req.ip;
      data.device = deviceDetector.parse(req.headers['user-agent'] || '');
      data.appSource = appSource;

      const { accessToken } = await service.refreshAccessToken(data, appSource);

      res.status(200).json(
        new APIResponse({
          accessToken,
          refreshToken,
          appSource
        })
      );
    } catch (e: any) {
      logger.error({ msg: 'Refresh token error', error: e.message, stack: e.stack });
      res.status(e.code || 400).json(new APIError('Token refresh failed', { details: e.message }));
    }
  });

  router.post('/check-invitation', async ({ body: { hash } }, res) => {
    logger.info({ msg: 'Check invitation request received' });

    const service = new InvitationService();
    return service
      .checkInvitation(hash)
      .then(({ email, hash }) => {
        res.json(new APIResponse({ email, valid: !!hash }));
      })
      .catch((err) => {
        res.status(401).json(
          new APIError('Unable to validate hash', {
            valid: false,
            details: err.message
          })
        );
      });
  });

  router.post('/accept-invitation', async (req: IAcqRequest, res: Response) => {
    logger.info({ msg: 'Accept invitation request received' });
    const service = new InvitationService();

    const { user } = req.body;
    const { hash = null } = user;

    return service
      .checkInvitation(hash)
      .then(async (_checkResult) => {
        if (!isValidInviteHash(user.hash)) {
          throw new Error('Invalid hash');
        }

        if (user.password !== user.password2) {
          throw new Error('Passwords does not match');
        }

        delete user.password2;

        if (!isValidEmail(user.email)) {
          logger.info({ msg: 'No user found for this invitation code' });
          throw new Error('No user found for this invitation code');
        }

        if (user.firstName.length < 3 || user.lastName.length < 3) {
          logger.info({ msg: 'Invalid user information' });
          throw new Error('Invalid user information');
        }

        if (!isValidPhoneNumber(user.phone)) {
          logger.info({ msg: 'Invalid user information' });
          throw new Error('Invalid phone number');
        }

        return service.acceptInvitation(user).then((result) => {
          logger.info({ msg: 'Accepting invitation' });
          // If everything completed then we are deleting the invitation by setting deletedAt.
          if (!result.deletedAt || !result.id) {
            throw new Error('Accepting invitation failed');
          }
          res.status(201).json(new APIResponse({ success: true }));
        });
      })
      .catch((err) => {
        res.status(400).json(
          new APIError('Error in accept invitation', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/forgotpassword', async (req: IAcqRequest, res) => {
    const { email } = req.body;

    if (!isValidEmail(email)) {
      res.status(422).json(new APIError('Invaild email!'));
    }

    const service = new AuthService();

    const getUserInfo = await service.getUserByEmail(email);

    if (!getUserInfo || !getUserInfo.role) {
      return res.status(404).json(
        new APIError('User not found', {
          msg: 'User not found'
        })
      );
    }

    if (!req.auth) {
      req.auth = {} as IAuth;
      req.auth.uid = getUserInfo.id;
      req.auth.oid = getUserInfo.role.orgId;
    }

    const emailService = new EmailService({ auth: req.auth });
    service.setEmailService(emailService);

    service
      .createResetPasswordLink(email)
      .then((result) => {
        res.status(201).json(
          new APIResponse({
            code: 201,
            details: 'E-mail sent',
            result
          })
        );
      })
      .catch((err) => {
        res.status(err.code).json(new APIError('', err));
      });
  });

  router.post('/check-hash', async (req: IAcqRequest, res: Response) => {
    const { hash } = req.body;

    if (!isValidInviteHash(hash)) {
      res.status(500).json(
        new APIResponse({
          valid: false
        })
      );
    }

    logger.info({ msg: 'Check Hash request received' });

    const service = new AuthService();

    service
      .checkResetPasswordHash(hash)
      .then((_checkResult) => {
        res.json(
          new APIResponse({
            valid: true
          })
        );
      })
      .catch((_checkError) => {
        res.status(500).json(
          new APIResponse({
            valid: false
          })
        );
      });
  });
  router.put('/resetpassword', async (req: IAcqRequest, res: Response) => {
    const { hash, password, password2 } = req.body;

    logger.info({
      msg: 'Trying to change password with reset link',
      hash: hash
    });

    if (!password || !isValidPassword(password)) {
      return res.status(406).json(
        new APIError('', {
          code: 406,
          details: 'Password must be longer than 5 char, contains at least 1 capital, 1 lower case letter and 1 number'
        })
      );
    }

    if (password !== password2) {
      return res.status(406).json(
        new APIError('', {
          code: 406,
          details: '[2] Passwords does not match'
        })
      );
    }

    const service = new AuthService();
    service
      .checkResetPasswordHash(hash)
      .then((checkResult) => {
        const uid = checkResult.id;
        service
          .setPassword(uid, password)
          .then(() => {
            res.status(200).json(
              new APIResponse({
                details: 'Password has changed'
              })
            );
          })
          .catch(() => {
            res.status(500).json(
              new APIError('', {
                code: 500,
                details: 'Server error'
              })
            );
          });
      })
      .catch((checkError) => {
        res.status(500).json(checkError);
      });
  });

  router.post('/revoke', async (req: IAcqRequest, res: Response) => {
    logger.info({ msg: 'Revoke token request received', pid: process.pid });
    const { userId } = req.body;
    try {
      const service = new AuthService();
      await service.revokeUserTokens({ userId: userId });

      res.status(200).json(new APIResponse({ success: true }));
    } catch (error: any) {
      logger.error(error);
      res.status(400).json(
        new APIError('Bad Request', {
          valid: false,
          name: 'Bad Request',
          details: error.toString()
        })
      );
    }
  });

  return router;
};
