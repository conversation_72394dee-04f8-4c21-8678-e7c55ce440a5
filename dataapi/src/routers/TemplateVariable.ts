import { TemplateVariables } from '@prisma/client';
import { Response, Router } from 'express';
import { APIError } from '../lib';
import { UnauthorizedError } from '../lib/errors';
import { logger } from '../logger';
import { TemplateVariableService } from '../services';
import { IAcqRequest } from '../types/Common';

export default () => {
  const router: Router = Router({ mergeParams: true });

  router.get('/', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Read pseduo variables request', uid: auth?.uid });

    const templateVariablesService: TemplateVariableService = new TemplateVariableService({ auth });

    return templateVariablesService
      .getAllPseudoVariables()
      .then((variables?: TemplateVariables[]) => {
        return res.json({ data: variables });
      })
      .catch((err) => {
        logger.error({ msg: 'Read pseduo variables error', err });
        return res.status(500).json(new APIError('Unable to read pseduo variable'));
      });
  });

  return router;
};
