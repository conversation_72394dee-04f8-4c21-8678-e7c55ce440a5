import { Router, Response } from 'express';
import { IAcqRequest } from '../types/Common';
import { ServerError, UnauthorizedError } from '../lib/errors';
import { logger } from '../logger';
import { IntegrationService } from '../services';

export default () => {
  const router = Router({ mergeParams: true });

  router.get('/', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;

    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Get integrations request', uid: auth.uid });

    const integration: IntegrationService = new IntegrationService({ auth });

    return integration
      .getIntegrations()
      .then((integrations) => {
        logger.info({ msg: 'Got integration list' });
        return res.status(200).json(integrations);
      })
      .catch((err) => {
        logger.error({ msg: 'Get integration list error', err });
        return res.status(500).json(new ServerError('Unable to read integrations'));
      });
  });

  return router;
};
