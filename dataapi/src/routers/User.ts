import { Router, Response } from 'express';
import { APIResponse, APIError, Office365 } from '../lib';
import { logger } from '../logger';
import { Office365Service, TokenService, UserIntegration, UserService } from '../services';
import { extractReqToken, isValidPassword } from '../lib/utils';
import { IAcqRequest } from '../types/Common';
import { UnauthorizedError } from '../lib/errors';
import AuthService from '../services/AuthService';
import { IUserTableLayout } from '../types/User';
import { EntityType } from '@prisma/client';

export default () => {
  const router: Router = Router({ mergeParams: true });

  router.post('/verify', async (req: IAcqRequest, res: Response) => {
    try {
      const token = extractReqToken(req);
      const appSource = TokenService.detectAppSource(req);

      const isValid = await TokenService.verifyAccessToken(token, appSource);

      if (!isValid) {
        throw new Error('Token validation failed');
      }

      logger.info({ msg: 'Token verified successfully', appSource, isValid });
      res.status(200).json(new APIResponse({ valid: true }));
    } catch (err: any) {
      logger.info({ msg: 'Unauthorized request, missing or invalid token.' });
      res.status(401).json(
        new APIError('Invalid token', {
          valid: false,
          name: 'InvalidToken',
          details: err.toString()
        })
      );
    }
  });

  router.get('/', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    const user = new UserService({ auth });

    return user
      .getUserData()
      .then((user) => {
        logger.debug({ msg: 'Got user data', user });
        return res.status(200).json(user);
      })
      .catch((error) => {
        logger.error({ msg: 'Error getting user data', error });
        return res.status(500).json(new APIError('Unable to read user data', { details: error.toString() }));
      });
  });

  router.put('/settings', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { settings, calendar }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'Set user settings request', user: auth.uid, settings, calendar });

    const user = new UserService({ auth });

    return user
      .setUserData({ settings, calendar })
      .then((updated) => {
        if (updated) {
          return res.status(200).json(true);
        }
        logger.error({ msg: 'User data set failed' });
        return res.status(500).json(false);
      })
      .catch((error) => {
        logger.error({ msg: 'Error setting user settings data', error });
        return res.status(500).json(new APIError('Unable to set user settings data', { details: error.toString() }));
      });
  });

  router.get('/integration', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Read integrations request', user: auth.uid });

    const user = new UserIntegration(auth);

    return user
      .getIntegrations()
      .then((integrations) => {
        logger.info({ msg: 'Integrations', integrations });
        return res.json(new APIResponse({ integrations }));
      })
      .catch((err) => {
        logger.error({ msg: 'Fetch user integration error', err });
        return res.status(500).json(
          new APIError('Unable to read integrations', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/integration', async (req: IAcqRequest, res: Response) => {
    const { auth, body: data }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    logger.info({ msg: 'New integration request', data });

    const user = new UserIntegration(auth);

    return user
      .createIntegration(data)
      .then((result) => {
        return res.status(201).json(new APIResponse(result));
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to add integration', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/o365-integration', async (req: IAcqRequest, res: Response) => {
    const { auth, body: data } = req;
    logger.info({ msg: 'New integration request', data });

    const user = new UserIntegration(auth);

    return user
      .extractO365Integration(data)
      .then(async (parsedData) => {
        logger.info({ msg: 'Office365 login response parsed' });
        return user
          .createIntegration(parsedData)
          .then((result) => {
            return res.status(201).json(new APIResponse(result));
          })
          .catch((err) => {
            return res.status(500).json(
              new APIError('Unable to add integration', {
                details: err.message
              })
            );
          });
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError(err, {
            details: err.message
          })
        );
      });
  });

  router.delete('/integration/:id', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { id: integrationId }
    } = req;
    logger.info({ msg: 'Remove integration request', integrationId });

    const user = new UserIntegration(auth);

    return user
      .removeIntegration(integrationId)
      .then((result) => {
        return res.json(new APIResponse(result));
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to remove integration', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/read365contacts', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Read Office365 contacts request', user: auth.uid });

    const office365 = new Office365({ auth });
    return office365
      .validateToken()
      .then(async (accessToken) => {
        if (!accessToken) {
          throw new Error('Cannot validate access token');
        }
        const office365Service = new Office365Service({ accessToken });
        return office365Service
          .readContacts()
          .then((result) => {
            return res.json(new APIResponse(result));
          })
          .catch((err) => {
            return res.status(500).json(
              new APIError('Unable to get Office365 contacts', {
                details: err.toString()
              })
            );
          });
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to read Office365 token', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/read365calendar', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    logger.info({ msg: 'Read Office365 calendar request', user: auth.uid });

    const office365 = new Office365({ auth });
    return office365
      .validateToken()
      .then(async (accessToken) => {
        if (!accessToken) {
          throw new Error('Cannot validate access token');
        }
        const office365Service = new Office365Service({ accessToken });
        return office365Service
          .readCalendar()
          .then((result) => {
            return res.json(new APIResponse(result));
          })
          .catch((err) => {
            return res.status(500).json(
              new APIError('Unable to get Office365 calendar', {
                details: err.toString()
              })
            );
          });
      })
      .catch((err) => {
        return res.status(500).json(
          new APIError('Unable to read Office365 token', {
            details: err.toString()
          })
        );
      });
  });

  router.put('/changepassword', async (req: IAcqRequest, res: Response) => {
    const { auth }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    const { uid, email } = auth;
    const { currentPassword, newPassword, confirmPassword } = req.body;

    logger.info({
      msg: 'Change password request received',
      userId: uid
    });

    if (!currentPassword || !newPassword || !confirmPassword) {
      return res.status(406).json(
        new APIError('Invalid form', {
          code: 406,
          details: 'currentPassword',
          msg: 'Invalid form'
        })
      );
    }

    if (currentPassword === newPassword || currentPassword === confirmPassword) {
      return res.status(406).json(
        new APIError('Current and new password cannot be the same', {
          code: 406,
          details: 'newPassword',
          msg: 'Current and new password cannot be the same'
        })
      );
    }

    if (!newPassword || !isValidPassword(newPassword)) {
      return res.status(406).json(
        new APIError('Password must be more longer than 5 char, contains at least 1 number, 1 uppercase, 1 lowercase character', {
          code: 406,
          details: 'Please use stronger password',
          msg: 'Password must be more longer than 5 char, contains at least 1 number, 1 uppercase, 1 lowercase character'
        })
      );
    }

    if (newPassword !== confirmPassword) {
      return res.status(406).json(
        new APIError('Passwords do not match', {
          code: 406,
          details: 'confirmPassword',
          msg: 'Passwords do not match'
        })
      );
    }

    const service = new AuthService();
    return service
      .authenticate({ password: currentPassword, email })
      .then(async (_authResult) => {
        return service
          .setPassword(uid, newPassword)
          .then((_setResult) => {
            return res.status(200).json(
              new APIResponse({
                details: 'Password has changed'
              })
            );
          })
          .catch((setError) => {
            return res.status(500).json(setError);
          });
      })
      .catch((_authError) => {
        return res.status(401).json(
          new APIError('Current password is not correct', {
            code: 401,
            msg: 'Current password is not correct',
            details: 'currentPassword'
          })
        );
      });
  });

  router.get('/timezone', async (req: IAcqRequest, res: Response) => {
    try {
      const { auth }: IAcqRequest = req;
      if (!auth) {
        return res.status(401).json(new UnauthorizedError('Unauthorized'));
      }
      const service = new UserService({ auth });
      return res.status(200).json(new APIResponse(await service.getTimezone()));
    } catch (err: any) {
      return res.status(400).json(
        new APIError('Unable to read timezone', {
          details: err.toString()
        })
      );
    }
  });

  router.put('/timezone', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      body: { timezone, weeklyHours, meetingLength }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service = new UserService({ auth });

    return service
      .setTimezone({ calendarTimezone: timezone, weeklyHours, meetingLength })
      .then((result) => {
        return res.json(new APIResponse(result));
      })
      .catch((err) => {
        return res.status(400).json(
          new APIError('Unable to set timezone', {
            details: err.toString()
          })
        );
      });
  });

  router.post('/user-preference', async (req: IAcqRequest, res: Response) => {
    logger.info({ msg: 'save user preference received' });
    const { auth, body }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }
    const service = new UserService({ auth });
    return service
      .upsertUserPreference(body)
      .then((userPreference) => {
        logger.info({ userPreference });
        res.status(201).json(new APIResponse(userPreference));
      })
      .catch((err) => {
        return res.status(400).json(
          new APIError('Unable to upsert user preferences', {
            details: err.toString()
          })
        );
      });
  });

  router.get('/user-preference/:key', async (req: IAcqRequest, res: Response) => {
    try {
      const {
        auth,
        params: { key }
      }: IAcqRequest = req;
      if (!auth) {
        return res.status(401).json(new UnauthorizedError('Unauthorized'));
      }
      const service = new UserService({ auth });

      const userPreference = await service.getUserPreference(key);
      res.status(201).json(new APIResponse(userPreference));
    } catch (e: any) {
      return res.status(400).json(
        new APIError('Unable to get user preferences', {
          details: e.toString()
        })
      );
    }
  });

  router.get('/tablelayout/:entityType', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { entityType }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service = new UserService({ auth });

    return service
      .getTableLayout({ entityType: entityType as EntityType })
      .then((layout: IUserTableLayout[]) => {
        return res.status(200).json(layout);
      })
      .catch((error: Error) => {
        return res.status(500).json(new APIError('Unable to get table layout', { details: error.toString() }));
      });
  });

  router.put('/tablelayout/:entityType', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { entityType },
      body: layout
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service = new UserService({ auth });

    return service
      .setTableLayout({ entityType: entityType as EntityType, layout })
      .then((layout: IUserTableLayout[]) => {
        return res.status(200).json(layout);
      })
      .catch((error: Error) => {
        return res.status(500).json(new APIError('Unable to set table layout', { details: error.toString() }));
      });
  });

  router.post('/tablelayout/:entityType', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { entityType },
      body: layout
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service = new UserService({ auth });

    return service
      .pushTableLayout({ entityType: entityType as EntityType, layout })
      .then((layout: IUserTableLayout[]) => {
        return res.status(201).json(layout);
      })
      .catch((error: Error) => {
        return res.status(500).json(new APIError('Unable to push table layout', { details: error.toString() }));
      });
  });

  router.delete('/tablelayout/:entityType/:itemId', async (req: IAcqRequest, res: Response) => {
    const {
      auth,
      params: { entityType, itemId }
    }: IAcqRequest = req;
    if (!auth) {
      return res.status(401).json(new UnauthorizedError('Unauthorized'));
    }

    const service = new UserService({ auth });

    return service
      .removeFromTableLayout({ entityType: entityType as EntityType, itemId })
      .then((layout: IUserTableLayout[]) => {
        return res.status(200).json(layout);
      })
      .catch((error: Error) => {
        return res.status(500).json(new APIError('Unable to push table layout', { details: error.toString() }));
      });
  });

  return router;
};
