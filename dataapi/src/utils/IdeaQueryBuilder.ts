import { ICompanyIdeaStatus } from '../types/ListCompanyIdea';

export class IdeaQueryBuilder {
  private whereConditions: string[] = [];
  private orderByClause: string = '';
  private scoringModelId: string = '';

  constructor(
    private orgId: string,
    private listCompanyId: string
  ) {
    this.addBaseConditions();
  }

  private addBaseConditions() {
    this.whereConditions.push(`lci.org_id = '${this.orgId}'`);
    this.whereConditions.push(`EXISTS (
      SELECT 1 FROM list_company_idea_source_mapping lcsim
      JOIN list_source ls ON lcsim.source_id = ls.id
      WHERE lcsim.idea_id = lci.id
      AND ls.list_company_id = '${this.listCompanyId}'
    )`);
    this.whereConditions.push(`EXISTS (
      SELECT 1 FROM list_company_idea_mapping lcim
      WHERE lcim.idea_id = lci.id
      AND lcim.list_company_id = '${this.listCompanyId}'
    )`);
  }

  addTextSearch(text?: string) {
    if (text) {
      this.whereConditions.push(`(lci.company_name ILIKE '%${text}%' OR lci.website ILIKE '%${text}%')`);
    }
  }

  addSourceFilter(listSourceIds?: string[]) {
    if (listSourceIds?.length) {
      this.whereConditions.push(`EXISTS (
        SELECT 1 FROM list_company_idea_source_mapping lcsim
        WHERE lcsim.idea_id = lci.id
        AND lcsim.source_id IN (${listSourceIds.map((id) => `'${id}'`).join(',')})
      )`);
    }
  }

  addShortlistFilter(inShortlist?: string[]) {
    if (inShortlist?.length) {
      const shortlistConditions = inShortlist
        .map(
          (val) =>
            `EXISTS (
            SELECT 1 FROM list_company_idea_mapping lcim
            WHERE lcim.idea_id = lci.id
            AND lcim.list_company_id = '${this.listCompanyId}'
            AND lcim.in_shortlist = ${val === 'true'}
          )`
        )
        .join(' OR ');
      this.whereConditions.push(`(${shortlistConditions})`);
    }
  }

  addStatusFilter(recordStatus?: ICompanyIdeaStatus[]) {
    if (recordStatus?.length) {
      const statusConditions = recordStatus
        .map((status) => {
          switch (status) {
            case ICompanyIdeaStatus.InAcqwired:
              return `lci.company_id IS NOT NULL AND NOT EXISTS (
                SELECT 1 FROM company c WHERE c.id = lci.company_id AND c.deleted_at IS NOT NULL
              )`;
            case ICompanyIdeaStatus.Invalid:
              return `(lci.company_name IS NULL OR lci.website IS NULL)`;
            case ICompanyIdeaStatus.Idea:
            default:
              return `(lci.company_id IS NULL OR EXISTS (
                SELECT 1 FROM company c WHERE c.id = lci.company_id AND c.deleted_at IS NOT NULL
              )) AND lci.company_name IS NOT NULL AND lci.website IS NOT NULL`;
          }
        })
        .join(' OR ');
      this.whereConditions.push(`(${statusConditions})`);
    }
  }

  addSorting(sortKey?: string, sortOrder?: string) {
    if (sortKey && sortKey.startsWith('scoringResults.')) {
      const [_, modelId] = sortKey.split('.');
      this.scoringModelId = modelId;
      this.orderByClause = `ORDER BY COALESCE(sr.total_score, -1) ${sortOrder?.toUpperCase() || 'ASC'}`;
    } else if (sortKey) {
      const sortKeyMap: { [key: string]: string } = {
        companyName: 'company_name',
        website: 'website'
      };
      const dbColumnName = sortKeyMap[sortKey] || sortKey;
      this.orderByClause = `ORDER BY lci.${dbColumnName} ${sortOrder?.toUpperCase() || 'ASC'}`;
    } else {
      this.orderByClause = 'ORDER BY lci.id DESC';
    }
  }

  buildCountQuery(): string {
    return `
      SELECT COUNT(*) as total
      FROM list_company_idea lci
      WHERE ${this.whereConditions.join(' AND ')}
    `;
  }

  buildDataQuery(pageSize: number, pageIndex: number): string {
    const offset = pageIndex * pageSize;
    return `
      SELECT lci.*${this.scoringModelId ? ', sr.total_score' : ''}
      FROM list_company_idea lci
      ${this.scoringModelId ? `LEFT JOIN scoring_result sr ON lci.id = sr.entity_id AND sr.scoring_model_id = '${this.scoringModelId}'` : ''}
      WHERE ${this.whereConditions.join(' AND ')}
      ${this.orderByClause}
      LIMIT ${pageSize} OFFSET ${offset}
    `;
  }
}
