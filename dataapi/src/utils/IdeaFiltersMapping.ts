import { ICompanyIdeaStatus, IListCompanyIdeaFilters } from "../types/ListCompanyIdea";

export const IdeaFiltersMapping = (filters: IListCompanyIdeaFilters) => {
  const {
    listCompanyId,
    text,
    inShortlist: rawInShortlist,
    recordStatus: rawRecordStatus,
    listSourceIds
  } = filters;

  let adaptedInShortlist: boolean | undefined = undefined;
  if (Array.isArray(rawInShortlist)) {
    if (rawInShortlist.length === 1 && typeof rawInShortlist[0] === 'string') {
      if (rawInShortlist[0].toLowerCase() === 'true') adaptedInShortlist = true;
      else if (rawInShortlist[0].toLowerCase() === 'false') adaptedInShortlist = false;
    } else if (rawInShortlist.length === 0) {
      // Empty array means no filter, so adaptedInShortlist remains undefined
    }
  } else if (typeof rawInShortlist === 'boolean') {
    adaptedInShortlist = rawInShortlist;
  }

  let adaptedRecordStatus: ICompanyIdeaStatus | undefined = undefined;
  if (Array.isArray(rawRecordStatus)) {
    if (rawRecordStatus.length === 1 && typeof rawRecordStatus[0] === 'string' && Object.values(ICompanyIdeaStatus).includes(rawRecordStatus[0] as ICompanyIdeaStatus)) {
      adaptedRecordStatus = rawRecordStatus[0] as ICompanyIdeaStatus;
    } else if (rawRecordStatus.length === 0) {
      // Empty array means no filter, so adaptedRecordStatus remains undefined
    }
  } else if (typeof rawRecordStatus === 'string' && Object.values(ICompanyIdeaStatus).includes(rawRecordStatus as ICompanyIdeaStatus)) {
    adaptedRecordStatus = rawRecordStatus as ICompanyIdeaStatus;
  }

  return {
    listCompanyId,
    text,
    inShortlist: adaptedInShortlist,
    recordStatus: adaptedRecordStatus,
    listSourceIds
  };
};
