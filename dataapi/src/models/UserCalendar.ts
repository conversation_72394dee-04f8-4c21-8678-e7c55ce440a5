import { Prisma } from '@prisma/client'; 
import { db } from '../database';

const UserCalendarExtension = Prisma.defineExtension({
  name: 'UserCalendar',
  model: {
    userCalendar: {
      createDefaultCalendar: async ({
        userId,
        orgId,
        active,
        weeklyHours
      }: {
        userId: string,
        orgId: string,
        active: boolean,
        weeklyHours: Array<{ available: number; startTime: string; endTime: string }>
      }, { tx }: { tx: any }) => {
        return (tx || db).userCalendar.create({
          data: {
            orgId, userId, active, weeklyHours
          }
        });
      }
    }
  }
});

export default UserCalendarExtension;

