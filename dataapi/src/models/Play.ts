import { IPlay, IPlaybook, IPlaybookFilters, IPlaybookResponse, IPlaybookUsers, ISinglePlay, ListPlaybookParams, IPlayInitialData } from '../types/Play';
import UnauthorizedError from '../lib/errors/Unauthorized';
import BadRequest from '../lib/errors/BadRequest';
import { db } from '../database';
import { LlmProvider, PlayStatus, Prisma } from '@prisma/client';

import { NotFoundError } from '../lib/errors';
import { PAGE_SIZE } from '../constants';
import { SortOrder } from '../types/Common';
import { logger } from '../logger';
interface IPlayDeleteParams {
  orgId: string;
  playId: string;
  isDraft?: boolean;
  tx?: any;
}

interface IPlayGetParams {
  orgId: string;
  playId: string;
  llmEngine?: LlmProvider;
}

const PlayExtension = Prisma.defineExtension({
  name: 'Play',
  model: {
    play: {
      deleteById: async ({ orgId, playId, isDraft, tx }: IPlayDeleteParams): Promise<boolean> => {
        const connection = tx || db;

        return connection.play
          .findFirst({
            where: { orgId, id: playId, isDraft },
            select: { id: true }
          })
          .then(async (play: { id: string } | null): Promise<boolean> => {
            if (!play) {
              throw new NotFoundError("Couldn't find Play");
            }

            return connection.play
              .delete({ where: { id: playId } })
              .then((): boolean => true)
              .catch((): boolean => false);
          });
      },
      listPlays: async ({ orgId }): Promise<IPlay[]> => {
        if (!orgId) {
          throw new UnauthorizedError('Invalid organization id.');
        }

        return db.play.findMany({
          where: { orgId, deletedAt: null, status: PlayStatus.published },
          select: {
            id: true,
            name: true,
            description: true,
            createdBy: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          }
        });
      },
      getPlaybookUsers: async ({ orgId }: { orgId: string }): Promise<IPlaybookUsers[]> => {
        if (!orgId) {
          throw new UnauthorizedError('Invalid organization id.');
        }
        const query = `
          SELECT DISTINCT u.id,p.acqwired_provided_plays,
          CASE
              WHEN p.acqwired_provided_plays AND EXISTS (
                  SELECT 1
                  FROM "org_user_role" our
                  JOIN "user_role" r ON our.role_id = r.id
                  WHERE our.user_id = u.id
                  AND r.role = 'admin'
              ) THEN o.name
              ELSE CONCAT(u.first_name, ' ', u.last_name)
          END as name
              FROM "play" p
              JOIN "user" u ON p.created_by_id = u.id
              LEFT JOIN "org_user_role" our ON u.id = our.user_id
              LEFT JOIN "user_role" r ON our.role_id = r.id
              LEFT JOIN "organization" o ON our.org_id = o.id
              WHERE p.org_id = $1::uuid
              ORDER BY u.id, name
          `;

        return await db.$queryRawUnsafe<IPlaybookUsers[]>(query, orgId);
      },
      getSortedPlayIds: (model: IPlaybookFilters, filterQuery: string): Promise<{ id: string }[]> => {
        const MappingDictionary = new Map<string, string>([
          ['playName', 'name'],
          ['createdBy', 'created_by_id'],
          ['createdAt', 'created_at']
        ]);

        if (filterQuery.length > 0) {
          filterQuery = ` WHERE ${filterQuery}`;
        }

        const sortOrder = model.sortOrder === SortOrder.DESC ? SortOrder.DESC : SortOrder.ASC;
        let sortQuery: string;

        if (!model.sortKey) {
          sortQuery = ` SELECT id, UPPER(fp.name) as sort_flag from filtered_plays fp `;
        } else if (model.sortKey === 'enrichmentsIncluded') {
          sortQuery = ` SELECT id, (select count(*) from play_task pt where pt.play_id = fp.id and pt.enrichment_id is not null)
          as sort_flag from filtered_plays fp `;
        } else if (model.sortKey === 'useCount') {
          sortQuery = ` SELECT id, (select count(*) from execution e where e.type_id = fp.id and e.type = 'play')
          as sort_flag from filtered_plays fp `;
        } else if (model.sortKey === 'playType') {
          sortQuery = ` SELECT id, (select c.llm_engine from play_config c where c.id = fp.config_id)
          as sort_flag from filtered_plays fp `;
        } else {
          sortQuery = ` SELECT id, fp.${MappingDictionary.get(model.sortKey)} as sort_flag from filtered_plays fp `;
        }

        const offset = PAGE_SIZE * model.pageIndex;

        const finalQuery = `WITH filtered_plays as (
            SELECT p.id, p.config_id, p.name, p.created_by_id, p.acqwired_provided_plays, p.created_at, p_config.llm_engine from play p INNER JOIN play_config p_config ON p.config_id = p_config.id ${filterQuery}
        ),
        sorted_plays as (
            ${sortQuery}
            ORDER BY sort_flag ${sortOrder}
        ) SELECT sp.id FROM sorted_plays sp
          LIMIT ${PAGE_SIZE} OFFSET ${offset};`;

        logger.error({ finalQuery });

        return db.$queryRaw`${Prisma.raw(finalQuery)}` as any;
      },
      listPlaybook: async ({ orgId, pageSize, playIds, whereClause }: ListPlaybookParams): Promise<IPlaybookResponse> => {
        if (!orgId) {
          throw new UnauthorizedError('Invalid organization id.');
        }

        const playsCount = await db.play.count({
          where: { orgId, deletedAt: null, status: PlayStatus.published }
        });

        const recordCount = await db.play.count({
          where: {
            AND: whereClause
          }
        });

        const plays = await db.play.findMany({
          where: { id: { in: playIds } },
          include: {
            createdBy: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                role: {
                  where: {
                    userRole: {
                      role: 'admin'
                    },
                    orgId: orgId
                  },
                  select: {
                    organization: {
                      select: {
                        name: true
                      }
                    }
                  }
                }
              }
            },
            config: {
              select: {
                llmEngine: true
              }
            },
            tasks: {
              select: {
                enrichmentId: true
              },
              where: {
                enrichmentId: { not: null }
              }
            },
            templates: {
              select: {
                template: true
              }
            }
          }
        });

        const executionCountMap: Record<string, number> = (
          await db.execution.groupBy({
            by: ['typeId'],
            where: {
              typeId: { in: playIds },
              type: 'play'
            },
            _count: {
              typeId: true
            }
          })
        ).reduce(
          (acc, item) => {
            acc[item.typeId] = item._count.typeId;
            return acc;
          },
          {} as Record<string, number>
        );

        const formattedPlays: IPlaybook[] = plays.map((play) => {
          let owner = '';
          if (play.acqwiredProvidedPlays && play.createdBy?.role) {
            owner = play.createdBy?.role?.organization?.name || '';
          } else {
            owner = play.createdBy ? `${play.createdBy.firstName} ${play.createdBy.lastName}` : '';
          }

          const playTypeMap = {
            [LlmProvider.non_ai]: 'Non-Ai',
            [LlmProvider.openai]: 'Ai'
          };

          return {
            id: play.id,
            playName: play.name,
            playType: playTypeMap[play.config.llmEngine] || play.config.llmEngine,
            description: play.description || '',
            acqwiredProvidedPlays: play.acqwiredProvidedPlays,
            template: play.templates[0]?.template || '',
            useCount: executionCountMap[play.id] || 0,
            enrichmentsIncluded: play.tasks.length,
            createdBy: owner,
            createdAt: play.createdAt.toISOString()
          };
        });

        const sortedPlays = playIds.map((id) => formattedPlays.find((play) => play.id === id)).filter((play) => !!play) as any;

        return {
          playbooks: sortedPlays,
          totalRecords: recordCount,
          totalPages: Math.ceil(recordCount / pageSize) || 0,
          hasRecords: !!playsCount
        };
      },
      getPlayById: async (payload: IPlayGetParams): Promise<ISinglePlay | null> => {
        const { orgId, playId, llmEngine } = payload;
        if (!orgId) {
          throw new UnauthorizedError('Invalid organization id.');
        }
        if (!playId) {
          throw new BadRequest('Invalid play id.');
        }
        const whereClause: Prisma.PlayWhereUniqueInput = {
          id: playId,
          orgId,
          deletedAt: null,
          templates: { some: { llmEngine: llmEngine } }
        };

        return db.play.findUnique({
          where: whereClause,
          select: {
            id: true,
            name: true,
            description: true,
            acqwiredProvidedPlays: true,
            status: true,
            templates: {
              select: {
                id: true,
                llmEngine: true,
                title: true,
                template: true,
                fieldMapping: {
                  select: {
                    schemaField: {
                      select: {
                        id: true,
                        key: true,
                        label: true,
                        entity: true
                      }
                    }
                  }
                }
              }
            },
            tasks: {
              select: {
                id: true,
                data: true,
                queueTaskType: true,
                enrichment: {
                  select: {
                    id: true,
                    data: true
                  }
                }
              }
            }
          }
        });
      },
      createDefaultPlays: async ({ orgId, createdById }: { orgId: string; createdById: string }, { tx }: { tx: any }): Promise<void> => {
        const txDb = tx || db;
        const initialPlays: IPlayInitialData[] = await txDb.playInitialData.findMany();

        for (const initialPlay of initialPlays) {
          const play = await txDb.play.create({
            data: {
              configId: initialPlay.configId,
              orgId,
              createdById,
              name: initialPlay.name,
              acqwiredProvidedPlays: true,
              description: initialPlay.description,
              status: PlayStatus.published
            }
          });

          const initialTemplate = await txDb.templateInitialData.findFirst({
            where: { initialPlayId: initialPlay.id }
          });

          if (!initialTemplate) {
            throw new NotFoundError('Initial template not found');
          }

          const template = await txDb.template.create({
            data: {
              orgId,
              playId: play.id,
              llmEngine: initialTemplate.llmEngine,
              template: initialTemplate.template
            }
          });

          if (initialTemplate.attachmentKey) {
            const field = await txDb.entityField.findFirst({
              where: {
                orgId,
                key: initialTemplate.attachmentKey,
                entity: initialTemplate.attachmentKeyEntity
              }
            });

            if (!field) {
              throw new NotFoundError('Field not found');
            }

            await txDb.templateAttachmentFieldMapping.create({
              data: {
                templateId: template.id,
                schemaFieldId: field.id
              }
            });
          }

          const { data } = initialPlay;

          if (data && data.taskSteps) {
            for (const taskStep of data.taskSteps) {
              // Create a new task record in the database
              const enrichment = await txDb.enrichment.findFirst({
                where: {
                  orgId,
                  outputField: {
                    entity: taskStep.enrichmentKey?.entity,
                    key: taskStep.enrichmentKey?.key,
                    orgId
                  }
                },
                select: { id: true }
              });

              if (taskStep.enrichmentKey?.entity && !enrichment) {
                throw new NotFoundError('Enrichment not found');
              }

              await txDb.playTask.create({
                data: {
                  playId: play.id,
                  enrichmentId: taskStep.enrichmentKey?.entity ? enrichment.id : null,
                  priority: taskStep.priority,
                  type: taskStep.type,
                  queueTaskType: taskStep.queueTaskType,
                  data: {}
                }
              });
            }
          }
        }
        return;
      }
    }
  }
});

export default PlayExtension;
