import { Prisma } from '@prisma/client';
import { db } from '../database';
import { UserRole } from '../types/Common';

const UserExtension = Prisma.defineExtension({
  name: 'User',
  model: {
    user: {
      createFirstOrgAdminUser: async ({
        email, firstName, lastName,
        role, orgId, active
      }: {
        email: string, firstName: string,
        lastName: string, role: UserRole,
        orgId: string, active: boolean
        }, { tx }: { tx: any }) => {
        return (tx || db).user.create({
          data: {
            email,
            active,
            firstName,
            lastName,
            createdAt: new Date(),
            updatedAt: null,
            deletedAt: null,
            role: {
              create: {
                userRole: {
                  connect: { role }
                },
                organization: {
                  connect: { id: orgId }
                }
              }
            }
          },
          select: { id: true, email: true }
        });
      }
    }
  }
});

export default UserExtension;
