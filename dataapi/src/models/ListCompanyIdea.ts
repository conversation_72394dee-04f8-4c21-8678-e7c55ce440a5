import { ExecutionStatus, ExecutionType, ListCompanyIdea, ListCompanyIdeaMapping, Prisma } from '@prisma/client';
import { db } from '../database';
import { BadRequestError, UnauthorizedError } from '../lib/errors';
import { ICompanyIdeaStatus, IListCompanyIdeaFilters, IListCompanyIdeaResponse, IListCompanyIdeas } from '../types/ListCompanyIdea';
import { IdeaQueryBuilder } from '../utils/IdeaQueryBuilder';
import { EntityType } from '@prisma/client';
import { IScoringDetails } from '../types/ScoringResult';

/**
 * Transforms a single ListCompanyIdea with additional properties like status and inShortlist.
 * @param idea The ListCompanyIdea entity to transform.
 * @returns The transformed ListCompanyIdea with status and additional properties.
 */
const transformListCompanyIdea = (idea: Partial<ListCompanyIdea & { company?: any; listCompanySourceIdeaMapping?: any[]; listCompanyIdeaMapping?: any[] }>, listCompanyId: string): IListCompanyIdeas => {
  let status: ICompanyIdeaStatus = ICompanyIdeaStatus.Idea;

  if (idea.companyId && idea.company?.deletedAt === null) {
    status = ICompanyIdeaStatus.InAcqwired;
  } else if (!idea.companyName || !idea.website) {
    status = ICompanyIdeaStatus.Invalid;
  }

  return {
    ...idea,
    sources: idea.listCompanySourceIdeaMapping?.map((m: any) => ({ ...m.listSource, sourceType: m.listSource.intentionSource.sourceType })) || [],
    inShortlist: idea.listCompanyIdeaMapping?.find((m: ListCompanyIdeaMapping) => m.listCompanyId === listCompanyId)?.inShortlist || false,
    status
  } as IListCompanyIdeas;
};

/**
 * Prisma extension for `listCompanyIdea` model with custom methods.
 */
const ListCompanyIdeaExtension = Prisma.defineExtension({
  name: 'ListCompanyIdea',
  model: {
    listCompanyIdea: {
      /**
       * Retrieves ListCompanyIdeas for a specific ListCompany with unique ideas only.
       */
      getListCompanyIdeas: async ({ listCompanyId }: { listCompanyId: string }): Promise<IListCompanyIdeas[]> => {
        if (!listCompanyId) {
          throw new BadRequestError('Invalid list company id.');
        }

        // Build Prisma query for filtering unique ideas by listCompanyId
        const query: Prisma.ListCompanyIdeaWhereInput = {
          listCompanyIdeaMapping: {
            some: {
              listCompanyId
            }
          }
        };

        // Fetch unique ideas with necessary relations
        const uniqueIdeas = await db.listCompanyIdea.findMany({
          where: query,
          include: {
            listCompanySourceIdeaMapping: {
              include: {
                listSource: {
                  include: {
                    intentionSource: true
                  }
                }
              }
            },
            listCompanyIdeaMapping: {
              where: {
                listCompanyId
              }
            },
            company: true
          }
        });

        // Transform the results with status and additional fields
        return uniqueIdeas.map((idea) => transformListCompanyIdea(idea, listCompanyId));
      },

      /**
       * Fetches filtered ListCompanyIdeas with pagination, sorting, and filters.
       */
      getFilteredListCompanyIdeas: async ({
        orgId,
        listSourceIds,
        pageSize,
        pageIndex,
        sortKey,
        sortOrder,
        text,
        inShortlist,
        recordStatus,
        listCompanyId
      }: {
        orgId: string;
        pageSize: number;
      } & IListCompanyIdeaFilters): Promise<IListCompanyIdeaResponse> => {
        if (!orgId) {
          throw new UnauthorizedError('Invalid organization id.');
        }

        if (!listCompanyId) {
          throw new BadRequestError('Invalid list company id');
        }

        if (!listSourceIds || listSourceIds.length === 0) {
          return {
            listCompanyIdeas: [],
            totalRecords: 0,
            totalPages: 0,
            hasRecords: false
          };
        }

        // Build query
        const queryBuilder = new IdeaQueryBuilder(orgId, listCompanyId);
        queryBuilder.addTextSearch(text);
        queryBuilder.addSourceFilter(listSourceIds);
        queryBuilder.addShortlistFilter(inShortlist);
        queryBuilder.addStatusFilter(recordStatus);
        queryBuilder.addSorting(sortKey, sortOrder);

        // Count total records
        const countQuery = queryBuilder.buildCountQuery();
        const countResult = await db.$queryRawUnsafe<{ total: bigint }[]>(countQuery);
        const totalRecords = Number(countResult[0].total);

        // Get paginated results
        const dataQuery = queryBuilder.buildDataQuery(pageSize, pageIndex);
        const rawResults = await db.$queryRawUnsafe<any[]>(dataQuery);
        const ideaIds = rawResults.map((r) => r.id);

        // Fetch full data for the ideas
        const listCompanyIdeas = await db.listCompanyIdea.findMany({
          where: {
            id: { in: ideaIds }
          },
          include: {
            listCompanySourceIdeaMapping: {
              include: {
                listSource: {
                  include: {
                    intentionSource: true
                  }
                }
              }
            },
            listCompanyIdeaMapping: {
              where: {
                listCompanyId
              }
            },
            company: true,
            scoringResults: {
              where: {
                scoringModel: {
                  listCompanyId
                }
              },
              select: {
                id: true,
                scoringModelId: true,
                entityId: true,
                executionId: true,
                details: true,
                totalScore: true,
                createdAt: true
              }
            }
          }
        });

        // Sort the results to match the raw query order
        const sortedIdeas = ideaIds.map((id) => listCompanyIdeas.find((idea) => idea.id === id));

        // Transform results
        const transformedIdeas = sortedIdeas.map((idea) => {
          const transformed = transformListCompanyIdea(idea!, listCompanyId);
          return {
            ...transformed,
            scoringResults: idea!.scoringResults || []
          };
        });

        // Get enrichment results
        const allEnrichmentResults = await db.enrichmentResult.findMany({
          where: {
            entityType: EntityType.idea,
            entityId: { in: transformedIdeas.map((idea) => idea.id) }
          },
          select: {
            id: true,
            enrichmentId: true,
            entityId: true,
            executionId: true,
            value: true,
            createdAt: true
          }
        });

        const executions =
          (await db.execution.findMany({
            where: {
              orgId,
              entityType: EntityType.idea,
              type: ExecutionType.enrichment,
              NOT: {
                OR: [{ status: ExecutionStatus.completed }, { status: ExecutionStatus.error }]
              }
            },
            select: {
              id: true,
              type: true,
              typeId: true,
              entityType: true,
              entityId: true,
              statusMessage: true,
              queueTasks: {
                select: {
                  type: true,
                  status: true,
                  data: true
                }
              }
            }
          })) || [];

        // Return paginated response
        return {
          listCompanyIdeas: transformedIdeas.map((idea) => {
            let enrichmentResults = allEnrichmentResults.filter((result) => result.entityId === idea.id);
            const currentExecution = executions.find((e) => e.entityId === idea.id) || null;
            if (currentExecution) {
              for (const task of currentExecution.queueTasks) {
                if (task.data && typeof task.data === 'object' && 'enrichmentIds' in task.data) {
                  const enrichmentIds: string[] = task.data.enrichmentIds as string[];
                  enrichmentResults = enrichmentResults.filter((e) => !enrichmentIds.includes(e.enrichmentId));

                  for (const enrichmentId of enrichmentIds) {
                    enrichmentResults.push({
                      id: crypto.randomUUID(),
                      entityId: currentExecution.entityId,
                      executionId: currentExecution.id,
                      enrichmentId: enrichmentId,
                      value: null,
                      createdAt: new Date()
                    });
                  }
                }
              }
            }
            const scoringResults =
              idea.scoringResults?.map((result) => ({
                ...result,
                id: result.id.toString(),
                details: result.details as unknown as IScoringDetails
              })) || [];
            return { ...idea, enrichmentResults, currentExecution, scoringResults };
          }),
          totalRecords,
          totalPages: Math.ceil(totalRecords / pageSize) || 0,
          hasRecords: !!totalRecords
        };
      },

      /**
       * Fetches all ListCompanyIdea IDs based on filters, without pagination.
       */
      getFilteredListCompanyIdeaIds: async ({ orgId, listSourceIds, text, inShortlist, recordStatus, listCompanyId }: { orgId: string; listCompanyId?: string; listSourceIds: string[]; text?: string; inShortlist?: boolean; recordStatus?: ICompanyIdeaStatus }): Promise<string[]> => {
        if (!listSourceIds || listSourceIds.length === 0) {
          return [];
        }
        const queryBuilder = new IdeaQueryBuilder(orgId, listCompanyId!);
        queryBuilder.addTextSearch(text);
        queryBuilder.addSourceFilter(listSourceIds);
        if (inShortlist !== undefined) {
          queryBuilder.addShortlistFilter(inShortlist ? ['true'] : ['false']);
        }
        if (recordStatus !== undefined) {
          queryBuilder.addStatusFilter([recordStatus]);
        }
        let dataQuery = queryBuilder.buildCountQuery();
        dataQuery = dataQuery.replace(/SELECT COUNT\(\*\) as total/i, 'SELECT lci.id');
        dataQuery = dataQuery.replace(/ORDER BY .*$/im, '');
        const rawResults = await db.$queryRawUnsafe<{ id: string }[]>(dataQuery);
        return rawResults.map((r) => r.id);
      },

      /**
       * Fetches a single ListCompanyIdea by ID and organization.
       */
      getListCompany: async ({ orgId, listCompanyIdeaId }: { orgId: string; listCompanyIdeaId: string }): Promise<ListCompanyIdea | null> => {
        if (!orgId) {
          throw new UnauthorizedError('Invalid organization id.');
        }
        if (!listCompanyIdeaId) {
          throw new BadRequestError('Invalid list company idea id.');
        }

        return db.listCompanyIdea.findUnique({
          where: { id: listCompanyIdeaId }
        });
      }
    }
  }
});

export default ListCompanyIdeaExtension;
