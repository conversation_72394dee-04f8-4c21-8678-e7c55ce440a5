import { Prisma } from '@prisma/client';

import { IEmailMessage } from '../types/EmailMessage';
import { db } from '../database';

const EmailMessageExtension = Prisma.defineExtension({
  name: 'EmailMessage',
  model: {
    emailMessage: {
      createEmail: async ({ userId, orgId, from, to, template, data, subject, scheduledAt, status }: IEmailMessage, { tx }: { tx: any }) => {
        return (tx || db).emailMessage.create({
          data: {
            userId, orgId, from, to, template, data,
            subject, scheduledAt, status
          }
        });
      }
    }
  }
});

export default EmailMessageExtension;
