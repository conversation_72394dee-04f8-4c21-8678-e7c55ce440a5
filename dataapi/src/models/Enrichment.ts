import { Enrichment, EntityType, Prisma } from '@prisma/client';
import { db } from '../database';
import { IEnrichment, IEnrichmentFilters, IEnrichmentListResponse } from '../types/Enrichment';
import { BadRequestError, UnauthorizedError } from '../lib/errors';
import { logger } from '../logger';
import { PAGE_SIZE } from '../constants';
//import { SortOrder } from '../types/Common';
//import { IFilterResults } from '../types/Company';

interface IPagination {
  pageIndex: number;
  sortKey: string;
  sortOrder: string;
}

interface EnrichmentListRequest {
  filters: IEnrichmentFilters;
  orgId: string;
  pagination: IPagination;
}

const EnrichmentExtension = Prisma.defineExtension({
  name: 'Enrichment',
  model: {
    enrichment: {
      listEnrichments: async ({ filters, pagination, orgId }: EnrichmentListRequest): Promise<IEnrichmentListResponse> => {
        const { text, provider, createdBy, createdAt } = filters;
        const { pageIndex, sortKey = 'createdAt', sortOrder = 'asc' } = pagination;
        logger.info({ sortKey, pagination });
        const pageSize: number = parseInt(pagination['take']) || PAGE_SIZE;

        if (!orgId) {
          throw new UnauthorizedError('Invalid organization id.');
        }

        const validSortKeys = ['name', 'entityType', 'assignedFieldLabel', 'isPrimary', 'createdBy', 'createdAt', 'updatedAt', 'enrichmentEntity', 'entityFieldId'];

        if (!validSortKeys.includes(sortKey)) {
          throw new Error('Invalid sortKey provided');
        }

        logger.info({ msg: 'Returning enrichments for', text, provider, createdBy, createdAt, pageSize });

        const whereClause: any = {
          orgId,
          deletedAt: null,
          entityType: { notIn: [EntityType.idea] }
        };
        if (text) {
          whereClause.name = {
            contains: text.trim(),
            mode: 'insensitive'
          };
        }
        if (provider?.length) {
          whereClause.config = { provider: { in: provider } };
        }
        if (createdBy?.length) {
          whereClause.createdById = { in: createdBy };
        }
        if (createdAt?.length === 2) {
          whereClause.createdAt = {
            gte: new Date(createdAt[0]),
            lte: new Date(createdAt[1])
          };
        }

        let orderClause: any = { [sortKey]: sortOrder };
        if (sortKey === 'assignedFieldLabel') {
          orderClause = { entityField: { label: sortOrder } };
        } else if (sortKey === 'createdBy') {
          orderClause = { createdBy: { firstName: sortOrder } };
        } else if (sortKey === 'enrichmentEntity') {
          orderClause = { entityType: sortOrder };
        } else if (sortKey === 'entityFieldId') {
          orderClause = { entityField: { label: sortOrder } };
        }

        const [rows, totalRecords, allRecordsCount] = await db.$transaction([
          db.enrichment.findMany({
            where: whereClause,
            orderBy: orderClause,
            select: {
              id: true,
              name: true,
              entityType: true,
              isPrimary: true,
              createdAt: true,
              updatedAt: true,
              createdById: true,
              llmModelId: true,
              data: true,
              listColumns: true,
              listCompanyId: true,
              entityField: {
                select: {
                  id: true
                }
              },
              config: true,
              createdBy: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true
                }
              }
            },
            skip: Math.max(pageIndex, 0) * pageSize,
            take: pageSize
          }),
          db.enrichment.count({
            where: whereClause
          }),
          db.enrichment.count({
            where: { orgId, deletedAt: null }
          })
        ]);

        const totalPages = Math.ceil(totalRecords / pageSize);
        const hasRecords = allRecordsCount > 0;

        if (rows) {
          const pagination = {
            totalRecords,
            totalPages,
            hasRecords
          };

          return { records: rows, pagination };
        }

        return {
          pagination: {
            hasRecords,
            totalRecords: 0,
            totalPages: 0
          },
          records: []
        };
      },
      getEnrichment: async ({ orgId, enrichmentId }: { orgId: string; enrichmentId: string }): Promise<IEnrichment | null> => {
        if (!orgId) {
          throw new UnauthorizedError('Invalid organization id.');
        }
        if (!enrichmentId) {
          throw new BadRequestError('Invalid enrichment id.');
        }

        return db.enrichment.findUnique({
          where: { orgId, id: enrichmentId },
          select: {
            id: true,
            config: true
          }
        });
      },
      updateEnrichment: async ({ orgId, id, name, llmModelId, assignedField, ...fields }: { orgId: string; id: string; name: string; llmModelId: string | null; assignedField: { fieldId: string; isPrimary: boolean } | null; [key: string]: string | object | null }): Promise<Enrichment> => {
        if (!orgId) {
          throw new UnauthorizedError('Invalid organization id.');
        }

        if (assignedField?.isPrimary) {
          await db.enrichment.updateMany({
            where: { orgId, isPrimary: true, entityFieldId: assignedField?.fieldId },
            data: { isPrimary: false }
          });
        }

        return db.enrichment.update({
          where: { id, orgId },
          data: {
            name,
            llmModelId: llmModelId === 'default' ? null : llmModelId,
            entityFieldId: assignedField?.fieldId || null,
            isPrimary: assignedField?.isPrimary || false,
            data: fields,
            updatedAt: new Date()
          }
        });
      }
    }
  }
});

export default EnrichmentExtension;
