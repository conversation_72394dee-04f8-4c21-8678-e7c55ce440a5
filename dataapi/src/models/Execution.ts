import { db } from '../database';
import { ExecutionStatus, Prisma } from '@prisma/client';
import BadRequest from '../lib/errors/BadRequest';
import { ICreateExecutionParams, ICreateQueueTaskParams } from '../types/Execution';
import { IPlay, IPlayExecution } from '../types/Play';
import { NotFoundError, UnprocessableContentError } from '../lib/errors';
import { ExecutionType } from '.prisma/client';

const ExecutionExtension = Prisma.defineExtension({
  name: 'Execution',
  model: {
    execution: {
      createExecution: async ({ orgId, type, typeId, userId, entityType, entityId, scheduledAt, executionId }: ICreateExecutionParams): Promise<{ id: string }> => {
        if (!entityId) {
          throw new BadRequest('Invalid entity id.');
        }

        if (executionId) {
          return db.execution.update({
            where: { id: executionId },
            data: {
              organization: { connect: { id: orgId } },
              type: type,
              typeId,
              entityType,
              entityId,
              status: ExecutionStatus.scheduled,
              createdBy: { connect: { id: userId } },
              createdAt: new Date(),
              scheduledAt
            },
            select: { id: true }
          });
        } else {
          return db.execution.create({
            data: {
              organization: { connect: { id: orgId } },
              type: type,
              typeId,
              entityType,
              entityId,
              status: ExecutionStatus.scheduled,
              createdBy: { connect: { id: userId } },
              createdAt: new Date(),
              scheduledAt
            },
            select: { id: true }
          });
        }
      },
      getPlayExecution: async ({ executionId }: { executionId: string }): Promise<IPlayExecution> => {
        return db.execution
          .findUnique({
            where: { id: executionId },
            select: {
              id: true,
              type: true,
              typeId: true,
              entityType: true,
              entityId: true,
              status: true,
              createdAt: true,
              scheduledAt: true,
              executedAt: true,
              queueTasks: {
                select: {
                  id: true,
                  type: true,
                  status: true,
                  scheduledAt: true
                }
              }
            }
          })
          .then(async (exec): Promise<IPlayExecution> => {
            if (!exec) {
              throw new NotFoundError('Execution not found');
            }
            if (exec.type !== ExecutionType.play) {
              throw new UnprocessableContentError('Invalid execution type');
            }

            return db.play
              .findUnique({
                where: { id: exec.typeId },
                select: {
                  id: true,
                  name: true
                }
              })
              .then((play: IPlay | null): IPlayExecution => {
                if (!play) {
                  throw new NotFoundError('Play not found');
                }

                return {
                  ...play,
                  execution: exec
                };
              });
          });
      },
      cancelExecution: async ({ executionId }: { executionId: string }): Promise<void> => {
        return db.execution
          .update({
            where: { id: executionId },
            data: { status: ExecutionStatus.cancelled }
          })
          .then((result) => {
            if (!result) {
              throw new UnprocessableContentError('Failed to cancel execution');
            }
          });
      }
    },
    queueTask: {
      createQueueTask: async ({ executionId, type, taskData = {}, scheduledAt, status = ExecutionStatus.pending }: ICreateQueueTaskParams): Promise<{ id: string }> => {
        if (!executionId) {
          throw new BadRequest('Invalid execution id.');
        }
        if (!type) {
          throw new BadRequest('Invalid type.');
        }

        return db.queueTask.create({
          data: {
            type,
            data: taskData,
            status,
            execution: { connect: { id: executionId } },
            scheduledAt
          },
          select: { id: true }
        });
      },
      getQueueTask: async () => {}
    }
  }
});

export default ExecutionExtension;
