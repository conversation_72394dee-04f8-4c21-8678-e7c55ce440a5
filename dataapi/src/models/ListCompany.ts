import { ListCompany, Prisma } from '@prisma/client';
import { db } from '../database';
import { BadRequestError, UnauthorizedError } from '../lib/errors';
import { IListCompanyFilters, IListCompanyResponse } from '../types/ListCompany';
import _ from 'lodash';
import { SortOrder } from '../types/Common';

export const listCompanyFilterClause = (query: IListCompanyFilters, orgId: string): Prisma.ListCompanyWhereInput[] => {
  const andQuery: Prisma.ListCompanyWhereInput[] = [{ orgId: orgId }];
  if (query.text) {
    andQuery.push({ name: { contains: query.text } });
  }

  if (query.createdBy?.length) {
    andQuery.push({ createdById: { in: query.createdBy } });
  }

  return andQuery;
};

const ListCompanyExtension = Prisma.defineExtension({
  name: 'ListCompany',
  model: {
    listCompany: {
      listListCompanies: async ({ orgId }): Promise<ListCompany[]> => {
        if (!orgId) {
          throw new UnauthorizedError('Invalid organization id.');
        }

        return db.listCompany.findMany({
          where: { orgId },
          select: {
            id: true,
            name: true,
            status: true,
            orgId: true,
            createdById: true,
            createdAt: true,
            updatedAt: true,
            createdBy: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          }
        });
      },
      getFilteredListCompanies: async ({ listCompanyId, orgId, pageIndex, pageSize, text, status, createdBy, sortKey = 'createdAt', sortOrder = SortOrder.DESC }: { orgId: string; pageSize: number } & IListCompanyFilters): Promise<IListCompanyResponse> => {
        if (!orgId) {
          throw new UnauthorizedError('Invalid organization id.');
        }

        const count = await db.listCompany.count({ where: { orgId } });

        const andCondition: object[] = [];
        if (text) {
          andCondition.push({
            name: {
              contains: text,
              mode: Prisma.QueryMode.insensitive
            }
          });
        }
        if (status?.length) {
          andCondition.push({
            OR: status.map((statusItem) => ({ status: statusItem }))
          });
        }
        if (createdBy?.length) {
          andCondition.push({
            OR: createdBy.map((userId) => ({
              createdBy: { id: userId }
            }))
          });
        }

        if (sortKey === 'totalShortListedIdeas') {
          const offset = pageIndex * pageSize;
          let whereConditions = `lc.org_id = '${orgId}'`;
          if (listCompanyId) {
            whereConditions += ` AND lc.id = '${listCompanyId}'`;
          }
          if (text) {
            whereConditions += ` AND lc.name ILIKE '%${text}%'`;
          }
          if (status && status.length > 0) {
            const statusList = status.map((s) => `'${s}'`).join(', ');
            whereConditions += ` AND lc.status IN (${statusList})`;
          }
          if (createdBy && createdBy.length > 0) {
            const createdByList = createdBy.map((id) => `'${id}'`).join(', ');
            whereConditions += ` AND lc.created_by_id IN (${createdByList})`;
          }

          const orderByClause = `ORDER BY "totalShortListedIdeas" ${sortOrder.toUpperCase()}`;
          const rawQuery = `
            SELECT
              lc.id,
              lc.name,
              lc.status,
              lc.created_at AS "createdAt",
              lc.updated_at AS "updatedAt",
              (SELECT COUNT(*) FROM list_company_idea_mapping lci WHERE lci.list_company_id = lc.id) AS "totalIdeas",
              (SELECT COUNT(*) FROM list_company_idea_mapping lci
                WHERE lci.list_company_id = lc.id AND lci.in_shortlist = true) AS "totalShortListedIdeas",
              (SELECT COUNT(*) FROM list_source ls WHERE ls.list_company_id = lc.id) AS "totalSources",
              (SELECT JSON_BUILD_OBJECT('id', u.id, 'firstName', u.first_name, 'lastName', u.last_name)
              FROM "user" u WHERE u.id = lc.created_by_id) AS "createdBy",
              COALESCE((
                SELECT JSON_AGG(JSON_BUILD_OBJECT('id', ls.id, 'sourceType', ls.source_type))
                FROM list_source ls
                WHERE ls.list_company_id = lc.id
              ), '[]'::json) AS "sources"
            FROM list_company lc
            WHERE ${whereConditions}
            ${orderByClause}
            LIMIT ${pageSize} OFFSET ${offset}
            `;

          return db.$queryRawUnsafe<any[]>(rawQuery).then(async (lists) => {
            const formattedLists = lists.map((list) => ({
              ...list,
              totalIdeas: typeof list.totalIdeas === 'bigint' ? Number(list.totalIdeas) : list.totalIdeas,
              totalShortListedIdeas: typeof list.totalShortListedIdeas === 'bigint' ? Number(list.totalShortListedIdeas) : list.totalShortListedIdeas,
              totalSources: typeof list.totalSources === 'bigint' ? Number(list.totalSources) : list.totalSources,
              sources: list.sources
            }));
            return {
              listCompanies: formattedLists,
              totalRecords: Number(count),
              totalPages: Math.ceil(Number(count) / pageSize) || 0,
              hasRecords: !!Number(count)
            };
          });
        } else {
          let orderClause;
          if (sortKey === 'totalIdeas') {
            orderClause = { listCompanyIdeaMapping: { _count: sortOrder } };
          } else if (sortKey === 'totalSources') {
            orderClause = { listSource: { _count: sortOrder } };
          } else if (sortKey === 'createdBy') {
            orderClause = { createdBy: { firstName: sortOrder } };
          } else {
            orderClause = { [sortKey]: sortOrder };
          }

          return db.listCompany
            .findMany({
              where: { orgId, id: listCompanyId, AND: andCondition },
              select: {
                id: true,
                name: true,
                status: true,
                createdAt: true,
                updatedAt: true,
                listCompanyIdeaMapping: {
                  select: {
                    listCompanyIdea: { select: { id: true } },
                    inShortlist: true
                  }
                },
                listSource: { select: { id: true, intentionSource: true } },
                createdBy: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true
                  }
                }
              },
              ...(sortKey ? { orderBy: orderClause } : {}),
              skip: pageIndex * pageSize,
              take: pageSize
            })
            .then((lists) => {
              if (!lists) {
                return [];
              }
              return lists.map((list) => {
                return {
                  id: list.id,
                  name: list.name,
                  status: list.status,
                  createdAt: list.createdAt,
                  updatedAt: list.updatedAt,
                  createdBy: list.createdBy,
                  sources: list.listSource.map((source) => ({
                    id: source.id,
                    sourceType: source.intentionSource.sourceType
                  })),
                  totalSources: list.listSource.length,
                  totalIdeas: list.listCompanyIdeaMapping.length,
                  totalShortListedIdeas: list.listCompanyIdeaMapping.filter((idea) => idea.inShortlist).length
                };
              });
            })
            .then((lists) => {
              return {
                listCompanies: lists,
                totalRecords: count,
                totalPages: Math.ceil(count / pageSize) || 0,
                hasRecords: !!count
              };
            });
        }
      },
      getListCompany: async ({ orgId, listCompanyId }: { orgId: string; listCompanyId: string }) => {
        if (!orgId) {
          throw new UnauthorizedError('Invalid organization id.');
        }
        if (!listCompanyId) {
          throw new BadRequestError('Invalid list company id.');
        }

        return db.listCompany
          .findUnique({
            where: { orgId, id: listCompanyId },
            select: {
              id: true,
              name: true,
              status: true,
              createdAt: true,
              updatedAt: true,
              listSource: {
                select: {
                  id: true,
                  status: true,
                  intentionSource: true,
                  parameters: true,
                  reason: true,
                  listCompanySourceIdeaMapping: true
                },
                orderBy: {
                  createdAt: 'desc'
                }
              },
              listCompanyIdeaMapping: true,
              createdBy: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true
                }
              }
            }
          })
          .then((list) => {
            if (!list) {
              return null;
            }
            const updatedSources = list.listSource.map((source) => {
              const { listCompanySourceIdeaMapping, ...sourceWithoutMapping } = source;
              const total = listCompanySourceIdeaMapping.length;
              const sourceType = source.intentionSource.sourceType;
              const shortlisted = listCompanySourceIdeaMapping.filter((mapping) => list.listCompanyIdeaMapping.some((ideaMapping) => ideaMapping.listCompanyIdeaId === mapping.listCompanyIdeaId && ideaMapping.inShortlist === true)).length;
              return { ...sourceWithoutMapping, total, shortlisted, sourceType };
            });
            return _.omit(
              {
                ...list,
                sources: updatedSources
              },
              'listSource',
              'listCompanyIdeaMapping'
            );
          });
      }
    }
  }
});

export default ListCompanyExtension;
