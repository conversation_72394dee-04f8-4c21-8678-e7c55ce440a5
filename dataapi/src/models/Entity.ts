import { EntityType, Prisma, EmailStatus, LlmProvider, PlayStatus, DataSourceType, FieldDataType } from '@prisma/client';
import { validate as uuidValidate } from 'uuid';
import { db } from '../database';
import { BadRequestError, NotFoundError } from '../lib/errors';
import { IContact, IField, IEntityField } from '../types/Entity';
import { ICompanyFilters, ICompanyGetResults, ICompanyMapping, IFlattenedField } from '../types/Company';
import { IContactFilters, IContactGetResults, IContactMapping, IGetEmailFieldsResult } from '../types/Contact';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { CompanyMandatoryFields, ContactMandatoryFields, defaultFieldConfiguration, EnrichmentMandatoryFields, prismaFieldMapping } from '../constants';
import { IFilterCondition, IFilterCriteria, SortOrder, TableFilterConditions } from '../types/Common';
import { IPlaybookFilters } from '../types/Play';
import { logger } from '../logger';
import { FieldType, ISimpleField } from '../types/Field';
import { getDomainFromUrl } from '../lib/utils';

export const mapPrismaFieldToSqlColumn = (field: string): string => {
  // Mapping from Prisma fields to SQL column names
  return prismaFieldMapping[field] || field;
};

export const mapPrismaFieldToColumn = (field: string): boolean => {
  const mapping = {
    name: CompanyMandatoryFields.NAME,
    website: CompanyMandatoryFields.WEB_URL,
    first_name: ContactMandatoryFields.FIRST_NAME,
    last_name: ContactMandatoryFields.LAST_NAME,
    email: ContactMandatoryFields.EMAIL,
    updatedAt: ContactMandatoryFields.UPDATED_AT,
    createdAt: ContactMandatoryFields.CREATED_AT,
    ownerId: ContactMandatoryFields.OWNER,
    createdBy: EnrichmentMandatoryFields.CREATED_BY
  };
  return field in mapping;
};

type StringFilterFunction = (value: string) => any;

// Create the Map with string keys and filter functions
const filterMap: Map<TableFilterConditions, StringFilterFunction> = new Map([
  [TableFilterConditions.StartsWith, (value) => ({ startsWith: value })],
  [TableFilterConditions.EndsWith, (value) => ({ endsWith: value })],
  [TableFilterConditions.Contains, (value) => ({ contains: value }) as any],
  [TableFilterConditions.NotContains, (value) => ({ not: value })],
  [TableFilterConditions.Equals, (value) => ({ equals: value })],
  [TableFilterConditions.NotEquals, (value) => ({ notEquals: value })],
  [TableFilterConditions.In, (value) => ({ in: value })],
  // [TableFilterConditions.NotContains, (value) => ({ notIn: value.split(',') })],
  [TableFilterConditions.GreaterThan, (value) => ({ gt: value })],
  [TableFilterConditions.GreaterThanOrEqual, (value) => ({ gte: value })],
  [TableFilterConditions.LessThan, (value) => ({ lt: value })],
  [TableFilterConditions.LessThanOrEqual, (value) => ({ lte: value })],
  [
    TableFilterConditions.Between,
    (value) => {
      return { gte: value[0], lte: value[1] };
    }
  ]
]);

export const checkConditionValue = (condition: IFilterCondition): { value; sign } => {
  let value = condition.data;
  let sign = '';
  switch (condition.condition) {
    case TableFilterConditions.Equals:
      value = `'${value}'`;
      sign = '=';
      break;
    case TableFilterConditions.NotEquals:
      value = `'${value}'`;
      sign = '!=';
      break;
    case TableFilterConditions.GreaterThan:
      value = `'${value}'`;
      sign = '>';
      break;
    case TableFilterConditions.GreaterThanOrEqual:
      value = `'${value}'`;
      sign = '>=';
      break;
    case TableFilterConditions.LessThan:
      value = `'${value}'`;
      sign = '<';
      break;
    case TableFilterConditions.LessThanOrEqual:
      value = `'${value}'`;
      sign = '<=';
      break;
    case TableFilterConditions.Contains:
      value = `'%${value}%'`;
      sign = 'ILIKE';
      break;
    case TableFilterConditions.NotContains:
      value = `'%${value}%'`;
      sign = 'NOT ILIKE';
      break;
    case TableFilterConditions.StartsWith:
      value = `'${value}%'`;
      sign = 'ILIKE';
      break;
    case TableFilterConditions.EndsWith:
      value = `'%${value}'`;
      sign = 'ILIKE';
      break;
    case TableFilterConditions.Between:
      value = `'${value[0]}' AND '${value[1]}'`;
      sign = 'BETWEEN';
      break;
    case TableFilterConditions.In:
      value = Array.isArray(value) ? `(${value.map((val) => `'${val}'`).join(', ')})` : `'${value}'`;
      sign = 'IN';
      break;
  }

  return { value, sign };
};

export const generateMultipleFilterConditions = (filters: IFilterCriteria[], entityTable: string, entityTableId: string) => {
  const multipleFilterConditionsArray = filters
    .map((filter) => {
      const key = mapPrismaFieldToColumn(filter.key);
      if (!key) {
        return `(${filter.items
          .map((condition) => {
            const { value, sign } = checkConditionValue(condition);
            return `e.value IS NOT NULL AND e.schema_field_id IN (SELECT id FROM entity_field WHERE (key = '${filter.key}' AND e.value ${sign} ${sign === 'BETWEEN' ? value.split(' AND ').join(' AND ') : `${value}`}))`;
          })
          .join(' OR ')})`;
      }
      return null;
    })
    .filter((condition) => condition !== null);

  const relationalKeys = filters.map((filter) => (mapPrismaFieldToColumn(filter.key) ? null : `'${filter.key}'`)).filter((key) => key !== null);

  let joinCondition = '';
  let filterQueryAddition = '';

  if (multipleFilterConditionsArray.length > 0) {
    joinCondition = ` LEFT JOIN ${entityTable} e ON c.id = e.${entityTableId}  AND e.schema_field_id IN ( SELECT id FROM entity_field WHERE key IN (${relationalKeys.join(',')})) `;

    const hasNegativePattern = filters.flatMap((f) => f.items).some((a) => a.condition.toLowerCase().includes('not') || a.condition.toLowerCase().includes('!'));
    if (hasNegativePattern) {
      multipleFilterConditionsArray.unshift('(e.schema_field_id IS NULL)');
    }
    const filterSubQuery = multipleFilterConditionsArray.join(' OR ');

    filterQueryAddition = ` AND (${filterSubQuery})`;
  }

  return { joinCondition, filterQueryAddition };
};

const GetCompanySelect: object = {
  id: true,
  name: true,
  website: true,
  sourceType: true,
  updatedAt: true,
  fields: {
    select: {
      schemaFieldId: true,
      value: true,
      schemaField: {
        select: {
          key: true,
          label: true,
          dataType: true
        }
      }
    }
  },
  entityMapping: {
    select: {
      id: true,
      isKey: true,
      isPrimary: true,
      contact: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          fields: {
            select: {
              schemaFieldId: true,
              value: true,
              schemaField: {
                select: {
                  key: true,
                  label: true,
                  dataType: true
                }
              }
            }
          }
        }
      }
    },
    where: {
      contact: {
        deletedAt: null
      }
    }
  },
  owner: {
    select: {
      id: true,
      firstName: true,
      lastName: true
    }
  },
  channelEmails: {
    where: {
      status: { in: [EmailStatus.error, EmailStatus.scheduled] }
    },
    orderBy: {
      updatedAt: SortOrder.DESC
    },
    take: 1
  }
};

export const GetContactSelect: Prisma.ContactSelect = {
  id: true,
  firstName: true,
  lastName: true,
  email: true,
  sourceType: true,
  updatedAt: true,
  fields: {
    select: {
      schemaFieldId: true,
      value: true,
      schemaField: {
        select: {
          key: true,
          label: true,
          dataType: true
        }
      }
    }
  },
  entityMapping: {
    select: {
      id: true,
      isPrimary: true,
      isKey: true,
      company: {
        select: {
          id: true,
          name: true,
          website: true,
          fields: {
            select: {
              schemaFieldId: true,
              value: true,
              schemaField: {
                select: {
                  key: true,
                  label: true,
                  dataType: true
                }
              }
            }
          }
        }
      }
    },
    where: {
      company: {
        deletedAt: null
      }
    }
  },
  owner: {
    select: {
      id: true,
      firstName: true,
      lastName: true
    }
  },
  channelEmails: {
    where: {
      status: { in: [EmailStatus.error, EmailStatus.scheduled] }
    },
    orderBy: {
      updatedAt: SortOrder.DESC
    },
    take: 1
  },
  fieldMetadata: {
    select: {
      schemaFieldId: true,
      type: true,
      dataType: true
    }
  }
};

const parseDateRange = (startDateString: string, endDateString: string): { gte: Date; lte: Date } => {
  dayjs.extend(customParseFormat);

  const startDate: Date = dayjs(startDateString, 'DD/MM/YYYY').toDate();
  const endDate: Date = dayjs(endDateString, 'DD/MM/YYYY').endOf('day').toDate();

  return {
    gte: startDate,
    lte: endDate
  };
};

const processFilters = <T extends object>(filters: IFilterCriteria[]): T => {
  const conditions: T = {} as T;

  filters.forEach((filter) => {
    for (let index = 0; index < filter.items.length; index++) {
      const condition = filter.items[index];

      const columnName = mapPrismaFieldToSqlColumn(filter.key);
      const key = mapPrismaFieldToColumn(filter.key);
      if (!key) {
        continue;
      }

      const sign = condition.condition;
      if (!filterMap.has(sign)) {
        throw new Error(`Unsupported sign: ${condition.condition}`);
      }

      (conditions as any)[columnName] = filterMap.get(sign)!(condition.data);
    }
  });

  return conditions;
};

export const contactWhereClause = (query: IContactFilters, orgId: string): Prisma.ContactWhereInput[] => {
  const andQuery: Prisma.ContactWhereInput[] = [{ orgId: orgId, deletedAt: null }];
  if (query.lastActivityDate && query.lastActivityDate.length >= 2) {
    andQuery.push({ updatedAt: parseDateRange(query.lastActivityDate[0], query.lastActivityDate[1]) });
  }
  if (query.lastModifiedDate && query.lastModifiedDate.length >= 2) {
    andQuery.push({ updatedAt: parseDateRange(query.lastModifiedDate[0], query.lastModifiedDate[1]) });
  }
  if (query.owners && query.owners.length > 0) {
    andQuery.push({ ownerId: { in: query.owners } });
  }
  if (query.filters && query.filters.length > 0) {
    const conditions = processFilters<Prisma.ContactWhereInput>(query.filters);
    if (Object.keys(conditions).length > 0) {
      andQuery.push({ OR: [conditions] });
    }
  }
  return andQuery;
};

export const playbookFilterClause = (query: IPlaybookFilters, orgId: string): Prisma.PlayWhereInput[] => {
  const andQuery: Prisma.PlayWhereInput[] = [{ orgId: orgId, deletedAt: null, status: PlayStatus.published }];
  if (query.text) {
    andQuery.push({ name: { contains: query.text } });
  }

  if (query.createdBy?.length) {
    const createdByOrConditions = query.createdBy.map((createdBy) => ({
      createdById: createdBy.id,
      acqwiredProvidedPlays: createdBy.acqwiredProvidedPlays
    }));
    andQuery.push({ OR: createdByOrConditions });
  }

  if (query.createdAt && query.createdAt.length === 2) {
    andQuery.push({ createdAt: parseDateRange(query.createdAt[0], query.createdAt[1]) });
  }
  if (query.playType) {
    andQuery.push({ config: { llmEngine: query.playType === 'openai' ? LlmProvider.openai : LlmProvider.non_ai } });
  }

  return andQuery;
};

export const convertPrismaWhereToSql = (
  where: Prisma.ContactWhereInput | Prisma.ContactWhereInput[] | Prisma.CompanyWhereInput | Prisma.CompanyWhereInput[] | Prisma.PlayWhereInput | Prisma.PlayWhereInput[] | Prisma.EnrichmentWhereInput | Prisma.EnrichmentWhereInput[] | Prisma.ListCompanyWhereInput[],
  prefix = 'c'
): string => {
  if (Array.isArray(where)) {
    return where.map((condition) => convertPrismaWhereToSql(condition, prefix)).join(' AND ');
  }

  const conditions: string[] = [];

  for (const [key, value] of Object.entries(where)) {
    const columnName = mapPrismaFieldToSqlColumn(key);

    if (key === 'OR') {
      const orConditions = (value as Prisma.ContactWhereInput[]).map((subWhere) => `(${convertPrismaWhereToSql(subWhere, prefix)})`);
      conditions.push(`(${orConditions.join(' OR ')})`);
    } else if (key === 'some') {
      Object.entries(value as Record<string, any>).map(([subKey, subValue]) => {
        conditions.push(`(${convertPrismaWhereToSql({ [subKey]: subValue }, 'e')})`);
      });
    } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      if (Object.keys(value).some((k) => ['contains', 'gte', 'lte', 'gt', 'lt', 'in', 'equals', 'not', 'notEquals', 'startsWith', 'endsWith'].includes(k))) {
        // Handle operators within the nested object
        for (const [operator, operand] of Object.entries(value)) {
          const dateFields = [prismaFieldMapping.updatedAt, prismaFieldMapping.createdAt];
          const checkDateValue = dateFields.includes(columnName);
          const prefixColumnName = checkDateValue ? `DATE(${prefix}.${columnName})` : `${prefix}.${columnName}`;
          const formattedDate = operand instanceof Date ? `'${operand.toISOString()}'` : `'${operand}'`;
          switch (operator) {
            case 'contains':
              conditions.push(`${prefix}.${columnName} ILIKE '%${operand}%'`);
              break;
            case 'not':
              conditions.push(`${prefix}.${columnName} NOT ILIKE '%${operand}%'`);
              break;
            case 'startsWith':
              conditions.push(`${prefix}.${columnName} ILIKE '${operand}%'`);
              break;
            case 'endsWith':
              conditions.push(`${prefix}.${columnName} ILIKE '%${operand}'`);
              break;
            case 'equals':
              if (checkDateValue) {
                conditions.push(`DATE(${prefix}.${columnName}) = '${operand}'`);
              } else {
                conditions.push(`LOWER(${prefix}.${columnName}) = LOWER('${operand}')`);
              }
              break;
            case 'notEquals':
              if (checkDateValue) {
                conditions.push(`DATE(${prefix}.${columnName}) != '${operand}'`);
              } else {
                conditions.push(`LOWER(${prefix}.${columnName}) != LOWER('${operand}')`);
              }
              break;
            case 'gte':
            case 'lte':
              conditions.push(`${prefixColumnName} ${operator === 'gte' ? '>=' : '<='} ${formattedDate}`);
              break;
            case 'gt':
              conditions.push(`${prefixColumnName} > ${formattedDate}`);
              break;
            case 'lt':
              conditions.push(`${prefixColumnName} < ${formattedDate}`);
              break;
            case 'in':
              conditions.push(`${prefix}.${columnName} IN (${(operand as any).map((o) => `'${o}'`).join(', ')})`);
              break;
            default:
              throw new Error(`Unsupported operator: ${operator}`);
          }
        }
      } else {
        // Handle nested object
        const nestedPrefix = `${prefix}_${columnName}`;
        const nestedConditions = convertPrismaWhereToSql(value, nestedPrefix);
        if (nestedConditions) {
          conditions.push(nestedConditions);
        }
      }
    } else if (value === null) {
      conditions.push(`${prefix}.${columnName} IS NULL`);
    } else {
      conditions.push(`${prefix}.${columnName} = '${value}'`);
    }
  }
  return conditions.join(' AND ');
};

export const companyWhereClause = (query: ICompanyFilters, orgId: string): Prisma.CompanyWhereInput[] => {
  const filterQuery: Prisma.CompanyWhereInput[] = [{ orgId: orgId, deletedAt: null }];

  if (query.lastActivityDate && query.lastActivityDate.length >= 2) {
    filterQuery.push({ updatedAt: parseDateRange(query.lastActivityDate[0], query.lastActivityDate[1]) });
  }
  if (query.lastModifiedDate && query.lastModifiedDate.length >= 2) {
    filterQuery.push({ updatedAt: parseDateRange(query.lastModifiedDate[0], query.lastModifiedDate[1]) });
  }
  if (query.owners && query.owners.length > 0) {
    filterQuery.push({ ownerId: { in: query.owners } });
  }

  if (query.filters && query.filters.length > 0) {
    const conditions = processFilters<Prisma.CompanyWhereInput>(query.filters);
    if (Object.keys(conditions).length > 0) {
      filterQuery.push({ OR: [conditions] });
    }
  }

  logger.debug({ rawFilterQuery: JSON.stringify(filterQuery) });
  return filterQuery;
};

const EntityExtension = Prisma.defineExtension({
  name: 'Entity',
  model: {
    contact: {
      getContactsWithRawWhere: async ({ whereClause }) => {
        return db.contact.findMany({
          where: { AND: whereClause },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            updatedAt: true,
            sourceType: true,
            fields: {
              select: {
                value: true,
                schemaField: {
                  select: {
                    id: true,
                    key: true,
                    label: true,
                    dataType: true
                  }
                }
              }
            },
            owner: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            },
            entityMapping: {
              select: {
                id: true,
                isPrimary: true,
                company: {
                  select: {
                    id: true,
                    name: true,
                    website: true
                  }
                }
              },
              where: {
                company: {
                  deletedAt: null
                }
              }
            },
            channelEmails: {
              where: {
                status: {
                  in: [
                    // prettier-ignore
                    EmailStatus.scheduled,
                    EmailStatus.ready,
                    EmailStatus.inqueue,
                    EmailStatus.processing,
                    EmailStatus.sent,
                    EmailStatus.error
                  ]
                }
              },
              select: {
                id: true,
                contact: { select: { id: true } },
                company: { select: { id: true } },
                createdBy: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    active: true
                  }
                },
                type: true,
                status: true,
                errorMessage: true,
                from: true,
                to: true,
                subject: true,
                body: true,
                createdAt: true,
                scheduledAt: true
              },
              orderBy: { scheduledAt: 'desc' }
            },
            participants: {
              where: { activity: { deletedAt: null } },
              select: {
                id: true,
                companyId: true,
                contactId: true,
                activity: {
                  select: {
                    id: true,
                    type: true,
                    source: true,
                    participants: {
                      select: {
                        contact: {
                          select: {
                            id: true,
                            firstName: true,
                            lastName: true
                          }
                        },
                        company: {
                          select: {
                            id: true,
                            name: true
                          }
                        }
                      }
                    },
                    acqwiredActivity: {
                      select: {
                        title: true,
                        content: true,
                        dueDate: true,
                        status: true,
                        createdAt: true,
                        updatedAt: true,
                        createdById: true
                      }
                    },
                    crmActivity: {
                      select: {
                        id: true,
                        orgId: true,
                        crmId: true,
                        activityId: true,
                        title: true,
                        content: true,
                        dueDate: true,
                        crmType: true,
                        status: true
                      }
                    },
                    channelEmail: {
                      select: {
                        id: true,
                        from: true,
                        to: true,
                        subject: true,
                        body: true,
                        status: true,
                        errorMessage: true,
                        cc: true,
                        scheduledAt: true,
                        createdAt: true,
                        executionId: true,
                        contact: {
                          select: {
                            id: true,
                            firstName: true,
                            lastName: true
                          }
                        },
                        execution: {
                          where: {
                            type: 'play'
                          },
                          select: {
                            id: true,
                            type: true,
                            typeId: true
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        });
      },
      getContacts: async ({ whereClause }: { whereClause: Prisma.ContactWhereInput[] }): Promise<IContactGetResults[] | null> => {
        return db.contact.findMany({
          where: { AND: whereClause },
          select: GetContactSelect as object
        }) as unknown as Promise<IContactGetResults[] | null>;
      },
      getContactById: async ({ contactId, orgId }: { contactId: string; orgId: string }): Promise<IContact | null> => {
        return db.contact.findUnique({
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          },
          where: { id: contactId, orgId, deletedAt: null }
        });
      },
      getFields: async ({ contactId, orgId }: { contactId: string; orgId: string }): Promise<IField[] | null> => {
        // Validate the contactId and orgId
        if (!contactId || !uuidValidate(contactId)) {
          throw new BadRequestError('Invalid contact id.');
        }
        if (!orgId || !uuidValidate(orgId)) {
          throw new BadRequestError('Invalid organization id.');
        }

        // Fetch the contact details
        const contact = (await db.contact.findFirst({
          where: { id: contactId, orgId, deletedAt: null },
          select: { firstName: true, lastName: true, email: true }
        })) as IContact | null;

        if (!contact) {
          throw new NotFoundError('Contact not found.');
        }

        // Query the database to find relevant fields
        const fields = await db.entityField.findMany({
          where: {
            entity: EntityType.contact,
            orgId,
            isInternal: false,
            deletedAt: null
          },
          orderBy: [
            { isMandatory: 'desc' }, // Sort by isMandatory first (true first)
            { label: 'asc' } // Then sort by label
          ],
          include: {
            contactFields: {
              where: { contactId },
              select: {
                id: true,
                value: true
              }
            }
          }
        });

        // Map the fields to the desired format, updating mandatory fields with contact details
        return fields.map((f: IEntityField): IField => {
          let value = f.contactFields?.[0]?.value || '';

          // Update the value for mandatory fields
          if (f.isMandatory) {
            switch (f.key) {
              case ContactMandatoryFields.FIRST_NAME:
                value = contact.firstName;
                break;
              case ContactMandatoryFields.LAST_NAME:
                value = contact.lastName;
                break;
              case ContactMandatoryFields.EMAIL:
                value = contact.email;
                break;
            }
          }

          return {
            id: f.contactFields?.[0]?.id || undefined,
            schemaFieldId: f.id,
            value: value,
            schemaField: {
              id: f.id,
              key: f.key,
              label: f.label,
              dataType: f.dataType,
              isMandatory: f.isMandatory
            }
          };
        });
      },
      getMappings: async ({ contactId }: { contactId: string }): Promise<IContactMapping[] | null> => {
        if (!contactId) {
          throw new BadRequestError('Invalid contact id.');
        }

        return db.companyContactMapping.findMany({
          where: { contactId, company: { deletedAt: null } },
          orderBy: { company: { name: SortOrder.ASC } },
          select: {
            isPrimary: true,
            isKey: true,
            company: {
              select: {
                id: true,
                name: true,
                website: true
              }
            }
          }
        });
      },
      getEmailFields: async ({ orgId }: { orgId: string }): Promise<IGetEmailFieldsResult[] | null> => {
        return db.entityField.findMany({
          where: {
            entity: EntityType.contact,
            dataType: 'email',
            orgId,
            deletedAt: null
          },
          select: {
            id: true,
            label: true
          }
        });
      },
      createEmailField: async ({ label, orgId }: { label: string; orgId: string }): Promise<IGetEmailFieldsResult | null> => {
        const key = label.toLowerCase().replace(/[^a-zA-Z0-9_]/g, '_');
        return db.entityField.create({
          data: {
            entity: EntityType.contact,
            label,
            key: key,
            dataType: FieldDataType.email,
            orgId,
            config: defaultFieldConfiguration
          },
          select: {
            id: true,
            label: true
          }
        });
      },
      createEntity: async ({ data, orgId, userId, tx }) => {
        const conn = tx || db;
        return conn.contact.create({
          data: {
            orgId: orgId,
            ownerId: userId,
            firstName: data.contact.firstName,
            lastName: data.contact.lastName,
            email: data.contact.email,
            sourceType: DataSourceType.acqwired,
            createdAt: new Date(),
            createdById: userId
          }
        });
      },
      modifyEntity: ({ data, tx }) => {
        const conn = tx || db;
        return conn.contact.update({
          where: { id: data.contact.id },
          data: {
            firstName: data.contact.firstName,
            lastName: data.contact.lastName,
            email: data.contact.email
          }
        });
      }
    },
    company: {
      getCompaniesWithRawWhere: async ({ whereClause }) => {
        return db.company.findMany({
          where: { AND: whereClause },
          select: {
            id: true,
            name: true,
            website: true,
            updatedAt: true,
            sourceType: true,
            fields: {
              select: {
                value: true,
                schemaField: {
                  select: {
                    id: true,
                    key: true,
                    label: true,
                    dataType: true
                  }
                }
              }
            },
            owner: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            },
            entityMapping: {
              select: {
                id: true,
                isKey: true,
                contact: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true
                  }
                }
              },
              where: {
                contact: {
                  deletedAt: null
                }
              }
            },
            channelEmails: {
              where: {
                status: {
                  in: [
                    // prettier-ignore
                    EmailStatus.scheduled,
                    EmailStatus.ready,
                    EmailStatus.inqueue,
                    EmailStatus.processing,
                    EmailStatus.sent,
                    EmailStatus.error
                  ]
                }
              },
              select: {
                id: true,
                contact: { select: { id: true } },
                company: { select: { id: true } },
                createdBy: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    active: true
                  }
                },
                type: true,
                status: true,
                errorMessage: true,
                from: true,
                to: true,
                subject: true,
                body: true,
                createdAt: true,
                scheduledAt: true
              },
              orderBy: { scheduledAt: 'desc' }
            },
            participants: {
              where: { activity: { deletedAt: null } },
              select: {
                id: true,
                contactId: true,
                companyId: true,
                activity: {
                  select: {
                    id: true,
                    type: true,
                    source: true,
                    participants: {
                      select: {
                        contact: {
                          select: {
                            id: true,
                            firstName: true,
                            lastName: true
                          }
                        },
                        company: {
                          select: {
                            id: true,
                            name: true
                          }
                        }
                      }
                    },
                    acqwiredActivity: {
                      select: {
                        title: true,
                        content: true,
                        dueDate: true,
                        status: true,
                        createdAt: true,
                        updatedAt: true,
                        createdById: true
                      }
                    },
                    crmActivity: {
                      select: {
                        id: true,
                        orgId: true,
                        crmId: true,
                        activityId: true,
                        title: true,
                        content: true,
                        dueDate: true,
                        crmType: true,
                        status: true
                      }
                    },
                    channelEmail: {
                      select: {
                        id: true,
                        from: true,
                        to: true,
                        subject: true,
                        body: true,
                        status: true,
                        errorMessage: true,
                        cc: true,
                        scheduledAt: true,
                        createdAt: true,
                        executionId: true,
                        contact: {
                          select: {
                            id: true,
                            firstName: true,
                            lastName: true
                          }
                        },
                        execution: {
                          where: {
                            type: 'play'
                          },
                          select: {
                            id: true,
                            type: true,
                            typeId: true
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        });
      },
      getCompanyById: async ({ companyId, orgId }: { companyId: string; orgId: string }): Promise<ICompanyGetResults | null> => {
        return db.company.findFirst({
          select: GetCompanySelect,
          where: { id: companyId, orgId, deletedAt: null }
        }) as unknown as Promise<ICompanyGetResults | null>;
      },
      getFields: async ({ companyId, orgId }: { companyId: string; orgId: string }): Promise<ISimpleField[] | null> => {
        if (!companyId) {
          throw new BadRequestError('Invalid company id.');
        }
        if (!orgId) {
          throw new BadRequestError('Invalid organization id.');
        }

        const company = await db.company.findFirst({
          where: { id: companyId, orgId, deletedAt: null },
          select: { name: true, website: true }
        });

        if (!company) {
          throw new NotFoundError('Company not found.');
        }

        return db.entityField
          .findMany({
            where: {
              entity: EntityType.company,
              orgId,
              deletedAt: null
            },
            orderBy: [{ isMandatory: SortOrder.DESC }, { label: SortOrder.ASC }],
            include: {
              companyFields: {
                where: { companyId },
                select: {
                  id: true,
                  value: true
                }
              }
            }
          })
          .then((field) => {
            return field.map((f: IEntityField): ISimpleField => {
              let value = f.companyFields?.[0]?.value || '';
              if (f.isMandatory) {
                switch (f.key) {
                  case CompanyMandatoryFields.NAME:
                    value = company.name;
                    break;
                  case CompanyMandatoryFields.WEB_URL:
                    value = company.website;
                    break;
                }
              }

              return {
                id: f.id,
                type: FieldType.Acqwired,
                key: f.key,
                label: f.label,
                dataType: f.dataType,
                entityType: EntityType.company,
                value
              };
            });
          });
      },
      getMappings: async ({ companyId }: { companyId: string }): Promise<ICompanyMapping[] | null> => {
        if (!companyId) {
          throw new BadRequestError('Invalid company id.');
        }

        return db.companyContactMapping.findMany({
          where: { companyId, contact: { deletedAt: null } },
          orderBy: { contact: { firstName: SortOrder.ASC } },
          select: {
            isPrimary: true,
            isKey: true,
            contact: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        });
      },

      createEntity: async ({ data, orgId, userId, tx }) => {
        const conn = tx || db;
        const domain = getDomainFromUrl(data.company.website);
        return conn.company.create({
          data: {
            orgId,
            name: data.company.name,
            website: data.company.website,
            domain: domain,
            sourceType: DataSourceType.acqwired,
            createdAt: new Date(),
            createdById: userId,
            ownerId: userId
          }
        });
      },
      modifyEntity: async ({ data, tx }) => {
        const conn = tx || db;
        const domain = getDomainFromUrl(data.company.website);
        return conn.company.update({
          where: { id: data.company.id },
          data: {
            name: data.company.name,
            website: data.company.website,
            domain: domain
          }
        });
      }
    },
    entityField: {
      createMandatoryFields: async ({ orgId }: { orgId: string }, { tx }: { tx: any }): Promise<void> => {
        const initialFields = await (tx || db).entityFieldInitialData.findMany({
          select: {
            entity: true,
            label: true,
            dataType: true,
            key: true,
            isInternal: true,
            isAttachment: true,
            isMandatory: true
          }
        });

        await (tx || db).entityField.createMany({
          data: initialFields.map((field) => ({ ...field, orgId }))
        });
      },
      getEnrichmentFields: async ({ companyId, contactId, orgId, entity }: { companyId?: string; contactId?: string; orgId: string; entity: EntityType }): Promise<any[] | null> => {
        if (!orgId) {
          throw new BadRequestError('Invalid organization id.');
        }

        if (!companyId && !contactId) {
          throw new BadRequestError('Invalid company or contact id.');
        }

        let query: object;
        if (companyId) {
          query = {
            include: {
              companyFields: {
                where: { companyId },
                select: {
                  id: true,
                  value: true
                }
              },
              enrichments: {
                select: {
                  id: true
                }
              }
            }
          };
        } else {
          query = {
            include: {
              contactFields: {
                where: { contactId },
                select: {
                  id: true,
                  value: true
                }
              },
              enrichments: {
                select: {
                  id: true
                }
              }
            }
          };
        }

        return db.entityField
          .findMany({
            where: {
              entity,
              orgId,
              deletedAt: null
            },
            ...query
          })
          .then((response) => {
            if (!response) {
              return null;
            }
            return response.map((f: IEntityField): IFlattenedField => {
              return {
                id: f.id,
                fkey: f.key,
                key: f.key,
                label: f.label,
                value: companyId ? f.companyFields?.[0]?.value || '' : contactId ? f.contactFields?.[0]?.value || '' : '',
                dataType: f.dataType,
                enrichmentId: f.enrichments?.[0]?.id || ''
              };
            });
          });
      }
    }
  }
});

export default EntityExtension;
