import { Prisma, Stats, StatsKeyType, StatsType } from '@prisma/client';
import { db } from '../database';

const StatsExtension = Prisma.defineExtension({
  name: 'Stats',
  model: {
    stats: {
      getStatsByEntities: async (entityIds: string[], type: StatsType, key: StatsKeyType): Promise<Stats[]> => {
        return await db.stats.findMany({
          where: {
            typeId: {
              in: entityIds
            },
            type,
            key
          }
        });
      }
    }
  }
});

export default StatsExtension;
