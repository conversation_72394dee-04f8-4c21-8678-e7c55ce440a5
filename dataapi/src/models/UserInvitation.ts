import { Prisma } from '@prisma/client';
import { db } from '../database';

const UserInvitationExtension = Prisma.defineExtension({
  name: 'UserInvitation',
  model: {
    userInvitation: {
      createInvitation: async ({
        userId,
        orgId,
        email,
        hash
      }: {
        userId: string,
        orgId: string,
        email: string,
        hash: string
      }, { tx }: { tx: any }) => {
        return (tx || db).userInvitation.create({
          data: {
            email,
            organization: {
              connect: {
                id: orgId
              }
            },
            userId: userId,
            hash: hash,
            deletedAt: null
          }
        });
      }
    }
  }
});

export default UserInvitationExtension;

