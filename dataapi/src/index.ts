import process from 'node:process';

import config from './config';
import express, { Express } from 'express';
import initApp from './init-app';
import { logger } from './logger';
import { disconnect } from './database';

logger.info({ msg: 'Dataapi service starting...', pid: process.pid });
const app: Express = express();
app.disable('x-powered-by');
app.disable('etag');
initApp(app);

const server = app.listen(config.port, () => {
  logger.info(`Data API Server started on port ${config.port}`);
});

server.on('close', async () => {
  logger.debug('Data API HTTP service closed');
  await disconnect();
});

const shutdown = async (signal:any) => {
  logger.info({ msg: `Received ${signal}. Shutting down gracefully...`, pid: process.pid });
  server.close(async (err) => {
    if (err) {
      logger.error({ msg: 'Error during server close', err });
      process.exit(1);
    }
    await disconnect();
    process.exit(0);
  });
};

process.on('SIGTERM', () => shutdown('SIGTERM'));
process.on('SIGINT', () => shutdown('SIGINT'));
