import eslint from '@eslint/js';
import tslint from 'typescript-eslint';
import typescript from '@typescript-eslint/parser';
import globals from 'globals';

export default tslint.config(eslint.configs.recommended, ...tslint.configs.recommended, {
  languageOptions: {
    parser: typescript,
    parserOptions: {
      ecmaVersion: 2020,
      sourceType: 'module'
    },
    globals: {
      URL: 'readable',
      Headers: 'readable',
      URLSearchParams: 'readable',
      crypto: 'readable',
      fetch: 'readable',
      Buffer: 'readable',
      Express: 'readable',
      hbs: 'readable',
      setTimeout: 'readable',
      process: 'readable',
      Request: 'readable',
      Response: 'readable'
    }
  },
  files: ['src/**/*.js', 'src/**/*.ts', 'src/**/*.tsx'],
  ignores: ['tailwind.config.js', '**/*.test.ts'],
  rules: {
    // Enforce consistent indentation
    indent: 'off',
    '@/indent': ['error', 2, { SwitchCase: 1 }],
    // Require semicolons at the end of statements
    semi: ['error', 'always'],
    // Disable built-in unused variable handling
    'no-unused-vars': 'off',
    // Disallow locally undefined global variable usage (Usable ones are defined above in "globals")
    'no-undef': 'error',
    // Disallow unused variables except for those starting with an underscore
    '@typescript-eslint/no-unused-vars': [
      'warn',
      {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
        caughtErrorsIgnorePattern: '^_'
      }
    ],
    // Require the use of === and !==
    eqeqeq: ['error', 'always'],
    // Disallow the use of console
    'no-console': ['error', { allow: ['error'] }],
    // Allow the use of empty object literals
    '@typescript-eslint/no-empty-object-type': 'off',
    // Disallow the use of ts-ignore comments
    '@typescript-eslint/ban-ts-comment': 'error',
    // Allow the use of any. CANT AVOID THAT FOR NOW
    '@typescript-eslint/no-explicit-any': 'off',
    // Empty line on end of file
    'eol-last': ['error', 'always']
  }
});
