ARG NODE_VERSION=20
ARG ENV_NAME=prod
ARG SERVICE_DIR=dataapi
ARG ALPINE_VERSION="3.18"

FROM node:$NODE_VERSION-alpine${ALPINE_VERSION} AS builder
ARG SERVICE_DIR
ARG ALPINE_VERSION
WORKDIR ${SERVICE_DIR}
COPY --chown=node:node .env package.json loader.js tsconfig.json filestreamrotator.d.ts ./
COPY --chown=node:node node_modules ./node_modules
COPY --chown=node:node src ./src

FROM node:$NODE_VERSION-alpine${ALPINE_VERSION} AS production
RUN apk --no-cache --update add curl openssl1.1-compat bind-tools
ARG SERVICE_DIR
WORKDIR ${SERVICE_DIR}
COPY --chown=node:node .env package.json loader.js tsconfig.json filestreamrotator.d.ts ./
COPY --chown=node:node --from=builder /${SERVICE_DIR}/node_modules ./node_modules
COPY --chown=node:node --from=builder /${SERVICE_DIR}/src ./src
COPY --chown=node:node logs ./logs

USER node
EXPOSE 3002

CMD ["npm", "run", "start"]
