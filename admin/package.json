{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3006", "build": "next build", "start": "next start -p 3006", "test": "node --test", "lint": "./node_modules/.bin/eslint ./src --ignore-pattern '*.config.js'"}, "dependencies": {"@firebase/messaging-compat": "0.2.17", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui-float/react": "0.14.2", "@headlessui/react": "1.7.7", "@heroui/react": "^2.7.8", "@heroui/toast": "^2.0.11", "@prisma/client": "6.2.1", "@react-aria/visually-hidden": "3.8.12", "@react-stately/toast": "^3.1.1", "@rehookify/datepicker": "6.3.0", "@tanstack/react-table": "8.5.13", "autoprefixer": "^10.4.7", "axios": "^1.5.1", "cookies-next": "^4.2.1", "date-fns": "^2.30.0", "dayjs": "^1.11.7", "file-stream-rotator": "^0.6.1", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.30.1", "next": "13.5.8", "next-csrf": "^0.2.1", "next-themes": "^0.2.1", "pino": "^8.8.0", "pino-http": "^8.3.2", "postcss": "^8.4.13", "prisma": "6.2.1", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "tailwindcss": "^3.4.3", "uuid": "^11.0.3"}, "devDependencies": {"@eslint/js": "^8.57.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "24.0.1", "@types/react": "18.2.25", "@types/react-dom": "18.2.25", "@typescript-eslint/eslint-plugin": "8.16.0", "@typescript-eslint/parser": "8.16.0", "eslint": "9.16.0", "eslint-define-config": "2.1.0", "eslint-plugin-react": "7.37.2", "eslint-plugin-react-hooks": "5.0.0", "globals": "15.14.0", "nodemon": "^3.0.3", "prettier": "3.4.2", "tailwind-variants": "0.1.20", "typescript": "5.7.2", "typescript-eslint": "8.18.1"}, "prisma": {"seed": "node ./.prisma/seed/index"}, "eslintConfig": "eslint.config.mjs"}