ARG NODE_VERSION=20
ARG ENV_NAME=prod
ARG SERVICE_DIR=admin
ARG ALPINE_VERSION="3.18"

FROM node:$NODE_VERSION-alpine${ALPINE_VERSION} AS builder
ARG SERVICE_DIR
WORKDIR ${SERVICE_DIR}
COPY --chown=node:node .env package.json config.ts next.config.js postcss.config.js tailwind.config.js tsconfig.json ./
COPY --chown=node:node node_modules ./node_modules
COPY --chown=node:node public ./public
COPY --chown=node:node src ./src
RUN npm run build

FROM node:$NODE_VERSION-alpine AS production
RUN apk add --no-cache --update bind-tools
ARG SERVICE_DIR
WORKDIR ${SERVICE_DIR}
COPY --chown=node:node .env package.json ./
COPY --chown=node:node src ./src
COPY --chown=node:node public ./public
COPY --chown=node:node prisma ./.prisma
COPY --chown=node:node --from=builder /${SERVICE_DIR}/node_modules/ ./node_modules
COPY --chown=node:node --from=builder /${SERVICE_DIR}/.next ./.next
COPY --chown=node:node logs ./logs

USER node
EXPOSE 3006

CMD ["npm", "start"]