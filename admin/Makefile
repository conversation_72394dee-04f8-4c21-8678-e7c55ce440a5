#env
ENV_NAME=${env}

# service information
SERVICE_NAME=admin
SERVICE_DIR=admin
GIT_REPO_ROOT=`pwd`/../

# build constants
COMMIT_SHA:=$(shell git rev-parse --short=9 HEAD)
BRANCH_NAME:=$(shell git rev-parse --abbrev-ref HEAD | tr '/' '-')

IMAGE_URL=gcr.io/acqwired-${ENV_NAME}/${SERVICE_NAME}

IMAGE_WITH_COMMIT=${IMAGE_URL}:${COMMIT_SHA}
IMAGE_WITH_BRANCH:=${IMAGE_URL}:${BRANCH_NAME}
IMAGE_WITH_LATEST:=${IMAGE_URL}:latest

help:: ## show this help text
	@gawk -vG=$$(tput setaf 2) -vR=$$(tput sgr0) ' \
	  match($$0, "^(([^#:]*[^ :]) *:)?([^#]*)##([^#].+|)$$",a) { \
	    if (a[2] != "") { printf "    make %s%-18s%s %s\n", G, a[2], R, a[4]; next }\
	    if (a[3] == "") { print a[4]; next }\
	    printf "\n%-36s %s\n","",a[4]\
	  }' $(MAKEFILE_LIST)
	@echo "" # blank line at the end
.DEFAULT_GOAL := help

.PHONY: set-env
set-env: ## set environment
	@rm -f .env.*
	@cp env/.env.${ENV_NAME} ./.env
	@echo "Admin Environment set to" ${ENV_NAME}

.PHONY: add-secret
add-secret: ## add a secret, usage: make add-secret secret="KEY=VAL" env=ENV_NAME
ifneq ("${secret}","")
	@(test -f .env && echo ${secret} >> .env && echo "Secret added to .env") || echo "ERROR: .env file does not exist"
else
	@echo usage: make add-secret secret="KEY=VAL" env=ENV_NAME
endif

.PHONY: install-deps
install-deps: ## install dependencies
	@npm ci;
	mkdir -p ./prisma
	cp ../postgresql/prisma/schema.prisma ./prisma/schema.prisma
	./node_modules/.bin/prisma generate --no-hints;

.PHONY: unit-test
unit-test: ## run tests
	@npm run test

.PHONY: build
build: ## build for an env, usage: make build env=ENV_NAME
	DOCKER_BUILDKIT=1 docker build \
		-f Dockerfile \
		-t ${IMAGE_WITH_COMMIT} \
		-t ${IMAGE_WITH_BRANCH} \
		-t ${IMAGE_WITH_LATEST} \
		--build-arg SERVICE_DIR=${SERVICE_DIR} \
		--build-arg NODE_VERSION=20 \
		--build-arg ENV_NAME=${ENV_NAME} .
	@echo "Build completed"
	@echo "Tags :"
	@echo ${IMAGE_WITH_COMMIT}
	@echo ${IMAGE_WITH_BRANCH}
	@echo ${IMAGE_WITH_LATEST}

.PHONY: push
push: ## push container to repository
	@docker push ${IMAGE_URL} --all-tags

.PHONY: set
set: ## deploy to k8s cluster
	kubectl set image deployments/acqwired-admin acqwired-admin=gcr.io/acqwired-${ENV_NAME}/admin:${COMMIT_SHA} 