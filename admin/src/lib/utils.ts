import * as React from 'react';
import moment from 'moment';

export function capitalize(str: string | any): string | any {
  if (typeof str !== 'string') {
    return str;
  }
  return str.charAt(0).toUpperCase() + str.toLowerCase().slice(1);
}

export function validatePassword(password: string): boolean {
  return /^(?=\S*?[A-Z])(?=\S*?[a-z])(?=\S*?[0-9])\S{5,}$/.test(password);
}

export function parseExpiresIn(expiresIn: string): number {
  const duration = parseInt(expiresIn, 10);
  const unitMatch = expiresIn.match(/[a-zA-Z]+/);

  if (!unitMatch) {
    throw new Error(`Invalid expiresIn format: ${expiresIn}`);
  }

  let unit = unitMatch[0];

  switch (unit) {
    case 's':
      unit = 'seconds';
      break;
    case 'm':
      unit = 'minutes';
      break;
    case 'h':
      unit = 'hours';
      break;
    case 'd':
      unit = 'days';
      break;
    default:
      throw new Error(`Invalid expiresIn format: ${expiresIn}`);
  }

  return moment.duration(duration, unit as moment.DurationInputArg2).asSeconds();
}

export const handleCopyText = (textToCopy: string, e: React.MouseEvent | Event, setCopySuccess: (success: boolean) => void): void => {
  const textarea = document.createElement('textarea');
  textarea.value = textToCopy;
  document.body.appendChild(textarea);
  textarea.select();
  document.execCommand('copy');
  document.body.removeChild(textarea);
  setCopySuccess(true);
  if (e && 'stopPropagation' in e) {
    e.stopPropagation();
  }
};

export const toPascalCase = (str: string): string => {
  return str.replaceAll(/_/g, ' ').replace(/(\w)(\w*)/g, function (_, firstChar: string, rest: string) {
    return firstChar.toUpperCase() + rest.toLowerCase();
  });
};

export const capitalizeWords = (string: string): string => {
  return string.replace(/\b\w/g, (char: string) => char.toUpperCase());
};

export const formatKey = (label: string): string => {
  // Convert to lowercase and replace unsupported characters with '_'
  let fieldKey = label.toLowerCase().replace(/[^a-z0-9_]/g, '_'); // Replace non-supported characters with '_'

  // Ensure the name does not start with a number
  if (/^[0-9]/.test(fieldKey)) {
    fieldKey = `_${fieldKey}`;
  }

  return fieldKey.replace(/_+/g, '_'); // Replace multiple underscores with a single underscore
};

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(amount);
}

export function getTimeAgo(date: Date | string | null | undefined): string {
  if (!date) return 'Unknown'; // Handle missing dates

  const now = new Date();
  const inputDate = typeof date === 'string' ? new Date(date) : date;
  const diffInSeconds = Math.floor((now.getTime() - inputDate.getTime()) / 1000);

  if (diffInSeconds < 10) return 'Just now';
  if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours} hours ago`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays === 1) return 'Yesterday';
  if (diffInDays < 7) return `${diffInDays} days ago`;

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) return `${diffInWeeks} weeks ago`;

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) return `${diffInMonths} months ago`;

  const diffInYears = Math.floor(diffInMonths / 12);
  return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;
}

export const calculateWeightDistribution = (weights: number[]): number[] => {
  const total = weights.reduce((sum, w) => sum + w, 0);

  if (total === 0) {
    // Avoid division by zero: distribute equally
    const equal = 100 / weights.length;
    return weights.map(() => equal);
  }

  return weights.map((w) => Number(((w / total) * 100).toFixed(2)));
};
