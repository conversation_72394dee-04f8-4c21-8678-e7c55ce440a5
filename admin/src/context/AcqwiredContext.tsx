import React, { createContext, ReactNode, useContext } from 'react';
import { AuthContext } from './AuthContext';

export interface IAcqwired {
  isLoading: boolean;
  userData: any;
}

const AcqwiredContext = createContext<IAcqwired>({
  isLoading: false,
  userData: null
});

const AcqwiredProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isLoading, userData } = useContext(AuthContext);

  return <AcqwiredContext.Provider value={{ isLoading, userData }}>{children}</AcqwiredContext.Provider>;
};

const useAcqwired = () => useContext(AcqwiredContext);

export { AcqwiredProvider, useAcqwired };
