import { useRouter } from 'next/router';
import React, { useState, createContext, useEffect, ReactNode } from 'react';
import { AuthService, UserService } from '../services';
import { TokenResponse } from '../services/TokenResponse';
import { IResult } from '../components/Result';

interface IAuthContext {
  isAdmin: boolean;
  isAuthenticated: boolean;
  isLoading: boolean;
  userData: TokenResponse | null;
  getUserAuth: (tok: any) => Promise<void>;
  userSignIn: ({ email, password }: { email: string; password: string }) => Promise<IResult>;
  userSignout: () => Promise<void>;
}

const AuthContext = createContext<IAuthContext>({
  isAdmin: false,
  isAuthenticated: false,
  isLoading: false,
  userData: null,
  getUserAuth: async () => {},
  userSignIn: async () => {
    return {} as any;
  },
  userSignout: async () => {}
});

interface IAuthProviderProps {
  children: ReactNode;
}

const AuthProvider: React.FC<IAuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userData, setUserData] = useState<any>(null);

  const router = useRouter();

  const resetAuthState = () => {
    setIsAuthenticated(false);
    setIsAdmin(false);
    setIsLoading(false);
  };

  const navigateToLogin = () => {
    resetAuthState();
    if (router.pathname === '/login') {
      router.push(router.asPath);
    } else {
      router.push({
        pathname: '/login',
        query: { returnUrl: router.asPath }
      });
    }
  };

  const checkAdminPermission = (tokenData: TokenResponse | null) => {
    const permissions = tokenData?.scope?.split(' ').map((permission) => permission.trim());
    setIsAdmin(permissions?.includes('admin') || false);
  };

  const getUserAuth = async () => {
    if (localStorage) {
      const storedData = localStorage.getItem('userData');

      if (storedData) {
        const parsedToken: TokenResponse = JSON.parse(storedData);
        setUserData(parsedToken);
        checkAdminPermission(parsedToken);
      }

      try {
        const auth = await UserService.validateAuth();
        if (!auth) {
          setUserData(null);
          navigateToLogin();
        } else {
          setIsAuthenticated(true);
        }
      } catch (err) {
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const userSignIn = async ({ email, password }: { email: string; password: string }): Promise<IResult> => {
    const result = await AuthService.signin({ email, password });

    if (result.success) {
      localStorage.setItem('userData', JSON.stringify(result.data));
      setUserData(result.data);
      await getUserAuth();
      if (router.query?.returnUrl && router.query?.returnUrl !== '/') {
        await router.push(router?.query?.returnUrl as string);
      } else {
        await router.push('/');
      }
    }

    return result;
  };

  const userSignout = async () => {
    localStorage.removeItem('userData');
    setUserData(null);
    await UserService.signout();
    router.push({ pathname: '/logout', query: { returnUrl: router.asPath } });
  };

  useEffect(() => {
    if (router.route !== '/accept-invitation' && router.route !== '/account/resetpassword/[hash]') {
      getUserAuth();
    }
  }, []);

  return <AuthContext.Provider value={{ isAdmin, isAuthenticated, isLoading, userData, getUserAuth, userSignIn, userSignout }}>{children}</AuthContext.Provider>;
};

export { AuthContext, AuthProvider };
