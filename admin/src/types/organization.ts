export interface IOrganization {
  id: string;
  name: string;
  aiSummary: string;
  website: string;
  createdAt: string;
  membership?: {
    id: string;
    type: string;
    maxUser: number;
    tier: string;
    status: string;
    organizationType: string;
    creditBalance: number;
    endsAt?: string;
  };
  addresses?: Array<{
    id: string;
    title: string;
    addressLine1: string;
    addressLine2?: string;
    zipCode: number;
    purpose: string;
    city: {
      id: string;
      cityName: string;
      phoneCode?: string;
      state: {
        id: string;
        state: string;
        country: {
          id: string;
          country: string;
          isoCode: string;
          phoneCode?: string;
        };
      };
    };
  }>;
  orgContacts?: Array<{
    id: string;
    type: string;
    purpose: string;
    value: string;
    label?: string;
  }>;
  orgIntegrations?: Array<{
    id: string;
    provider: string;
    name: string;
    type: string;
    active: boolean;
    expiresAt: string;
    authToken?: any;
    data?: {
      aiCredits?: number;
    };
  }>;
}

// Keep the old interface for backward compatibility
export interface IOrganizationLegacy {
  info: {
    id: string;
    name: string;
    website: string;
    status: string;
    type: string;
  };
}

export interface IApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// Organization creation form types
export interface ICreateOrganizationRequest {
  contact: {
    email: string;
    firstName: string;
    lastName?: string;
  };
  firmType: string;
  firmName: string;
  aiSummary: string;
  website: string;
  membership: {
    type: string;
    maxUsers: number;
    initialCredit?: number;
  };
  integrations?: Array<{
    provider: string;
    activeUntil?: string;
    keyName: string;
    key: string;
    type: string;
    aiCredits: number;
  }>;
  address: {
    title: string;
    addressLine1: string;
    addressLine2?: string;
    zipCode: number;
    purpose: string;
    city: {
      cityName: string;
      state: {
        state: string;
        country: {
          country: string;
          isoCode: string;
        };
      };
    };
  };
  phone?: Array<{
    phone: string;
    contactName: string;
    purpose?: string;
  }>;
}

export interface ICreateOrganizationResponse {
  isInvited: boolean;
  orgId: string;
  userId: string;
}

export interface IOrganizationFormData {
  orgTypes: string[];
  membershipTypes: string[];
  contactPurposes: string[];
  integrationProviders: string[];
  integrationTypes: string[];
  orgStatuses: string[];
  orgTiers: string[];
}
