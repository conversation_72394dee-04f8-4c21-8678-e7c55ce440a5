import { instance, nonInterceptedInstance } from './Http';
import { Result } from '../components/Result';
import { TokenResponse } from './TokenResponse';

const api = '/api/v1/auth';

const AuthService = {
  async signin({ email, password }: { email: string; password: string }): Promise<Result<TokenResponse>> {
    try {
      const response = await nonInterceptedInstance.post<any>('/api/account/login', { email, password });

      return { success: true, message: '', statusCode: response.status, data: response.data };
    } catch (err: any) {
      console.error(err);
      return { success: false, message: err.message, statusCode: err.response.status, data: null };
    }
  },

  async verifyInvitationHash(hash: any) {
    try {
      const response = await instance.post<any>(`${api}/check-invitation`, { hash });
      return response.data;
    } catch (err) {
      console.error(err);
    }
  },

  async checkHash(hash: any) {
    try {
      const response = await instance.post<any>(`${api}/check-hash`, { hash });
      return response.data;
    } catch (err) {
      console.error(err);
    }
  },

  async refreshToken(): Promise<void> {
    try {
      await nonInterceptedInstance.post<any>('/api/account/refresh');
    } catch (err: any) {
      console.error(err);
      throw err;
    }
  },

  async acceptInvitation(form: any) {
    try {
      const response = await instance.post<any>(`/api/invitation/accept-invitation`, { user: form });
      return response.data;
    } catch (err) {
      console.error(err);
    }
  }
};

export default AuthService;
