import { instance } from './Http';
import { IApiResponse } from '../types/organization';

export interface ICreditOverview {
  currentBalance: number;
  totalPurchased: number;
  totalUsed: number;
  tier: string;
  nextExpiration: string | null;
  activePurchasesCount: number;
}

export interface ICreditPurchase {
  id: number;
  creditAmount: number;
  purchasePrice: number;
  expiresAt: string | null;
  purchasedAt: string;
  createdAt: string;
}

export interface ICreditUsage {
  id: string;
  apiProvider: string;
  apiOperation: string;
  apiUnit: string;
  apiAmount: number;
  usedFor: string;
  tier: string;
  creditCost: number;
  realCost: number;
  metadata: any;
  createdAt: string;
}

export interface ICreditTransaction {
  id: string;
  type: string;
  creditUsageId: string | null;
  creditPurchaseId: number | null;
  creditAmount: number;
  metadata: any;
  createdAt: string;
  creditUsage?: {
    apiProvider: string;
    apiOperation: string;
    usedFor: string;
  } | null;
  creditPurchase?: {
    purchasePrice: number;
  } | null;
}

export interface IPaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface IAddCreditPurchaseRequest {
  creditAmount: number;
  purchasePrice: number;
  expiresAt?: string;
  notes?: string;
}

export interface IAddCreditPurchaseResponse {
  purchase: ICreditPurchase;
  newBalance: number;
}

const backofficeapi = '/api/v1/backoffice';

class CreditService {
  async getOrganizationCredits(orgId: string): Promise<ICreditOverview> {
    try {
      const response = await instance.get<IApiResponse<ICreditOverview>>(`${backofficeapi}/organization/${orgId}/credits`);
      return response.data.data;
    } catch (err) {
      console.error('Error fetching organization credits:', err);
      throw err;
    }
  }

  async getCreditPurchases(orgId: string, page: number = 1, limit: number = 10): Promise<IPaginatedResponse<ICreditPurchase>> {
    try {
      const response = await instance.get<IApiResponse<{ purchases: ICreditPurchase[]; pagination: any }>>(`${backofficeapi}/organization/${orgId}/credit-purchases?page=${page}&limit=${limit}`);
      return {
        data: response.data.data.purchases,
        pagination: response.data.data.pagination
      };
    } catch (err) {
      console.error('Error fetching credit purchases:', err);
      throw err;
    }
  }

  async getCreditUsage(orgId: string, page: number = 1, limit: number = 10): Promise<IPaginatedResponse<ICreditUsage>> {
    try {
      const response = await instance.get<IApiResponse<{ usage: ICreditUsage[]; pagination: any }>>(`${backofficeapi}/organization/${orgId}/credit-usage?page=${page}&limit=${limit}`);
      return {
        data: response.data.data.usage,
        pagination: response.data.data.pagination
      };
    } catch (err) {
      console.error('Error fetching credit usage:', err);
      throw err;
    }
  }

  async getCreditTransactions(orgId: string, page: number = 1, limit: number = 10): Promise<IPaginatedResponse<ICreditTransaction>> {
    try {
      const response = await instance.get<IApiResponse<{ transactions: ICreditTransaction[]; pagination: any }>>(`${backofficeapi}/organization/${orgId}/credit-transactions?page=${page}&limit=${limit}`);
      return {
        data: response.data.data.transactions,
        pagination: response.data.data.pagination
      };
    } catch (err) {
      console.error('Error fetching credit transactions:', err);
      throw err;
    }
  }

  async addCreditPurchase(orgId: string, creditPurchase: IAddCreditPurchaseRequest): Promise<IAddCreditPurchaseResponse> {
    try {
      const response = await instance.post<IApiResponse<IAddCreditPurchaseResponse>>(`${backofficeapi}/organization/${orgId}/credit-purchase`, creditPurchase);
      return response.data.data;
    } catch (err) {
      console.error('Error adding credit purchase:', err);
      throw err;
    }
  }
}

export default new CreditService();
