import axios, { AxiosInstance, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';
import AuthService from './Auth';

interface PromiseQueueItem {
  resolve: () => void;
  reject: (error: AxiosError) => void;
}

const instance: AxiosInstance = axios.create();
const nonInterceptedInstance: AxiosInstance = axios.create();
let onSignOut: (() => void) | null = null;

export const setSignOutHandler = (signOutHandler: () => void) => {
  onSignOut = signOutHandler;
};

// Helper function to get cookie value
const getCookie = (name: string): string | null => {
  if (typeof document === 'undefined') return null;
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
  return null;
};

// Request interceptor to add auth token
instance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getCookie('admin_accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

let isRefreshing: boolean = false;
let failedQueue: PromiseQueueItem[] = [];

const processQueue = (error: AxiosError | null): void => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve();
    }
  });

  failedQueue = [];
};

instance.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest: InternalAxiosRequestConfig & { _retry?: boolean } = error.config!;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise<AxiosResponse>((resolve, reject) => {
          failedQueue.push({
            resolve: () => resolve(instance(originalRequest)),
            reject
          });
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      return new Promise<AxiosResponse>((resolve, reject) => {
        AuthService.refreshToken()
          .then(() => {
            processQueue(null);
            resolve(instance(originalRequest));
          })
          .catch((err: AxiosError) => {
            onSignOut?.();
            reject(err);
          })
          .finally(() => {
            isRefreshing = false;
          });
      });
    }

    return Promise.reject(error);
  }
);

export interface IHttpErrorResponse {
  data: {
    msg: string | undefined;
  };
}

export { nonInterceptedInstance, instance };
