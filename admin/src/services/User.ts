import { nonInterceptedInstance, instance } from './Http';

const api = '/api/v1/user';

const UserService = {
  async signout(): Promise<boolean> {
    try {
      const response = await nonInterceptedInstance.post<any>('/api/account/logout');
      return response.status >= 200 && response.status < 400;
    } catch (err: any) {
      console.error(err);
      return false;
    }
  },

  async validateAuth() {
    try {
      const { data } = await instance.post<any>(`${api}/verify`);
      const { valid } = data.data;
      return valid;
    } catch (err) {
      console.error(err);
      return false;
    }
  },

  async resetPassword(hash: any, password: any, password2: any) {
    try {
      const response = await instance.put<any>(`/api/account/reset-password`, { hash, password, password2 });
      return response.data;
    } catch (err) {
      console.error(err);
      return err;
    }
  },

  async changePassword(currentPassword: any, newPassword: any, confirmPassword: any) {
    try {
      const response = await instance.put<any>(`${api}/changepassword`, { currentPassword, newPassword, confirmPassword });
      return response.data.data;
    } catch (err) {
      return err;
    }
  }
};

export default UserService;
