import { instance } from './Http';
import { IOrganization, IApiResponse, ICreateOrganizationRequest, ICreateOrganizationResponse, IOrganizationFormData } from '../types/organization';

const backofficeapi = '/api/v1/backoffice';

const OrganizationService = {
  async getAllOrganizations(): Promise<IOrganization[]> {
    try {
      const response = await instance.get<IApiResponse<IOrganization[]>>(`${backofficeapi}/organizations`);
      return response.data.data || [];
    } catch (err) {
      console.error('Error fetching organizations:', err);
      return [];
    }
  },

  async getOrganizationById(id: string): Promise<IOrganization | null> {
    try {
      const response = await instance.get<IApiResponse<IOrganization>>(`${backofficeapi}/organization/${id}`);
      return response.data.data || null;
    } catch (err) {
      console.error('Error fetching organization:', err);
      return null;
    }
  },

  async getOrganizationFormData(): Promise<IOrganizationFormData | null> {
    try {
      const response = await instance.get<IApiResponse<IOrganizationFormData>>(`${backofficeapi}/organization-form-data`);
      return response.data.data;
    } catch (err) {
      console.error('Failed to fetch organization form data:', err);
      return null;
    }
  },

  async createOrganization(data: ICreateOrganizationRequest): Promise<ICreateOrganizationResponse | null> {
    try {
      const response = await instance.post<IApiResponse<ICreateOrganizationResponse>>(`${backofficeapi}/invite-organization`, data);
      return response.data.data;
    } catch (err) {
      console.error('Failed to create organization:', err);
      throw err;
    }
  },

  async updateOrganization(orgId: string, data: Partial<IOrganization>): Promise<IOrganization> {
    try {
      const response = await instance.put<IApiResponse<IOrganization>>(`${backofficeapi}/organization/${orgId}`, data);
      return response.data.data;
    } catch (err) {
      console.error('Error updating organization:', err);
      throw err;
    }
  },

  // Address Management
  async addOrganizationAddress(orgId: string, address: any): Promise<any> {
    try {
      const response = await instance.post<IApiResponse<any>>(`${backofficeapi}/organization/${orgId}/addresses`, address);
      return response.data.data;
    } catch (err) {
      console.error('Error adding address:', err);
      throw err;
    }
  },

  async updateOrganizationAddress(addressId: string, address: any): Promise<any> {
    try {
      const response = await instance.put<IApiResponse<any>>(`${backofficeapi}/organization/addresses/${addressId}`, address);
      return response.data.data;
    } catch (err) {
      console.error('Error updating address:', err);
      throw err;
    }
  },

  async deleteOrganizationAddress(addressId: string): Promise<any> {
    try {
      const response = await instance.delete<IApiResponse<any>>(`${backofficeapi}/organization/addresses/${addressId}`);
      return response.data.data;
    } catch (err) {
      console.error('Error deleting address:', err);
      throw err;
    }
  },

  // Contact Management
  async addOrganizationContact(orgId: string, contact: any): Promise<any> {
    try {
      const response = await instance.post<IApiResponse<any>>(`${backofficeapi}/organization/${orgId}/contacts`, contact);
      return response.data.data;
    } catch (err) {
      console.error('Error adding contact:', err);
      throw err;
    }
  },

  async updateOrganizationContact(contactId: string, contact: any): Promise<any> {
    try {
      const response = await instance.put<IApiResponse<any>>(`${backofficeapi}/organization/contacts/${contactId}`, contact);
      return response.data.data;
    } catch (err) {
      console.error('Error updating contact:', err);
      throw err;
    }
  },

  async deleteOrganizationContact(contactId: string): Promise<any> {
    try {
      const response = await instance.delete<IApiResponse<any>>(`${backofficeapi}/organization/contacts/${contactId}`);
      return response.data.data;
    } catch (err) {
      console.error('Error deleting contact:', err);
      throw err;
    }
  },

  // Integration Management
  async addOrganizationIntegration(orgId: string, integration: any): Promise<any> {
    try {
      const response = await instance.post<IApiResponse<any>>(`${backofficeapi}/organization/${orgId}/integrations`, integration);
      return response.data.data;
    } catch (err) {
      console.error('Error adding integration:', err);
      throw err;
    }
  },

  async updateOrganizationIntegration(integrationId: string, integration: any): Promise<any> {
    try {
      const response = await instance.put<IApiResponse<any>>(`${backofficeapi}/organization/integrations/${integrationId}`, integration);
      return response.data.data;
    } catch (err) {
      console.error('Error updating integration:', err);
      throw err;
    }
  },

  async deleteOrganizationIntegration(integrationId: string): Promise<any> {
    try {
      const response = await instance.delete<IApiResponse<any>>(`${backofficeapi}/organization/integrations/${integrationId}`);
      return response.data.data;
    } catch (err) {
      console.error('Error deleting integration:', err);
      throw err;
    }
  }
};

export default OrganizationService;
