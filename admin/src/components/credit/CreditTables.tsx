import React, { useState } from 'react';
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Chip, Pagination, Card, CardHeader, CardBody, Tabs, Tab, Spinner } from '@heroui/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCreditCard, faChartLine, faExchangeAlt, faCalendarAlt, faDollarSign, faCoins } from '@fortawesome/free-solid-svg-icons';
import { ICreditPurchase, ICreditUsage, ICreditTransaction, IPaginatedResponse } from '../../services/Credit';

interface CreditTablesProps {
  purchases: IPaginatedResponse<ICreditPurchase> | null;
  usage: IPaginatedResponse<ICreditUsage> | null;
  transactions: IPaginatedResponse<ICreditTransaction> | null;
  loading: {
    purchases: boolean;
    usage: boolean;
    transactions: boolean;
  };
  onPageChange: {
    purchases: (page: number) => void;
    usage: (page: number) => void;
    transactions: (page: number) => void;
  };
}

const CreditTables: React.FC<CreditTablesProps> = ({ purchases, usage, transactions, loading, onPageChange }) => {
  const [activeTab, setActiveTab] = useState('purchases');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'purchase':
        return 'success';
      case 'usage':
        return 'warning';
      case 'refund':
        return 'primary';
      default:
        return 'default';
    }
  };

  const getApiProviderColor = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'openai':
        return 'success';
      case 'anthropic':
        return 'secondary';
      case 'google':
        return 'warning';
      default:
        return 'primary';
    }
  };

  const renderPurchasesTable = () => (
    <Card>
      <CardHeader className="flex gap-2 items-center">
        <FontAwesomeIcon icon={faCreditCard} className="text-primary" />
        <h3 className="text-lg font-semibold">Credit Purchases</h3>
      </CardHeader>
      <CardBody>
        {loading.purchases ? (
          <div className="flex justify-center py-8">
            <Spinner size="lg" />
          </div>
        ) : (
          <>
            <Table aria-label="Credit purchases table">
              <TableHeader>
                <TableColumn>PURCHASE DATE</TableColumn>
                <TableColumn>CREDIT AMOUNT</TableColumn>
                <TableColumn>PURCHASE PRICE</TableColumn>
                <TableColumn>COST PER CREDIT</TableColumn>
                <TableColumn>EXPIRES AT</TableColumn>
              </TableHeader>
              <TableBody emptyContent="No credit purchases found">
                {(purchases?.data || []).map((purchase) => (
                  <TableRow key={purchase.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <FontAwesomeIcon icon={faCalendarAlt} className="text-gray-400 text-sm" />
                        {formatDate(purchase.purchasedAt)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <FontAwesomeIcon icon={faCoins} className="text-success text-sm" />
                        <span className="font-semibold text-success">{formatCurrency(purchase.creditAmount)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <FontAwesomeIcon icon={faDollarSign} className="text-primary text-sm" />
                        {formatCurrency(purchase.purchasePrice)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-gray-600">{formatCurrency(purchase.purchasePrice / purchase.creditAmount)}</span>
                    </TableCell>
                    <TableCell>
                      {purchase.expiresAt ? (
                        <Chip size="sm" color={new Date(purchase.expiresAt) > new Date() ? 'success' : 'danger'} variant="flat">
                          {formatDate(purchase.expiresAt)}
                        </Chip>
                      ) : (
                        <Chip size="sm" color="primary" variant="flat">
                          Never expires
                        </Chip>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {purchases && purchases.pagination.totalPages > 1 && (
              <div className="flex justify-center mt-4">
                <Pagination total={purchases.pagination.totalPages} page={purchases.pagination.page} onChange={onPageChange.purchases} showControls />
              </div>
            )}
          </>
        )}
      </CardBody>
    </Card>
  );

  const renderUsageTable = () => (
    <Card>
      <CardHeader className="flex gap-2 items-center">
        <FontAwesomeIcon icon={faChartLine} className="text-warning" />
        <h3 className="text-lg font-semibold">Credit Usage</h3>
      </CardHeader>
      <CardBody>
        {loading.usage ? (
          <div className="flex justify-center py-8">
            <Spinner size="lg" />
          </div>
        ) : (
          <>
            <Table aria-label="Credit usage table">
              <TableHeader>
                <TableColumn>DATE</TableColumn>
                <TableColumn>API PROVIDER</TableColumn>
                <TableColumn>OPERATION</TableColumn>
                <TableColumn>AMOUNT</TableColumn>
                <TableColumn>CREDIT COST</TableColumn>
                <TableColumn>USED FOR</TableColumn>
              </TableHeader>
              <TableBody emptyContent="No credit usage found">
                {(usage?.data || []).map((usageItem) => (
                  <TableRow key={usageItem.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <FontAwesomeIcon icon={faCalendarAlt} className="text-gray-400 text-sm" />
                        {formatDate(usageItem.createdAt)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Chip size="sm" color={getApiProviderColor(usageItem.apiProvider)} variant="flat">
                        {usageItem.apiProvider}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{usageItem.apiOperation}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">
                        {usageItem.apiAmount} {usageItem.apiUnit}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <FontAwesomeIcon icon={faCoins} className="text-warning text-sm" />
                        <span className="font-semibold text-warning">{formatCurrency(usageItem.creditCost)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Chip size="sm" color="default" variant="flat">
                        {usageItem.usedFor}
                      </Chip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {usage && usage.pagination.totalPages > 1 && (
              <div className="flex justify-center mt-4">
                <Pagination total={usage.pagination.totalPages} page={usage.pagination.page} onChange={onPageChange.usage} showControls />
              </div>
            )}
          </>
        )}
      </CardBody>
    </Card>
  );

  const renderTransactionsTable = () => (
    <Card>
      <CardHeader className="flex gap-2 items-center">
        <FontAwesomeIcon icon={faExchangeAlt} className="text-secondary" />
        <h3 className="text-lg font-semibold">Credit Transactions</h3>
      </CardHeader>
      <CardBody>
        {loading.transactions ? (
          <div className="flex justify-center py-8">
            <Spinner size="lg" />
          </div>
        ) : (
          <>
            <Table aria-label="Credit transactions table">
              <TableHeader>
                <TableColumn>DATE</TableColumn>
                <TableColumn>TYPE</TableColumn>
                <TableColumn>AMOUNT</TableColumn>
                <TableColumn>DETAILS</TableColumn>
              </TableHeader>
              <TableBody emptyContent="No credit transactions found">
                {(transactions?.data || []).map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <FontAwesomeIcon icon={faCalendarAlt} className="text-gray-400 text-sm" />
                        {formatDate(transaction.createdAt)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Chip size="sm" color={getTransactionTypeColor(transaction.type)} variant="flat">
                        {transaction.type}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <FontAwesomeIcon icon={faCoins} className="text-primary text-sm" />
                        <span className={`font-semibold ${transaction.type === 'purchase' ? 'text-success' : 'text-warning'}`}>
                          {transaction.type === 'purchase' ? '+' : '-'}
                          {formatCurrency(transaction.creditAmount)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-gray-600">
                        {transaction.creditUsage && (
                          <span>
                            {transaction.creditUsage.apiProvider} - {transaction.creditUsage.apiOperation}
                          </span>
                        )}
                        {transaction.creditPurchase && <span>Purchase - {formatCurrency(transaction.creditPurchase.purchasePrice)}</span>}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {transactions && transactions.pagination.totalPages > 1 && (
              <div className="flex justify-center mt-4">
                <Pagination total={transactions.pagination.totalPages} page={transactions.pagination.page} onChange={onPageChange.transactions} showControls />
              </div>
            )}
          </>
        )}
      </CardBody>
    </Card>
  );

  return (
    <div className="space-y-6">
      <Tabs selectedKey={activeTab} onSelectionChange={(key) => setActiveTab(key as string)} color="primary" variant="underlined">
        <Tab key="purchases" title="Purchases">
          {renderPurchasesTable()}
        </Tab>
        <Tab key="usage" title="Usage">
          {renderUsageTable()}
        </Tab>
        <Tab key="transactions" title="Transactions">
          {renderTransactionsTable()}
        </Tab>
      </Tabs>
    </div>
  );
};

export default CreditTables;
