import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ody, <PERSON>dal<PERSON>ooter, Button, Input, Textarea, Card, CardBody } from '@heroui/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faCoins, faDollarSign, faCalendarAlt } from '@fortawesome/free-solid-svg-icons';
import { IAddCreditPurchaseRequest } from '../../services/Credit';

interface AddCreditFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (creditPurchase: IAddCreditPurchaseRequest) => Promise<void>;
  loading?: boolean;
}

const AddCreditForm: React.FC<AddCreditFormProps> = ({ isOpen, onClose, onSubmit, loading = false }) => {
  const [formData, setFormData] = useState<IAddCreditPurchaseRequest>({
    creditAmount: 0,
    purchasePrice: 0,
    expiresAt: '',
    notes: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Helper function to clear specific field errors
  const clearFieldError = (fieldName: string) => {
    if (errors[fieldName]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Credit amount validation
    if (!formData.creditAmount || formData.creditAmount <= 0) {
      newErrors.creditAmount = 'Credit amount must be greater than 0';
    }

    // Purchase price validation
    if (!formData.purchasePrice || formData.purchasePrice <= 0) {
      newErrors.purchasePrice = 'Purchase price must be greater than 0';
    }

    // Expiration date validation (optional)
    if (formData.expiresAt) {
      const expirationDate = new Date(formData.expiresAt);
      const now = new Date();
      if (expirationDate <= now) {
        newErrors.expiresAt = 'Expiration date must be in the future';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const submitData = {
        ...formData,
        expiresAt: formData.expiresAt || undefined
      };
      await onSubmit(submitData);
      handleClose();
    } catch (error) {
      console.error('Error adding credit purchase:', error);
    }
  };

  const handleClose = () => {
    setFormData({
      creditAmount: 0,
      purchasePrice: 0,
      expiresAt: '',
      notes: ''
    });
    setErrors({});
    onClose();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const calculateCostPerCredit = () => {
    if (formData.creditAmount > 0 && formData.purchasePrice > 0) {
      return formData.purchasePrice / formData.creditAmount;
    }
    return 0;
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="2xl" scrollBehavior="inside">
      <ModalContent>
        <ModalHeader className="flex gap-2 items-center">
          <FontAwesomeIcon icon={faPlus} className="text-primary" />
          Add Credit Purchase
        </ModalHeader>
        <ModalBody>
          <div className="space-y-6">
            {/* Purchase Summary Card */}
            {(formData.creditAmount > 0 || formData.purchasePrice > 0) && (
              <Card className="bg-primary/5 border border-primary/20">
                <CardBody className="p-4">
                  <h4 className="font-semibold text-primary mb-2">Purchase Summary</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Credit Amount:</span>
                      <p className="font-semibold">{formatCurrency(formData.creditAmount)}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Total Price:</span>
                      <p className="font-semibold">{formatCurrency(formData.purchasePrice)}</p>
                    </div>
                    <div className="col-span-2">
                      <span className="text-gray-600">Cost per Credit:</span>
                      <p className="font-semibold text-primary">{formatCurrency(calculateCostPerCredit())}</p>
                    </div>
                  </div>
                </CardBody>
              </Card>
            )}

            {/* Form Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Credit Amount"
                type="number"
                min="0"
                step="0.01"
                value={formData.creditAmount.toString()}
                onChange={(e) => {
                  const value = parseFloat(e.target.value) || 0;
                  setFormData((prev) => ({ ...prev, creditAmount: value }));
                  clearFieldError('creditAmount');
                }}
                isInvalid={!!errors.creditAmount}
                errorMessage={errors.creditAmount}
                startContent={<FontAwesomeIcon icon={faCoins} className="text-gray-400" />}
                placeholder="0.00"
                isRequired
              />

              <Input
                label="Purchase Price"
                type="number"
                min="0"
                step="0.01"
                value={formData.purchasePrice.toString()}
                onChange={(e) => {
                  const value = parseFloat(e.target.value) || 0;
                  setFormData((prev) => ({ ...prev, purchasePrice: value }));
                  clearFieldError('purchasePrice');
                }}
                isInvalid={!!errors.purchasePrice}
                errorMessage={errors.purchasePrice}
                startContent={<FontAwesomeIcon icon={faDollarSign} className="text-gray-400" />}
                placeholder="0.00"
                isRequired
              />
            </div>

            <Input
              label="Expiration Date (Optional)"
              type="datetime-local"
              value={formData.expiresAt}
              onChange={(e) => {
                setFormData((prev) => ({ ...prev, expiresAt: e.target.value }));
                clearFieldError('expiresAt');
              }}
              isInvalid={!!errors.expiresAt}
              errorMessage={errors.expiresAt}
              startContent={<FontAwesomeIcon icon={faCalendarAlt} className="text-gray-400" />}
              description="Leave empty for credits that never expire"
            />

            <Textarea
              label="Notes (Optional)"
              value={formData.notes}
              onChange={(e) => {
                setFormData((prev) => ({ ...prev, notes: e.target.value }));
              }}
              placeholder="Add any notes about this credit purchase..."
              maxRows={3}
            />
          </div>
        </ModalBody>
        <ModalFooter>
          <Button color="danger" variant="light" onPress={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button color="primary" onPress={handleSubmit} isLoading={loading} startContent={!loading && <FontAwesomeIcon icon={faPlus} />}>
            Add Credits
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default AddCreditForm;
