import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import CreditOverview from './CreditOverview';
import CreditTables from './CreditTables';
import CreditService, { ICreditOverview, ICreditPurchase, ICreditUsage, ICreditTransaction, IPaginatedResponse } from '../../services/Credit';

interface CreditManagementProps {
  organizationId: string;
  onCreditUpdate?: () => void;
  onRefresh?: () => void;
  onAddCredits?: () => void;
}

const CreditManagement: React.FC<CreditManagementProps> = ({ organizationId, onCreditUpdate: _onCreditUpdate }) => {
  // State for credit data
  const [credits, setCredits] = useState<ICreditOverview | null>(null);
  const [purchases, setPurchases] = useState<IPaginatedResponse<ICreditPurchase> | null>(null);
  const [usage, setUsage] = useState<IPaginatedResponse<ICreditUsage> | null>(null);
  const [transactions, setTransactions] = useState<IPaginatedResponse<ICreditTransaction> | null>(null);

  // Loading states
  const [loading, setLoading] = useState({
    overview: true,
    purchases: true,
    usage: true,
    transactions: true,
    addCredit: false
  });

  // Pagination states
  const [_currentPages, _setCurrentPages] = useState({
    purchases: 1,
    usage: 1,
    transactions: 1
  });

  // Load credit overview
  const loadCreditOverview = async () => {
    try {
      setLoading((prev) => ({ ...prev, overview: true }));
      const data = await CreditService.getOrganizationCredits(organizationId);
      setCredits(data);
    } catch (error) {
      console.error('Error loading credit overview:', error);
      toast.error('Failed to load credit overview');
    } finally {
      setLoading((prev) => ({ ...prev, overview: false }));
    }
  };

  // Load credit purchases
  const loadCreditPurchases = async (page: number = 1) => {
    try {
      setLoading((prev) => ({ ...prev, purchases: true }));
      const data = await CreditService.getCreditPurchases(organizationId, page, 10);
      setPurchases(data);
      _setCurrentPages((prev) => ({ ...prev, purchases: page }));
    } catch (error) {
      console.error('Error loading credit purchases:', error);
      toast.error('Failed to load credit purchases');
    } finally {
      setLoading((prev) => ({ ...prev, purchases: false }));
    }
  };

  // Load credit usage
  const loadCreditUsage = async (page: number = 1) => {
    try {
      setLoading((prev) => ({ ...prev, usage: true }));
      const data = await CreditService.getCreditUsage(organizationId, page, 10);
      setUsage(data);
      _setCurrentPages((prev) => ({ ...prev, usage: page }));
    } catch (error) {
      console.error('Error loading credit usage:', error);
      toast.error('Failed to load credit usage');
    } finally {
      setLoading((prev) => ({ ...prev, usage: false }));
    }
  };

  // Load credit transactions
  const loadCreditTransactions = async (page: number = 1) => {
    try {
      setLoading((prev) => ({ ...prev, transactions: true }));
      const data = await CreditService.getCreditTransactions(organizationId, page, 10);
      setTransactions(data);
      _setCurrentPages((prev) => ({ ...prev, transactions: page }));
    } catch (error) {
      console.error('Error loading credit transactions:', error);
      toast.error('Failed to load credit transactions');
    } finally {
      setLoading((prev) => ({ ...prev, transactions: false }));
    }
  };

  // Page change handlers
  const handlePageChange = {
    purchases: (page: number) => loadCreditPurchases(page),
    usage: (page: number) => loadCreditUsage(page),
    transactions: (page: number) => loadCreditTransactions(page)
  };

  // Load initial data
  useEffect(() => {
    if (organizationId) {
      Promise.all([loadCreditOverview(), loadCreditPurchases(1), loadCreditUsage(1), loadCreditTransactions(1)]);
    }
  }, [organizationId]);

  return (
    <div className="space-y-6">
      {/* Credit Overview */}
      <CreditOverview credits={credits!} loading={loading.overview} />

      {/* Credit Tables */}
      <CreditTables
        purchases={purchases}
        usage={usage}
        transactions={transactions}
        loading={{
          purchases: loading.purchases,
          usage: loading.usage,
          transactions: loading.transactions
        }}
        onPageChange={handlePageChange}
      />
    </div>
  );
};

export default CreditManagement;
