import React from 'react';
import { <PERSON>, CardBody, CardHeader, Chip, Progress } from '@heroui/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCoins, faCreditCard, faChartLine, faClock } from '@fortawesome/free-solid-svg-icons';
import { ICreditOverview } from '../../services/Credit';

interface CreditOverviewProps {
  credits: ICreditOverview | null;
  loading?: boolean;
}

const CreditOverview: React.FC<CreditOverviewProps> = ({ credits, loading = false }) => {
  const usagePercentage = credits && credits.totalPurchased > 0 ? (credits.totalUsed / credits.totalPurchased) * 100 : 0;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'No expiration';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTierColor = (tier: string) => {
    switch (tier?.toLowerCase()) {
      case 'premium':
        return 'warning';
      case 'enterprise':
        return 'secondary';
      case 'basic':
      default:
        return 'primary';
    }
  };

  // If loading or no credits data, show skeleton
  if (loading || !credits) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardBody className="p-6">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded"></div>
            </CardBody>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Credit Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Current Balance */}
        <Card className="border-l-4 border-l-success">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Current Balance</p>
                <p className="text-2xl font-bold text-success">{formatCurrency(credits.currentBalance)}</p>
                <Chip size="sm" color={getTierColor(credits.tier)} variant="flat" className="mt-2">
                  {credits.tier} Tier
                </Chip>
              </div>
              <div className="p-3 bg-success/10 rounded-full">
                <FontAwesomeIcon icon={faCoins} className="text-success text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Total Purchased */}
        <Card className="border-l-4 border-l-primary">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Total Purchased</p>
                <p className="text-2xl font-bold text-primary">{formatCurrency(credits.totalPurchased)}</p>
                <p className="text-xs text-gray-500 mt-1">{credits.activePurchasesCount} active purchases</p>
              </div>
              <div className="p-3 bg-primary/10 rounded-full">
                <FontAwesomeIcon icon={faCreditCard} className="text-primary text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Total Used */}
        <Card className="border-l-4 border-l-warning">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Total Used</p>
                <p className="text-2xl font-bold text-warning">{formatCurrency(credits.totalUsed)}</p>
                <p className="text-xs text-gray-500 mt-1">{usagePercentage.toFixed(1)}% of purchased</p>
              </div>
              <div className="p-3 bg-warning/10 rounded-full">
                <FontAwesomeIcon icon={faChartLine} className="text-warning text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Next Expiration */}
        <Card className="border-l-4 border-l-danger">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Next Expiration</p>
                <p className="text-lg font-bold text-danger">{formatDate(credits.nextExpiration)}</p>
                {credits.nextExpiration && <p className="text-xs text-gray-500 mt-1">{Math.ceil((new Date(credits.nextExpiration).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days</p>}
              </div>
              <div className="p-3 bg-danger/10 rounded-full">
                <FontAwesomeIcon icon={faClock} className="text-danger text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Usage Progress */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Credit Usage Overview</h3>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span>Used: {formatCurrency(credits.totalUsed)}</span>
              <span>Remaining: {formatCurrency(credits.currentBalance)}</span>
            </div>
            <Progress
              value={usagePercentage}
              color="warning"
              className="w-full"
              showValueLabel={true}
              formatOptions={{
                style: 'percent',
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
              }}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>0%</span>
              <span>100%</span>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default CreditOverview;
