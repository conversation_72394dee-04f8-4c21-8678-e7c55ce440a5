import React, { useMemo, useState } from 'react';
import { ColumnDef, flexRender, getCoreRowModel, getSortedRowModel, SortingState, useReactTable } from '@tanstack/react-table';

export interface OrgTableColumn<T> {
  id: string;
  header: string;
  accessorKey: string;
  cell?: (row: T) => React.ReactNode;
}

interface OrgTableProps<T extends { id: string }> {
  columns: OrgTableColumn<T>[];
  data: T[];
  emptyMessage?: string;
}

export function OrgTable<T extends { id: string }>({ columns, data, emptyMessage }: OrgTableProps<T>) {
  const [sorting, setSorting] = useState<SortingState>([]);

  // Convert OrgTableColumn to ColumnDef for tanstack/react-table
  const columnDefs = useMemo<ColumnDef<T, any>[]>(
    () =>
      columns.map((col) => ({
        id: col.id,
        accessorKey: col.accessorKey,
        header: col.header,
        cell: col.cell ? (info) => (col.cell as (row: T) => React.ReactNode)(info.row.original) : (info) => String(info.getValue() ?? '')
      })),
    [columns]
  );

  const table = useReactTable({
    data,
    columns: columnDefs,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: { sorting },
    onSortingChange: setSorting,
    manualSorting: false
  });

  if (!data.length) return <div>{emptyMessage || 'No data'}</div>;

  return (
    <table className="min-w-full divide-y divide-gray-200">
      <thead>
        {table.getHeaderGroups().map((headerGroup) => (
          <tr key={headerGroup.id}>
            {headerGroup.headers.map((header) => (
              <th key={header.id} className="cursor-pointer text-left" onClick={header.column.getCanSort() ? header.column.getToggleSortingHandler() : undefined}>
                {flexRender(header.column.columnDef.header, header.getContext())}
                {header.column.getIsSorted() === 'asc' && ' ▲'}
                {header.column.getIsSorted() === 'desc' && ' ▼'}
              </th>
            ))}
          </tr>
        ))}
      </thead>
      <tbody>
        {table.getRowModel().rows.map((row) => (
          <tr key={row.id}>
            {row.getVisibleCells().map((cell) => (
              <td key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );
}

export default OrgTable;
