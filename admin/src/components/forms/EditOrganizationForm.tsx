import React, { useState, useEffect } from 'react';
import { Button, Input, Textarea, Select, SelectItem, Card, CardBody, CardHeader, Tabs, Tab, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure } from '@heroui/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSave, faTimes, faPlus, faEdit, faTrash } from '@fortawesome/free-solid-svg-icons';
import OrganizationService from '../../services/Organization';
import { IOrganization, IOrganizationFormData } from '../../types/organization';
import { formattedDisplayString } from '../../utils/string';
import { formatCurrency } from '../../lib/utils';
import { toast } from 'react-hot-toast';

interface EditOrganizationFormProps {
  organization: IOrganization;
  onSubmit: (data: Partial<IOrganization>) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

export default function EditOrganizationForm({ organization, onSubmit, onCancel, loading = false }: EditOrganizationFormProps) {
  const [formData, setFormData] = useState<IOrganization>(organization);
  const [formOptions, setFormOptions] = useState<IOrganizationFormData | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [modalErrors, setModalErrors] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState('basic');
  const [editingItem, setEditingItem] = useState<any>(null);
  const [editingType, setEditingType] = useState<'address' | 'contact' | 'integration' | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();

  // Helper function to clear specific field errors
  const clearFieldError = (fieldName: string) => {
    if (errors[fieldName]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  // Helper function to clear modal field errors
  const clearModalError = (fieldName: string) => {
    if (modalErrors[fieldName]) {
      setModalErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  useEffect(() => {
    const fetchFormData = async () => {
      const options = await OrganizationService.getOrganizationFormData();
      setFormOptions(options);
    };
    fetchFormData();
  }, []);

  const refreshOrganization = async () => {
    try {
      const updated = await OrganizationService.getOrganizationById(organization.id);
      if (updated) {
        setFormData(updated);
      }
    } catch (error) {
      console.error('Failed to refresh organization:', error);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Basic Info Validation
    if (!formData.name) newErrors.name = 'Organization name is required';
    if (!formData.website) newErrors.website = 'Website is required';
    if (!formData.aiSummary) newErrors.aiSummary = 'AI summary is required';

    // Website validation
    const urlRegex = /^https?:\/\/.+/;
    if (formData.website && !urlRegex.test(formData.website)) {
      newErrors.website = 'Website must be a valid URL (http:// or https://)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Validate modal forms (address, contact, integration)
  const validateModalForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (editingType === 'address') {
      if (!editingItem.title?.trim()) newErrors.title = 'Address title is required';
      if (!editingItem.addressLine1?.trim()) newErrors.addressLine1 = 'Address line 1 is required';
      if (!editingItem.zipCode || editingItem.zipCode <= 0) newErrors.zipCode = 'Zip code is required';
      if (!editingItem.city?.cityName?.trim()) newErrors.cityName = 'City is required';
      if (!editingItem.city?.state?.state?.trim()) newErrors.state = 'State is required';
      if (!editingItem.city?.state?.country?.country?.trim()) newErrors.country = 'Country is required';
      if (!editingItem.city?.state?.country?.isoCode?.trim()) {
        newErrors.isoCode = 'Country ISO code is required';
      } else if (!/^[A-Z]{2}$/.test(editingItem.city.state.country.isoCode.toUpperCase())) {
        newErrors.isoCode = 'Country ISO code must be exactly 2 letters (e.g., US, CA, GB)';
      }
    } else if (editingType === 'contact') {
      if (!editingItem.value?.trim()) newErrors.value = 'Contact value is required';
      if (!editingItem.type) newErrors.type = 'Contact type is required';

      // Email validation
      if (editingItem.type === 'email') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (editingItem.value && !emailRegex.test(editingItem.value)) {
          newErrors.value = 'Invalid email format';
        }
      }

      // Phone validation
      if (editingItem.type === 'phone') {
        if (editingItem.value && !/^[+]?[1-9][\d]{0,15}$/.test(editingItem.value.replace(/[\s\-()]/g, ''))) {
          newErrors.value = 'Invalid phone number format';
        }
      }
    } else if (editingType === 'integration') {
      if (!editingItem.provider) newErrors.provider = 'Provider is required';
      if (!editingItem.type) newErrors.type = 'Type is required';
      if (!editingItem.name?.trim()) newErrors.name = 'Name is required';

      // Only validate key if it's a new integration or if user entered a new key
      if (!editingItem.id && !editingItem.key?.trim()) {
        newErrors.key = 'API key is required for new integrations';
      }

      // Validate expiresAt only if provided (it's optional)
      if (editingItem.expiresAt) {
        const expiresDate = new Date(editingItem.expiresAt);
        const now = new Date();
        if (expiresDate <= now) {
          newErrors.expiresAt = 'Expiration date must be in the future';
        }
      }
    }

    setModalErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.SyntheticEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    await onSubmit(formData);
  };

  // Address Management
  const handleAddAddress = () => {
    setEditingItem({
      title: '',
      addressLine1: '',
      addressLine2: '',
      zipCode: '',
      purpose: 'billing',
      city: {
        cityName: '',
        state: {
          state: '',
          country: {
            country: '',
            isoCode: ''
          }
        }
      }
    });
    setEditingType('address');
    setModalErrors({});
    onOpen();
  };

  const handleEditAddress = (address: any) => {
    setEditingItem(address);
    setEditingType('address');
    setModalErrors({});
    onOpen();
  };

  const handleSaveAddress = async () => {
    if (!validateModalForm()) {
      return;
    }

    try {
      if (editingItem.id) {
        await OrganizationService.updateOrganizationAddress(editingItem.id, editingItem);
        toast.success('Address updated successfully');
      } else {
        await OrganizationService.addOrganizationAddress(organization.id, editingItem);
        toast.success('Address added successfully');
      }
      await refreshOrganization();
      setModalErrors({});
      onClose();
    } catch (error: any) {
      toast.error(error.message || 'Failed to save address');
    }
  };

  const handleDeleteAddress = async (addressId: string) => {
    try {
      await OrganizationService.deleteOrganizationAddress(addressId);
      toast.success('Address deleted successfully');
      await refreshOrganization();
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete address');
    }
  };

  // Contact Management
  const handleAddContact = () => {
    setEditingItem({
      type: 'email',
      purpose: 'billing',
      value: '',
      label: ''
    });
    setEditingType('contact');
    setModalErrors({});
    onOpen();
  };

  const handleEditContact = (contact: any) => {
    setEditingItem(contact);
    setEditingType('contact');
    setModalErrors({});
    onOpen();
  };

  const handleSaveContact = async () => {
    if (!validateModalForm()) {
      return;
    }

    try {
      if (editingItem.id) {
        await OrganizationService.updateOrganizationContact(editingItem.id, editingItem);
        toast.success('Contact updated successfully');
      } else {
        await OrganizationService.addOrganizationContact(organization.id, editingItem);
        toast.success('Contact added successfully');
      }
      await refreshOrganization();
      setModalErrors({});
      onClose();
    } catch (error: any) {
      toast.error(error.message || 'Failed to save contact');
    }
  };

  const handleDeleteContact = async (contactId: string) => {
    try {
      await OrganizationService.deleteOrganizationContact(contactId);
      toast.success('Contact deleted successfully');
      await refreshOrganization();
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete contact');
    }
  };

  // Integration Management
  const handleAddIntegration = () => {
    setEditingItem({
      provider: '',
      name: '',
      type: '',
      active: true,
      expiresAt: '',
      key: '',
      authToken: {},
      data: {}
    });
    setEditingType('integration');
    setModalErrors({});
    onOpen();
  };

  const handleEditIntegration = (integration: any) => {
    setEditingItem({
      ...integration,
      key: '' // Don't populate the key field for security - user can enter new one if needed
    });
    setEditingType('integration');
    setModalErrors({});
    onOpen();
  };

  const handleSaveIntegration = async () => {
    if (!validateModalForm()) {
      return;
    }

    try {
      if (editingItem.id) {
        await OrganizationService.updateOrganizationIntegration(editingItem.id, editingItem);
        toast.success('Integration updated successfully');
      } else {
        await OrganizationService.addOrganizationIntegration(organization.id, editingItem);
        toast.success('Integration added successfully');
      }
      await refreshOrganization();
      setModalErrors({});
      onClose();
    } catch (error: any) {
      toast.error(error.message || 'Failed to save integration');
    }
  };

  const handleDeleteIntegration = async (integrationId: string) => {
    try {
      await OrganizationService.deleteOrganizationIntegration(integrationId);
      toast.success('Integration deleted successfully');
      await refreshOrganization();
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete integration');
    }
  };

  const renderBasicInfoTab = () => (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold">Basic Information</h3>
      </CardHeader>
      <CardBody className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Organization Name"
            value={formData.name}
            onChange={(e) => {
              setFormData((prev) => ({ ...prev, name: e.target.value }));
              clearFieldError('name');
            }}
            isInvalid={!!errors.name}
            errorMessage={errors.name}
            isRequired
          />
          <Select
            label="Organization Type"
            selectedKeys={formData.membership?.organizationType ? [formData.membership.organizationType] : []}
            onSelectionChange={(keys) => {
              const value = Array.from(keys)[0] as string;
              setFormData((prev) => ({
                ...prev,
                membership: { ...prev.membership!, organizationType: value }
              }));
            }}
            isRequired
            selectionMode="single"
            children={(formOptions?.orgTypes || []).map((type) => (
              <SelectItem key={type} textValue={formattedDisplayString(type)}>
                {formattedDisplayString(type)}
              </SelectItem>
            ))}
          />
        </div>
        <Input
          label="Website"
          type="url"
          value={formData.website}
          onChange={(e) => {
            setFormData((prev) => ({ ...prev, website: e.target.value }));
            clearFieldError('website');
          }}
          isInvalid={!!errors.website}
          errorMessage={errors.website}
          placeholder="https://example.com"
          isRequired
        />
        <Textarea
          label="AI Summary"
          value={formData.aiSummary}
          onChange={(e) => {
            setFormData((prev) => ({ ...prev, aiSummary: e.target.value }));
            clearFieldError('aiSummary');
          }}
          isInvalid={!!errors.aiSummary}
          errorMessage={errors.aiSummary}
          placeholder="Brief description of the organization..."
          isRequired
        />
      </CardBody>
    </Card>
  );

  const renderMembershipTab = () => (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold">Membership Details</h3>
      </CardHeader>
      <CardBody className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Select
            label="Membership Type"
            selectedKeys={formData.membership?.type ? [formData.membership.type] : []}
            onSelectionChange={(keys) => {
              const value = Array.from(keys)[0] as string;
              setFormData((prev) => ({
                ...prev,
                membership: { ...prev.membership!, type: value }
              }));
            }}
            isRequired
            children={(formOptions?.membershipTypes || []).map((type) => (
              <SelectItem key={type} textValue={formattedDisplayString(type)}>
                {formattedDisplayString(type)}
              </SelectItem>
            ))}
          />
          <Select
            label="Status"
            selectedKeys={formData.membership?.status ? [formData.membership.status] : []}
            onSelectionChange={(keys) => {
              const value = Array.from(keys)[0] as string;
              setFormData((prev) => ({
                ...prev,
                membership: { ...prev.membership!, status: value }
              }));
            }}
            children={(formOptions?.orgStatuses || []).map((status) => (
              <SelectItem key={status} textValue={formattedDisplayString(status)}>
                {formattedDisplayString(status)}
              </SelectItem>
            ))}
          />
          <Select
            label="Tier"
            selectedKeys={formData.membership?.tier ? [formData.membership.tier] : []}
            onSelectionChange={(keys) => {
              const value = Array.from(keys)[0] as string;
              setFormData((prev) => ({
                ...prev,
                membership: { ...prev.membership!, tier: value }
              }));
            }}
            children={(formOptions?.orgTiers || []).map((tier) => (
              <SelectItem key={tier} textValue={formattedDisplayString(tier)}>
                {formattedDisplayString(tier)}
              </SelectItem>
            ))}
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Max Users"
            type="number"
            min="1"
            value={formData.membership?.maxUser?.toString() || '5'}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                membership: { ...prev.membership!, maxUser: parseInt(e.target.value) || 0 }
              }))
            }
            isRequired
          />
          <Input
            label="Ends At"
            type="datetime-local"
            placeholder="Select Date"
            value={formData.membership?.endsAt ? new Date(formData.membership.endsAt).toISOString().slice(0, 16) : ''}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                membership: { ...prev.membership!, endsAt: e.target.value }
              }))
            }
          />
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <label className="text-sm font-medium text-gray-600">Credit Balance (Read-only)</label>
          <div className="mt-1 text-lg font-semibold text-gray-900">{formatCurrency(formData.membership?.creditBalance || 0)}</div>
          <p className="text-xs text-gray-500 mt-1">Credit balance is calculated internally and cannot be modified directly.</p>
        </div>
      </CardBody>
    </Card>
  );

  const renderAddressesTab = () => (
    <Card>
      <CardHeader className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Addresses</h3>
        <Button size="sm" color="primary" variant="flat" startContent={<FontAwesomeIcon icon={faPlus} />} onPress={handleAddAddress}>
          Add Address
        </Button>
      </CardHeader>
      <CardBody className="space-y-4">
        {formData.addresses && formData.addresses.length > 0 ? (
          formData.addresses.map((address, index) => (
            <div key={address.id || index} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-medium">{address.title}</h4>
                  <p className="text-sm text-gray-600">{formattedDisplayString(address.purpose)}</p>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" variant="light" isIconOnly onPress={() => handleEditAddress(address)}>
                    <FontAwesomeIcon icon={faEdit} />
                  </Button>
                  <Button size="sm" variant="light" color="danger" isIconOnly onPress={() => handleDeleteAddress(address.id)}>
                    <FontAwesomeIcon icon={faTrash} />
                  </Button>
                </div>
              </div>
              <div className="text-sm">
                <div>{address.addressLine1}</div>
                {address.addressLine2 && <div>{address.addressLine2}</div>}
                <div>
                  {address.city.cityName}, {address.city.state.state} {address.zipCode}
                </div>
                <div>{address.city.state.country.country}</div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center text-gray-500 py-8">No addresses found</div>
        )}
      </CardBody>
    </Card>
  );

  const renderContactsTab = () => (
    <Card>
      <CardHeader className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Contacts</h3>
        <Button size="sm" color="primary" variant="flat" startContent={<FontAwesomeIcon icon={faPlus} />} onPress={handleAddContact}>
          Add Contact
        </Button>
      </CardHeader>
      <CardBody className="space-y-4">
        {formData.orgContacts && formData.orgContacts.length > 0 ? (
          formData.orgContacts.map((contact, index) => (
            <div key={contact.id || index} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-medium">{contact.value}</h4>
                  <p className="text-sm text-gray-600">
                    {formattedDisplayString(contact.type)} - {formattedDisplayString(contact.purpose)}
                  </p>
                  {contact.label && <p className="text-sm text-gray-500">{contact.label}</p>}
                </div>
                <div className="flex gap-2">
                  <Button size="sm" variant="light" isIconOnly onPress={() => handleEditContact(contact)}>
                    <FontAwesomeIcon icon={faEdit} />
                  </Button>
                  <Button size="sm" variant="light" color="danger" isIconOnly onPress={() => handleDeleteContact(contact.id)}>
                    <FontAwesomeIcon icon={faTrash} />
                  </Button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center text-gray-500 py-8">No contacts found</div>
        )}
      </CardBody>
    </Card>
  );

  const renderIntegrationsTab = () => (
    <Card>
      <CardHeader className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Integrations</h3>
        <Button size="sm" color="primary" variant="flat" startContent={<FontAwesomeIcon icon={faPlus} />} onPress={handleAddIntegration}>
          Add Integration
        </Button>
      </CardHeader>
      <CardBody className="space-y-4">
        {formData.orgIntegrations && formData.orgIntegrations.length > 0 ? (
          formData.orgIntegrations.map((integration, index) => (
            <div key={integration.id || index} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-medium">{integration.name || integration.provider}</h4>
                  <p className="text-sm text-gray-600">
                    {formattedDisplayString(integration.provider)} - {formattedDisplayString(integration.type)}
                  </p>
                  <p className="text-sm text-gray-500">
                    Status: <span className={integration.active ? 'text-green-600' : 'text-red-600'}>{integration.active ? 'Active' : 'Inactive'}</span>
                  </p>
                  <p className="text-sm text-gray-500">
                    API Key: <span className="font-mono">****{integration.authToken ? '****' : 'Not set'}</span>
                  </p>
                  {integration.expiresAt && <p className="text-sm text-gray-500">Expires: {new Date(integration.expiresAt).toLocaleDateString()}</p>}
                </div>
                <div className="flex gap-2">
                  <Button size="sm" variant="light" isIconOnly onPress={() => handleEditIntegration(integration)}>
                    <FontAwesomeIcon icon={faEdit} />
                  </Button>
                  <Button size="sm" variant="light" color="danger" isIconOnly onPress={() => handleDeleteIntegration(integration.id)}>
                    <FontAwesomeIcon icon={faTrash} />
                  </Button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center text-gray-500 py-8">No integrations found</div>
        )}
      </CardBody>
    </Card>
  );

  const renderModal = () => {
    if (!editingType || !editingItem) return null;

    const isEditing = !!editingItem.id;
    const title = `${isEditing ? 'Edit' : 'Add'} ${editingType.charAt(0).toUpperCase() + editingType.slice(1)}`;

    return (
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalContent>
          <ModalHeader>{title}</ModalHeader>
          <ModalBody>
            {editingType === 'address' && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="Title"
                    value={editingItem.title}
                    onChange={(e) => {
                      setEditingItem({ ...editingItem, title: e.target.value });
                      clearModalError('title');
                    }}
                    isInvalid={!!modalErrors.title}
                    errorMessage={modalErrors.title}
                    isRequired
                  />
                  <Select
                    label="Purpose"
                    selectedKeys={[editingItem.purpose]}
                    onSelectionChange={(keys) => setEditingItem({ ...editingItem, purpose: Array.from(keys)[0] })}
                    children={(formOptions?.contactPurposes || []).map((purpose) => (
                      <SelectItem key={purpose} textValue={formattedDisplayString(purpose)}>
                        {formattedDisplayString(purpose)}
                      </SelectItem>
                    ))}
                  />
                </div>
                <Input
                  label="Address Line 1"
                  value={editingItem.addressLine1}
                  onChange={(e) => {
                    setEditingItem({ ...editingItem, addressLine1: e.target.value });
                    clearModalError('addressLine1');
                  }}
                  isInvalid={!!modalErrors.addressLine1}
                  errorMessage={modalErrors.addressLine1}
                  isRequired
                />
                <Input label="Address Line 2" value={editingItem.addressLine2 || ''} onChange={(e) => setEditingItem({ ...editingItem, addressLine2: e.target.value })} />
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="City"
                    value={editingItem.city?.cityName || ''}
                    onChange={(e) =>
                      setEditingItem({
                        ...editingItem,
                        city: { ...editingItem.city, cityName: e.target.value }
                      })
                    }
                  />
                  <Input
                    label="State"
                    value={editingItem.city?.state?.state || ''}
                    onChange={(e) =>
                      setEditingItem({
                        ...editingItem,
                        city: {
                          ...editingItem.city,
                          state: { ...editingItem.city?.state, state: e.target.value }
                        }
                      })
                    }
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="Country"
                    value={editingItem.city?.state?.country?.country || ''}
                    onChange={(e) =>
                      setEditingItem({
                        ...editingItem,
                        city: {
                          ...editingItem.city,
                          state: {
                            ...editingItem.city?.state,
                            country: { ...editingItem.city?.state?.country, country: e.target.value }
                          }
                        }
                      })
                    }
                  />
                  <Input
                    label="Country Code"
                    value={editingItem.city?.state?.country?.isoCode || ''}
                    onChange={(e) => {
                      const value = e.target.value.toUpperCase(); // Auto-convert to uppercase
                      setEditingItem({
                        ...editingItem,
                        city: {
                          ...editingItem.city,
                          state: {
                            ...editingItem.city?.state,
                            country: { ...editingItem.city?.state?.country, isoCode: value }
                          }
                        }
                      });
                      clearModalError('isoCode');
                    }}
                    isInvalid={!!modalErrors.isoCode}
                    errorMessage={modalErrors.isoCode}
                    placeholder="US"
                    maxLength={2}
                    isRequired
                  />
                </div>
                <Input label="Zip Code" type="number" value={editingItem.zipCode?.toString() || ''} onChange={(e) => setEditingItem({ ...editingItem, zipCode: parseInt(e.target.value) || 0 })} />
              </div>
            )}

            {editingType === 'contact' && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Select
                    label="Type"
                    selectedKeys={[editingItem.type]}
                    onSelectionChange={(keys) => setEditingItem({ ...editingItem, type: Array.from(keys)[0] })}
                    children={['email', 'phone'].map((type) => (
                      <SelectItem key={type} textValue={formattedDisplayString(type)}>
                        {formattedDisplayString(type)}
                      </SelectItem>
                    ))}
                  />
                  <Select
                    label="Purpose"
                    selectedKeys={[editingItem.purpose]}
                    onSelectionChange={(keys) => setEditingItem({ ...editingItem, purpose: Array.from(keys)[0] })}
                    children={(formOptions?.contactPurposes || []).map((purpose) => (
                      <SelectItem key={purpose} textValue={formattedDisplayString(purpose)}>
                        {formattedDisplayString(purpose)}
                      </SelectItem>
                    ))}
                  />
                </div>
                <Input
                  label="Value"
                  value={editingItem.value}
                  onChange={(e) => {
                    setEditingItem({ ...editingItem, value: e.target.value });
                    clearModalError('value');
                  }}
                  placeholder={editingItem.type === 'email' ? '<EMAIL>' : '+1234567890'}
                  isInvalid={!!modalErrors.value}
                  errorMessage={modalErrors.value}
                  isRequired
                />
                <Input label="Label" value={editingItem.label || ''} onChange={(e) => setEditingItem({ ...editingItem, label: e.target.value })} placeholder="Optional label" />
              </div>
            )}

            {editingType === 'integration' && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Select
                    label="Provider"
                    selectedKeys={[editingItem.provider]}
                    onSelectionChange={(keys) => setEditingItem({ ...editingItem, provider: Array.from(keys)[0] })}
                    children={(formOptions?.integrationProviders || []).map((provider) => (
                      <SelectItem key={provider} textValue={formattedDisplayString(provider)}>
                        {formattedDisplayString(provider)}
                      </SelectItem>
                    ))}
                  />
                  <Select
                    label="Type"
                    selectedKeys={[editingItem.type]}
                    onSelectionChange={(keys) => setEditingItem({ ...editingItem, type: Array.from(keys)[0] })}
                    children={(formOptions?.integrationTypes || []).map((type) => (
                      <SelectItem key={type} textValue={formattedDisplayString(type)}>
                        {formattedDisplayString(type)}
                      </SelectItem>
                    ))}
                  />
                </div>
                <Input
                  label="Name"
                  value={editingItem.name || ''}
                  onChange={(e) => {
                    setEditingItem({ ...editingItem, name: e.target.value });
                    clearModalError('name');
                  }}
                  isInvalid={!!modalErrors.name}
                  errorMessage={modalErrors.name}
                  isRequired
                />
                <Input
                  label="API Key"
                  type="password"
                  value={editingItem.key || ''}
                  onChange={(e) => {
                    setEditingItem({ ...editingItem, key: e.target.value });
                    clearModalError('key');
                  }}
                  placeholder={editingItem.id ? 'Leave empty to keep existing key' : 'Enter API key or token'}
                  description={editingItem.id ? 'Leave empty to keep the current API key' : ''}
                  isInvalid={!!modalErrors.key}
                  errorMessage={modalErrors.key}
                  isRequired={!editingItem.id} // Only required for new integrations
                />
                <Input
                  label="Expires At (Optional)"
                  type="datetime-local"
                  placeholder="Select Date"
                  value={editingItem.expiresAt ? new Date(editingItem.expiresAt).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    setEditingItem({ ...editingItem, expiresAt: e.target.value });
                    clearModalError('expiresAt');
                  }}
                  isInvalid={!!modalErrors.expiresAt}
                  errorMessage={modalErrors.expiresAt}
                />
                <div className="flex items-center gap-2">
                  <input type="checkbox" checked={editingItem.active} onChange={(e) => setEditingItem({ ...editingItem, active: e.target.checked })} />
                  <label>Active</label>
                </div>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button color="default" variant="flat" onPress={onClose}>
              Cancel
            </Button>
            <Button color="primary" onPress={editingType === 'address' ? handleSaveAddress : editingType === 'contact' ? handleSaveContact : handleSaveIntegration}>
              Save
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    );
  };

  if (!formOptions) {
    return <div>Loading form data...</div>;
  }

  return (
    <>
      <form onSubmit={handleSubmit} className="space-y-6">
        <Tabs selectedKey={activeTab} onSelectionChange={(key) => setActiveTab(key as string)}>
          <Tab key="basic" title="Basic Information">
            {renderBasicInfoTab()}
          </Tab>
          <Tab key="membership" title="Membership">
            {renderMembershipTab()}
          </Tab>
          <Tab key="addresses" title="Addresses">
            {renderAddressesTab()}
          </Tab>
          <Tab key="contacts" title="Contacts">
            {renderContactsTab()}
          </Tab>
          <Tab key="integrations" title="Integrations">
            {renderIntegrationsTab()}
          </Tab>
        </Tabs>

        {/* Form Actions */}
        <div className="flex justify-end gap-4">
          <Button color="default" variant="flat" onPress={onCancel} startContent={<FontAwesomeIcon icon={faTimes} />}>
            Cancel
          </Button>
          <Button color="primary" type="submit" isLoading={loading} startContent={<FontAwesomeIcon icon={faSave} />}>
            Save Changes
          </Button>
        </div>
      </form>

      {renderModal()}
    </>
  );
}
