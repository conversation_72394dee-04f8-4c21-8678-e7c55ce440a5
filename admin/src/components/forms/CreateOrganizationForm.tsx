import React, { useState, useEffect } from 'react';
import { Button, Input, Textarea, Select, SelectItem, Card, CardBody, CardHeader } from '@heroui/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faTrash, faSave, faTimes } from '@fortawesome/free-solid-svg-icons';
import OrganizationService from '../../services/Organization';
import { ICreateOrganizationRequest, IOrganizationFormData } from '../../types/organization';
import { formattedDisplayString } from '../../utils/string';

interface CreateOrganizationFormProps {
  onSubmit: (data: ICreateOrganizationRequest) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

interface Integration {
  provider: string;
  activeUntil?: string;
  keyName: string;
  key: string;
  type: string;
  aiCredits: number;
}

interface PhoneNumber {
  phone: string;
  contactName: string;
  purpose: string;
}

export default function CreateOrganizationForm({ onSubmit, onCancel, loading = false }: CreateOrganizationFormProps) {
  const [formData, setFormData] = useState<ICreateOrganizationRequest>({
    contact: {
      email: '',
      firstName: '',
      lastName: ''
    },
    firmType: '',
    firmName: '',
    aiSummary: '',
    website: '',
    membership: {
      type: '',
      maxUsers: 5
    },
    integrations: [],
    address: {
      title: '',
      addressLine1: '',
      addressLine2: '',
      zipCode: 0,
      purpose: 'billing',
      city: {
        cityName: '',
        state: {
          state: '',
          country: {
            country: '',
            isoCode: ''
          }
        }
      }
    },
    phone: []
  });

  const [formOptions, setFormOptions] = useState<IOrganizationFormData | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Helper function to clear specific field errors
  const clearFieldError = (fieldName: string) => {
    if (errors[fieldName]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  useEffect(() => {
    const fetchFormData = async () => {
      const options = await OrganizationService.getOrganizationFormData();
      setFormOptions(options);
    };
    fetchFormData();
  }, []);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields validation
    if (!formData.contact.email) newErrors['contact.email'] = 'Email is required';
    if (!formData.contact.firstName) newErrors['contact.firstName'] = 'First name is required';
    if (!formData.firmName) newErrors.firmName = 'Firm name is required';
    if (!formData.firmType) newErrors.firmType = 'Firm type is required';
    if (!formData.aiSummary) newErrors.aiSummary = 'AI summary is required';
    if (!formData.website) newErrors.website = 'Website is required';

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.contact.email && !emailRegex.test(formData.contact.email)) {
      newErrors['contact.email'] = 'Invalid email format';
    }

    // Website validation
    const urlRegex = /^https?:\/\/.+/;
    if (formData.website && !urlRegex.test(formData.website)) {
      newErrors.website = 'Website must be a valid URL (http:// or https://)';
    }

    // Address validation (now required)
    if (!formData.address?.title) newErrors['address.title'] = 'Address title is required';
    if (!formData.address?.addressLine1) newErrors['address.addressLine1'] = 'Address line 1 is required';
    if (!formData.address?.zipCode || formData.address.zipCode <= 0) newErrors['address.zipCode'] = 'Zip code is required';
    if (!formData.address?.city.cityName) newErrors['address.city.cityName'] = 'City is required';
    if (!formData.address?.city.state.state) newErrors['address.city.state.state'] = 'State is required';
    if (!formData.address?.city.state.country.country) newErrors['address.city.state.country.country'] = 'Country is required';
    if (!formData.address?.city.state.country.isoCode) {
      newErrors['address.city.state.country.isoCode'] = 'Country ISO code is required';
    } else if (!/^[A-Z]{2}$/.test(formData.address.city.state.country.isoCode.toUpperCase())) {
      newErrors['address.city.state.country.isoCode'] = 'Country ISO code must be exactly 2 letters (e.g., US, CA, GB)';
    }

    // Membership validation (now required)
    if (!formData.membership?.type) newErrors['membership.type'] = 'Membership type is required';
    if (!formData.membership?.maxUsers || formData.membership.maxUsers < 1) {
      newErrors['membership.maxUsers'] = 'Max users must be at least 1';
    }

    // Phone validation - if phone numbers are added, validate required fields
    if (formData.phone && formData.phone.length > 0) {
      formData.phone.forEach((phone, index) => {
        if (!phone.phone.trim()) {
          newErrors[`phone.${index}.phone`] = 'Phone number is required';
        }
        if (!phone.contactName.trim()) {
          newErrors[`phone.${index}.contactName`] = 'Contact name is required';
        }
        // Phone number format validation (basic)
        if (phone.phone.trim() && !/^[+]?[1-9][\d]{0,15}$/.test(phone.phone.replace(/[\s\-()]/g, ''))) {
          newErrors[`phone.${index}.phone`] = 'Invalid phone number format';
        }
      });
    }

    // Integration validation - if integrations are added, validate all required fields
    if (formData.integrations && formData.integrations.length > 0) {
      formData.integrations.forEach((integration, index) => {
        if (!integration.provider) {
          newErrors[`integration.${index}.provider`] = 'Provider is required';
        }
        if (!integration.type) {
          newErrors[`integration.${index}.type`] = 'Type is required';
        }
        if (!integration.keyName.trim()) {
          newErrors[`integration.${index}.keyName`] = 'Key name is required';
        }
        if (!integration.key.trim()) {
          newErrors[`integration.${index}.key`] = 'API key is required';
        }
        // Validate activeUntil only if provided (it's optional)
        if (integration.activeUntil) {
          // Validate that the date is in the future
          const activeDate = new Date(integration.activeUntil);
          const now = new Date();
          if (activeDate <= now) {
            newErrors[`integration.${index}.activeUntil`] = 'Active until date must be in the future';
          }
        }
        if (integration.aiCredits < 0) {
          newErrors[`integration.${index}.aiCredits`] = 'AI credits must be non-negative';
        }
      });
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.SyntheticEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const submitData = { ...formData };

    // Remove optional sections if empty
    if (submitData.integrations?.length === 0) {
      delete submitData.integrations;
    }
    if (submitData.phone?.length === 0) {
      delete submitData.phone;
    }

    await onSubmit(submitData);
  };

  const addIntegration = () => {
    const newIntegration: Integration = {
      provider: '',
      activeUntil: '',
      keyName: '',
      key: '',
      type: '',
      aiCredits: 0
    };
    setFormData((prev) => ({
      ...prev,
      integrations: [...(prev.integrations || []), newIntegration]
    }));
  };

  const removeIntegration = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      integrations: prev.integrations?.filter((_, i) => i !== index) || []
    }));
  };

  const updateIntegration = (index: number, field: keyof Integration, value: string | number) => {
    setFormData((prev) => ({
      ...prev,
      integrations: prev.integrations?.map((integration, i) => (i === index ? { ...integration, [field]: value } : integration)) || []
    }));

    // Clear validation errors for this field when user starts typing/selecting
    const errorKey = `integration.${index}.${field}`;
    if (errors[errorKey]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[errorKey];
        return newErrors;
      });
    }
  };

  const addPhoneNumber = () => {
    const newPhone: PhoneNumber = {
      phone: '',
      contactName: '',
      purpose: 'billing'
    };
    setFormData((prev) => ({
      ...prev,
      phone: [...(prev.phone || []), newPhone]
    }));
  };

  const removePhoneNumber = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      phone: prev.phone?.filter((_, i) => i !== index) || []
    }));
  };

  const updatePhoneNumber = (index: number, field: keyof PhoneNumber, value: string) => {
    setFormData((prev) => ({
      ...prev,
      phone: prev.phone?.map((phone, i) => (i === index ? { ...phone, [field]: value } : phone)) || []
    }));

    // Clear validation errors for this field when user starts typing
    const errorKey = `phone.${index}.${field}`;
    if (errors[errorKey]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[errorKey];

        // Also clear phone format error if user is correcting the phone number
        if (field === 'phone') {
          delete newErrors[`phone.${index}.phone`];
        }

        return newErrors;
      });
    }
  };

  if (!formOptions) {
    return <div>Loading form data...</div>;
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Contact Information */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Contact Information</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Email"
              type="email"
              value={formData.contact.email}
              onChange={(e) => {
                setFormData((prev) => ({
                  ...prev,
                  contact: { ...prev.contact, email: e.target.value }
                }));
                clearFieldError('contact.email');
              }}
              isInvalid={!!errors['contact.email']}
              errorMessage={errors['contact.email']}
              isRequired
            />
            <Input
              label="First Name"
              value={formData.contact.firstName}
              onChange={(e) => {
                setFormData((prev) => ({
                  ...prev,
                  contact: { ...prev.contact, firstName: e.target.value }
                }));
                clearFieldError('contact.firstName');
              }}
              isInvalid={!!errors['contact.firstName']}
              errorMessage={errors['contact.firstName']}
              isRequired
            />
          </div>
          <Input
            label="Last Name"
            value={formData.contact.lastName || ''}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                contact: { ...prev.contact, lastName: e.target.value }
              }))
            }
          />
        </CardBody>
      </Card>

      {/* Organization Information */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Organization Information</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Firm Name"
              value={formData.firmName}
              onChange={(e) => {
                setFormData((prev) => ({ ...prev, firmName: e.target.value }));
                clearFieldError('firmName');
              }}
              isInvalid={!!errors.firmName}
              errorMessage={errors.firmName}
              isRequired
            />
            <Select
              label="Firm Type"
              selectedKeys={formData.firmType ? [formData.firmType] : []}
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string;
                setFormData((prev) => ({ ...prev, firmType: value }));
              }}
              isInvalid={!!errors.firmType}
              errorMessage={errors.firmType}
              isRequired
              selectionMode="single"
              children={formOptions.orgTypes.map((type) => (
                <SelectItem key={type} textValue={formattedDisplayString(type)}>
                  {formattedDisplayString(type)}
                </SelectItem>
              ))}
            />
          </div>
          <Input
            label="Website"
            type="url"
            value={formData.website}
            onChange={(e) => {
              setFormData((prev) => ({ ...prev, website: e.target.value }));
              clearFieldError('website');
            }}
            isInvalid={!!errors.website}
            errorMessage={errors.website}
            placeholder="https://example.com"
            isRequired
          />
          <Textarea label="AI Summary" value={formData.aiSummary} onChange={(e) => setFormData((prev) => ({ ...prev, aiSummary: e.target.value }))} isInvalid={!!errors.aiSummary} errorMessage={errors.aiSummary} placeholder="Brief description of the organization..." isRequired />
        </CardBody>
      </Card>

      {/* Membership (Required) */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Membership</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Select
              label="Membership Type"
              selectedKeys={formData.membership?.type ? [formData.membership.type] : []}
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string;
                setFormData((prev) => ({
                  ...prev,
                  membership: { ...prev.membership!, type: value }
                }));
              }}
              isInvalid={!!errors['membership.type']}
              errorMessage={errors['membership.type']}
              isRequired
              children={formOptions.membershipTypes.map((type) => (
                <SelectItem key={type} textValue={formattedDisplayString(type)}>
                  {formattedDisplayString(type)}
                </SelectItem>
              ))}
            />
            <Input
              label="Max Users"
              type="number"
              min="1"
              value={formData.membership?.maxUsers?.toString() || '5'}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  membership: { ...prev.membership!, maxUsers: parseInt(e.target.value) || 0 }
                }))
              }
              isInvalid={!!errors['membership.maxUsers']}
              errorMessage={errors['membership.maxUsers']}
              isRequired
            />
          </div>
        </CardBody>
      </Card>

      {/* Address (Required) */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Address</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Address Title"
              value={formData.address?.title || ''}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  address: { ...prev.address!, title: e.target.value }
                }))
              }
              isInvalid={!!errors['address.title']}
              errorMessage={errors['address.title']}
              placeholder="e.g., Headquarters"
              isRequired
            />
            <Select
              label="Purpose"
              selectedKeys={formData.address?.purpose ? [formData.address.purpose] : ['billing']}
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string;
                setFormData((prev) => ({
                  ...prev,
                  address: { ...prev.address!, purpose: value }
                }));
              }}
              children={formOptions.contactPurposes.map((purpose) => (
                <SelectItem key={purpose} textValue={formattedDisplayString(purpose)}>
                  {formattedDisplayString(purpose)}
                </SelectItem>
              ))}
            />
          </div>
          <Input
            label="Address Line 1"
            value={formData.address?.addressLine1 || ''}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                address: { ...prev.address!, addressLine1: e.target.value }
              }))
            }
            isInvalid={!!errors['address.addressLine1']}
            errorMessage={errors['address.addressLine1']}
            isRequired
          />
          <Input
            label="Address Line 2"
            value={formData.address?.addressLine2 || ''}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                address: { ...prev.address!, addressLine2: e.target.value }
              }))
            }
          />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Input
              label="City"
              value={formData.address?.city.cityName || ''}
              onChange={(e) => {
                setFormData((prev) => ({
                  ...prev,
                  address: {
                    ...prev.address!,
                    city: { ...prev.address!.city, cityName: e.target.value }
                  }
                }));
                clearFieldError('address.city.cityName');
              }}
              isInvalid={!!errors['address.city.cityName']}
              errorMessage={errors['address.city.cityName']}
              isRequired
            />
            <Input
              label="State"
              value={formData.address?.city.state.state || ''}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  address: {
                    ...prev.address!,
                    city: {
                      ...prev.address!.city,
                      state: { ...prev.address!.city.state, state: e.target.value }
                    }
                  }
                }))
              }
              isInvalid={!!errors['address.city.state.state']}
              errorMessage={errors['address.city.state.state']}
              isRequired
            />
            <Input
              label="Country"
              value={formData.address?.city.state.country.country || ''}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  address: {
                    ...prev.address!,
                    city: {
                      ...prev.address!.city,
                      state: {
                        ...prev.address!.city.state,
                        country: { ...prev.address!.city.state.country, country: e.target.value }
                      }
                    }
                  }
                }))
              }
              isInvalid={!!errors['address.city.state.country.country']}
              errorMessage={errors['address.city.state.country.country']}
              isRequired
            />
            <Input
              label="Country Code"
              value={formData.address?.city.state.country.isoCode || ''}
              onChange={(e) => {
                const value = e.target.value.toUpperCase(); // Auto-convert to uppercase
                setFormData((prev) => ({
                  ...prev,
                  address: {
                    ...prev.address!,
                    city: {
                      ...prev.address!.city,
                      state: {
                        ...prev.address!.city.state,
                        country: { ...prev.address!.city.state.country, isoCode: value }
                      }
                    }
                  }
                }));
                clearFieldError('address.city.state.country.isoCode');
              }}
              isInvalid={!!errors['address.city.state.country.isoCode']}
              errorMessage={errors['address.city.state.country.isoCode']}
              placeholder="US"
              maxLength={2}
              isRequired
            />
          </div>
          <Input
            label="Zip Code"
            type="number"
            min="1"
            value={formData.address?.zipCode?.toString() || ''}
            onChange={(e) => {
              const value = e.target.value;
              const numValue = value === '' ? 0 : parseInt(value) || 0;
              setFormData((prev) => ({
                ...prev,
                address: { ...prev.address!, zipCode: numValue }
              }));
            }}
            isInvalid={!!errors['address.zipCode']}
            errorMessage={errors['address.zipCode']}
            isRequired
          />
        </CardBody>
      </Card>

      {/* Phone Numbers */}
      <Card>
        <CardHeader className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Phone Numbers (Optional)</h3>
          <Button size="sm" color="primary" variant="flat" startContent={<FontAwesomeIcon icon={faPlus} />} onPress={addPhoneNumber}>
            Add Phone
          </Button>
        </CardHeader>
        {formData.phone && formData.phone.length > 0 && (
          <CardBody className="space-y-4">
            {formData.phone.map((phone, index) => (
              <div key={index} className="flex gap-4 items-end">
                <Input label="Phone Number" value={phone.phone} onChange={(e) => updatePhoneNumber(index, 'phone', e.target.value)} className="flex-1" isInvalid={!!errors[`phone.${index}.phone`]} errorMessage={errors[`phone.${index}.phone`]} isRequired />
                <Input label="Contact Name" value={phone.contactName} onChange={(e) => updatePhoneNumber(index, 'contactName', e.target.value)} className="flex-1" isInvalid={!!errors[`phone.${index}.contactName`]} errorMessage={errors[`phone.${index}.contactName`]} isRequired />
                <Select
                  label="Purpose"
                  selectedKeys={phone.purpose ? [phone.purpose] : []}
                  onSelectionChange={(keys) => {
                    const value = Array.from(keys)[0] as string;
                    updatePhoneNumber(index, 'purpose', value);
                  }}
                  className="flex-1"
                  children={formOptions.contactPurposes.map((purpose) => (
                    <SelectItem key={purpose} textValue={formattedDisplayString(purpose)}>
                      {formattedDisplayString(purpose)}
                    </SelectItem>
                  ))}
                />
                <Button size="sm" color="danger" variant="flat" isIconOnly onPress={() => removePhoneNumber(index)}>
                  <FontAwesomeIcon icon={faTrash} />
                </Button>
              </div>
            ))}
          </CardBody>
        )}
      </Card>

      {/* Integrations */}
      <Card>
        <CardHeader className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Integrations (Optional)</h3>
          <Button size="sm" color="primary" variant="flat" startContent={<FontAwesomeIcon icon={faPlus} />} onPress={addIntegration}>
            Add Integration
          </Button>
        </CardHeader>
        {formData.integrations && formData.integrations.length > 0 && (
          <CardBody className="space-y-6">
            {formData.integrations.map((integration, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Integration {index + 1}</h4>
                  <Button size="sm" color="danger" variant="flat" isIconOnly onPress={() => removeIntegration(index)}>
                    <FontAwesomeIcon icon={faTrash} />
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Select
                    label="Provider"
                    selectedKeys={integration.provider ? [integration.provider] : []}
                    onSelectionChange={(keys) => {
                      const value = Array.from(keys)[0] as string;
                      updateIntegration(index, 'provider', value);
                    }}
                    isInvalid={!!errors[`integration.${index}.provider`]}
                    errorMessage={errors[`integration.${index}.provider`]}
                    isRequired
                    children={formOptions.integrationProviders.map((provider) => (
                      <SelectItem key={provider} textValue={formattedDisplayString(provider)}>
                        {formattedDisplayString(provider)}
                      </SelectItem>
                    ))}
                  />
                  <Select
                    label="Type"
                    selectedKeys={integration.type ? [integration.type] : []}
                    onSelectionChange={(keys) => {
                      const value = Array.from(keys)[0] as string;
                      updateIntegration(index, 'type', value);
                    }}
                    isInvalid={!!errors[`integration.${index}.type`]}
                    errorMessage={errors[`integration.${index}.type`]}
                    isRequired
                    children={formOptions.integrationTypes.map((type) => (
                      <SelectItem key={type} textValue={formattedDisplayString(type)}>
                        {formattedDisplayString(type)}
                      </SelectItem>
                    ))}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input label="Key Name" value={integration.keyName} onChange={(e) => updateIntegration(index, 'keyName', e.target.value)} isInvalid={!!errors[`integration.${index}.keyName`]} errorMessage={errors[`integration.${index}.keyName`]} isRequired />
                  <Input label="API Key" type="password" value={integration.key} onChange={(e) => updateIntegration(index, 'key', e.target.value)} isInvalid={!!errors[`integration.${index}.key`]} errorMessage={errors[`integration.${index}.key`]} isRequired />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Active Until (Optional)"
                    type="datetime-local"
                    placeholder="Select Date"
                    value={integration.activeUntil}
                    onChange={(e) => updateIntegration(index, 'activeUntil', e.target.value)}
                    isInvalid={!!errors[`integration.${index}.activeUntil`]}
                    errorMessage={errors[`integration.${index}.activeUntil`]}
                  />
                  <Input
                    label="AI Credits"
                    type="number"
                    min="0"
                    value={integration.aiCredits.toString()}
                    onChange={(e) => updateIntegration(index, 'aiCredits', parseInt(e.target.value) || 0)}
                    isInvalid={!!errors[`integration.${index}.aiCredits`]}
                    errorMessage={errors[`integration.${index}.aiCredits`]}
                    isRequired
                  />
                </div>
              </div>
            ))}
          </CardBody>
        )}
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end gap-4">
        <Button color="default" variant="flat" onPress={onCancel} startContent={<FontAwesomeIcon icon={faTimes} />}>
          Cancel
        </Button>
        <Button color="primary" type="submit" isLoading={loading} startContent={<FontAwesomeIcon icon={faSave} />}>
          Create Organization
        </Button>
      </div>
    </form>
  );
}
