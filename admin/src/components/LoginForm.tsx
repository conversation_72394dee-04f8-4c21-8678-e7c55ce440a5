import React, { useState, useContext } from 'react';
import { useRouter } from 'next/router';
import Input from './Input';
import { AuthContext } from '../context';
import { SaveButton } from './Button';
import Patterns from '../utils/patterns';

function LoginForm() {
  const router = useRouter();
  const { userSignIn } = useContext(AuthContext);
  const [serverError, setServerError] = useState(false);
  const [serverErrorText, setServerErrorText] = useState('');

  const [formValues, setFormValues] = useState({
    email: '',
    password: ''
  });

  const [validation, setValidation] = useState({
    email: true,
    password: true
  });

  const [disableButton, setDisableButton] = useState(false);

  const validationFns = {
    email: () => Patterns.Email.test(formValues.email),
    password: () => formValues.password.length > 4
  };

  const formValidator = () => {
    const newValidation = {
      email: validationFns.email(),
      password: validationFns.password()
    };
    setValidation(newValidation);
    return Object.values(newValidation).includes(false);
  };

  const onSubmit = async (e: { preventDefault: () => void; stopPropagation: () => void }) => {
    e.preventDefault();
    e.stopPropagation();
    if (formValidator()) {
      setDisableButton(false);
      return;
    }
    setDisableButton(true);
    const loginResult = await userSignIn({ email: formValues.email, password: formValues.password });
    if (!loginResult.success) {
      setServerError(true);
      setServerErrorText(loginResult.message);
      setDisableButton(false);
    }

    if (loginResult.success) {
      const returnUrl = router.query.returnUrl?.toString() ?? '/';
      await router.push(returnUrl);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <p className="mt-2 text-sm text-gray-600">Sign in to access the admin dashboard</p>
      </div>
      <form method="POST" className="text-left" onSubmit={onSubmit}>
        <div className="space-y-4">
          <Input title="Email" onChange={(e) => setFormValues({ ...formValues, email: e.target.value })} name="email" type="email" error={validation.email ? '' : 'Please enter a valid email'} value={formValues.email} />

          <Input title="Password" onChange={(e) => setFormValues({ ...formValues, password: e.target.value })} name="password" type="password" error={validation.password ? '' : 'Password must be at least 5 characters'} value={formValues.password} />
        </div>

        {serverError && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md text-sm">
            <p className="font-medium">{serverErrorText}</p>
          </div>
        )}

        <div className="mt-6">
          <SaveButton spinner disabled={disableButton} text="Sign In" className="w-full px-4 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors" />
        </div>
      </form>
    </div>
  );
}

export default LoginForm;
