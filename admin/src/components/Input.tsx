import React from 'react';

interface InputProps {
  className?: string;
  type?: string;
  name: string;
  title: string;
  value?: string;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  onBlur?: React.FocusEventHandler<HTMLInputElement>;
  onKeyUp?: React.KeyboardEventHandler<HTMLInputElement>;
  error?: string;
  required?: boolean;
  minlength?: number;
  maxlength?: number;
  tabIndex?: number;
  autoFocus?: boolean;
  autoComplete?: string;
  min?: number;
  onKeyDown?: React.KeyboardEventHandler<HTMLInputElement>;
}

const Input: React.FC<InputProps> = ({ className = '', type = 'text', name, title, value, onChange, onBlur, onKeyUp, error, required, minlength, maxlength, tabIndex, autoFocus, autoComplete, min, onKeyDown }) => {
  const id = name;

  const inputClasses = `cursor-text block px-2.5 pb-2.5 pt-4 w-full text-sm bg-transparent rounded border appearance-none text-slate-900 focus:outline-none focus:ring-0 peer ${className}`;
  const inputValid = inputClasses + ' border-slate-350 focus:border-acq-600';
  const inputError = inputClasses + ' focus:border-red-700 border-red-700';

  const labelClasses =
    'cursor-text absolute text-sm duration-300 transform -translate-y-4 scale-75 top-2 origin-[0] bg-white px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1 ';
  const labelValid = labelClasses + 'text-slate-400 peer-focus:text-acq-600';
  const labelError = labelClasses + 'text-red-700 peer-focus:text-red-700';

  return (
    <>
      <div className={`relative ${className}`}>
        <input
          autoComplete={autoComplete}
          minLength={minlength}
          maxLength={maxlength}
          required={required}
          value={value}
          type={type}
          onChange={onChange}
          onKeyUp={onKeyUp}
          onBlur={onBlur}
          className={error ? inputError : inputValid}
          name={name}
          id={name}
          placeholder=""
          tabIndex={tabIndex}
          autoFocus={autoFocus}
          min={min}
          onKeyDown={onKeyDown}
        />
        <label htmlFor={id} className={error ? labelError : labelValid}>
          {title}
        </label>
      </div>
      {error ? <div className="text-sm text-red-700 formError">{error}</div> : ''}
    </>
  );
};

export default Input;
