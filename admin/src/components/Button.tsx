import { faSpinner } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React, { createElement } from 'react';

interface IButtonProps {
  disabled?: boolean;
  children?: React.ReactNode;
  className?: string;
  others?: any;
  as?: any;
  title?: string;
  onClick?: React.MouseEventHandler<HTMLElement>;
}

interface ISaveButtonProps extends IButtonProps {
  disabled?: boolean;
  text?: string;
  spinner?: boolean;
}

export default function Button({ children, className, disabled, ...others }: IButtonProps) {
  const element = others.as ? others.as : 'button';
  const classes = `${className || ''} button`;
  return createElement(element, { className: classes, disabled, ...others }, children);
}

export function SaveButton({ text, className, onClick, disabled, spinner, title }: ISaveButtonProps) {
  return (
    <button title={title} type="submit" disabled={disabled} onClick={onClick} className={`button-active ${className || ''}`}>
      {disabled ? (
        spinner ? (
          <>
            <FontAwesomeIcon icon={faSpinner} spin={true} /> Please wait...{' '}
          </>
        ) : (
          text
        )
      ) : (
        text
      )}
    </button>
  );
}
