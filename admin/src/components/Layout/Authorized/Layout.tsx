import Head from 'next/head';
import React, { useContext, ReactNode, useEffect } from 'react';
import { AuthContext } from '../../../context';
import { navigationItems, NavigationItem, ProfileSection } from './Navigation';
import { setSignOutHandler } from '../../../services/Http';
import { HeroUIProvider } from '@heroui/react';

interface LayoutProps {
  children: ReactNode;
  title?: string;
}

export default function Layout({ children, title = 'Admin App' }: LayoutProps) {
  const { isAuthenticated, isLoading, userSignout } = useContext(AuthContext);

  // Set up the sign out handler for HTTP interceptor
  useEffect(() => {
    setSignOutHandler(userSignout);
  }, [userSignout]);

  if (isLoading) {
    return <div className="text-center mt-20">Loading...</div>;
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>{title}</title>
      </Head>
      <HeroUIProvider>
        <div className="flex h-screen overflow-hidden">
          <aside className="w-64 bg-white border-r border-acqgray-200 flex flex-col h-screen">
            <div className="p-6 text-center border-b border-acqgray-200">
              <img src="/assets/images/acqwired-logo.svg" alt="Acqwired Logo" className="mx-auto h-10" />
            </div>
            <nav className="flex-1 py-4">
              {navigationItems.map((item, i) => (
                <NavigationItem key={i} item={item} />
              ))}
            </nav>
            <ProfileSection />
          </aside>
          <main className="flex-1 bg-acqgray-50 p-8 overflow-y-auto">{children}</main>
        </div>
      </HeroUIProvider>
    </>
  );
}
