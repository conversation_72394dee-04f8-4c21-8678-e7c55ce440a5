import React, { useContext } from 'react';
import { faHome, faBuilding, faUserCircle, faSignOutAlt } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { AuthContext } from '../../../context';
import { useRouter } from 'next/router';
import { logout } from '../../../utils/logout'; // Fixed import path
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';

interface NavigationItemProps {
  item: {
    name: string;
    href: string;
    icon: IconDefinition;
    onClick?: () => void;
  };
}

const navigationItems = [
  {
    name: 'Home',
    href: '/',
    icon: faHome
  },
  {
    name: 'Organizations',
    href: '/organizations',
    icon: faBuilding
  },
  {
    name: 'Logout',
    href: '#',
    icon: faSignOutAlt,
    onClick: logout
  }
];

const NavigationItem = ({ item }: NavigationItemProps) => {
  const router = useRouter();
  return (
    <div className="item" data-type="item" data-active={router.route.startsWith(item.href) ? 'true' : 'false'}>
      <a
        href={item.href}
        onClick={(e) => {
          e.preventDefault();
          if (item.onClick) {
            item.onClick();
          } else {
            router.push(item.href);
          }
        }}
        className="flex items-center px-4 py-2 text-sm text-gray-600 hover:bg-gray-100"
      >
        <FontAwesomeIcon icon={item.icon} className="mr-3 h-5 w-5" />
        {item.name}
      </a>
    </div>
  );
};

const ProfileSection = () => {
  const { userData } = useContext(AuthContext);
  return (
    <div className="mt-auto p-4 border-t border-acqgray-200 flex items-center">
      <div className="flex items-center">
        <FontAwesomeIcon icon={faUserCircle} className="mr-2 text-acq-500" />
        <span className="text-sm font-medium">{userData?.email || 'User'}</span>
      </div>
    </div>
  );
};

export { navigationItems, NavigationItem, ProfileSection };
