import React, { useEffect, useContext } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { AuthContext } from '../../../context';

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  desc?: string;
  keys?: string;
}

export default function Layout({ children, title, desc, keys }: LayoutProps) {
  title = title || 'Acqwired | Intelligent M&A Decisions';
  keys = keys || 'acqwired, acquire, acquisition, m&a, startup acquisition';
  desc = desc || 'We built Acqwired from the ground up to help you unlock hidden value in your company and crush your annual M&A goals. We designed it for those who demand superior M&A performance';

  const router = useRouter();
  const { isAuthenticated, isLoading } = useContext(AuthContext);

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push('/');
    }
  }, [isAuthenticated, isLoading, router]);

  return (
    <>
      <Head>
        <meta name="theme-color" content="#ffffff" />
        <link rel="icon" href="/favicon.ico" />
        <meta charSet="utf-8" />
        <meta httpEquiv="x-ua-compatible" content="ie=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="robots" content="index,follow" />
        <meta name="description" content={desc} />
        <meta name="keywords" content={keys} />
        <title>{title}</title>
      </Head>
      <main className='container text-center flex h-screen'>
        {children}
      </main>
    </>
  );
}
