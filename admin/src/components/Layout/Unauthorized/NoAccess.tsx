import router from "next/router";
import {
  Button
} from "../../../components";

export default function NoAccess() {
  return (
    <>
      <div className="lg:px-24 lg:py-24 md:py-20 md:px-44 px-4 py-24 items-center flex justify-center flex-col-reverse lg:flex-row md:gap-28 gap-16">
        <div className="xl:pt-24 w-full xl:w-1/2 relative pb-12 lg:pb-0">
          <div className="relative">
            <div className="absolute">
              <div className="">
                <span className="my-2 text-gray-800 font-bold text-2xl">
                  Looks like you have found the doorway to the great nothing
                </span>
                <p className="my-2 text-gray-800">Sorry about that! Please visit our homepage to get where you need to go.</p>
                <Button className="button-active" onClick={() => (router.push('/'))}>Take me there!</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
