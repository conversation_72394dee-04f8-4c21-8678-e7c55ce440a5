import React, { useContext } from 'react';
import Layout from '../components/Layout/Authorized/Layout';
import { AuthContext } from '../context';
import Link from 'next/link';

export default function Home() {
  const { isAuthenticated, userData } = useContext(AuthContext);

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Welcome to the Admin App</h1>
          <p className="mt-4 text-gray-600">Please log in to access the admin dashboard.</p>
          <Link href="/login" className="mt-6 inline-block px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
            Go to Login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <Layout>
      <div className="text-center mt-20">
        <h1 className="text-2xl font-bold">Welcome, {userData?.firstName || 'User'}!</h1>
        <p className="mt-4 text-gray-600">You are logged in as {userData?.email}</p>
      </div>
    </Layout>
  );
}
