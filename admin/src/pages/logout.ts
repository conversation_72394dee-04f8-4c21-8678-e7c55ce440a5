import { setup } from '../lib/csrf';
import cookie from 'cookie';
import { parse } from 'url';

function Logout() {
  return null;
}

export const getServerSideProps = setup(async (req, res: any) => {
  const parsedUrl = parse(req.url as string, true);
  const query = parsedUrl.query;
  const returnUrl = (query?.returnUrl as string) || '/';
  const redirectUrl = new URLSearchParams({ returnUrl }).toString();

  const cookieOptions: cookie.CookieSerializeOptions = {
    maxAge: -1,
    sameSite: 'none',
    httpOnly: true,
    secure: true,
    path: '/'
  };

  // Clear admin-specific cookies
  res.setHeader('Set-Cookie', [cookie.serialize('admin_accessToken', '', cookieOptions), cookie.serialize('admin_refreshToken', '', cookieOptions)]);

  return {
    redirect: {
      destination: `/login?${redirectUrl.toString()}`,
      permanent: false
    },
    props: {}
  };
});

export default Logout;
