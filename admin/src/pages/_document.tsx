import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        <link rel="icon" type="image/svg+xml" href="/assets/images/favicon-black.svg" />
        <link rel="icon" type="image/png" href="/assets/images/favicon-black.svg" />
        <link rel="shortcut icon" href="/assets/images/favicon-black.svg" />
        <link rel="apple-touch-icon" href="/assets/images/favicon-black.svg" />
        <link rel="alternate icon" href="/favicon.ico" />
        <meta name="theme-color" content="#000000" />
        <meta charSet="utf-8" />
        <meta httpEquiv="x-ua-compatible" content="ie=edge" />
        <meta name="robots" content="index,follow" />
        <meta name="description" content="Acqwired Admin App" />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
