import { useState } from 'react';
import { useRouter } from 'next/router';
import Layout from '../../components/Layout/Authorized/Layout';
import CreateOrganizationForm from '../../components/forms/CreateOrganizationForm';
import OrganizationService from '../../services/Organization';
import { ICreateOrganizationRequest } from '../../types/organization';
import { toast } from 'react-hot-toast';

export default function CreateOrganizationPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (data: ICreateOrganizationRequest) => {
    setLoading(true);
    try {
      const result = await OrganizationService.createOrganization(data);
      if (result) {
        toast.success(`Organization created successfully! Invitation sent to ${data.contact.email}`);
        router.push('/organizations');
      }
    } catch (error: any) {
      console.error('Failed to create organization:', error);

      // Extract detailed error information
      let errorMessage = 'Failed to create organization';
      let errorDetails: string[] = [];

      if (error.response?.data) {
        const errorData = error.response.data;

        // Handle API error format
        if (errorData.message) {
          errorMessage = errorData.message;
        }

        // Handle validation errors or detailed error information
        if (errorData.details) {
          if (typeof errorData.details === 'string') {
            // Check if it's a validation error with multiple errors separated by semicolons
            if (errorData.details.includes('Validation errors:')) {
              const validationPart = errorData.details.replace('Validation errors: ', '');
              errorDetails = validationPart.split('; ').filter((detail: string) => detail.trim());
            } else {
              errorDetails = [errorData.details];
            }
          } else if (Array.isArray(errorData.details)) {
            errorDetails = errorData.details;
          }
        }

        // Handle specific validation error messages from backend
        if (errorData.error && typeof errorData.error === 'string') {
          errorMessage = errorData.error;
        }
      } else if (error.message) {
        // Check if the error message contains validation errors
        if (error.message.includes('Validation errors:')) {
          const validationPart = error.message.replace('Validation errors: ', '');
          errorDetails = validationPart.split('; ').filter((detail: string) => detail.trim());
          errorMessage = 'Please fix the following validation errors:';
        } else {
          errorMessage = error.message;
        }
      }

      // Display errors appropriately
      if (errorDetails.length > 0) {
        // Show main error message first
        toast.error(errorMessage, {
          duration: 4000
        });

        // Show each validation error as a separate toast for better visibility
        errorDetails.forEach((detail, index) => {
          setTimeout(
            () => {
              toast.error(`• ${detail}`, {
                duration: 6000
              });
            },
            (index + 1) * 500
          ); // Stagger the error messages
        });
      } else {
        // Single error message
        toast.error(errorMessage, {
          duration: 6000
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/organizations');
  };

  return (
    <Layout
      children={
        <div className="container mx-auto px-4 py-8">
          <div className="mb-6">
            <h1 className="text-2xl font-semibold">Create New Organization</h1>
            <p className="text-gray-600 mt-2">Create a new organization and send an invitation to the admin user.</p>
          </div>

          <div className="max-w-4xl">
            <CreateOrganizationForm onSubmit={handleSubmit} onCancel={handleCancel} loading={loading} />
          </div>
        </div>
      }
    />
  );
}
