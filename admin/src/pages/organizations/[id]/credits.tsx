import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Button, Breadcrumbs, BreadcrumbItem } from '@heroui/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft, faBuilding, faCoins, faRefresh, faPlus } from '@fortawesome/free-solid-svg-icons';
import { toast } from 'react-hot-toast';
import CreditManagement from '../../../components/credit/CreditManagement';
import AddCreditForm from '../../../components/credit/AddCreditForm';
import OrganizationService from '../../../services/Organization';
import CreditService, { IAddCreditPurchaseRequest } from '../../../services/Credit';
import { IOrganization } from '../../../types/organization';
import Layout from '../../../components/Layout/Authorized/Layout';

const OrganizationCreditsPage: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;
  const organizationId = id as string;

  const [organization, setOrganization] = useState<IOrganization | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isAddCreditModalOpen, setIsAddCreditModalOpen] = useState(false);
  const [addingCredit, setAddingCredit] = useState(false);

  // Load organization details
  useEffect(() => {
    if (organizationId) {
      loadOrganization();
    }
  }, [organizationId]);

  const loadOrganization = async () => {
    try {
      setLoading(true);
      const orgData = await OrganizationService.getOrganizationById(organizationId);
      setOrganization(orgData);
    } catch (error) {
      console.error('Error loading organization:', error);
      toast.error('Failed to load organization details');
    } finally {
      setLoading(false);
    }
  };

  const handleBackToOrganization = () => {
    router.push(`/organizations/${organizationId}`);
  };

  const handleBackToOrganizations = () => {
    router.push('/organizations');
  };

  const handleCreditUpdate = () => {
    // Refresh organization data when credits are updated
    loadOrganization();
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      // Trigger refresh in the CreditManagement component
      handleCreditUpdate();
      toast.success('Credit data refreshed');
    } catch (_error) {
      toast.error('Failed to refresh credit data');
    } finally {
      setRefreshing(false);
    }
  };

  const handleAddCredits = () => {
    setIsAddCreditModalOpen(true);
  };

  const handleCloseAddCreditModal = () => {
    setIsAddCreditModalOpen(false);
  };

  const handleAddCreditSubmit = async (creditPurchase: IAddCreditPurchaseRequest) => {
    try {
      setAddingCredit(true);
      const result = await CreditService.addCreditPurchase(organizationId as string, creditPurchase);

      toast.success(`Successfully added ${result.purchase.creditAmount} credits for ${result.purchase.purchasePrice}. New balance: ${result.newBalance}`, { duration: 5000 });

      // Refresh organization data and credit data
      handleCreditUpdate();
    } catch (error: any) {
      console.error('Error adding credit purchase:', error);

      // Extract detailed error information
      let errorMessage = 'Failed to add credit purchase';
      if (error.response?.data?.details) {
        if (typeof error.response.data.details === 'string') {
          if (error.response.data.details.includes('Validation errors:')) {
            const validationPart = error.response.data.details.replace('Validation errors: ', '');
            const validationErrors = validationPart.split('; ').filter((detail: string) => detail.trim());
            errorMessage = 'Please fix the following errors:';

            // Show main error message first
            toast.error(errorMessage, { duration: 4000 });

            // Show each validation error as a separate toast
            validationErrors.forEach((detail: string, index: number) => {
              setTimeout(
                () => {
                  toast.error(`• ${detail}`, { duration: 6000 });
                },
                (index + 1) * 500
              );
            });
            return;
          } else {
            errorMessage = error.response.data.details;
          }
        }
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage, { duration: 6000 });
    } finally {
      setAddingCredit(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4 w-1/3"></div>
            <div className="h-6 bg-gray-200 rounded mb-8 w-1/2"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <FontAwesomeIcon icon={faBuilding} className="text-gray-400 text-6xl mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Organization Not Found</h2>
            <p className="text-gray-600 mb-6">The organization you're looking for doesn't exist or you don't have access to it.</p>
            <Button color="primary" onPress={handleBackToOrganizations} startContent={<FontAwesomeIcon icon={faArrowLeft} />}>
              Back to Organizations
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        {/* Header with Back Button and Breadcrumbs */}
        <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Back Button */}
              <Button variant="light" size="sm" onPress={handleBackToOrganization} startContent={<FontAwesomeIcon icon={faArrowLeft} />} className="text-gray-600 hover:text-gray-900">
                Back
              </Button>

              {/* Separator */}
              <div className="h-6 w-px bg-gray-300"></div>

              {/* Breadcrumbs */}
              <Breadcrumbs>
                <BreadcrumbItem onPress={handleBackToOrganizations}>Organizations</BreadcrumbItem>
                <BreadcrumbItem onPress={handleBackToOrganization}>{organization.name}</BreadcrumbItem>
                <BreadcrumbItem>Credits</BreadcrumbItem>
              </Breadcrumbs>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button size="sm" variant="ghost" startContent={<FontAwesomeIcon icon={faRefresh} />} onPress={handleRefresh} isDisabled={refreshing} className="text-gray-700 hover:text-primary hover:bg-primary/10">
                Refresh
              </Button>
              <Button size="sm" color="primary" variant="flat" startContent={<FontAwesomeIcon icon={faPlus} />} onPress={handleAddCredits} className="font-medium">
                Add Credits
              </Button>
            </div>
          </div>
        </div>

        {/* Page Title Section */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <div className="p-2 bg-primary/10 rounded-lg">
              <FontAwesomeIcon icon={faCoins} className="text-primary text-xl" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">Credit Management</h1>
          </div>
          <p className="text-gray-600 text-lg ml-12">{organization.name}</p>
        </div>

        {/* Content */}
        <CreditManagement organizationId={organizationId} onCreditUpdate={handleCreditUpdate} />

        {/* Add Credit Form Modal */}
        <AddCreditForm isOpen={isAddCreditModalOpen} onClose={handleCloseAddCreditModal} onSubmit={handleAddCreditSubmit} loading={addingCredit} />
      </div>
    </Layout>
  );
};

export default OrganizationCreditsPage;
