import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import OrganizationService from '../../services/Organization';
import Layout from '../../components/Layout/Authorized/Layout';
import { <PERSON><PERSON>, Tooltip, Spinner, Card, CardHeader, CardBody, Chip } from '@heroui/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEye, faPlus, faBuilding, faEdit, faCoins, faCreditCard } from '@fortawesome/free-solid-svg-icons';
import { IOrganization } from '../../types/organization';
import OrgTable, { OrgTableColumn } from '../../components/ui/OrgTable';
import { formattedDisplayString } from '../../utils/string';
import { formatCurrency } from '../../lib/utils';

export default function OrganizationsPage() {
  const router = useRouter();
  const [organizations, setOrganizations] = useState<IOrganization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openTooltip, setOpenTooltip] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        const data = await OrganizationService.getAllOrganizations();
        setOrganizations(data);
      } catch (err) {
        setError('Failed to fetch organizations');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizations();
  }, []);

  const columns: OrgTableColumn<any>[] = [
    {
      id: 'name',
      header: 'Name',
      accessorKey: 'name',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <FontAwesomeIcon icon={faBuilding} className="text-gray-400" />
          <span className="font-medium">{row.name}</span>
        </div>
      )
    },
    {
      id: 'website',
      header: 'Website',
      accessorKey: 'website',
      cell: (row) => (
        <a href={row.website} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
          {row.website}
        </a>
      )
    },
    {
      id: 'status',
      header: 'Status',
      accessorKey: 'status',
      cell: (row) => (
        <Chip color={row.status === 'active' ? 'success' : 'warning'} variant="flat" size="sm">
          {formattedDisplayString(row.status)}
        </Chip>
      )
    },
    {
      id: 'type',
      header: 'Type',
      accessorKey: 'type',
      cell: (row) => (
        <Chip color="primary" variant="flat" size="sm">
          {formattedDisplayString(row.type)}
        </Chip>
      )
    },
    {
      id: 'creditBalance',
      header: 'Credit Balance',
      accessorKey: 'creditBalance',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <FontAwesomeIcon icon={faCreditCard} className="text-green-500" />
          <span className="font-medium">{formatCurrency(row.creditBalance || 0)}</span>
        </div>
      )
    },
    {
      id: 'actions',
      header: 'Actions',
      accessorKey: 'id',
      cell: (row) => (
        <div className="flex gap-2">
          <Tooltip
            content="View Details"
            placement="top"
            isOpen={openTooltip === `view-${row.id}`}
            closeDelay={0}
          >
            <Button
              isIconOnly
              variant="light"
              size="sm"
              onPress={() => router.push(`/organizations/${row.id}`)}
              className="hover:bg-primary/10"
              onMouseEnter={() => setOpenTooltip(`view-${row.id}`)}
              onMouseLeave={() => setOpenTooltip(null)}
            >
              <FontAwesomeIcon icon={faEye} className="text-primary" />
            </Button>
          </Tooltip>
          <Tooltip
            content="Manage Credits"
            placement="top"
            isOpen={openTooltip === `credits-${row.id}`}
            closeDelay={0}
          >
            <Button
              isIconOnly
              variant="light"
              size="sm"
              onPress={() => router.push(`/organizations/${row.id}/credits`)}
              className="hover:bg-warning/10"
              onMouseEnter={() => setOpenTooltip(`credits-${row.id}`)}
              onMouseLeave={() => setOpenTooltip(null)}
            >
              <FontAwesomeIcon icon={faCoins} className="text-warning" />
            </Button>
          </Tooltip>
          <Tooltip
            content="Edit Organization"
            placement="top"
            isOpen={openTooltip === `edit-${row.id}`}
            closeDelay={0}
          >
            <Button
              isIconOnly
              variant="light"
              size="sm"
              onPress={() => router.push(`/organizations/${row.id}?edit=true`)}
              className="hover:bg-success/10"
              onMouseEnter={() => setOpenTooltip(`edit-${row.id}`)}
              onMouseLeave={() => setOpenTooltip(null)}
            >
              <FontAwesomeIcon icon={faEdit} className="text-success" />
            </Button>
          </Tooltip>
        </div>
      )
    }
  ];

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[60vh] bg-gray-50">
          <Spinner label="Loading organizations..." size="lg" color="primary" />
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[60vh] bg-gray-50">
          <Card className="max-w-md shadow-xl rounded-2xl">
            <CardBody>
              <div className="text-lg text-danger text-center">{error}</div>
            </CardBody>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex-1 flex flex-col">
        <div className="container mx-auto px-4 flex-1 flex flex-col">
          <Card className="shadow-xl rounded-2xl border-0 flex flex-col flex-1 min-h-0">
            <CardHeader className="flex flex-col sm:flex-row justify-between items-start sm:items-center px-8 py-6 border-b bg-white rounded-t-2xl">
              <div className="flex items-center gap-3">
                <FontAwesomeIcon icon={faBuilding} className="text-primary text-3xl" />
                <div>
                  <h1 className="text-3xl font-bold">Organizations</h1>
                  <p className="text-gray-500 text-sm mt-1">Manage and view all your organizations here.</p>
                </div>
              </div>
              <Button color="primary" variant="flat" startContent={<FontAwesomeIcon icon={faPlus} />} onPress={() => router.push('/organizations/create')} className="font-medium mt-4 sm:mt-0" size="md">
                Create
              </Button>
            </CardHeader>
            <CardBody className="p-4 bg-white rounded-b-2xl flex-1 min-h-0 overflow-y-auto">
              <OrgTable columns={columns} data={organizations} emptyMessage="No organizations found. Click the button above to create one." />
            </CardBody>
          </Card>
        </div>
      </div>
    </Layout>
  );
}
