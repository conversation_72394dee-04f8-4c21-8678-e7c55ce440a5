import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import Layout from '../../components/Layout/Authorized/Layout';
import { <PERSON><PERSON>, Spinner, Card, CardBody, CardHeader, Chip, Breadcrumbs, BreadcrumbItem } from '@heroui/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft, faEdit, faGlobe, faEnvelope, faPhone, faMapMarkerAlt, faUsers, faCreditCard, faPlug, faCoins, faBuilding } from '@fortawesome/free-solid-svg-icons';
import OrganizationService from '../../services/Organization';
import { IOrganization } from '../../types/organization';
import { formattedDisplayString } from '../../utils/string';
import { formatCurrency } from '../../lib/utils';
import { toast } from 'react-hot-toast';
import EditOrganizationForm from '../../components/forms/EditOrganizationForm';

export default function OrganizationDetailsPage() {
  const router = useRouter();
  const { id, edit } = router.query;
  const [org, setOrg] = useState<IOrganization | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (!id) return;
    setLoading(true);
    OrganizationService.getOrganizationById(id as string)
      .then((data) => setOrg(data))
      .catch(() => setError('Failed to fetch organization details'))
      .finally(() => setLoading(false));
  }, [id]);

  // Check if edit mode should be enabled from query parameter
  useEffect(() => {
    if (edit === 'true') {
      setIsEditing(true);
    }
  }, [edit]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'danger';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  const handleSave = async (data: Partial<IOrganization>) => {
    try {
      await OrganizationService.updateOrganization(org?.id || '', data);
      toast.success('Organization updated successfully');
      setIsEditing(false);
      // Refresh organization data
      fetchOrganization();
    } catch (error: any) {
      toast.error(error.message || 'Failed to update organization');
    }
  };

  const fetchOrganization = () => {
    if (!id) return;
    setLoading(true);
    OrganizationService.getOrganizationById(id as string)
      .then((data) => setOrg(data))
      .catch(() => setError('Failed to fetch organization details'))
      .finally(() => setLoading(false));
  };

  if (loading) {
    return (
      <Layout
        children={
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center justify-center h-64">
              <Spinner label="Loading organization details..." size="lg" />
            </div>
          </div>
        }
      />
    );
  }

  if (error || !org) {
    return (
      <Layout
        children={
          <div className="container mx-auto px-4 py-8">
            <Button variant="light" size="sm" onPress={() => router.push('/organizations')}>
              <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
              Back to Organizations
            </Button>
            <div className="mt-6 bg-white rounded-lg shadow p-6">
              <div className="text-red-500 text-center">{error || 'Organization not found'}</div>
            </div>
          </div>
        }
      />
    );
  }

  return (
    <Layout
      children={
        <div className="container mx-auto px-4 py-8">
          {/* Header with Back Button and Breadcrumbs */}
          <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Back Button */}
                <Button variant="light" size="sm" onPress={() => router.push('/organizations')} startContent={<FontAwesomeIcon icon={faArrowLeft} />} className="text-gray-600 hover:text-gray-900">
                  Back
                </Button>

                {/* Separator */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Breadcrumbs */}
                <Breadcrumbs>
                  <BreadcrumbItem onPress={() => router.push('/organizations')}>Organizations</BreadcrumbItem>
                  <BreadcrumbItem>{org.name}</BreadcrumbItem>
                </Breadcrumbs>
              </div>

              {/* Action Buttons */}
              {!isEditing && (
                <div className="flex gap-2">
                  <Button size="sm" variant="ghost" startContent={<FontAwesomeIcon icon={faCoins} className="text-warning" />} onPress={() => router.push(`/organizations/${org.id}/credits`)} className="text-gray-700 hover:text-warning hover:bg-warning/10">
                    Credits
                  </Button>
                  <Button size="sm" color="primary" variant="flat" startContent={<FontAwesomeIcon icon={faEdit} />} onPress={handleEdit} className="font-medium">
                    Edit
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Page Title Section */}
          <div className="mb-6">
            <div className="flex items-center space-x-3 mb-2">
              <div className="p-2 bg-primary/10 rounded-lg">
                <FontAwesomeIcon icon={faBuilding} className="text-primary text-xl" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900">{isEditing ? 'Edit Organization' : 'Organization Details'}</h1>
            </div>
            <p className="text-gray-600 text-lg ml-12">{org.name}</p>
          </div>

          <div className="space-y-6">
            {isEditing ? (
              <EditOrganizationForm organization={org} onSubmit={handleSave} onCancel={handleCancel} />
            ) : (
              <>
                {/* Organization Information */}
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center w-full">
                      <h1 className="text-2xl font-bold">{org.name}</h1>
                      {org.membership && (
                        <Chip color={getStatusColor(org.membership.status)} variant="flat">
                          {org.membership.status}
                        </Chip>
                      )}
                    </div>
                  </CardHeader>
                  <CardBody className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600">Website</label>
                        <div className="flex items-center mt-1">
                          <FontAwesomeIcon icon={faGlobe} className="mr-2 text-gray-400" />
                          <a href={org.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                            {org.website}
                          </a>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Created</label>
                        <div className="mt-1">{formatDate(org.createdAt)}</div>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">AI Summary</label>
                      <div className="mt-1 p-3 bg-gray-50 rounded-md">{org.aiSummary}</div>
                    </div>
                  </CardBody>
                </Card>

                {/* Membership Details */}
                {org.membership && (
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold flex items-center">
                        <FontAwesomeIcon icon={faUsers} className="mr-2" />
                        Membership Details
                      </h3>
                    </CardHeader>
                    <CardBody className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-600">Type</label>
                          <div className="mt-1 font-medium">{formattedDisplayString(org.membership.type)}</div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Status</label>
                          <div className="mt-1">
                            <Chip color={getStatusColor(org.membership.status)} variant="flat">
                              {formattedDisplayString(org.membership.status)}
                            </Chip>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Tier</label>
                          <div className="mt-1 font-medium">{formattedDisplayString(org.membership.tier)}</div>
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-600">Organization Type</label>
                          <div className="mt-1 font-medium">{formattedDisplayString(org.membership.organizationType)}</div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Max Users</label>
                          <div className="mt-1 font-medium">{org.membership.maxUser}</div>
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-600">Credit Balance</label>
                          <div className="mt-1 font-medium flex items-center">
                            <FontAwesomeIcon icon={faCreditCard} className="mr-2 text-green-500" />
                            {formatCurrency(org.membership.creditBalance)}
                          </div>
                        </div>
                        {org.membership.endsAt && (
                          <div>
                            <label className="text-sm font-medium text-gray-600">Ends At</label>
                            <div className="mt-1 font-medium">{formatDate(org.membership.endsAt)}</div>
                          </div>
                        )}
                      </div>
                    </CardBody>
                  </Card>
                )}

                {/* Address Information */}
                {org.addresses && org.addresses.length > 0 && (
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold flex items-center">
                        <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-2" />
                        Addresses
                      </h3>
                    </CardHeader>
                    <CardBody className="space-y-4">
                      {org.addresses.map((address, index) => (
                        <div key={address.id} className={index > 0 ? 'border-t pt-4' : ''}>
                          <div className="flex justify-between items-center mb-2">
                            <h4 className="font-medium">{address.title}</h4>
                            <Chip size="sm" variant="flat">
                              {address.purpose}
                            </Chip>
                          </div>
                          <div className="text-gray-700">
                            <div>{address.addressLine1}</div>
                            {address.addressLine2 && <div>{address.addressLine2}</div>}
                            <div>
                              {address.city.cityName}, {address.city.state.state} {address.zipCode}
                            </div>
                            <div>
                              {address.city.state.country.country} ({address.city.state.country.isoCode})
                            </div>
                          </div>
                        </div>
                      ))}
                    </CardBody>
                  </Card>
                )}

                {/* Contact Information */}
                {org.orgContacts && org.orgContacts.length > 0 && (
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold flex items-center">
                        <FontAwesomeIcon icon={faEnvelope} className="mr-2" />
                        Contact Information
                      </h3>
                    </CardHeader>
                    <CardBody className="space-y-4">
                      {org.orgContacts.map((contact, index) => (
                        <div key={index} className="border-b last:border-b-0 pb-4 last:pb-0">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="text-sm font-medium text-gray-600">Type</label>
                              <div className="mt-1 font-medium flex items-center">
                                <FontAwesomeIcon icon={contact.type === 'email' ? faEnvelope : faPhone} className="mr-2 text-gray-400" />
                                {formattedDisplayString(contact.type)}
                              </div>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-600">Purpose</label>
                              <div className="mt-1 font-medium">{formattedDisplayString(contact.purpose)}</div>
                            </div>
                          </div>
                          <div className="mt-4">
                            <label className="text-sm font-medium text-gray-600">Value</label>
                            <div className="mt-1 font-medium">{contact.value}</div>
                          </div>
                          {contact.label && (
                            <div className="mt-2">
                              <label className="text-sm font-medium text-gray-600">Label</label>
                              <div className="mt-1 font-medium">{contact.label}</div>
                            </div>
                          )}
                        </div>
                      ))}
                    </CardBody>
                  </Card>
                )}

                {/* Integrations */}
                {org.orgIntegrations && org.orgIntegrations.length > 0 && (
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold flex items-center">
                        <FontAwesomeIcon icon={faPlug} className="mr-2" />
                        Integrations
                      </h3>
                    </CardHeader>
                    <CardBody className="space-y-4">
                      {org.orgIntegrations.map((integration) => (
                        <div key={integration.id} className="border rounded-lg p-4">
                          <div className="flex justify-between items-center mb-2">
                            <h4 className="font-medium">{integration.name}</h4>
                            <Chip color={integration.active ? 'success' : 'danger'} size="sm">
                              {integration.active ? 'Active' : 'Inactive'}
                            </Chip>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Provider:</span> {integration.provider}
                            </div>
                            <div>
                              <span className="text-gray-600">Type:</span> {integration.type}
                            </div>
                            <div>
                              <span className="text-gray-600">Expires:</span> {formatDate(integration.expiresAt)}
                            </div>
                          </div>
                          {integration.data?.aiCredits && (
                            <div className="mt-2 text-sm">
                              <span className="text-gray-600">AI Credits:</span> {integration.data.aiCredits}
                            </div>
                          )}
                        </div>
                      ))}
                    </CardBody>
                  </Card>
                )}
              </>
            )}
          </div>
        </div>
      }
    />
  );
}
