import * as React from 'react';
import { NextPage } from 'next';
import { UnauthorizedLayout as Layout } from '../components';
import { LoginForm } from '../components';
import { setup } from '../lib/csrf';

const Login: NextPage = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="flex w-full max-w-3xl bg-white rounded-lg shadow-xl overflow-hidden">
        <div className="hidden md:flex flex-col items-center justify-center bg-gray-100 p-10 w-1/2">
          <a href="https://acqwired.com/">
            <img src="/assets/images/acqwired-logo.svg" alt="Acqwired Logo" className="w-40 h-auto" />
          </a>
        </div>
        <div className="flex flex-col justify-center p-10 w-full md:w-1/2">
          <h2 className="text-2xl font-bold text-center mb-6">Admin <PERSON>gin</h2>
          <LoginForm />
        </div>
      </div>
    </div>
  );
};

(Login as any).getLayout = function getLayout(page: React.ReactElement) {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps = setup(async () => {
  return { props: {} };
});

export default Login; 
