import { NextApiRequest, NextApiResponse } from 'next';
import jwt from 'jsonwebtoken';
import { parseExpiresIn } from '../../../lib/utils';
import config from '../../../../config';
import { csrf } from '../../../lib/csrf';

interface ITokenResponse {
  firstName: string;
  lastName: string;
  email: string;
  scope: string;
  oid: string;
  uid: string;
  lid: string;
  tokenType: string;
}

interface IAccessToken extends jwt.JwtPayload, ITokenResponse {
  aud: string;
  iss: string;
}

type UserDetails = {
  firstName: string;
  lastName: string;
  email: string;
  scope: string;
  oid: string;
  uid: string;
};

const handler = csrf(async (req: NextApiRequest, res: NextApiResponse) => {
  const { internalHostDataAPI } = config;
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).json({
      name: 'Bad Request',
      msg: 'Must provide a valid username and password'
    });
  }

  try {
    const options = {
      method: 'POST',
      body: JSON.stringify({ email, password, appSource: 'admin' }),
      headers: {
        authorization: req.headers['authorization'] || '',
        'user-agent': req.headers['user-agent'] || '',
        'content-type': 'application/json',
        accept: 'application/json'
      }
    };

    const response = await fetch(`${internalHostDataAPI}/auth/signin`, options);

    if (!response.ok) {
      throw new Error('Login failed.');
    }

    const { data } = await response.json();
    const { accessToken, refreshToken, expiresIn, refreshExpiresIn } = data;

    const userDetails = validateAndExtractToken(accessToken);
    setAuthCookies(res, accessToken, refreshToken, expiresIn, refreshExpiresIn);

    res.status(200).json(userDetails);
  } catch (error: any) {
    res.status(401).json({
      msg: 'Invalid credentials',
      error: error.message
    });
  }
});

function validateAndExtractToken(accessToken: string): UserDetails {
  const parsedToken = jwt.decode(accessToken) as IAccessToken | null;

  if (!parsedToken || !parsedToken.exp || Date.now() / 1000 >= parsedToken.exp) {
    throw new Error('Invalid or expired token');
  }

  if (Date.now() / 1000 >= parsedToken.exp) {
    throw new Error('Token expired');
  }

  const { firstName = '', lastName = '', email = '', scope = '', oid = '', uid = '' } = parsedToken;

  return { firstName, lastName, email, scope, oid, uid };
}

function setAuthCookies(res: NextApiResponse, accessToken: string, refreshToken: string, expiresIn: string, refreshExpiresIn: string) {
  const cookieOptions = `Secure; HttpOnly; Path=/; SameSite=None;`;

  res.setHeader('Set-Cookie', [`admin_accessToken=${accessToken}; ${cookieOptions} Max-Age=${parseExpiresIn(expiresIn)};`, `admin_refreshToken=${refreshToken}; ${cookieOptions} Max-Age=${parseExpiresIn(refreshExpiresIn)};`]);
}

export default handler;
