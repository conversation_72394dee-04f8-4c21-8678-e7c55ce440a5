import { NextApiRequest, NextApiResponse } from 'next';
import cookie from 'cookie';
import jwt from 'jsonwebtoken';

interface IErrorResponse {
  msg: string;
  details?: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<IErrorResponse | { msg: string }>) {
  if (req.method !== 'POST') {
    return res.status(405).json({ msg: 'Method Not Allowed' });
  }

  const cookies = cookie.parse(req.headers.cookie || '');
  const { admin_accessToken } = cookies;

  if (!admin_accessToken) {
    return res.status(401).json({ msg: 'Unauthorized' });
  }

  try {
    jwt.decode(admin_accessToken); // Not validating, just decoding

    // Clear admin-specific cookies
    const cookieOptions = 'HttpOnly; Path=/; Max-Age=0; Secure; SameSite=None';
    res.setHeader('Set-Cookie', [`admin_accessToken=; ${cookieOptions}`, `admin_refreshToken=; ${cookieOptions}`]);

    res.status(200).json({ msg: 'Logged out successfully' });
  } catch (error) {
    res.status(500).json({ msg: 'Error logging out', details: error instanceof Error ? error.message : 'Unknown error' });
  }
}
