import { NextApiRequest, NextApiResponse } from 'next';
import { parseExpiresIn } from '../../../lib/utils';
import config from '../../../../config';
import { csrf } from '../../../lib/csrf';

const handler = csrf(async (req: NextApiRequest, res: NextApiResponse) => {
  const { internalHostDataAPI } = config;

  try {
    // Extract refresh token from cookies
    const cookies =
      req.headers.cookie?.split(';').reduce(
        (acc, cookie) => {
          const [key, value] = cookie.trim().split('=');
          acc[key] = value;
          return acc;
        },
        {} as Record<string, string>
      ) || {};

    const refreshToken = cookies['admin_refreshToken'];

    if (!refreshToken) {
      throw new Error('No refresh token found');
    }

    const options = {
      method: 'POST',
      body: JSON.stringify({ refreshToken, appSource: 'admin' }),
      headers: {
        authorization: req.headers['authorization'] || '',
        'user-agent': req.headers['user-agent'] || '',
        'content-type': 'application/json',
        accept: 'application/json'
      }
    };

    const response = await fetch(`${internalHostDataAPI}/auth/refresh`, options);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Token refresh failed: ${errorText}`);
    }

    const { data } = await response.json();
    const { accessToken, refreshToken: newRefreshToken } = data;

    // Set app-specific cookies
    const cookieOptions = `Secure; HttpOnly; Path=/; SameSite=None;`;
    res.setHeader('Set-Cookie', [`admin_accessToken=${accessToken}; ${cookieOptions} Max-Age=${parseExpiresIn('3600')};`, `admin_refreshToken=${newRefreshToken || refreshToken}; ${cookieOptions} Max-Age=${parseExpiresIn('86400')};`]);

    res.status(200).json({ success: true });
  } catch (error: any) {
    console.error('Admin refresh error:', error);
    res.status(401).json({
      msg: 'Token refresh failed',
      error: error.message
    });
  }
});

export default handler;
