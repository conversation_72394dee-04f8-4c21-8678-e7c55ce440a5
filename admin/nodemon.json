{"verbose": true, "watch": ["src/**/*.ts", "src/**/*.js", "src/**/*.json", "src/*.json", "src/*.ts", "src/*.js", "./*.ts", "./*.js", "./*.json"], "ignore": ["logs/*", "node_modules/*", "dist/*", "prisma/*", "src/tests/*"], "exec": "node --inspect=0.0.0.0:9228 ./node_modules/.bin/next dev -p 3006", "pollingInterval": 100, "delay": 100, "legacyWatch": true, "events": {"restart": "echo 'Nodemon restart event triggered!'"}, "ext": "ts,js,json"}