import { heroui } from '@heroui/theme';

module.exports = {
  mode: 'jit',
  content: ['./src/pages/**/*.{js,ts,jsx,tsx}', './src/components/**/*.{js,ts,jsx,tsx}', './node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}'],
  theme: {
    fontSize: {
      xs: ['0.6875rem', '0.875rem'],
      sm: ['0.75rem', '1rem'],
      base: ['0.875rem', '1.25rem'],
      lg: ['1rem', '1.5rem'],
      xl: ['1.125rem', '1.75rem'],
      '2xl': ['1.25rem', '1.75rem'],
      '3xl': ['1.5rem', '2rem'],
      '4xl': ['1.875rem', '2.25rem'],
      '5xl': ['2.25rem', '2.5rem']
    },
    extend: {
      height: {
        128: '32rem'
      },
      maxWidth: {
        '4/5': '80%',
        200: '200px'
      },
      minWidth: {
        md: '546px',
        44: '11rem',
        48: '12rem',
        52: '13rem'
      },
      colors: {
        black: '#091133',
        lightblue: '#181f3f',
        acq: {
          50: '#F0EFFF',
          100: '#E2E0FF',
          200: '#C4C1FF',
          300: '#A7A1FF',
          400: '#8982FF',
          500: '#6c63ff',
          600: '#564FCC',
          700: '#413B99',
          800: '#2B2866',
          900: '#091133'
        },
        acqgray: {
          50: '#f6f7f8',
          100: '#e5e7eb',
          200: '#dadde3',
          300: '#c1c7cf',
          400: '#a2aab8',
          500: '#8c93a5',
          600: '#7a8096',
          700: '#6e7287',
          800: '#5d5f70',
          900: '#4d4f5b'
        }
      },
      boxShadow: {
        '3xl': '-1px 8px 4px -3px rgba(0, 0, 0, 0.2)',
        '3xl-r': '1px 8px 9px 1px rgba(0, 0, 0, 0.2)',
        top: '0 -2px 2px rgba(0, 0, 0, 0.1)'
      },
      fontFamily: {
        roboto: ['var(--font-roboto)', 'sans-serif'],
        montserrat: ['var(--font-montserrat)', 'sans-serif']
      }
    },
    container: {
      center: true
    },
    screens: {
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px'
    }
  },
  plugins: [
    require('tailwindcss'),
    require('autoprefixer'),
    heroui({
      layout: {
        fontSize: {
          tiny: '0.6875rem',
          small: '0.75rem',
          medium: '0.875rem',
          large: '1rem'
        },
        radius: {
          small: '4px',
          medium: '8px',
          large: '12px'
        },
        borderWidth: {
          small: '1px',
          medium: '1px',
          large: '2px'
        }
      },
      themes: {
        light: {
          colors: {
            primary: {
              DEFAULT: '#6c63ff',
              foreground: '#ffffff',
              50: '#F0EFFF'
            }
          }
        }
      }
    })
  ]
};
