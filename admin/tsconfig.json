{"compilerOptions": {"target": "ES2018", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "checkJs": false, "skipLibCheck": true, "strict": true, "noImplicitAny": false, "strictFunctionTypes": true, "noUnusedLocals": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "sourceMap": true, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist"]}