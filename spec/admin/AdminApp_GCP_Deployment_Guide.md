# Admin App GCP Deployment Configuration

## Overview

This document outlines the necessary configurations added to deploy the Admin App to GCP staging environments (dev and prod) through GitHub Actions and Kustomize.

## Changes Made

### 1. GitHub Actions Workflows

#### A. Build Workflows

- **File**: `.github/workflows/build-dev.yml`
- **File**: `.github/workflows/build-prod.yml`

**Added admin-ci job** with the following configuration:

- Working directory: `./admin`
- Environment: `dev` or `prod`
- Node.js version: 20
- Build steps:
  - Set environment variables
  - Inject secrets (Salesforce, Office 365, Firebase, Google Maps)
  - Install dependencies
  - Run tests
  - Build container
  - Push to GCR repository

**Updated deploy job dependencies** to include `admin-ci`.

#### B. Deploy Workflows

- **File**: `.github/workflows/deploy-dev.yml`
- **File**: `.github/workflows/deploy-prod.yml`

**Added admin** to the service matrix for deployment.

### 2. Kustomize Configurations

#### A. Directory Structure Created

```
kustomize/base/admin/
├── dev/
│   ├── deployment.yaml
│   ├── service.yaml
│   ├── backendconfig.yaml
│   └── kustomization.yaml
└── prod/
    ├── deployment.yaml
    ├── service.yaml
    ├── backendconfig.yaml
    └── kustomization.yaml
```

#### B. Dev Environment (`kustomize/base/admin/dev/`)

**deployment.yaml**:

- Image: `gcr.io/acqwired-dev/admin:latest`
- Port: 3006
- Replicas: 1
- Resources: 250m CPU / 250Mi Memory (requests), 500m CPU / 500Mi Memory (limits)
- Cloud SQL Proxy for database connection
- Health checks on `/` endpoint

**service.yaml**:

- Type: NodePort
- Port: 3006
- Target: admin-web
- No backend config (admin is a user-facing web app like console/frontend)

**kustomization.yaml**:

- Namespace: dev
- Common labels: env=dev
- Includes deployment and service

#### C. Prod Environment (`kustomize/base/admin/prod/`)

**deployment.yaml**:

- Image: `gcr.io/acqwired-prod/admin:latest`
- Port: 3006
- Replicas: 2 (for high availability)
- Resources: 500m CPU / 500Mi Memory (requests), 1000m CPU / 1Gi Memory (limits)
- Cloud SQL Proxy for database connection
- Health checks on `/` endpoint

**service.yaml**:

- Type: NodePort
- Port: 3006
- Target: admin-web
- No backend config (admin is a user-facing web app like console/frontend)

**kustomization.yaml**:

- Namespace: prod
- Common labels: env=prod
- Includes deployment and service

### 3. Ingress/Load Balancer Configuration

#### A. Updated Ingress Rules

**Files Updated**:

- `kustomize/base/console/dev/ingress.yaml`
- `kustomize/base/console/prod/ingress.yaml`

**Added admin service routing**:

- **Dev**: `dev.admin.acqwired.com` → admin service port 3006
- **Prod**: `admin.acqwired.com` → admin service port 3006
- **API Routes**: `/api/v1` paths route to dataapi service for both admin domains

#### B. Updated Managed Certificates

**Files Updated**:

- `kustomize/base/console/dev/managedcert.yaml`
- `kustomize/base/console/prod/managedcert.yaml`

**Added admin domains**:

- **Dev**: `dev.admin.acqwired.com`
- **Prod**: `admin.acqwired.com`

### 4. Key Configuration Details

#### A. Container Configuration

- **Port**: 3006 (as defined in admin app)
- **Health Checks**: HTTP GET on `/` endpoint
- **Image**: Environment-specific GCR images
- **Service Account**: `acqwired-ksa` (same as other services)

#### B. Database Connection

- **Cloud SQL Proxy**: Configured for each environment
- **Dev**: `acqwired-dev:us-central1:acqwired-dev`
- **Prod**: `acqwired-prod:us-central1:acqwired-prod`

#### C. Resource Allocation

- **Dev**: Lower resource allocation for cost optimization
- **Prod**: Higher resource allocation for performance and reliability

#### D. Secrets Management

The admin CI job injects the following secrets:

- `SALESFORCE_CLIENT_ID` / `SALESFORCE_CLIENT_SECRET`
- `OFFICE_365_CLIENT_ID` / `OFFICE_365_CLIENT_SECRET`
- `DATABASE_URL`
- Firebase configuration variables
- `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`

#### E. Load Balancer Configuration

- **Backend Config**: 3600 second timeout for admin service
- **Frontend Config**: HTTP to HTTPS redirect (shared with other services)
- **Static IP**: Uses existing `acqwired-ip` static IP address
- **SSL Certificate**: Managed certificate includes admin domains

## Deployment Flow

### 1. Development Environment

1. Push to `dev` branch triggers `build-dev.yml`
2. Admin CI job builds and pushes container to `gcr.io/acqwired-dev/admin:latest`
3. Deploy workflow applies Kustomize configurations from `kustomize/base/admin/dev/`
4. Console ingress/certificates are updated to include admin routing
5. Admin service deployed to dev namespace in GKE
6. **Access URL**: `https://dev.admin.acqwired.com`

### 2. Production Environment

1. Release published triggers `build-prod.yml`
2. Admin CI job builds and pushes container to `gcr.io/acqwired-prod/admin:latest`
3. Deploy workflow applies Kustomize configurations from `kustomize/base/admin/prod/`
4. Console ingress/certificates are updated to include admin routing
5. Admin service deployed to prod namespace in GKE
6. **Access URL**: `https://admin.acqwired.com`

## Access Configuration

### Domain Setup

- **Dev**: `https://dev.admin.acqwired.com`
- **Prod**: `https://admin.acqwired.com`

### Ingress/Load Balancer

- **Shared Infrastructure**: Admin uses the same load balancer and static IP as console
- **SSL Certificates**: Managed certificates automatically include admin domains
- **Backend Configuration**: Custom timeout and health check settings
- **API Routing**: `/api/v1` paths route to dataapi service for admin domains

## Monitoring and Scaling

### Health Checks

- **Readiness Probe**: HTTP GET `/` on port 3006
- **Liveness Probe**: HTTP GET `/` on port 3006
- **Initial Delay**: 3 seconds
- **Period**: 3 seconds

### Scaling

- **Dev**: 1 replica (sufficient for development/testing)
- **Prod**: 2 replicas (high availability)
- **Auto-scaling**: Can be configured based on CPU/memory usage

## Security Considerations

### Network Policies

- Admin service should only be accessible from authorized networks
- Consider implementing network policies to restrict access

### Authentication

- Admin app has built-in super admin access control
- Only users with admin role or from superadmin organization can access

### Secrets

- All sensitive configuration is injected via GitHub Secrets
- Database connection secured via Cloud SQL Proxy

### SSL/TLS

- Managed SSL certificates for admin domains
- Automatic HTTP to HTTPS redirect
- TLS termination at load balancer

## Certificate Management for New Domains

When adding admin web apps to existing GCP environments, new domains need to be added to the managed certificate configuration. This requires careful coordination to ensure SSL certificates are properly provisioned and deployed.

### Understanding Managed Certificates

Google Cloud Platform uses **ManagedCertificate** resources to automatically provision and manage SSL certificates for your domains. When you add new domains to the `managedcert.yaml` files, GCP will:

1. Automatically provision new SSL certificates for the added domains
2. Update the existing certificate to include all domains in the specification
3. Handle certificate renewal automatically

### Certificate Update Process

#### 1. Domain Addition to Certificate Configuration

When adding new admin domains, the following files are updated:

**Development Environment:**

- File: `kustomize/base/console/dev/managedcert.yaml`
- Added domain: `dev.admin.acqwired.com`

**Production Environment:**

- File: `kustomize/base/console/prod/managedcert.yaml`
- Added domain: `admin.acqwired.com`

#### 2. Automatic Certificate Provisioning

After the `managedcert.yaml` files are updated and deployed via GitHub Actions:

1. **GitHub Actions Deployment**: The deploy workflow applies the updated `managedcert.yaml`
2. **GCP Certificate Manager**: Automatically detects the new domains and begins provisioning
3. **Certificate Generation**: New certificates are generated to include all specified domains
4. **Load Balancer Update**: The load balancer is automatically updated with the new certificate

#### 3. Manual Certificate Update Steps (If Needed)

In some cases, you may need to manually trigger certificate updates or verify the process:

##### Connect to GCP Environment

Follow the authentication steps from the main README:

```bash
# 1. Login to gcloud
gcloud auth login

# 2. Set project (choose appropriate environment)
gcloud config set project acqwired-dev    # For dev environment
# OR
gcloud config set project acqwired-prod   # For prod environment

# 3. Get cluster credentials
# For dev environment:
gcloud container clusters get-credentials dev-cluster --location=us-central1-a

# For prod environment:
gcloud container clusters get-credentials prod-cluster --location=us-east4

# 4. Setup IAP tunnel via bastion
# For dev environment:
gcloud compute ssh gke-bastion --project acqwired-dev --zone us-central1-a --tunnel-through-iap --ssh-key-expire-after 30m -- -L 8888:127.0.0.1:8888  -N -q -f

# For prod environment:
gcloud compute ssh gke-bastion --project acqwired-prod --zone us-east4-a --tunnel-through-iap --ssh-key-expire-after 30m -- -L 8888:127.0.0.1:8888  -N -q -f

# 5. Set proxy for kubectl commands
export HTTPS_PROXY=localhost:8888
```

##### Manual Certificate Update Commands

```bash
# Apply the updated managed certificate configuration
kubectl apply -f kustomize/base/console/dev/managedcert.yaml    # For dev
kubectl apply -f kustomize/base/console/prod/managedcert.yaml   # For prod

# Force update the ingress to recognize new certificate
kubectl apply -f kustomize/base/console/dev/ingress.yaml        # For dev
kubectl apply -f kustomize/base/console/prod/ingress.yaml       # For prod
```

#### 4. Certificate Status Verification

##### Check Managed Certificate Status

```bash
# Check the managed certificate resource
kubectl get managedcertificate -n dev acqwired-ssl-cert     # For dev
kubectl get managedcertificate -n prod acqwired-ssl-cert    # For prod

# Get detailed certificate status
kubectl describe managedcertificate -n dev acqwired-ssl-cert     # For dev
kubectl describe managedcertificate -n prod acqwired-ssl-cert    # For prod
```

**Expected Output:**

```yaml
Status:
  Certificate Status: Active
  Domain Status:
    - Domain: dev.acqwired.com
      Status: Active
    - Domain: dev.console.acqwired.com
      Status: Active
    - Domain: dev.admin.acqwired.com
      Status: Active # This should show as Active once provisioned
    - Domain: dev.e.acqwired.com
      Status: Active
```

##### Check Ingress Status

```bash
# Verify ingress configuration
kubectl get ingress -n dev managed-cert-ingress     # For dev
kubectl get ingress -n prod managed-cert-ingress    # For prod

# Get detailed ingress information
kubectl describe ingress -n dev managed-cert-ingress     # For dev
kubectl describe ingress -n prod managed-cert-ingress    # For prod
```

#### 5. DNS Configuration Verification

Ensure that the new admin domains are properly configured in your DNS provider to point to the GCP load balancer:

```bash
# Get the load balancer IP address
kubectl get ingress -n dev managed-cert-ingress -o jsonpath='{.status.loadBalancer.ingress[0].ip}'     # For dev
kubectl get ingress -n prod managed-cert-ingress -o jsonpath='{.status.loadBalancer.ingress[0].ip}'    # For prod

# Verify DNS resolution
nslookup dev.admin.acqwired.com    # For dev
nslookup admin.acqwired.com        # For prod
```

**DNS Records Required:**

- **Dev**: `dev.admin.acqwired.com` → Load Balancer IP
- **Prod**: `admin.acqwired.com` → Load Balancer IP

### Certificate Provisioning Timeline

**Important**: SSL certificate provisioning can take time. Here's what to expect:

1. **Initial Application**: 0-5 minutes - Certificate resource is created
2. **Domain Validation**: 5-15 minutes - Google validates domain ownership
3. **Certificate Issuance**: 15-60 minutes - Certificate is issued and deployed
4. **Load Balancer Update**: 5-10 minutes - Load balancer is updated with new certificate

**Total Time**: Typically 30-90 minutes for complete certificate provisioning and deployment.

### Troubleshooting Certificate Issues

#### Common Certificate Problems

1. **Certificate Status: FailedNotVisible**

   - **Cause**: Domain doesn't resolve to the load balancer IP
   - **Solution**: Verify DNS configuration and wait for propagation

2. **Certificate Status: Provisioning**

   - **Cause**: Certificate is still being provisioned
   - **Solution**: Wait for the provisioning process to complete (up to 60 minutes)

3. **Certificate Status: Failed**
   - **Cause**: Domain validation failed
   - **Solution**: Check DNS configuration and domain ownership

#### Debugging Commands

```bash
# Check certificate events for errors
kubectl get events --field-selector involvedObject.name=acqwired-ssl-cert -n dev     # For dev
kubectl get events --field-selector involvedObject.name=acqwired-ssl-cert -n prod    # For prod

# Check load balancer status
gcloud compute forwarding-rules list --project=acqwired-dev      # For dev
gcloud compute forwarding-rules list --project=acqwired-prod     # For prod

# Check SSL certificate status in GCP Console
gcloud compute ssl-certificates list --project=acqwired-dev      # For dev
gcloud compute ssl-certificates list --project=acqwired-prod     # For prod
```

#### Force Certificate Recreation (Last Resort)

If certificates are stuck in a failed state:

```bash
# Delete the managed certificate (this will trigger recreation)
kubectl delete managedcertificate -n dev acqwired-ssl-cert     # For dev
kubectl delete managedcertificate -n prod acqwired-ssl-cert    # For prod

# Wait 2-3 minutes, then reapply
kubectl apply -f kustomize/base/console/dev/managedcert.yaml        # For dev
kubectl apply -f kustomize/base/console/prod/managedcert.yaml       # For prod
```

**⚠️ Warning**: This will cause temporary SSL certificate unavailability during recreation.

## Next Steps

1. **Verify GitHub Secrets**: Ensure all required secrets are configured in GitHub repository settings
2. **Test Deployment**: Deploy to dev environment first to verify configuration
3. **DNS Configuration**: Ensure admin domains point to the correct load balancer IP
4. **Certificate Verification**: Monitor certificate provisioning status using the commands above
5. **Monitor Deployment**: Check logs and metrics after deployment
6. **Production Deployment**: Once dev deployment and certificates are verified, proceed with production

## Troubleshooting

### Common Issues

1. **Container fails to start**: Check environment variables and secrets
2. **Database connection issues**: Verify Cloud SQL Proxy configuration
3. **Health check failures**: Ensure admin app starts correctly on port 3006
4. **Image pull failures**: Verify GCR permissions and image exists
5. **SSL certificate issues**: Check managed certificate status and DNS configuration (see Certificate Management section above)
6. **Load balancer issues**: Verify backend config and ingress rules
7. **Certificate provisioning failures**: See Certificate Management troubleshooting section above

### Debugging Commands

```bash
# Check pod status
kubectl get pods -n dev -l app=admin

# Check logs
kubectl logs -n dev deployment/acqwired-admin

# Check service
kubectl get svc -n dev acqwired-admin

# Check ingress
kubectl get ingress -n dev managed-cert-ingress

# Check managed certificate
kubectl get managedcertificate -n dev acqwired-ssl-cert

# Describe deployment
kubectl describe deployment -n dev acqwired-admin

# Check admin service
kubectl get svc -n dev acqwired-admin
```

This configuration ensures the admin app is properly integrated into your existing GCP deployment pipeline and follows the same patterns as your other services, with proper load balancing, SSL certificates, and ingress routing.
