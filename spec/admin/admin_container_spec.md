# Admin Management Container – Complete Build & Deployment Guide

## 1. Overview

The **Admin Management Container** is a modern, containerized web application (built with Next.js) designed for admin users to manage organizations within the Acqwired platform. It provides a secure login page, a dashboard to list all organizations, and features to create and manage organization records. This container is intended to run alongside the existing console and dataapi services, using the same backend API. The application is accessible at **https://admin.console.local** via Nginx reverse proxy.

---

## 2. Project Structure & Key Files

```
admin/
├── .next/                # Next.js build output (auto-generated)
├── src/                  # Source code (pages, components, services, etc.)
│   ├── pages/            # Next.js page routes (e.g., login, organizations)
│   ├── components/       # Reusable UI components (LoginForm, OrgTable, etc.)
│   ├── services/         # API and business logic (auth, organization CRUD)
│   ├── context/          # React context providers (auth, global state)
│   └── utils/            # Utility functions and helpers
├── public/               # Static assets (images, favicon, etc.)
├── env/                  # Environment variable files
├── logs/                 # Application logs
├── package.json          # Project metadata and scripts
├── package-lock.json     # Dependency lock file
├── tsconfig.json         # TypeScript configuration
├── next.config.js        # Next.js configuration
├── Dockerfile            # Production Docker build
├── Dockerfile.local      # Local development Docker build
├── config.ts             # Project configuration
├── Makefile              # Build and deployment automation
├── postcss.config.js     # PostCSS configuration
├── tailwind.config.js    # Tailwind CSS configuration
├── .prettierrc           # Prettier code formatting config
├── eslint.config.mjs     # ESLint configuration
└── next-env.d.ts         # Next.js TypeScript environment
```

**Key File Purposes:**
- `src/pages/`: Main app routes (e.g., `/login`, `/organizations`)
- `src/components/`: UI components (LoginForm, OrgTable, OrgForm, etc.)
- `src/services/`: API logic (auth, organization CRUD, HTTP client)
- `src/context/`: Auth and global state management
- `env/`: Environment variable files for different environments
- `Dockerfile`, `Dockerfile.local`: Docker build instructions for production and local dev
- `Makefile`: Automation for build, test, and deployment
- `next.config.js`, `tsconfig.json`, `tailwind.config.js`: Framework and tooling configuration

---

## 3. A-Z: Building, Running, and Deploying the Admin Container

### A. Prerequisites
- Node.js (v20+)
- Docker & Docker Compose
- Git
- Code editor (VS Code recommended)
- Unix-based OS or Windows with WSL2

### B. Initial Setup
1. **Create Project Directory:**
   ```bash
   mkdir admin && cd admin
   ```
2. **Initialize Next.js Project:**
   ```bash
   npx create-next-app@latest . --typescript
   npm install tailwindcss postcss autoprefixer
   npx tailwindcss init -p
   # Add any other dependencies as needed
   ```
3. **Set Up Environment Variables:**
   ```bash
   mkdir env
   cp ../console/env/.env.example env/.env.local
   # Edit env/.env.local as needed
   # Ensure DATA_API_URL points to http://dataapi:3002
   # Ensure NEXT_PUBLIC_BASE_URL=https://admin.console.local
   ```

### C. Local Development
- **Run with Next.js:**
  ```bash
  npm run dev
  # App runs on http://localhost:3006
  ```
- **Run with Docker:**
  ```bash
  docker build -f Dockerfile.local -t acqwired-admin-local .
  docker run -p 3006:3006 --env-file env/.env.local acqwired-admin-local
  # Access at http://localhost:3006 (or via Nginx at https://admin.console.local)
  ```

### D. Production Build & Run
- **Build:**
  ```bash
  npm run build
  # or
  docker build -t acqwired-admin .
  ```
- **Run:**
  ```bash
  npm start
  # or
  docker run -p 3006:3006 --env-file env/.env.production acqwired-admin
  # Access at https://admin.console.local
  ```

### E. Docker Compose (Recommended for Multi-Container Setup)
- **Sample service definition:**
  ```yaml
  services:
    admin:
      build:
        context: ./admin
        dockerfile: Dockerfile.local
      env_file:
        - admin/env/.env.local
      environment:
        - NODE_ENV=development
        - DATA_API_URL=http://dataapi:3002
        - NEXT_PUBLIC_BASE_URL=https://admin.console.local
      depends_on:
        dataapi:
          condition: service_healthy
      ports:
        - "3006:3006"
      volumes:
        - ./admin/logs:/admin/logs
        - ./admin:/admin
      healthcheck:
        test: curl --fail http://localhost:3006/ || exit 1
        start_period: 5s
        interval: 5s
        timeout: 10s
        retries: 20
  ```
- **Start all containers:**
  ```bash
  docker-compose up -d
  # Access at https://admin.console.local
  ```

---

## 4. Dockerfile(s) & Container Build Process

- **Dockerfile** (production): Multi-stage build, uses Node.js Alpine, installs dependencies, builds Next.js app, runs as non-root user, exposes port 3006.
- **Dockerfile.local**: For local dev, exposes port 3006, runs `npm run dev`.

**Build Steps:**
1. Copy source, config, and env files
2. Install dependencies
3. Build Next.js app
4. Copy build output and node_modules to final image
5. Set user, expose port, and define CMD

---

## 5. Nginx Configuration & Role

- **Purpose:** Acts as a reverse proxy, SSL terminator, and router for frontend and API traffic.
- **Key Config (nginx.conf):**
  ```nginx
  upstream admin-service {
      server admin:3006;
  }
  server {
      listen 443 ssl;
      server_name admin.console.local;
      ssl_certificate /etc/ssl/certs/acq-selfsigned.crt;
      ssl_certificate_key /etc/ssl/private/acq-selfsigned.key;
      ssl_protocols TLSv1.2 TLSv1.3;
      ssl_prefer_server_ciphers on;
      ssl_ciphers HIGH:!aNULL:!MD5;
      client_max_body_size 25M;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_set_header Host $host;
      proxy_set_header X-Forwarded-Host $host;
      proxy_set_header X-Forwarded-Port $server_port;
      location / {
          proxy_pass http://admin-service;
          proxy_http_version 1.1;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
      }
      location /api/v1/ {
          rewrite ^/api/v1/(.*) /$1 break;
          proxy_pass http://data-service;
      }
  }
  ```
- **SSL:** Use self-signed for dev, real certs for prod.
- **Routing:** `/` and static assets to admin, `/api/v1/` to backend.
- **Access:** Visit [https://admin.console.local](https://admin.console.local) in your browser (ensure your hosts file maps this domain to your Docker/Nginx IP).

---

## 6. Container Networking & Inter-Container Communication

- **Docker Compose** creates a shared network for all services.
- **Service Discovery:** Containers communicate using service names (e.g., `dataapi:3002`).
- **Environment Variables:**
  - `DATA_API_URL=http://dataapi:3002` (used by admin to call backend)
  - `NEXT_PUBLIC_BASE_URL=https://admin.console.local` (used for absolute URLs)
- **Port Mapping:**
  - Admin: 3006
  - Backend: 3002

---

## 7. Backend Connection (API Flow)

- **API Calls:**
  - All backend requests go through the URL set in `DATA_API_URL`.
  - Example: `src/services/Http.ts` sets up Axios with `baseURL: process.env.DATA_API_URL`.
- **Next.js Rewrites:**
  - `next.config.js` rewrites `/api/:path*` to backend API.
- **Authentication:**
  - JWT tokens managed via cookies, sent with API requests.
- **Health Checks:**
  - Admin: `curl http://localhost:3006/`
  - Backend: `curl http://dataapi:3002/health`

---

## 8. Login & Authentication Flow

- **Login Page:** `/login` route, uses a `LoginForm` component (similar to console)
- **Auth Service:**
  - Handles sign-in, token refresh, etc.
  - Calls `/api/account/login` (handled by Next.js API route, which proxies to backend)
- **Token Management:**
  - Access and refresh tokens stored in secure HTTP-only cookies
  - Automatic refresh before expiration
- **Protected Routes:**
  - Auth context manages auth state
  - Unauthenticated users are redirected to `/login`
- **Security:**
  - CSRF protection via server-side setup
  - Secure cookie configuration
  - Token validation and expiration checks

---

## 9. Organization Management Features

- **Organization List Page:**
  - Fetches and displays all organizations from the backend
  - Table/grid view with search and pagination
- **Create Organization:**
  - Form to add a new organization (name, type, contact, etc.)
  - Validates input and submits to backend
- **Manage Organization:**
  - Edit and update organization details
  - Delete or deactivate organizations
  - View organization details and related data
- **Admin-Only Access:**
  - Only authenticated admin users can access management features

---

## 10. Project Journey & Best Practices

- **Initialization:** Bootstrapped with Next.js and TypeScript
- **Styling:** Tailwind CSS for rapid UI
- **State Management:** Context API and/or Zustand/Redux
- **API Integration:** Services in `src/services/` for backend communication
- **Dockerization:** Multiple Dockerfiles for different environments
- **CI/CD:** Makefile and scripts for automation
- **Security:** Strict env var management, never commit secrets
- **Scaling:** Kubernetes support via custom Dockerfile if needed
- **Current State:** Robust, production-ready, easily portable

**Best Practices:**
- Use environment variables for all secrets and endpoints
- Run containers as non-root
- Use health checks and monitoring
- Keep dependencies up to date
- Write unit/integration tests for critical logic
- Use Makefile for automation
- Document everything (like this!)

---

## 11. How to Replicate for a New Project

1. Copy the folder structure and Dockerfiles
2. Update `package.json`, `tsconfig.json`, and configs for your new project
3. Replace or refactor code in `src/` as needed
4. Set up new environment variables in `env/`
5. Test locally, then build and deploy using Docker and/or Kubernetes
6. Set up nginx as a reverse proxy for SSL and routing (use your desired domain, e.g., `admin.console.local`)
7. Use Docker Compose for multi-container orchestration
8. Follow best practices for security, scaling, and monitoring

---

## 12. References
- [Next.js Documentation](https://nextjs.org/docs)
- [Docker Documentation](https://docs.docker.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

---

## 13. Testing
Write unit and integration tests for all critical logic (e.g., authentication, organization CRUD). Use Jest or your preferred framework. Run tests with `npm test` or `yarn test`.

## 14. CI/CD
Set up CI/CD pipelines (e.g., GitHub Actions, GitLab CI) for linting, testing, and building Docker images. Example: run lint, test, and docker build jobs on every push.

## 15. Static Assets
Add a favicon and logo in the `public/` directory for branding and a professional look.

## 16. Hosts File
Add `127.0.0.1 admin.console.local` to your `/etc/hosts` (or Windows equivalent) to access the app via the custom domain in your browser.

## 17. Kubernetes/Cloud Deployment
If deploying to Kubernetes, use a `Dockerfile.kube` and create a deployment YAML. Example:
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: admin
  template:
    metadata:
      labels:
        app: admin
    spec:
      containers:
      - name: admin
        image: acqwired-admin:latest
        ports:
        - containerPort: 3006
        env:
        - name: DATA_API_URL
          value: "http://dataapi:3002"
```
Reference your cloud provider's documentation for ingress and SSL setup.

## 18. Troubleshooting
- **Port conflicts:** Ensure port 3006 is free.
- **Nginx misconfiguration:** Double-check your nginx.conf and domain mapping.
- **Docker network issues:** Use `docker network inspect` to debug.
- **Backend not reachable:** Ensure `dataapi` is running and accessible from the container.

## 19. API/Backend Schema
If the organization API expects a specific schema, refer to the backend/dataapi documentation or codebase. Update the schema as needed to match your frontend requirements.

## 20. Frontend Routing
- `/organizations`: Main admin page listing all organizations
- `/login`: Authentication page
- `/organizations/create`: Page or modal for creating a new organization

## 21. Security
- Use HTTPS in production (with valid SSL certificates)
- Set secure, HTTP-only cookies for authentication
- Regularly update dependencies and audit for vulnerabilities
- Never commit secrets or sensitive data to version control

---
