# Organization Creation Flow

This document outlines the complete process for creating a new organization in the Acqwired platform, including database schema relationships.

## Database Schema Overview

The organization creation process involves multiple related tables in the database:

### Core Organization Tables
1. **Organization** (`Organization` model)
   ```prisma
   model Organization {
     id                  String                  @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     name                String                  @unique @db.VarChar(100)
     website             String                  @db.VarChar(255)
     createdAt           DateTime                @default(now()) @db.Timestamptz(6)
     updatedAt           DateTime?               @db.Timestamptz(6)
     deletedAt           DateTime?               @db.Timestamptz(6)
     aiSummary           String                  @default("")
     membership          OrgMembership?
     addresses           OrgAddress[]
     orgContacts         OrgContact[]
     orgIntegrations     OrgIntegration[]
     userRoles           OrgUserRole[]
     settings            OrgSetting?
     userCalendars       UserCalendar[]
     entityFields        EntityField[]
     emailMessages       EmailMessage[]
     playChannelSettings OrgPlayChannelSetting[]
     OrgLlmModels        OrgLlmModels[]
   }
   ```

2. **OrgMembership** (`OrgMembership` model)
   ```prisma
   model OrgMembership {
     id               String         @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     maxUser          Int            @db.SmallInt
     startedAt        DateTime       @default(now())
     endsAt           DateTime?
     orgId            String         @unique @db.Uuid
     type             MembershipType
     tier             OrgTier        @default(tier_standard)
     status           OrgStatus      @default(active)
     organizationType OrgType        @default(Private_Equity)
     creditBalance    Decimal        @default(0) @db.Decimal(10, 2)
     organization     Organization   @relation(fields: [orgId], references: [id], onDelete: Cascade)
   }
   ```

3. **OrgAddress** (`OrgAddress` model)
   ```prisma
   model OrgAddress {
     id           String         @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     orgId        String?        @db.Uuid
     title        String         @db.VarChar(100)
     zipCode      Int
     purpose      ContactPurpose
     addressLine1 String         @db.VarChar(250)
     addressLine2 String?        @db.VarChar(250)
     cityId       String         @db.Uuid
     city         SystemCity     @relation(fields: [cityId], references: [id])
     organization Organization?  @relation(fields: [orgId], references: [id])
   }
   ```

4. **OrgContact** (`OrgContact` model)
   ```prisma
   model OrgContact {
     id           String         @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     orgId        String?        @db.Uuid
     label        String         @db.VarChar(100)
     value        String         @db.VarChar(100)
     type         ContactType
     purpose      ContactPurpose
     organization Organization?  @relation(fields: [orgId], references: [id])
   }
   ```

5. **OrgIntegration** (`OrgIntegration` model)
   ```prisma
   model OrgIntegration {
     id           String              @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     orgId        String?             @db.Uuid
     provider     IntegrationProvider
     name         String?             @db.VarChar(64)
     expiresAt    DateTime?           @db.Timestamptz(6)
     refreshedAt  DateTime?           @db.Timestamptz(6)
     createdById  String?             @db.Uuid
     createdAt    DateTime            @default(now()) @db.Timestamptz(6)
     updatedAt    DateTime?           @db.Timestamptz(6)
     type         IntegrationType
     active       Boolean             @default(true)
     organization Organization?       @relation(fields: [orgId], references: [id])
     createdBy    User?               @relation(fields: [createdById], references: [id])
     authToken    Json                @db.JsonB
     data         Json?               @db.JsonB
   }
   ```

6. **OrgSetting** (`OrgSetting` model)
   ```prisma
   model OrgSetting {
     id                  String       @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     orgId              String       @db.Uuid
     useSystemLlmSetting Boolean     @default(true)
     createdAt          DateTime     @default(now()) @db.Timestamptz(6)
     updatedAt          DateTime?    @db.Timestamptz(6)
     enrichmentSettings Json         @db.JsonB
     playSettings       Json         @db.JsonB
     organization       Organization @relation(fields: [orgId], references: [id])
   }
   ```

7. **OrgLlmModels** (`OrgLlmModels` model)
   ```prisma
   model OrgLlmModels {
     id           String         @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     orgId        String         @db.Uuid
     llmModelId   String         @db.Uuid
     isEnabled    Boolean        @default(true)
     isDefault    Boolean        @default(false)
     organization Organization   @relation(fields: [orgId], references: [id])
     llmModel     SystemLlmModel @relation(fields: [llmModelId], references: [id])
   }
   ```

### System Reference Tables
1. **SystemCity** (`SystemCity` model)
   ```prisma
   model SystemCity {
     id        String       @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     stateId   String       @db.Uuid
     phoneCode Int?         @db.SmallInt
     cityName  String       @db.VarChar(50)
     state     SystemState  @relation(fields: [stateId], references: [id])
     addresses OrgAddress[]
   }
   ```

2. **SystemState** (`SystemState` model)
   ```prisma
   model SystemState {
     id        String        @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     countryId String        @db.Uuid
     state     String        @db.VarChar(50)
     country   SystemCountry @relation(fields: [countryId], references: [id])
     cities    SystemCity[]
   }
   ```

3. **SystemCountry** (`SystemCountry` model)
   ```prisma
   model SystemCountry {
     id        String        @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     country   String        @db.VarChar(50)
     phoneCode Int?          @db.SmallInt
     isoCode   String        @db.VarChar(2)
     states    SystemState[]
   }
   ```

### User Related Tables
1. **User** (`User` model)
   ```prisma
   model User {
     id              String            @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     orgId           String            @db.Uuid
     firstName       String            @db.VarChar(50)
     lastName        String?           @db.VarChar(50)
     email           String            @unique @db.VarChar(100)
     role            UserRole
     active          Boolean           @default(false)
     organization    Organization      @relation(fields: [orgId], references: [id])
     userInvitations UserInvitation[]
     userCalendars   UserCalendar[]
   }
   ```

2. **UserInvitation** (`UserInvitation` model)
   ```prisma
   model UserInvitation {
     id                String       @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     userId            String       @db.Uuid
     orgId             String       @db.Uuid
     email             String       @db.VarChar(100)
     hash              String       @unique @db.VarChar(64)
     invitationAccepted Boolean     @default(false)
     user              User         @relation(fields: [userId], references: [id])
     organization      Organization @relation(fields: [orgId], references: [id])
   }
   ```

3. **UserCalendar** (`UserCalendar` model)
   ```prisma
   model UserCalendar {
     id           String       @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     orgId        String       @db.Uuid
     userId       String       @db.Uuid
     active       Boolean      @default(true)
     weeklyHours  Json         @db.JsonB
     user         User         @relation(fields: [userId], references: [id])
     organization Organization @relation(fields: [orgId], references: [id])
   }
   ```

4. **EmailMessage** (`EmailMessage` model)
   ```prisma
   model EmailMessage {
     id          String            @id @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
     userId      String            @db.Uuid
     orgId       String            @db.Uuid
     from        String            @db.VarChar(100)
     to          String            @db.VarChar(100)
     template    EmailTemplate
     status      EmailMessageStatus
     scheduledAt DateTime          @db.Timestamptz(6)
     data        Json              @db.JsonB
     subject     String            @db.VarChar(255)
     user        User              @relation(fields: [userId], references: [id])
     organization Organization     @relation(fields: [orgId], references: [id])
   }
   ```

## API Endpoint

- **Endpoint**: `POST /api/v1/backoffice/invite-organization`
- **Host**: `http://console.acqwired.local`
- **Authorization**: Requires authentication with superadmin privileges (auth.oid must match config.superadminOrgID)

## Request Payload Structure

```json
{
  "contact": {
    "email": "<EMAIL>",
    "firstName": "FirstName",
    "lastName": "LastName" // lastName is optional but recommended
  },
  "firmType": "Private_Equity",
  "firmName": "Organization Name",
  "aiSummary": "AI summary description",
  "website": "example.com",
  "membership": {
    "type": "Launch_Partner_L1",
    "maxUsers": 10
  },
  "integrations": [
    {
      "provider": "openai",
      "activeUntil": "2024-12-31T00:00:00.000Z",
      "keyName": "API Key Name",
      "key": "sk-actualAPIKey",
      "type": "llm",
      "aiCredits": 1000
    }
  ],
  "address": {
    "title": "Office Location",
    "addressLine1": "123 Main St",
    "addressLine2": "Suite 100",
    "zipCode": 12345,
    "purpose": "billing",
    "city": {
      "cityName": "City Name",
      "state": {
        "state": "State Name",
        "country": {
          "country": "Country Name",
          "isoCode": "US"
        }
      }
    }
  },
  "phone": [
    {
      "phone": "**********",
      "contactName": "Contact Name",
      "purpose": "billing"
    }
  ]
}
```

## Database Creation Process

The organization creation process follows these steps in sequence, executed within a database transaction to ensure atomicity:

### 1. Request Validation

Before creating an organization, the system validates the request payload using the `validateOrgRequest` method:

- **Contact Information**: 
  - Email and first name are required
  - Email must follow valid pattern (Patterns.Email)
  - First name must follow valid pattern (Patterns.FirstName)
  - Last name is optional but recommended

- **Firm Details**: 
  - Firm type must be a valid OrgType enum value
  - Firm name is required and must follow valid pattern (Patterns.CompanyName)
  - Website must be a valid URL format (Patterns.URL)
  - AI Summary is required

- **Address**: 
  - If provided, validates all required address components:
    - title
    - addressLine1
    - zipCode
    - city
    - city.cityName
    - city.state
    - city.state.state
    - city.state.country
    - city.state.country.isoCode
    - purpose (must be valid ContactPurpose enum)

- **Membership**: 
  - If provided, validates:
    - Membership type (must be valid MembershipType enum)
    - maxUsers (must be positive number)

- **Integrations**: 
  - If provided, validates all integration fields:
    - provider
    - activeUntil
    - keyName
    - key
    - aiCredits (must be non-negative)
    - type

- **Phone Numbers**: 
  - If provided, validates:
    - phone (required)
    - contactName (required)
    - purpose (optional, must be valid ContactPurpose enum if provided)

### 2. Database Transaction

All database operations are executed within a transaction using `db.$transaction` with `DB_TRANSACTION_SETTING` to ensure atomicity. If any step fails, all changes are rolled back. Each operation has specific error handling with detailed logging.

### 3. Organization Creation

Creates the organization record with the following fields:
```typescript
const org = await tx.organization.create({
  data: {
    name: req.firmName,
    website: req.website,
    aiSummary: req.aiSummary,
    status: OrgStatus.active,
    type: req.firmType as OrgType,
    createdAt: new Date()
  }
});
```

### 4. Organization Contacts Creation

Creates organization contact records:
```typescript
await tx.orgContact.createMany({
  data: [
    {
      orgId: org.id,
      type: ContactType.email,
      purpose: ContactPurpose.administration,
      value: req.contact.email,
      label: req.contact.firstName
    },
    ...req.phone.map(phone => ({
      orgId: org.id,
      type: ContactType.phone,
      purpose: phone.purpose ?? ContactPurpose.billing,
      value: phone.phone,
      label: phone.contactName
    }))
  ]
});
```

### 5. Organization Membership Creation

If membership details are provided:
```typescript
await tx.orgMembership.create({
  data: {
    orgId: org.id,
    type: req.membership.type as MembershipType,
    maxUser: req.membership.maxUsers,
    tier: OrgTier.tier_standard,
    status: OrgStatus.active,
    organizationType: req.firmType as OrgType,
    creditBalance: 0
  }
});
```

### 6. Organization Address Creation

If address information is provided:
```typescript
const country = await tx.systemCountry.upsert({
  where: { country: req.address.city.state.country.country },
  create: {
    country: req.address.city.state.country.country,
    isoCode: req.address.city.state.country.isoCode
  },
  update: {}
});

const state = await tx.systemState.upsert({
  where: {
    state_countryId: {
      state: req.address.city.state.state,
      countryId: country.id
    }
  },
  create: {
    state: req.address.city.state.state,
    countryId: country.id
  },
  update: {}
});

const city = await tx.systemCity.upsert({
  where: {
    cityName_stateId: {
      cityName: req.address.city.cityName,
      stateId: state.id
    }
  },
  create: {
    cityName: req.address.city.cityName,
    stateId: state.id
  },
  update: {}
});

await tx.orgAddress.create({
  data: {
    orgId: org.id,
    title: req.address.title,
    addressLine1: req.address.addressLine1,
    addressLine2: req.address.addressLine2,
    zipCode: req.address.zipCode,
    purpose: req.address.purpose as ContactPurpose,
    cityId: city.id
  }
});
```

### 7. Admin User Creation

```typescript
const user = await tx.user.create({
  data: {
    orgId: org.id,
    firstName: req.contact.firstName,
    lastName: req.contact.lastName,
    email: req.contact.email,
    role: UserRole.ADMIN,
    active: false
  }
});
```

### 7a. OrgUserRole Creation (Required)

After creating the admin user, you **must** create an OrgUserRole record to map the user to their role in the organization:

```typescript
await tx.orgUserRole.create({
  data: {
    orgId: org.id,
    userId: user.id,
    roleId: adminRoleId, // Set this to your ADMIN role's ID
    // createdAt/updatedAt will default if set in schema
  }
});
```
*Note: Obtain `adminRoleId` from your roles table or enum as appropriate.*

### 8. Organization Integrations Creation

If integration details are provided:
```typescript
await tx.orgIntegration.createMany({
  data: req.integrations.map(integration => ({
    orgId: org.id,
    provider: integration.provider as IntegrationProvider,
    name: integration.keyName,
    expiresAt: new Date(integration.activeUntil),
    type: integration.type as IntegrationType,
    active: true,
    authToken: { key: integration.key },
    data: { aiCredits: integration.aiCredits },
    createdById: user.id
  }))
});
```

### 9. User Calendar Creation

```typescript
await tx.userCalendar.create({
  data: {
    orgId: org.id,
    userId: user.id,
    active: true,
    weeklyHours: DefaultTime
  }
});
```

### 10. User Invitation Creation

```typescript
const hash = genRandomHex(32);
await tx.userInvitation.create({
  data: {
    userId: user.id,
    orgId: org.id,
    email: user.email,
    hash,
    invitationAccepted: false
  }
});
```

### 11. Email Message Creation

```typescript
await tx.emailMessage.create({
  data: {
    userId: user.id,
    orgId: org.id,
    from: config.systemEmailSender,
    to: user.email,
    template: EmailTemplate.INVITE_ORGANIZATION,
    status: EmailMessageStatus.raw,
    scheduledAt: new Date(),
    data: {
      linkDomain: config.email.data.linkDomain,
      assetDomain: config.email.data.assetDomain,
      source: config.email.data.source,
      consoleDomain: config.email.data.consoleDomain,
      firstName: req.contact.firstName,
      invitationLink: `${config.email.data.invitationLink}?hash=${hash}`,
      companyName: req.firmName
    },
    subject: config.email.subjects.invitation
  }
});
```

### 12. Organization Settings Creation

```typescript
await tx.orgSetting.create({
  data: {
    orgId: org.id,
    useSystemLlmSetting: true,
    enrichmentSettings: [],
    playSettings: []
  }
});
```

### 13. Organization LLM Models Creation

```typescript
await tx.orgLlmModels.createMany({
  data: defaultLlmModels.map(model => ({
    orgId: org.id,
    llmModelId: model.id,
    isEnabled: true,
    isDefault: model.isDefault
  }))
});
```

### 14. OrgPlayChannelSetting Creation (Optional)

If your business logic requires default play channel settings for each new organization, add:

```typescript
await tx.orgPlayChannelSetting.create({
  data: {
    orgId: org.id,
    playChannelId: defaultPlayChannelId, // Set to your default
    llmModelId: defaultLlmModelId,       // Set to your default
    isDefault: true
  }
});
```
*Note: Only add this if you want default play channel settings at org creation. Otherwise, configure later as needed.*

## Response Structure

```json
{
  "isInvited": true,
  "orgId": "organization-uuid",
  "userId": "user-uuid"
}
```

## Error Handling

- If validation fails, returns validation errors with detailed messages
- If any step in the transaction fails, the entire transaction is rolled back
- All errors are logged with appropriate context using the logger
- Each step in the transaction has specific error handling with custom error messages
- API responses include appropriate HTTP status codes (500 for server errors, 400 for validation errors)
- The BackOffice constructor validates that the auth has superadmin privileges before allowing any operations

## Post-Creation Process

1. The invitation email is sent to the contact's email address (processed by email service)
2. Upon accepting the invitation by clicking the link with the hash:
   - The user sets their password
   - The user account is activated (active flag set to true)
   - The invitationAccepted flag is set to true
   - The user can log in to the new organization

## Implementation Notes

1. The BackOffice service requires superadmin privileges to execute this operation
2. The transaction is configured with DB_TRANSACTION_SETTING for proper isolation
3. Organization creation is fully atomic - if any step fails, all changes are rolled back
4. The genRandomHex method is used to generate a 32-character random hash for the invitation link
5. Default plays creation is included but the ticket mentioned that "default Play creation is not part of the scope of this ticket" (ACQ-952)
6. All database operations are wrapped in try-catch blocks with specific error handling
7. Each step includes detailed logging for debugging purposes
8. The process follows a strict order of operations to maintain data consistency
9. All IDs are generated using the `gen_uuid_v7_fn()` database function
10. Foreign key relationships are maintained throughout the creation process
11. The schema uses proper indexing and constraints for data integrity
12. All timestamps are stored in UTC
13. The schema supports soft deletion where applicable
14. All sensitive data (like API keys) is properly stored and encrypted
