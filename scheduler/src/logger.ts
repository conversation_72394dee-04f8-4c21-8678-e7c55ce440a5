import pinoHttp from 'pino-http';
import pino from 'pino';
import config from './config';

/**
 * In case you need pretty print log files don't add any dependency to logger
 * use jq, i.e:
 * tail -f file | jq -nR -c 'inputs|fromjson?' |jq 'with_entries( select(.key|contains("msg") ) )'
 * tail -f file | jq -nR -c 'inputs|fromjson?' |jq 'with_entries( select(.key|contains("req") | not ) )'
 * tail -f file | jq -nR -c 'inputs|fromjson?' |jq .
 */

// Mapping Pino levels to GCP Logging severity levels
const levelToSeverity = {
  trace: 'DEBUG',
  debug: 'DEBUG',
  info: 'INFO',
  warn: 'WARNING',
  error: 'ERROR',
  fatal: 'CRITICAL'
};

const streams = pino.multistream([
  {
    level: 'trace',
    stream: process.stdout
  }
]);

const errorSerializer = (err: unknown) => {
  if (err instanceof Error) {
    return {
      message: err.message,
      stack: err.stack ? err.stack.split('\n') : undefined,
      data: { ...err }
    };
  }
  return err;
};

const logger = pino({
  level: config.logLevel,
  timestamp: () => `,"time":"${(new Date()).toLocaleTimeString()}"`,
  formatters: {
    level(label) {
      return { severity: levelToSeverity[label] || 'DEFAULT' };
    }
  },
  serializers: { error: errorSerializer }
}, streams);

// pino-http v8+ syntax: pass config and stream separately
const httpLogger = pinoHttp({
  autoLogging: {
    ignore: (req) => {
      return (req.url === '/healthcheck' && config.env !== 'production') ||
        (req.url === "/" && req.headers['user-agent'] === 'kube-probe/1.27');
    }
  },
  redact: {
    paths: ['req.headers.cookie'],
    remove: true
  },
  customSuccessMessage: function (req, res) {
    return `${res.statusCode} ${req.method} ${req.headers.host} ${req.url}`;
  },
  customErrorMessage: function (_req, res, _err) {
    return 'request errored with status code: ' + res.statusCode;
  },
  customReceivedMessage: function (req, _res) {
    return 'request received: ' + req.method + ' ' + req.url;
  }
}, streams);

export { logger, httpLogger };
