name: 'Deploy Services'
on:
  workflow_call:

permissions: write-all

jobs:
  deploy:
    if: ${{ github.event_name != 'pull_request' }}

    name: Deploy - ${{ matrix.service }}
    runs-on: ubuntu-latest
    environment: dev
    strategy:
      max-parallel: 1
      matrix:
        service: ['dataapi', 'frontend', 'worker', 'scheduler', 'console', 'admin']
    env:
      REPO: ${{ vars.REPO }}
      PROJECT_ID: ${{ vars.PROJECT_ID }}
      CLUSTER: ${{ vars.CLUSTER }}
      REGION: ${{ vars.REGION }}
      ZONE: ${{ vars.ZONE }}
      BASTION: ${{ vars.BASTION }}
      ENV_NAME: 'dev'
      SERVICE_NAME: ${{ matrix.service }}

    steps:
      - name: 'Checkout'
        uses: actions/checkout@v4

      - name: Google Authenticate
        id: 'google_auth'
        uses: 'google-github-actions/auth@v2'
        with:
          token_format: access_token
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Set up Cloud SDK
        id: 'google_cloud_sdk'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          install_components: 'gke-gcloud-auth-plugin'
          project_id: '${{ env.PROJECT_ID }}'

        # Setup GITHUB ENV
      - name: Set ENV_VAR for gke-gcloud-auth-plugin
        id: 'google_set_env_var'
        run: |-
          echo "USE_GKE_GCLOUD_AUTH_PLUGIN=True" >> $GITHUB_ENV
          gke-gcloud-auth-plugin --version

        # Generate random port number to use for tunneling
      - name: generate random export
        id: 'bastion_iap_port'
        run: |
          echo "TUNNEL_PORT=$((999 + RANDOM % 9999))" >> $GITHUB_ENV

        # Get the GKE credentials and prepare deployment manifest
      - name: Get the GKE credentials
        id: 'bastion_iap_tunnel'
        run: |-
          ## Install numpy
          $(gcloud info --format="value(basic.python_location)") -m pip install numpy

          # Setup tunneling
          gcloud compute ssh ${GKE_BASTION} --project ${GCP_PROJECT} --zone ${GKE_ZONE} --tunnel-through-iap --ssh-key-expire-after 30m -- -L 8888:127.0.0.1:8888  -N -q -f

          # Fetch GKE Cluster credentials
          gcloud container clusters get-credentials ${GKE_CLUSTER} --zone ${GKE_ZONE} --internal-ip
        env:
          GKE_REGION: ${{ env.REGION }}
          GKE_ZONE: ${{ env.ZONE }}
          GKE_CLUSTER: ${{ env.CLUSTER }}
          GKE_BASTION: ${{ env.BASTION }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # Execute the manifests
      - name: Deploy changes
        id: 'kubectl'
        env:
          HTTPS_PROXY: localhost:8888
        run: |-
          kubectl get pods
          kubectl apply -f service.yaml
          kubectl apply -f deployment.yaml
          COMMIT_SHA=$(git rev-parse --short=9 HEAD)
          if [ ${{ matrix.service }} = "console" ]; then
              kubectl apply -f managedcert.yaml
              kubectl apply -f frontendconfig.yaml
              kubectl apply -f serviceaccount.yaml
              kubectl annotate serviceaccount acqwired-ksa iam.gke.io/gcp-service-account=acqwired-gsa@acqwired-${{ env.ENV_NAME }}.iam.gserviceaccount.com
              # kubectl apply -f ingress.yaml
              sleep 300
          fi
          if [ ${{ matrix.service }} = "dataapi" ]; then
              kubectl apply -f backendconfig.yaml
              sleep 300
          fi
          kubectl set image deployment/acqwired-${{ env.SERVICE_NAME }} acqwired-${{ env.SERVICE_NAME }}=${{ env.REPO }}/${{ env.PROJECT_ID}}/${{ env.SERVICE_NAME }}:${COMMIT_SHA}
        working-directory: kustomize/base/${{ matrix.service }}/${{ vars.ENV_NAME }}
