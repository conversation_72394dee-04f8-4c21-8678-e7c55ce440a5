name: 'Build'
on:
  push:
    branches:
      - dev

  #pull_request:
  #  branches:
  #    - dev

  workflow_dispatch:

env:
  REPO: ${{ vars.REPO }}
  PROJECT_ID: ${{ vars.PROJECT_ID }}
  CLUSTER: ${{ vars.CLUSTER }}
  REGION: ${{ vars.REGION }}
  ZONE: ${{ vars.ZONE }}
  BASTION: ${{ vars.BASTION }}
  ENV_NAME: 'dev'

permissions: write-all

jobs:
  console-ci:
    defaults:
      run:
        working-directory: ./console

    name: Console
    runs-on: ubuntu-latest
    environment: 'dev'

    steps:
      # SETUP
      - name: 'Checkout'
        uses: actions/checkout@v4

      - name: 'Setup Node'
        uses: actions/setup-node@v4
        with:
          node-version: 20

        #BUILD
      - name: 'Set environment vars'
        run: |
          make set-env env=${{ env.ENV_NAME }}

      - name: 'Inject secrets'
        run: |
          make add-secret env=${{ env.ENV_NAME }} secret=SALESFORCE_CLIENT_ID=${{ secrets.SALESFORCE_CLIENT_ID }}
          make add-secret env=${{ env.ENV_NAME }} secret=SALESFORCE_CLIENT_SECRET=${{ secrets.SALESFORCE_CLIENT_SECRET }}
          make add-secret env=${{ env.ENV_NAME }} secret=OFFICE_365_CLIENT_ID=${{ secrets.OFFICE_365_CLIENT_ID }}
          make add-secret env=${{ env.ENV_NAME }} secret=OFFICE_365_CLIENT_SECRET=${{ secrets.OFFICE_365_CLIENT_SECRET }}
          make add-secret env=${{ env.ENV_NAME }} secret=DATABASE_URL=${{ secrets.DATABASE_URL }}
          make add-secret env=${{ env.ENV_NAME }} secret=NEXT_PUBLIC_FIREBASE_API_KEY=${{ vars.NEXT_PUBLIC_FIREBASE_API_KEY }}
          make add-secret env=${{ env.ENV_NAME }} secret=NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=${{ vars.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN }}
          make add-secret env=${{ env.ENV_NAME }} secret=NEXT_PUBLIC_FIREBASE_DATABASE_URL=${{ vars.NEXT_PUBLIC_FIREBASE_DATABASE_URL }}
          make add-secret env=${{ env.ENV_NAME }} secret=NEXT_PUBLIC_FIREBASE_PROJECT_ID=${{ vars.NEXT_PUBLIC_FIREBASE_PROJECT_ID }}
          make add-secret env=${{ env.ENV_NAME }} secret=NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=${{ vars.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET }}
          make add-secret env=${{ env.ENV_NAME }} secret=NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${{ vars.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID }}
          make add-secret env=${{ env.ENV_NAME }} secret=NEXT_PUBLIC_FIREBASE_APP_ID=${{ vars.NEXT_PUBLIC_FIREBASE_APP_ID }}
          make add-secret env=${{ env.ENV_NAME }} secret=NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=${{ secrets.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY }}

      - name: 'Install dependencies'
        run: make install-deps env=${{ env.ENV_NAME }}

      - name: 'Run tests'
        run: make unit-test env=${{ env.ENV_NAME }}

      - name: 'Build Container'
        run: make build env=${{ env.ENV_NAME }}

      - name: Google Authenticate
        if: ${{ github.event_name != 'pull_request' }}
        id: 'google_auth'
        uses: 'google-github-actions/auth@v2'
        with:
          token_format: access_token
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Set up Cloud SDK
        if: ${{ github.event_name != 'pull_request' }}
        id: 'google_cloud_sdk'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          install_components: 'gke-gcloud-auth-plugin'
          project_id: '${{ env.PROJECT_ID }}'

      # Setup GITHUB ENV
      - name: Set ENV_VAR for gke-gcloud-auth-plugin
        if: ${{ github.event_name != 'pull_request' }}
        id: 'google_set_env_var'
        run: |-
          echo "USE_GKE_GCLOUD_AUTH_PLUGIN=True" >> $GITHUB_ENV
          gke-gcloud-auth-plugin --version

      - name: 'Docker login'
        if: ${{ github.event_name != 'pull_request' }}
        uses: 'docker/login-action@v3'
        with:
          registry: ${{ env.REPO }}
          username: 'oauth2accesstoken'
          password: '${{ steps.google_auth.outputs.access_token }}'

      - name: 'Test gcloud'
        if: ${{ github.event_name != 'pull_request' }}
        run: |
          gcloud info

        # PUSH IMAGE
      - name: 'Push artifacts to repository'
        if: ${{ github.event_name != 'pull_request' }}
        run: |
          make push env=${{ env.ENV_NAME }}

  dataapi-ci:
    defaults:
      run:
        working-directory: ./dataapi

    name: Dataapi
    runs-on: ubuntu-latest
    environment: 'dev'

    steps:
      # SETUP
      - name: 'Checkout'
        uses: actions/checkout@v4

      - name: 'Setup Node'
        uses: actions/setup-node@v4
        with:
          node-version: 20

        #BUILD
      - name: 'Set environment vars'
        run: make set-env env=${{ env.ENV_NAME }}

      - name: 'Inject secrets'
        run: |
          make add-secret env=${{ env.ENV_NAME }} secret=SALESFORCE_CLIENT_ID=${{ secrets.SALESFORCE_CLIENT_ID }}
          make add-secret env=${{ env.ENV_NAME }} secret=SALESFORCE_CLIENT_SECRET=${{ secrets.SALESFORCE_CLIENT_SECRET }}
          make add-secret env=${{ env.ENV_NAME }} secret=OFFICE_365_CLIENT_ID=${{ secrets.OFFICE_365_CLIENT_ID }}
          make add-secret env=${{ env.ENV_NAME }} secret=OFFICE_365_CLIENT_SECRET=${{ secrets.OFFICE_365_CLIENT_SECRET }}
          make add-secret env=${{ env.ENV_NAME }} secret=DATABASE_URL=${{ secrets.DATABASE_URL }}
          make add-secret env=${{ env.ENV_NAME }} secret=FIREBASE_DATABASE_URL=${{ vars.FIREBASE_DATABASE_URL }}
          make add-secret env=${{ env.ENV_NAME }} secret=GOOGLE_MAPS_API_KEY=${{ secrets.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY }}
          make add-secret env=${{ env.ENV_NAME }} secret=SOURCE_SCRUB_CLIENT_KEY=${{ secrets.SOURCE_SCRUB_CLIENT_KEY }}
          make add-secret env=${{ env.ENV_NAME }} secret=SOURCE_SCRUB_USERNAME=${{ secrets.SOURCE_SCRUB_USERNAME }}
          make add-secret env=${{ env.ENV_NAME }} secret=SOURCE_SCRUB_PASSWORD=${{ secrets.SOURCE_SCRUB_PASSWORD }}
          make add-secret env=${{ env.ENV_NAME }} secret=EXA_API_KEY=${{ secrets.EXA_API_KEY }}

      - name: 'Install dependencies'
        run: make install-deps env=${{ env.ENV_NAME }}

      # Tests are not supported yet
      # - name: 'Run tests'
      #   run: make unit-test env=${{ env.ENV_NAME }}

      - name: 'Build Container'
        run: make build env=${{ env.ENV_NAME }}

      - name: Google Authenticate
        id: 'google_auth'
        uses: 'google-github-actions/auth@v2'
        with:
          token_format: access_token
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Set up Cloud SDK
        id: 'google_cloud_sdk'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          install_components: 'gke-gcloud-auth-plugin'
          project_id: '${{ env.PROJECT_ID }}'

      # Setup GITHUB ENV
      - name: Set ENV_VAR for gke-gcloud-auth-plugin
        id: 'google_set_env_var'
        run: |-
          echo "USE_GKE_GCLOUD_AUTH_PLUGIN=True" >> $GITHUB_ENV
          gke-gcloud-auth-plugin --version

      - name: 'Docker login'
        uses: 'docker/login-action@v3'
        with:
          registry: ${{ env.REPO }}
          username: 'oauth2accesstoken'
          password: '${{ steps.google_auth.outputs.access_token }}'

      - name: 'Test gcloud'
        run: |
          gcloud info

        # PUSH IMAGE
      - name: 'Push artifacts to repository'
        run: |
          make push env=${{ env.ENV_NAME }}

  frontend-ci:
    defaults:
      run:
        working-directory: ./frontend

    name: Frontend
    runs-on: ubuntu-latest
    environment: 'dev'

    steps:
      # SETUP
      - name: 'Checkout'
        uses: actions/checkout@v4

      - name: 'Setup Node'
        uses: actions/setup-node@v4
        with:
          node-version: 20

        #BUILD
      - name: 'Set environment vars'
        run: make set-env env=${{ env.ENV_NAME }}

      - name: 'Inject secrets'
        run: |
          make add-secret env=${{ env.ENV_NAME }} secret=SENDGRID_API_KEY=${{ secrets.SENDGRID_API_KEY }}
          make add-secret env=${{ env.ENV_NAME }} secret=AWS_S3_SECRET_ACCESS_KEY=${{ secrets.AWS_S3_SECRET_ACCESS_KEY }}
          make add-secret env=${{ env.ENV_NAME }} secret=DATABASE_URL=${{ secrets.DATABASE_URL }}
          make add-secret env=${{ env.ENV_NAME }} secret=WEB_DB_PASS=${{ secrets.DB_PASS }}

      - name: 'Install dependencies'
        run: make install-deps env=${{ env.ENV_NAME }}

      - name: 'Run tests'
        run: make unit-test env=${{ env.ENV_NAME }}

      - name: 'Build Container'
        run: make build env=${{ env.ENV_NAME }}

      - name: Google Authenticate
        id: 'google_auth'
        uses: 'google-github-actions/auth@v2'
        with:
          token_format: access_token
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Set up Cloud SDK
        id: 'google_cloud_sdk'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          install_components: 'gke-gcloud-auth-plugin'
          project_id: '${{ env.PROJECT_ID }}'

      # Setup GITHUB ENV
      - name: Set ENV_VAR for gke-gcloud-auth-plugin
        id: 'google_set_env_var'
        run: |-
          echo "USE_GKE_GCLOUD_AUTH_PLUGIN=True" >> $GITHUB_ENV
          gke-gcloud-auth-plugin --version

      - name: 'Docker login'
        uses: 'docker/login-action@v3'
        with:
          registry: ${{ env.REPO }}
          username: 'oauth2accesstoken'
          password: '${{ steps.google_auth.outputs.access_token }}'

      - name: 'Test gcloud'
        run: |
          gcloud info

        # PUSH IMAGE
      - name: 'Push artifacts to repository'
        run: |
          make push env=${{ env.ENV_NAME }}

  worker-ci:
    defaults:
      run:
        working-directory: ./worker

    name: Worker
    runs-on: ubuntu-latest
    environment: 'dev'

    steps:
      # SETUP
      - name: 'Checkout'
        uses: actions/checkout@v4

      - name: 'Setup Node'
        uses: actions/setup-node@v4
        with:
          node-version: 20

        #BUILD
      - name: 'Set environment vars'
        run: make set-env env=${{ env.ENV_NAME }}

      - name: 'Inject secrets'
        run: |
          make add-secret env=${{ env.ENV_NAME }} secret=SENDGRID_API_KEY=${{ secrets.SENDGRID_API_KEY }}
          make add-secret env=${{ env.ENV_NAME }} secret=DATABASE_URL=${{ secrets.DATABASE_URL }}
          make add-secret env=${{ env.ENV_NAME }} secret=FIREBASE_DATABASE_URL=${{ vars.FIREBASE_DATABASE_URL }}
          make add-secret env=${{ env.ENV_NAME }} secret=GOOGLE_CUSTOM_SEARCH_API_KEY=${{ secrets.GOOGLE_CUSTOM_SEARCH_API_KEY }}
          make add-secret env=${{ env.ENV_NAME }} secret=GOOGLE_CUSTOM_SEARCH_API_CONTEXT=${{ secrets.GOOGLE_CUSTOM_SEARCH_API_CONTEXT }}
          make add-secret env=${{ env.ENV_NAME }} secret=PROXYCURL_API_KEY=${{ secrets.PROXYCURL_API_KEY }}
          make add-secret env=${{ env.ENV_NAME }} secret=MIXRANK_API_KEY=${{ secrets.MIXRANK_API_KEY }}

      - name: 'Install dependencies'
        run: make install-deps env=${{ env.ENV_NAME }}

      # Tests are not supported yet
      # - name: 'Run tests'
      #   run: make unit-test env=${{ env.ENV_NAME }}

      - name: 'Build Container'
        run: make build env=${{ env.ENV_NAME }}

      - name: Google Authenticate
        id: 'google_auth'
        uses: 'google-github-actions/auth@v2'
        with:
          token_format: access_token
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Set up Cloud SDK
        id: 'google_cloud_sdk'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          install_components: 'gke-gcloud-auth-plugin'
          project_id: '${{ env.PROJECT_ID }}'

      # Setup GITHUB ENV
      - name: Set ENV_VAR for gke-gcloud-auth-plugin
        id: 'google_set_env_var'
        run: |-
          echo "USE_GKE_GCLOUD_AUTH_PLUGIN=True" >> $GITHUB_ENV
          gke-gcloud-auth-plugin --version

      - name: 'Docker login'
        uses: 'docker/login-action@v3'
        with:
          registry: ${{ env.REPO }}
          username: 'oauth2accesstoken'
          password: '${{ steps.google_auth.outputs.access_token }}'

      - name: 'Test gcloud'
        run: |
          gcloud info

        # PUSH IMAGE
      - name: 'Push artifacts to repository'
        run: |
          make push env=${{ env.ENV_NAME }}

  scheduler-ci:
    defaults:
      run:
        working-directory: ./scheduler

    name: Scheduler
    runs-on: ubuntu-latest
    environment: 'dev'

    steps:
      # SETUP
      - name: 'Checkout'
        uses: actions/checkout@v4

      - name: 'Setup Node'
        uses: actions/setup-node@v4
        with:
          node-version: 20

        #BUILD
      - name: 'Set environment vars'
        run: make set-env env=${{ env.ENV_NAME }}

      - name: 'Inject secrets'
        run: |
          make add-secret env=${{ env.ENV_NAME }} secret=DATABASE_URL=${{ secrets.DATABASE_URL}}

      - name: 'Install dependencies'
        run: make install-deps env=${{ env.ENV_NAME }}

      # Tests are not supported yet
      # - name: 'Run tests'
      #   run: make unit-test env=${{ env.ENV_NAME }}

      - name: 'Build Container'
        run: make build env=${{ env.ENV_NAME }}

      - name: Google Authenticate
        id: 'google_auth'
        uses: 'google-github-actions/auth@v2'
        with:
          token_format: access_token
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Set up Cloud SDK
        id: 'google_cloud_sdk'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          install_components: 'gke-gcloud-auth-plugin'
          project_id: '${{ env.PROJECT_ID }}'

      # Setup GITHUB ENV
      - name: Set ENV_VAR for gke-gcloud-auth-plugin
        id: 'google_set_env_var'
        run: |-
          echo "USE_GKE_GCLOUD_AUTH_PLUGIN=True" >> $GITHUB_ENV
          gke-gcloud-auth-plugin --version

      - name: 'Docker login'
        uses: 'docker/login-action@v3'
        with:
          registry: ${{ env.REPO }}
          username: 'oauth2accesstoken'
          password: '${{ steps.google_auth.outputs.access_token }}'

      - name: 'Test gcloud'
        run: |
          gcloud info

        # PUSH IMAGE
      - name: 'Push artifacts to repository'
        run: |
          make push env=${{ env.ENV_NAME }}

  admin-ci:
    defaults:
      run:
        working-directory: ./admin

    name: Admin
    runs-on: ubuntu-latest
    environment: 'dev'

    steps:
      # SETUP
      - name: 'Checkout'
        uses: actions/checkout@v4

      - name: 'Setup Node'
        uses: actions/setup-node@v4
        with:
          node-version: 20

        #BUILD
      - name: 'Set environment vars'
        run: make set-env env=${{ env.ENV_NAME }}

      - name: 'Inject secrets'
        run: |
          make add-secret env=${{ env.ENV_NAME }} secret=DATABASE_URL=${{ secrets.DATABASE_URL }}

      - name: 'Install dependencies'
        run: make install-deps env=${{ env.ENV_NAME }}

      - name: 'Run tests'
        run: make unit-test env=${{ env.ENV_NAME }}

      - name: 'Build Container'
        run: make build env=${{ env.ENV_NAME }}

      - name: Google Authenticate
        id: 'google_auth'
        uses: 'google-github-actions/auth@v2'
        with:
          token_format: access_token
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Set up Cloud SDK
        id: 'google_cloud_sdk'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          install_components: 'gke-gcloud-auth-plugin'
          project_id: '${{ env.PROJECT_ID }}'

      # Setup GITHUB ENV
      - name: Set ENV_VAR for gke-gcloud-auth-plugin
        id: 'google_set_env_var'
        run: |-
          echo "USE_GKE_GCLOUD_AUTH_PLUGIN=True" >> $GITHUB_ENV
          gke-gcloud-auth-plugin --version

      - name: 'Docker login'
        uses: 'docker/login-action@v3'
        with:
          registry: ${{ env.REPO }}
          username: 'oauth2accesstoken'
          password: '${{ steps.google_auth.outputs.access_token }}'

      - name: 'Test gcloud'
        run: |
          gcloud info

        # PUSH IMAGE
      - name: 'Push artifacts to repository'
        run: |
          make push env=${{ env.ENV_NAME }}

  deploy:
    name: Deploy Services
    needs:
      - console-ci
      - dataapi-ci
      - frontend-ci
      - worker-ci
      - scheduler-ci
      - admin-ci
    secrets: inherit
    uses: './.github/workflows/deploy-dev.yml'
