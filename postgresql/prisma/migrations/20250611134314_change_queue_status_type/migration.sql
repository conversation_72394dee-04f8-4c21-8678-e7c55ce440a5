/*
  Warnings:

  - Changed the type of `status` on the `queue_tasks` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/

-- AlterTable
ALTER TABLE "queue_tasks" ADD COLUMN "new_status" "execution_status";

UPDATE "queue_tasks"
SET "new_status" =
      CASE "status"
        WHEN 'scheduled'::queue_status THEN 'scheduled'::execution_status
        WHEN 'pending'::queue_status THEN 'pending'::execution_status
        WHEN 'inqueue'::queue_status THEN 'inqueue'::execution_status
        WHEN 'received'::queue_status THEN 'ready'::execution_status
        WHEN 'success'::queue_status THEN 'completed'::execution_status
        WHEN 'error'::queue_status THEN 'error'::execution_status
        ELSE NULL
        END;

ALTER TABLE "queue_tasks" DROP COLUMN "status";
ALTER TABLE "queue_tasks" RENAME COLUMN "new_status" TO "status";

-- DropEnum
DROP TYPE "queue_status";
