DO $$
DECLARE
  v_intention_id UUID;
BEGIN
  -- Get intention_id where name matches
  SELECT id INTO v_intention_id
  FROM public.intention
  WHERE name = 'Similar Companies';

  -- If found, insert or update the intention_source
  IF v_intention_id IS NOT NULL THEN
    INSERT INTO public.intention_source(
      id, intention_id, source_type, name, description, order_by, created_at
    ) VALUES (
      'c1c28ff9-05c7-471c-959d-adcff87c2b4e',
      v_intention_id,
      'exa',
      'Exa - Similar Companies',
      'Find similar companies with Exa Search',
      3,
      CURRENT_TIMESTAMP
    )
    ON CONFLICT (id) DO UPDATE
      SET intention_id = EXCLUDED.intention_id,
          source_type = EXCLUDED.source_type,
          name = EXCLUDED.name,
          description = EXCLUDED.description,
          order_by = EXCLUDED.order_by,
          created_at = EXCLUDED.created_at;
  END IF;
END $$;
