generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "linux-musl-arm64-openssl-1.1.x", "linux-musl-arm64-openssl-3.0.x", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model SystemCity {
  id        String       @id(map: "_system_city_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  stateId   String       @map("state_id") @db.Uuid
  phoneCode Int?         @map("phone_code") @db.SmallInt
  cityName  String       @map("city_name") @db.VarChar(50)
  state     SystemState  @relation(fields: [stateId], references: [id], map: "_system_state_fk")
  addresses OrgAddress[]

  @@map("_system_city")
}

model SystemConfig {
  id    String @id(map: "_system_config_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  key   String @unique(map: "config_key_unq") @db.VarChar(20)
  value String @map("value")

  @@map("_system_config")
}

model SystemCountry {
  id        String        @id(map: "_system_countrie_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  country   String        @unique(map: "country_name_unq") @db.VarChar(50)
  phoneCode Int?          @map("phone_code") @db.SmallInt
  isoCode   String        @map("iso_code") @db.Char(3)
  state     SystemState[]

  @@map("_system_country")
}

model SystemState {
  id        String        @id(map: "_system_state_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  countryId String        @map("country_id") @db.Uuid
  state     String        @map("state") @db.VarChar(50)
  code      String?       @map("code") @db.Char(3)
  city      SystemCity[]
  country   SystemCountry @relation(fields: [countryId], references: [id], map: "_system_county_fk")

  @@map("_system_state")
}

model SystemLlmModel {
  id                    String                  @id(map: "_system_llm_engine_list_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  name                  String                  @map("name") @db.VarChar(32)
  provider              LlmProvider             @map("provider")
  model                 String                  @map("model") @db.VarChar(32)
  isEnabled             Boolean                 @default(true) @map("is_enabled") @db.Boolean
  isDefault             Boolean                 @default(false) @map("is_default") @db.Boolean
  orgPlayChannelSetting OrgPlayChannelSetting[]
  systemPlayChannel     SystemPlayChannel[]
  enrichments           Enrichment[]
  plays                 Play[]
  OrgLlmModels          OrgLlmModels[]          @relation(map: "system_llm_model_fk")
  EnrichmentConfig      EnrichmentConfig[]

  @@map("_system_llm_model")
}

model SystemPlayChannel {
  id                    String                  @id(map: "_system_play_channel_list_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  name                  String                  @map("name") @db.VarChar(32)
  channel               String                  @map("channel")
  defaultLlmModelId     String                  @map("default_llm_model_id") @db.Uuid
  defaultLlmModel       SystemLlmModel          @relation(fields: [defaultLlmModelId], references: [id], map: "system_llm_model_fk")
  orgPlayChannelSetting OrgPlayChannelSetting[]

  @@map("_system_play_channel")
}

model Lead {
  id                    String    @id(map: "lead_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  email                 String    @map("email")
  lastName              String    @map("last_name")
  firstName             String    @map("first_name")
  companyName           String    @map("company_name")
  mailingListSubscribed Boolean   @default(false) @map("mailing_list_subscribed")
  createdAt             DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt             DateTime? @map("updated_at") @db.Timestamptz(6)

  @@map("lead")
}

model JobApplication {
  id          String    @id(map: "job_application_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  email       String    @map("email")
  phone       String?   @map("phone")
  lastName    String    @map("last_name")
  firstName   String    @map("first_name")
  coverLetter String?   @map("cover_letter")
  fileName    String?   @map("file_name")
  fileType    String?   @map("file_type")
  keepMyData  Boolean   @default(false) @map("keep_my_data")
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime? @map("updated_at") @db.Timestamptz(6)

  @@map("job_application")
}

model Organization {
  id                  String                  @id(map: "organization_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  name                String                  @unique(map: "org_name_unq") @db.VarChar(100)
  website             String                  @map("website") @db.VarChar(255)
  createdAt           DateTime                @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt           DateTime?               @map("updated_at") @db.Timestamptz(6)
  deletedAt           DateTime?               @map("deleted_at") @db.Timestamptz(6)
  aiSummary           String                  @default("") @map("summary")
  companies           Company[]
  contacts            Contact[]
  channelEmails       ChannelEmail[]
  enrichments         Enrichment[]
  userInvitations     UserInvitation[]
  membership          OrgMembership?
  addresses           OrgAddress[]
  orgContacts         OrgContact[]
  orgIntegrations     OrgIntegration[]
  userRoles           OrgUserRole[]
  plays               Play[]
  crmFilters          CrmFilter[]
  crmMappings         CrmMapping[]
  crmLists            CrmList[]
  userIntegrations    UserIntegration[]
  settings            OrgSetting?
  userSettings        UserSetting[]
  userCalendars       UserCalendar[]
  entityFields        EntityField[]
  userCrmMappings     UserCrmMapping[]
  templates           Template[]
  executions          Execution[]
  emailMessages       EmailMessage[]
  playChannelSettings OrgPlayChannelSetting[]
  listCompany         ListCompany[]
  activities          Activity[]
  companyIdeas        ListCompanyIdea[]
  OrgLlmModels        OrgLlmModels[]          @relation(map: "organization_fk")
  UserTableLayout     UserTableLayout[]       @relation(map: "organization_fk")
  creditPurchases     CreditPurchase[]
  creditUsages        CreditUsage[]
  creditTransactions  CreditTransactionLog[]

  @@map("organization")
}

model OrgMembership {
  id               String         @id(map: "membership_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  maxUser          Int            @map("max_user") @db.SmallInt
  startedAt        DateTime       @default(now()) @map("started_at")
  endsAt           DateTime?      @map("ends_at")
  orgId            String         @unique @map("org_id") @db.Uuid
  type             MembershipType @map("type")
  tier             OrgTier        @default(tier_standard) @map("tier")
  status           OrgStatus      @default(active) @map("status")
  organizationType OrgType        @default(Private_Equity) @map("organization_type")
  creditBalance    Decimal        @default(0) @map("credit_balance") @db.Decimal(10, 2)
  organization     Organization   @relation(fields: [orgId], references: [id], onDelete: Cascade, map: "organization_fk")

  @@map("org_membership")
}

model OrgAddress {
  id           String         @id(map: "org_address_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId        String?        @map("org_id") @db.Uuid
  title        String         @map("title") @db.VarChar(100)
  zipCode      Int            @map("zip_code")
  purpose      ContactPurpose @map("purpose")
  addressLine1 String         @map("address_line_1") @db.VarChar(250)
  addressLine2 String?        @map("address_line_2") @db.VarChar(250)
  cityId       String         @map("city_id") @db.Uuid
  city         SystemCity     @relation(fields: [cityId], references: [id], map: "_system_city_fk")
  organization Organization?  @relation(fields: [orgId], references: [id], map: "organization_fk")

  @@unique([title, addressLine1, addressLine2, cityId], name: "org_address_uq")
  @@map("org_address")
}

model OrgContact {
  id           String         @id(map: "org_contact_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId        String?        @map("org_id") @db.Uuid
  label        String         @map("label") @db.VarChar(100)
  value        String         @map("value") @db.VarChar(100)
  type         ContactType    @map("type")
  purpose      ContactPurpose @map("purpose")
  organization Organization?  @relation(fields: [orgId], references: [id], map: "organization_fk")

  @@map("org_contact")
}

model OrgIntegration {
  id           String              @id(map: "org_integration_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId        String?             @map("org_id") @db.Uuid
  provider     IntegrationProvider @map("provider")
  name         String?             @map("name") @db.VarChar(64)
  expiresAt    DateTime?           @map("expires_at") @db.Timestamptz(6)
  refreshedAt  DateTime?           @map("refreshed_at") @db.Timestamptz(6)
  createdById  String?             @map("created_by_id") @db.Uuid
  createdAt    DateTime            @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime?           @map("updated_at") @db.Timestamptz(6)
  type         IntegrationType     @map("type")
  active       Boolean             @default(true) @map("active")
  organization Organization?       @relation(fields: [orgId], references: [id], map: "organization_fk")
  createdBy    User?               @relation(fields: [createdById], references: [id], map: "user_fk1")
  authToken    Json                @map("auth_token") @db.JsonB
  data         Json?               @db.JsonB

  @@map("org_integration")
}

model OrgUserRole {
  id           BigInt       @id(map: "org_user_role_pk") @default(autoincrement()) @db.BigInt
  orgId        String       @map("org_id") @db.Uuid
  userId       String       @unique @map("user_id") @db.Uuid
  roleId       String       @map("role_id") @db.Uuid
  createdAt    DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime?    @map("updated_at") @db.Timestamptz(6)
  userRole     UserRole     @relation(fields: [roleId], references: [id], map: "_user_role_fk")
  organization Organization @relation(fields: [orgId], references: [id], map: "organization_fk")
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade, map: "user_fk")

  @@unique([orgId, userId, roleId], name: "org_user_role_unique_constraint")
  @@map("org_user_role")
}

model OrgSetting {
  id                  String       @id(map: "org_setting_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId               String       @map("org_id") @db.Uuid
  useSystemLlmSetting Boolean      @default(true) @map("use_system_llm_setting") @db.Boolean
  createdAt           DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt           DateTime?    @map("updated_at") @db.Timestamptz(6)
  enrichmentSettings  Json         @map("enrichment_settings") @db.JsonB
  playSettings        Json         @map("play_settings") @db.JsonB
  organization        Organization @relation(fields: [orgId], references: [id], map: "organization_fk")

  @@unique([orgId], name: "org_setting_unique")
  @@map("org_setting")
}

model OrgPlayChannelSetting {
  id            String            @id(map: "org_play_channel_setting_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId         String            @map("org_id") @db.Uuid
  playChannelId String            @map("play_channel_id") @db.Uuid
  llmModelId    String            @map("llm_model_id") @db.Uuid
  isDefault     Boolean           @default(false) @map("is_default") @db.Boolean
  llmModel      SystemLlmModel    @relation(fields: [llmModelId], references: [id], map: "system_llm_model_fk")
  playChannel   SystemPlayChannel @relation(fields: [playChannelId], references: [id], map: "system_play_channel_fk")
  organization  Organization      @relation(fields: [orgId], references: [id], map: "organization_fk")

  @@unique([orgId, playChannelId, llmModelId], name: "org_play_channel_id_llm_model_id_unique")
  @@map("org_play_channel_setting")
}

model OrgLlmModels {
  id           String         @id(map: "org_llm_models_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId        String         @map("org_id") @db.Uuid
  llmModelId   String         @map("llm_model_id") @db.Uuid
  isEnabled    Boolean        @default(true) @map("is_enabled") @db.Boolean
  isDefault    Boolean        @default(false) @map("is_default") @db.Boolean
  organization Organization   @relation(fields: [orgId], references: [id], map: "organization_fk")
  llmModel     SystemLlmModel @relation(fields: [llmModelId], references: [id], map: "system_llm_model_fk")

  @@unique([orgId, llmModelId], name: "org_llm_model_unique")
  @@map("org_llm_models")
}

model User {
  id                   String             @id(map: "user_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  email                String             @map("email") @db.VarChar(200)
  firstName            String?            @map("first_name") @db.VarChar(100)
  lastName             String?            @map("last_name") @db.VarChar(100)
  password             String?            @map("password") @db.VarChar(100)
  phone                String?            @map("phone") @db.VarChar(15)
  active               Boolean            @map("active")
  uniqueKey            String?            @map("unique_key") @db.VarChar(100)
  invitationAccepted   Boolean?           @default(false) @map("invitation_accepted")
  createdAt            DateTime           @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt            DateTime?          @map("updated_at") @db.Timestamptz(6)
  deletedAt            DateTime?          @map("deleted_at") @db.Timestamptz(6)
  resetHash            String?            @map("reset_hash") @db.Char(32)
  resetExpiresAt       DateTime?          @map("reset_expires_at") @db.Timestamptz(6)
  invalidatedAt        DateTime?          @map("invalidated_at") @db.Timestamptz(6)
  deletedById          String?            @map("deleted_by_id") @db.Uuid
  createdByCompanies   Company[]          @relation("createdBy")
  deletedByCompanies   Company[]          @relation("deletedBy")
  createdByContacts    Contact[]          @relation("createdBy")
  deletedByContacts    Contact[]          @relation("deletedBy")
  createdByActivities  AcqwiredActivity[] @relation("createdBy")
  deletedByActivities  Activity[]         @relation("deletedBy")
  ownedByCompanies     Company[]          @relation("ownedBy")
  ownedByContacts      Contact[]          @relation("ownedBy")
  deletedByFields      EntityField[]      @relation("deletedBy")
  userCrmMappings      UserCrmMapping[]
  channelEmails        ChannelEmail[]
  invitations          UserInvitation[]
  orgIntegrations      OrgIntegration[]
  role                 OrgUserRole?
  plays                Play[]
  tokens               UserToken[]
  integrations         UserIntegration[]
  settings             UserSetting[]
  calendars            UserCalendar[]
  executions           Execution[]
  emailMessages        EmailMessage[]
  userPreference       UserPreference[]
  enrichments          Enrichment[]
  listCompany          ListCompany[]      @relation(map: "user_fk1")
  resources            Resource[]         @relation(map: "user_fk1")
  tableLayouts         UserTableLayout[]  @relation(map: "user_fk")
  createdScoringModels ScoringModel[]     @relation("createdBy")
  updatedScoringModels ScoringModel[]     @relation("updatedBy")

  @@map("user")
}

model UserRole {
  id         String        @id(map: "_user_role_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  role       String        @unique(map: "role_name_unq") @db.VarChar(20)
  permission Int           @map("permission")
  userRole   OrgUserRole[]

  @@map("user_role")
}

model UserInvitation {
  id           String       @id(map: "invitation_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId        String       @map("org_id") @db.Uuid
  email        String       @map("email") @db.VarChar(200)
  hash         String       @map("hash") @db.Char(32)
  createdAt    DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime?    @map("updated_at") @db.Timestamptz(6)
  cancelledAt  DateTime?    @map("cancelled_at") @db.Timestamptz(6)
  deletedAt    DateTime?    @map("deleted_at") @db.Timestamptz(6)
  userId       String       @map("user_id") @db.Uuid
  createdById  String?      @map("created_by_id") @db.Uuid
  organization Organization @relation(fields: [orgId], references: [id], map: "organization_fk")
  createdBy    User?        @relation(fields: [createdById], references: [id], map: "user_fk1")

  @@map("user_invitation")
}

model UserToken {
  id        BigInt      @id(map: "token_pk") @default(autoincrement()) @db.BigInt
  userId    String      @map("user_id") @db.Uuid
  logId     String      @map("log_id") @db.Uuid
  token     String      @map("token")
  ip        String      @map("ip") @db.Inet
  type      String?     @map("type") @db.VarChar(10)
  service   String?     @map("service")
  createdAt DateTime    @default(now()) @map("created_at") @db.Timestamptz(6)
  usedAt    DateTime    @default(now()) @map("used_at") @db.Timestamptz(6)
  status    TokenStatus @map("status")

  device   Json  @map("device") @db.JsonB
  location Json? @map("location") @db.JsonB

  user User @relation(fields: [userId], references: [id], map: "user_fk")

  @@map("user_token")
}

model UserIntegration {
  id          String              @id(map: "user_integration_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId       String              @map("org_id") @db.Uuid
  name        String?             @map("name") @db.VarChar(64)
  userId      String              @map("user_id") @db.Uuid
  createdAt   DateTime            @default(now()) @map("created_at") @db.Timestamptz(6)
  expiresAt   DateTime            @map("expires_at") @db.Timestamptz(6)
  provider    IntegrationProvider @map("provider")
  refreshedAt DateTime?           @map("refreshed_at") @db.Timestamptz(6)
  updatedAt   DateTime?           @map("updated_at") @db.Timestamptz(6)

  metadata Json @map("metadata") @db.JsonB
  token    Json @map("token") @db.JsonB

  organization Organization @relation(fields: [orgId], references: [id], map: "organization_fk")
  user         User         @relation(fields: [userId], references: [id], map: "user_fk")

  @@unique([orgId, userId, provider], name: "uniqueOrgUserIntegration")
  @@map("user_integration")
}

model UserSetting {
  id           String       @id(map: "user_setting_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  userId       String       @map("user_id") @db.Uuid
  orgId        String       @map("org_id") @db.Uuid
  key          String       @map("key") @db.VarChar(100)
  value        String       @map("value")
  createdAt    DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime?    @map("updated_at") @db.Timestamptz(6)
  organization Organization @relation(fields: [orgId], references: [id], map: "organization_fk")
  user         User         @relation(fields: [userId], references: [id], map: "user_fk")

  @@unique([userId, orgId, key], name: "uniqueUserKey")
  @@map("user_setting")
}

model UserCalendar {
  id            String    @id(map: "user_calendar_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  userId        String    @map("user_id") @db.Uuid
  orgId         String?   @map("org_id") @db.Uuid
  active        Boolean   @default(false) @map("active")
  meetingLength Int       @default(30) @map("calendar_meeting_length") @db.SmallInt
  timezone      Int       @default(-5) @map("calendar_timezone") @db.SmallInt
  createdAt     DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime? @map("updated_at") @db.Timestamptz(6)
  weeklyHours   Json?     @map("calendar_weekly_hours")

  user         User          @relation(fields: [userId], references: [id], map: "user_fk")
  organization Organization? @relation(fields: [orgId], references: [id], map: "organization_fk")

  @@map("user_calendar")
}

model UserCrmMapping {
  id           BigInt       @id(map: "user_crm_mapping_pk") @default(autoincrement()) @db.BigInt
  userId       String       @map("user_id") @db.Uuid
  orgId        String       @map("org_id") @db.Uuid
  crmUserId    String       @map("crm_user_id") @db.VarChar(64)
  email        String       @map("email") @db.VarChar(200)
  crmType      CrmType      @map("crm_type")
  createdAt    DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime?    @map("updated_at") @db.Timestamptz(6)
  organization Organization @relation(fields: [orgId], references: [id], map: "organization_fk")
  user         User         @relation(fields: [userId], references: [id], map: "user_fk")

  @@unique([userId, orgId, crmType], name: "user_crm_mapping_unique")
  @@unique([orgId, crmUserId, crmType], name: "user_crm_id_unique")
  @@map("user_crm_mapping")
}

model UserTableLayout {
  id           BigInt       @id(map: "user_table_layout_pk") @default(autoincrement()) @db.BigInt
  userId       String       @map("user_id") @db.Uuid
  orgId        String       @map("org_id") @db.Uuid
  entityType   EntityType   @map("entity_type")
  value        Json         @map("value") @db.JsonB
  createdAt    DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime?    @map("updated_at") @db.Timestamptz(6)
  user         User         @relation(fields: [userId], references: [id], map: "user_fk")
  organization Organization @relation(fields: [orgId], references: [id], map: "organization_fk")

  @@unique([userId, orgId, entityType], name: "userid_orgid_type_unique")
  @@map("user_table_layout")
}

model EntityField {
  id                   String                           @id(map: "field_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  entity               EntityType                       @map("entity")
  orgId                String?                          @map("org_id") @db.Uuid
  label                String                           @map("label") @db.VarChar(100)
  dataType             FieldDataType                    @map("data_type")
  key                  String                           @map("key") @db.VarChar(50)
  isInternal           Boolean                          @default(false) @map("is_internal")
  isAttachment         Boolean                          @default(false) @map("is_attachment")
  isMandatory          Boolean                          @default(false) @map("is_mandatory")
  config               Json                             @map("config") @db.JsonB
  createdAt            DateTime                         @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt            DateTime?                        @map("updated_at") @db.Timestamptz(6)
  invalidatedAt        DateTime                         @default(now()) @map("invalidated_at") @db.Timestamptz(6)
  deletedAt            DateTime?                        @map("deleted_at") @db.Timestamptz(6)
  deletedById          String?                          @map("deleted_by_id") @db.Uuid
  organization         Organization?                    @relation(fields: [orgId], references: [id], map: "organization_fk")
  deletedBy            User?                            @relation("deletedBy", fields: [deletedById], references: [id])
  companyFields        CompanyField[]
  contactFields        ContactField[]
  companyAttachments   CompanyAttachment[]
  contactAttachments   ContactAttachment[]
  templateFieldMapping TemplateAttachmentFieldMapping[]
  enrichments          Enrichment[]
  crmMappings          CrmMapping[]
  contactFieldMetadata ContactFieldMetadata[]

  @@unique([orgId, entity, key], name: "org_entity_key_unique")
  @@map("entity_field")
}

model EntityFieldInitialData {
  id           BigInt        @id(map: "entity_field_initial_data_pk") @default(autoincrement()) @db.BigInt
  entity       EntityType    @map("entity")
  label        String        @map("label") @db.VarChar(100)
  dataType     FieldDataType @map("data_type")
  key          String        @map("key") @db.VarChar(50)
  isInternal   Boolean       @default(false) @map("is_internal")
  isAttachment Boolean       @default(false) @map("is_attachment")
  isMandatory  Boolean       @default(false) @map("is_mandatory")

  @@unique([entity, key], name: "entity_key_initial_data_unique")
  @@map("entity_field_initial_data")
}

model Company {
  id              String                  @id(map: "company_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId           String                  @map("org_id") @db.Uuid
  ownerId         String?                 @map("owner_id") @db.Uuid
  name            String                  @map("name") @db.VarChar(255)
  website         String                  @map("website") @db.VarChar(255)
  domain          String                  @map("domain") @db.VarChar(255)
  sourceType      DataSourceType          @map("source_type")
  createdAt       DateTime                @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt       DateTime?               @map("updated_at") @db.Timestamptz(6)
  deletedAt       DateTime?               @map("deleted_at") @db.Timestamptz(6)
  createdById     String?                 @map("created_by_id") @db.Uuid
  deletedById     String?                 @map("deleted_by_id") @db.Uuid
  createdBy       User?                   @relation("createdBy", fields: [createdById], references: [id])
  deletedBy       User?                   @relation("deletedBy", fields: [deletedById], references: [id])
  owner           User?                   @relation("ownedBy", fields: [ownerId], references: [id])
  organization    Organization            @relation(fields: [orgId], references: [id], map: "organization_fk")
  fields          CompanyField[]
  attachments     CompanyAttachment[]
  entityMapping   CompanyContactMapping[]
  channelEmails   ChannelEmail[]
  participants    ActivityParticipant[]
  crmCompany      CrmCompany?
  ListCompanyIdea ListCompanyIdea[]       @relation(map: "company_fk")

  @@unique([orgId, domain], name: "unique_company")
  @@map("company")
}

model Contact {
  id            String                  @id(map: "contact_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId         String                  @map("org_id") @db.Uuid
  firstName     String                  @map("first_name") @db.VarChar(128)
  lastName      String                  @map("last_name") @db.VarChar(128)
  email         String                  @map("email") @db.VarChar(128)
  sourceType    DataSourceType          @map("source_type")
  createdAt     DateTime                @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime?               @map("updated_at") @db.Timestamptz(6)
  deletedAt     DateTime?               @map("deleted_at") @db.Timestamptz(6)
  createdById   String?                 @map("created_by_id") @db.Uuid
  deletedById   String?                 @map("deleted_by_id") @db.Uuid
  createdBy     User?                   @relation("createdBy", fields: [createdById], references: [id])
  deletedBy     User?                   @relation("deletedBy", fields: [deletedById], references: [id])
  ownerId       String?                 @map("owner_id") @db.Uuid
  owner         User?                   @relation("ownedBy", fields: [ownerId], references: [id])
  organization  Organization            @relation(fields: [orgId], references: [id], map: "organization_fk")
  entityMapping CompanyContactMapping[]
  fields        ContactField[]
  attachments   ContactAttachment[]
  channelEmails ChannelEmail[]
  participants  ActivityParticipant[]
  crmContact    CrmContact?
  fieldMetadata ContactFieldMetadata[]

  @@map("contact")
}

model CompanyContactMapping {
  id        BigInt   @id(map: "contact_company_mapping_pk") @default(autoincrement()) @db.BigInt
  contactId String   @map("contact_id") @db.Uuid
  companyId String   @map("company_id") @db.Uuid
  isPrimary Boolean? @map("is_primary")
  isKey     Boolean? @map("is_key")
  company   Company  @relation(fields: [companyId], references: [id], map: "company_fk")
  contact   Contact  @relation(fields: [contactId], references: [id], map: "contact_fk")

  @@unique([contactId, companyId], name: "company_contact_unique")
  @@map("contact_company_mapping")
}

model CompanyField {
  id            String         @id(map: "company_field_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  companyId     String         @map("company_id") @db.Uuid
  schemaFieldId String         @map("schema_field_id") @db.Uuid
  sourceType    DataSourceType @map("source_type")
  createdAt     DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime?      @map("updated_at") @db.Timestamptz(6)
  value         String         @map("value") @db.Text
  company       Company        @relation(fields: [companyId], references: [id], map: "company_fk")
  schemaField   EntityField    @relation(fields: [schemaFieldId], references: [id], map: "schema_field_fk")

  @@unique([companyId, schemaFieldId], name: "companyFieldMapping")
  @@map("company_field")
}

model ContactField {
  id            String         @id(map: "contact_field_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  contactId     String         @map("contact_id") @db.Uuid
  schemaFieldId String         @map("schema_field_id") @db.Uuid
  sourceType    DataSourceType @map("source_type")
  createdAt     DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime?      @map("updated_at") @db.Timestamptz(6)
  value         String         @map("value") @db.Text
  contact       Contact        @relation(fields: [contactId], references: [id], map: "contact_fk")
  schemaField   EntityField    @relation(fields: [schemaFieldId], references: [id], map: "schema_field_fk")

  @@unique([contactId, schemaFieldId], name: "contactFieldMapping")
  @@map("contact_field")
}

model CompanyAttachment {
  id            String         @id(map: "company_attachment_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  companyId     String         @map("company_id") @db.Uuid
  schemaFieldId String         @map("schema_field_id") @db.Uuid
  data          String         @map("data") @db.Text
  sourceType    DataSourceType @map("source_type")
  createdAt     DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime?      @map("updated_at") @db.Timestamptz(6)
  company       Company        @relation(fields: [companyId], references: [id], map: "company_fk")
  schemaField   EntityField    @relation(fields: [schemaFieldId], references: [id], map: "schema_field_fk")

  @@unique([companyId, schemaFieldId], name: "companyFieldMapping")
  @@map("company_attachment")
}

model ContactAttachment {
  id            String         @id(map: "contact_attachment_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  contactId     String         @map("contact_id") @db.Uuid
  schemaFieldId String         @map("schema_field_id") @db.Uuid
  data          String         @map("data") @db.Text
  sourceType    DataSourceType @map("source_type")
  createdAt     DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime?      @map("updated_at") @db.Timestamptz(6)
  contact       Contact        @relation(fields: [contactId], references: [id], map: "contact_fk")
  schemaField   EntityField    @relation(fields: [schemaFieldId], references: [id], map: "schema_field_fk")

  @@unique([contactId, schemaFieldId], name: "contactFieldMapping")
  @@map("contact_attachment")
}

model ContactFieldMetadata {
  id            String            @id(map: "contact_field_metadata_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  contactId     String            @map("contact_id") @db.Uuid
  schemaFieldId String            @map("schema_field_id") @db.Uuid
  type          FieldMetadataType @map("metadata_type")
  dataType      FieldDataType     @map("data_type")
  contact       Contact           @relation(fields: [contactId], references: [id], map: "contact_fk")
  schemaField   EntityField       @relation(fields: [schemaFieldId], references: [id], map: "schema_field_fk")

  @@unique([contactId, type, dataType], name: "contactFieldMetadata")
  @@map("contact_field_metadata")
}

model Activity {
  id          String         @id(map: "activity_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId       String         @map("org_id") @db.Uuid
  source      ActivitySource @map("source_type")
  type        ActivityType   @map("activity_type")
  deletedAt   DateTime?      @map("deleted_at") @db.Timestamptz(6)
  deletedById String?        @map("deleted_by_id") @db.Uuid

  deletedBy    User?        @relation("deletedBy", fields: [deletedById], references: [id])
  organization Organization @relation(fields: [orgId], references: [id], map: "organization_fk")

  crmActivity      CrmActivity?          @relation("crmActivityId")
  acqwiredActivity AcqwiredActivity?     @relation("acqwiredActivity")
  channelEmail     ChannelEmail?         @relation("channelEmail")
  participants     ActivityParticipant[]

  @@map("activity")
}

model ActivityParticipant {
  id         String  @id(map: "activity_participant_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  activityId String  @map("activity_id") @db.Uuid
  contactId  String? @map("contact_id") @db.Uuid
  companyId  String? @map("company_id") @db.Uuid

  activity Activity @relation(map: "activity_fk", fields: [activityId], references: [id])
  contact  Contact? @relation(map: "contact_fk", fields: [contactId], references: [id])
  company  Company? @relation(map: "company_fk", fields: [companyId], references: [id])

  @@unique([activityId, contactId, companyId], name: "UniqueActivityParticipant")
  @@map("activity_participant")
}

model AcqwiredActivity {
  id          String         @id(map: "acqwired_activity_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  activityId  String         @unique @map("activity_id") @db.Uuid
  title       String?        @map("title") @db.Text
  content     String?        @map("content") @db.Text
  status      ActivityStatus @map("activity_status")
  dueDate     DateTime       @map("due_date") @db.Timestamptz(6)
  createdAt   DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime?      @map("updated_at") @db.Timestamptz(6)
  createdById String         @map("created_by_id") @db.Uuid

  createdBy User     @relation("createdBy", fields: [createdById], references: [id])
  activity  Activity @relation("acqwiredActivity", fields: [activityId], references: [id])

  @@map("acqwired_activity")
}

model CrmCompany {
  id        BigInt  @id(map: "crm_company_pk") @default(autoincrement()) @db.BigInt
  orgId     String  @map("org_id") @db.Uuid
  crmId     String  @map("crm_id")
  companyId String  @unique @map("company_id") @db.Uuid
  crmType   CrmType @default(salesforce) @map("crm_type")
  fields    Json    @db.JsonB
  company   Company @relation(fields: [companyId], references: [id], map: "company_fk")

  @@unique([orgId, crmId, crmType], name: "crmCompanyConnection")
  @@map("crm_company")
}

model CrmContact {
  id            BigInt  @id(map: "crm_contact_pk") @default(autoincrement()) @db.BigInt
  orgId         String  @map("org_id") @db.Uuid
  crmId         String  @map("crm_id")
  crmCompanyIds Json    @map("crm_company_ids") @db.JsonB
  contactId     String  @unique @map("contact_id") @db.Uuid
  crmType       CrmType @default(salesforce) @map("crm_type")
  field         Json    @db.JsonB
  contact       Contact @relation(fields: [contactId], references: [id], map: "contact_fk")

  @@unique([orgId, crmId, crmType], name: "crmContactConnection")
  @@map("crm_contact")
}

model CrmActivity {
  id         String         @id(map: "crm_activity_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId      String         @map("org_id") @db.Uuid
  crmId      String         @map("crm_id")
  activityId String         @unique @map("activity_id") @db.Uuid
  title      String?        @map("title") @db.Text
  content    String?        @map("content") @db.Text
  status     ActivityStatus @map("activity_status")
  dueDate    DateTime       @map("due_date") @db.Timestamptz(6)
  crmType    CrmType        @default(salesforce) @map("crm_type")
  entityType CrmEntityType  @map("entity_type")
  field      Json           @db.JsonB
  startDate  DateTime?      @map("start_date") @db.Timestamptz(6)
  endDate    DateTime?      @map("end_date") @db.Timestamptz(6)

  activity Activity @relation("crmActivityId", fields: [activityId], references: [id])

  @@unique([orgId, crmId, crmType, entityType], name: "crmActivityConnection")
  @@map("crm_activity")
}

model CrmMapping {
  id            String       @id(map: "crm_mapping_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId         String       @map("org_id") @db.Uuid
  schemaFieldId String       @map("schema_field_id") @db.Uuid
  listId        String?      @map("list_id") @db.Uuid
  locked        Boolean
  createdAt     DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime?    @map("updated_at") @db.Timestamptz(6)
  entityType    EntityType   @map("entity_type")
  crmFieldKey   String       @map("crm_field_key")
  crmFieldLabel String?      @map("crm_field_label")
  crmType       CrmType      @default(salesforce) @map("crm_type")
  fieldSource   CrmFieldType @default(standard) @map("crm_field_type")
  externalId    String       @map("external_id")

  organization Organization @relation(fields: [orgId], references: [id], map: "organization_fk")
  entityField  EntityField  @relation(fields: [schemaFieldId], references: [id], map: "entity_field_fk")
  list         CrmList?     @relation(fields: [listId], references: [id], map: "list_fk")

  @@map("crm_mapping")
}

model CrmFilter {
  id           String               @id(map: "crm_filter_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId        String               @map("org_id") @db.Uuid
  filterType   EntityType           @map("filter_type")
  fetchRelated Boolean              @default(true) @map("fetch_related")
  createdAt    DateTime             @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime?            @map("updated_at") @db.Timestamptz(6)
  crmType      CrmType              @default(salesforce) @map("crm_type")
  organization Organization         @relation(fields: [orgId], references: [id], map: "organization_fk")
  parameters   CrmFilterParameter[]

  @@map("crm_filter")
}

model CrmSetting {
  id           String  @id(map: "crm_setting_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId        String  @map("org_id") @db.Uuid
  syncContacts Boolean @default(false) @map("sync_contacts")
  syncAccounts Boolean @default(false) @map("sync_accounts")
  inProgress   Boolean @default(false) @map("in_progress")
  crmType      CrmType @default(salesforce) @map("crm_type")

  @@unique([orgId, crmType], name: "orgId_crmType_unq")
  @@map("crm_setting")
}

model CrmSyncStatus {
  id            String   @id(map: "crm_sync_status_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId         String   @map("org_id") @db.Uuid
  status        Boolean
  createdAt     DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  accountSynced Boolean  @map("account_synced")
  contactSynced Boolean  @map("contact_synced")
  crmType       CrmType  @default(salesforce) @map("crm_type")

  @@map("crm_sync_status")
}

model CrmFilterParameter {
  id        String    @id(map: "crm_filter_parameter_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  field     String    @map("field") @db.VarChar(255)
  condition String    @map("condition") @db.VarChar(255)
  input     String    @map("input") @db.VarChar(255)
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime? @map("updated_at") @db.Timestamptz(6)
  filterId  String    @map("filter_id") @db.Uuid
  filter    CrmFilter @relation(fields: [filterId], references: [id], onDelete: Cascade, map: "filter_fk")

  @@map("crm_filter_parameter")
}

model CrmList {
  id           String     @id(map: "crm_list_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId        String     @map("org_id") @db.Uuid
  crmId        String     @map("crm_id")
  name         String     @map("name")
  entityType   EntityType @map("entity_type")
  crmType      CrmType    @map("crm_type")
  ownerId      String     @map("owner_id")
  fetchRelated Boolean    @map("fetch_related")

  organization Organization @relation(fields: [orgId], references: [id], map: "organization_fk")
  mappings     CrmMapping[]

  @@unique([crmId, orgId, crmType], name: "crmId_orgId_crmType_unq")
  @@map("crm_list")
}

model ChannelEmail {
  id           String      @id(map: "email_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId        String      @map("org_id") @db.Uuid
  contactId    String      @map("contact_id") @db.Uuid
  companyId    String      @map("company_id") @db.Uuid
  activityId   String?     @unique @map("activity_id") @db.Uuid
  from         String      @map("from") @db.VarChar(128)
  to           String      @map("to") @db.VarChar(128)
  cc           String[]    @map("cc") @db.VarChar(128)
  subject      String      @map("subject") @db.VarChar(256)
  status       EmailStatus @map("status")
  errorMessage String?     @map("error_message")
  type         EmailType   @map("type")
  createdById  String      @map("created_by_id") @db.Uuid
  scheduledAt  DateTime?   @map("scheduled_at") @db.Timestamptz(6)
  createdAt    DateTime    @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime?   @map("updated_at") @db.Timestamptz(6)
  deletedAt    DateTime?   @map("deleted_at") @db.Timestamptz(6)
  cancelledAt  DateTime?   @map("cancelled_at") @db.Timestamptz(6)
  executionId  String?     @unique @db.Uuid
  body         String      @map("body")

  company      Company      @relation(fields: [companyId], references: [id], map: "company_fk")
  contact      Contact      @relation(fields: [contactId], references: [id], map: "contact_fk")
  organization Organization @relation(fields: [orgId], references: [id], map: "organization_fk")
  createdBy    User         @relation(fields: [createdById], references: [id], map: "user_fk")
  execution    Execution?   @relation(fields: [executionId], references: [id])
  activity     Activity?    @relation("channelEmail", fields: [activityId], references: [id])

  @@map("channel_email")
}

model WebsiteContent {
  id             BigInt         @id(map: "website_content_pk") @default(autoincrement()) @db.BigInt
  protocol       ValidProtocols @map("protocol")
  hostname       String         @map("hostname") @db.VarChar(255)
  pathname       String         @map("pathname") @db.VarChar(4192)
  search         String         @map("search")
  content        String?        @map("content")
  lastHttpStatus Int?           @map("last_http_status") @db.SmallInt
  createdAt      DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime?      @map("updated_at") @db.Timestamptz(6)

  @@unique([hostname, pathname, search], name: "full_web_page_uri_unq")
  @@map("website_content")
}

model LinkedInContent {
  id             BigInt    @id(map: "linkedin_content_pk") @default(autoincrement()) @db.BigInt
  searchKey      String    @unique(map: "search_key") @db.VarChar(255)
  lastHttpStatus Int       @map("last_http_status") @db.SmallInt
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime? @map("updated_at") @db.Timestamptz(6)
  content        Json?     @map("content") @db.JsonB

  @@map("linkedin_content")
}

model Template {
  id           String                           @id(map: "template_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId        String                           @map("org_id") @db.Uuid
  playId       String                           @map("play_id") @db.Uuid
  llmEngine    LlmProvider                      @map("llm_provider")
  template     String                           @map("template")
  title        String?                          @map("title")
  organization Organization                     @relation(fields: [orgId], references: [id], map: "organization_fk")
  play         Play                             @relation(fields: [playId], references: [id], map: "play_fk", onDelete: Cascade)
  fieldMapping TemplateAttachmentFieldMapping[]

  @@unique([playId, llmEngine], name: "template_llmengine_playid_unique")
  @@map("template")
}

model TemplateInitialData {
  id                  BigInt          @id(map: "template_initial_data_pk") @default(autoincrement()) @db.BigInt
  initialPlayId       String          @map("play_id") @db.Uuid
  template            String          @map("template")
  llmEngine           LlmProvider     @map("llm_provider")
  attachmentKey       String?         @map("attachment_key")
  attachmentKeyEntity EntityType?     @map("attachment_key_entity")
  initialPlay         PlayInitialData @relation(fields: [initialPlayId], references: [id], map: "initial_play_fk")

  @@map("template_initial_data")
}

model TemplateAttachmentFieldMapping {
  id            BigInt      @id(map: "template_attachment_field_mapping_pk") @default(autoincrement()) @db.BigInt
  templateId    String      @map("template_id") @db.Uuid
  schemaFieldId String      @map("schema_field_id") @db.Uuid
  template      Template    @relation(fields: [templateId], references: [id], map: "template_fk", onDelete: Cascade)
  schemaField   EntityField @relation(fields: [schemaFieldId], references: [id], map: "schema_field_fk")

  @@map("template_attachment_field_mapping")
}

model Play {
  id                    String          @id(map: "play_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  configId              String          @map("config_id") @db.Uuid
  name                  String          @map("name") @db.VarChar(128)
  description           String?         @map("description") @db.VarChar(512)
  orgId                 String          @map("org_id") @db.Uuid
  createdById           String          @map("created_by_id") @db.Uuid
  llmModelId            String?         @map("llm_model_id") @db.Uuid
  acqwiredProvidedPlays Boolean         @default(false) @map("acqwired_provided_plays")
  status                PlayStatus      @map("status")
  createdAt             DateTime        @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt             DateTime?       @map("updated_at")
  deletedAt             DateTime?       @map("deleted_at")
  organization          Organization    @relation(fields: [orgId], references: [id], map: "organization_fk")
  createdBy             User?           @relation(fields: [createdById], references: [id], map: "user_fk")
  config                PlayConfig      @relation(fields: [configId], references: [id], map: "play_config_fk")
  llmModel              SystemLlmModel? @relation(fields: [llmModelId], references: [id], map: "system_llm_model_fk")
  templates             Template[]
  tasks                 PlayTask[]

  @@map("play")
}

model PlayInitialData {
  id           String                @id(map: "play_inital_data_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  configId     String                @map("config_id") @db.Uuid
  name         String                @map("name") @db.VarChar(128)
  description  String?               @map("description") @db.VarChar(512)
  data         Json?                 @db.JsonB
  templateData TemplateInitialData[]

  @@map("play_initial_data")
}

model PlayBatch {
  id   String @id(map: "play_batch_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  name String @map("name") @db.VarChar(64)

  @@map("play_batch")
}

model PlayConfig {
  id                String      @id(map: "play_config_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  channel           PlayChannel @map("channel")
  llmEngine         LlmProvider @map("llm_engine")
  promptPrependText String?     @map("prompt_prepend_text")
  plays             Play[]

  @@map("play_config")
}

model PlayTask {
  id            String        @id(map: "play_task_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  playId        String        @map("play_id") @db.Uuid
  enrichmentId  String?       @map("enrichment_id") @db.Uuid
  priority      Int           @default(0) @map("priority") @db.SmallInt
  type          TaskType      @map("task_type")
  queueTaskType QueueTaskType @map("queue_type")
  data          Json          @db.JsonB
  play          Play          @relation(fields: [playId], references: [id], map: "play_fk", onDelete: Cascade)
  enrichment    Enrichment?   @relation(fields: [enrichmentId], references: [id], map: "play_task_enrichment_fk", onDelete: Cascade)

  @@map("play_task")
}

model Enrichment {
  id               String             @id(map: "enrichment_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  name             String             @map("name") @db.VarChar(128)
  orgId            String             @map("org_id") @db.Uuid
  data             Json               @db.JsonB
  isPrimary        Boolean            @default(false) @map("is_primary")
  createdById      String             @map("created_by_id") @db.Uuid
  createdAt        DateTime           @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime?          @map("updated_at") @db.Timestamptz(6)
  deletedAt        DateTime?          @map("deleted_at") @db.Timestamptz(6)
  llmModelId       String?            @map("llm_model_id") @db.Uuid
  entityFieldId    String?            @map("entity_field_id") @db.Uuid
  configId         String             @map("config_id") @db.Uuid
  entityType       EntityType         @map("entity_type")
  listCompanyId    String?            @map("list_id") @db.Uuid
  listColumns      Json?              @map("list_columns") @db.JsonB
  createdBy        User               @relation(fields: [createdById], references: [id], map: "user_fk")
  organization     Organization       @relation(fields: [orgId], references: [id], map: "organization_fk")
  llmModel         SystemLlmModel?    @relation(fields: [llmModelId], references: [id])
  entityField      EntityField?       @relation(fields: [entityFieldId], references: [id])
  config           EnrichmentConfig   @relation(fields: [configId], references: [id])
  listCompany      ListCompany?       @relation(fields: [listCompanyId], references: [id], map: "list_company_fk")
  results          EnrichmentResult[]
  playTasks        PlayTask[]         @relation(map: "play_task_enrichment_fk")
  scoringFieldData ScoringFieldData[]

  @@map("enrichment")
}

model EnrichmentConfig {
  id                String             @id(map: "enrichment_config_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  name              String             @map("name") @db.VarChar(64)
  description       String             @map("description") @db.VarChar(1024)
  entityType        EntityType         @map("entity_type")
  enrichmentType    EnrichmentType     @map("enrichment_type")
  provider          EnrichmentProvider @default(acqwired)
  defaultLlmModelId String?            @map("default_llm_model_id") @db.Uuid
  data              Json               @db.JsonB
  additionalPrompts Json               @map("additional_prompts") @db.JsonB
  responseSchema    Json               @map("response_schema") @db.JsonB
  Enrichment        Enrichment[]
  defaultLlmModel   SystemLlmModel?    @relation(fields: [defaultLlmModelId], references: [id])

  @@map("enrichment_config")
}

model EnrichmentResult {
  id           String     @id(map: "enrichment_result_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  enrichmentId String     @map("enrichment_id") @db.Uuid
  executionId  String     @map("execution_id") @db.Uuid
  entityId     String     @map("entity_id") @db.Uuid
  entityType   EntityType @map("entity_type")
  value        Json       @map("value") @db.JsonB
  createdAt    DateTime   @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime?  @map("updated_at") @db.Timestamptz(6)

  enrichment Enrichment @relation(fields: [enrichmentId], references: [id], map: "enrichment_fk", onDelete: Cascade)
  execution  Execution  @relation(fields: [executionId], references: [id], map: "execution_fk")

  @@map("enrichment_result")
}

model Execution {
  id                String             @id(map: "task_execution_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId             String             @map("org_id") @db.Uuid
  type              ExecutionType      @map("type")
  typeId            String             @map("type_id") @db.Uuid
  entityType        EntityType         @map("entity_type")
  entityId          String             @map("entity_id") @db.Uuid
  status            ExecutionStatus    @map("status")
  statusMessage     String             @default("") @map("status_message") @db.VarChar(128)
  createdById       String             @map("created_by_id") @db.Uuid
  createdAt         DateTime           @default(now()) @map("created_at") @db.Timestamptz(6)
  scheduledAt       DateTime?          @map("scheduled_at") @db.Timestamptz(6)
  executedAt        DateTime?          @map("executed_at") @db.Timestamptz(6)
  completedAt       DateTime?          @map("completed_at") @db.Timestamptz(6)
  isSilent          Boolean?           @map("is_silent")
  createdBy         User               @relation(fields: [createdById], references: [id], map: "user_fk")
  organization      Organization       @relation(fields: [orgId], references: [id], map: "organization_fk")
  channelEmails     ChannelEmail[]
  queueTasks        QueueTask[]
  enrichmentResults EnrichmentResult[]
  scoringResults    ScoringResult[]

  @@map("execution")
}

model QueueTask {
  id          String          @id(map: "queue_tasks_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  executionId String?         @map("execution_id") @db.Uuid
  type        QueueTaskType   @map("type")
  status      ExecutionStatus @map("status")
  createdAt   DateTime        @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime?       @map("updated_at") @db.Timestamptz(6)
  scheduledAt DateTime?       @map("scheduled_at") @db.Timestamptz(6)

  data Json @map("data") @db.JsonB

  execution    Execution?     @relation(fields: [executionId], references: [id], map: "execution_fk")
  emailMessage EmailMessage[]

  @@map("queue_tasks")
}

model EmailMessage {
  id            String             @id(map: "email_messages_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  userId        String?            @map("user_id") @db.Uuid
  orgId         String?            @map("org_id") @db.Uuid
  from          String             @map("from") @db.VarChar(250)
  to            String             @map("to") @db.VarChar(250)
  cc            String[]           @default([]) @map("cc")
  bcc           String[]           @default([]) @map("bcc")
  template      String             @map("template") @db.VarChar(250)
  subject       String             @map("subject") @db.Text
  body          String?            @map("body") @db.Text
  status        EmailMessageStatus @map("status")
  statusMessage String?            @map("status_message") @db.Text
  createdAt     DateTime           @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime?          @map("updated_at") @db.Timestamptz(6)
  scheduledAt   DateTime           @map("scheduled_at") @db.Timestamptz(6)
  queueTaskId   String?            @map("queue_task_id") @db.Uuid
  data          Json               @map("data") @db.JsonB
  user          User?              @relation(fields: [userId], references: [id], map: "user_fk")
  organization  Organization?      @relation(fields: [orgId], references: [id], map: "organization_fk")
  queueTask     QueueTask?         @relation(fields: [queueTaskId], references: [id], map: "queue_task_fk")

  @@map("email_message")
}

model UserPreference {
  id     String @id(map: "user_preference_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  userId String @map("user_id") @db.Uuid
  key    String @map("key") @db.VarChar(50)
  value  Json   @map("value") @db.JsonB
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade, map: "user_fk")

  @@unique([userId, key], name: "unique_user_key")
  @@map("user_preference")
}

model TemplateVariables {
  id          String       @id(map: "template_variables_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  type        VariableType @map("type")
  namespace   String       @map("namespace") @db.VarChar(50)
  key         String       @map("key") @db.VarChar(50)
  description String       @map("description") @db.VarChar(255)

  @@unique([namespace, key], name: "unique_namespace_key")
  @@map("template_variables")
}

model Stats {
  id        String       @id(map: "stats_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  type      StatsType    @map("type")
  typeId    String       @map("type_id") @db.Uuid
  updatedAt DateTime     @default(now()) @map("updated_at") @db.Timestamptz(6)
  key       StatsKeyType @map("key")
  value     Int          @default(0) @map("value")

  @@unique([type, key, typeId], name: "unique_type_key_typeId")
  @@map("stats")
}

model ListCompany {
  id                     String                   @id(map: "list_company_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  name                   String
  status                 ListCompanyStatus        @default(inProgress) @map("status")
  orgId                  String                   @map("org_id") @db.Uuid
  createdById            String?                  @map("created_by_id") @db.Uuid
  createdAt              DateTime                 @default(now()) @map("created_at")
  updatedAt              DateTime?                @updatedAt @map("updated_at")
  organization           Organization             @relation(fields: [orgId], references: [id], map: "organization_fk")
  createdBy              User?                    @relation(fields: [createdById], references: [id], map: "user_fk1")
  listSource             ListSource[]             @relation(map: "list_company_fk")
  listCompanyIdeaMapping ListCompanyIdeaMapping[]
  enrichments            Enrichment[]
  scoringModels          ScoringModel[]

  @@map("list_company")
}

model Resource {
  id          String    @id(map: "resource_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  name        String
  fileType    String    @map("file_type")
  createdById String?   @map("created_by_id") @db.Uuid
  createdAt   DateTime  @default(now()) @map("created_at")
  deletedAt   DateTime? @map("deleted_at")
  fileSize    Int       @map("file_size")
  accessUrl   String    @map("access_url")
  md5         String
  createdBy   User?     @relation(fields: [createdById], references: [id], map: "user_fk1")

  @@map("resource")
}

model Intention {
  id               String            @id(map: "intention_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  name             String
  orderBy          Int               @map("order_by")
  createdAt        DateTime          @default(now()) @map("created_at") @db.Timestamptz(6)
  intentionSources IntentionSource[]

  @@map("intention")
}

model IntentionSource {
  id          String         @id(map: "intention_source_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  intentionId String         @map("intention_id") @db.Uuid
  sourceType  ListSourceType @map("source_type")
  name        String
  description String?
  parameters  Json?
  orderBy     Int            @map("order_by")
  createdAt   DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  intention   Intention      @relation(fields: [intentionId], references: [id], map: "intention_fk")
  listSources ListSource[]

  @@map("intention_source")
}

model ListSource {
  id                           String                         @id(map: "list_source_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  listCompanyId                String                         @map("list_company_id") @db.Uuid
  status                       ListSourceStatus               @default(inProgress) @map("status")
  intentionSourceId            String                         @map("intention_source_id") @db.Uuid
  parameters                   Json
  reason                       String?
  createdAt                    DateTime                       @default(now()) @map("created_at")
  listCompany                  ListCompany                    @relation(fields: [listCompanyId], references: [id], map: "list_company_fk", onDelete: Cascade)
  intentionSource              IntentionSource                @relation(fields: [intentionSourceId], references: [id], map: "intention_source_fk")
  listCompanySourceIdeaMapping ListCompanySourceIdeaMapping[]

  @@map("list_source")
}

model ListCompanyIdea {
  id                           String                         @id(map: "list_company_idea_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  orgId                        String                         @map("org_id") @db.Uuid
  companyName                  String?                        @map("company_name") @db.VarChar(255)
  website                      String?                        @map("website") @db.VarChar(255)
  domain                       String?                        @map("domain") @db.VarChar(255)
  companyId                    String?                        @unique @map("company_id") @db.Uuid
  fields                       Json?
  organization                 Organization                   @relation(fields: [orgId], references: [id], map: "organization_fk")
  company                      Company?                       @relation(fields: [companyId], references: [id], map: "company_fk")
  listCompanySourceIdeaMapping ListCompanySourceIdeaMapping[]
  listCompanyIdeaMapping       ListCompanyIdeaMapping[]
  scoringResults               ScoringResult[]

  @@unique([orgId, domain], name: "unique_list_company_idea")
  @@map("list_company_idea")
}

model ListCompanySourceIdeaMapping {
  id                Int             @id @default(autoincrement())
  listCompanyIdeaId String          @map("idea_id") @db.Uuid
  listSourceId      String          @map("source_id") @db.Uuid
  metadata          Json
  listCompanyIdea   ListCompanyIdea @relation(fields: [listCompanyIdeaId], references: [id], map: "list_company_id_fk", onDelete: Cascade)
  listSource        ListSource      @relation(fields: [listSourceId], references: [id], map: "list_source_fk", onDelete: Cascade)

  @@unique([listCompanyIdeaId, listSourceId])
  @@map("list_company_idea_source_mapping")
}

model ListCompanyIdeaMapping {
  id                Int             @id @default(autoincrement())
  listCompanyIdeaId String          @map("idea_id") @db.Uuid
  listCompanyId     String          @map("list_company_id") @db.Uuid
  inShortlist       Boolean         @default(false) @map("in_shortlist")
  listCompanyIdea   ListCompanyIdea @relation(fields: [listCompanyIdeaId], references: [id], map: "list_company_idea_id_fk", onDelete: Cascade)
  listCompany       ListCompany     @relation(fields: [listCompanyId], references: [id], map: "list_company_map_fk", onDelete: Cascade)

  @@unique([listCompanyIdeaId, listCompanyId])
  @@map("list_company_idea_mapping")
}

model CreditPurchase {
  id            Int                    @id @default(autoincrement())
  orgId         String                 @map("org_id") @db.Uuid
  creditAmount  Decimal                @map("credit_amount") @db.Decimal(10, 2)
  purchasePrice Decimal                @map("purchase_price") @db.Decimal(10, 2)
  expiresAt     DateTime?              @map("expires_at") @db.Timestamptz(6)
  purchasedAt   DateTime               @default(now()) @map("purchased_at") @db.Timestamptz(6)
  createdAt     DateTime               @default(now()) @map("created_at") @db.Timestamptz(6)
  organization  Organization           @relation(fields: [orgId], references: [id], onDelete: Cascade, map: "organization_fk")
  transactions  CreditTransactionLog[]

  @@map("credit_purchase")
}

model CreditUsage {
  id           BigInt                 @id @default(autoincrement())
  orgId        String                 @map("org_id") @db.Uuid
  apiProvider  ApiProvider            @map("api_provider")
  apiOperation ApiOperation           @map("api_operation")
  apiUnit      ApiUnit                @map("api_unit")
  apiAmount    Int                    @map("api_amount")
  usedFor      UsedFor                @map("used_for")
  tier         OrgTier                @map("tier")
  creditCost   Decimal                @map("credit_cost") @db.Decimal(10, 2)
  realCost     Decimal                @map("real_cost") @db.Decimal(15, 10)
  metadata     Json?                  @db.JsonB
  createdAt    DateTime               @default(now()) @map("created_at") @db.Timestamptz(6)
  organization Organization           @relation(fields: [orgId], references: [id], onDelete: Cascade, map: "organization_fk")
  transactions CreditTransactionLog[]

  @@map("credit_usage")
}

model CreditApiPricing {
  id              Int          @id @default(autoincrement())
  tier            OrgTier      @map("tier")
  apiProvider     ApiProvider  @map("api_provider")
  apiOperation    ApiOperation @map("api_operation")
  apiUnit         ApiUnit      @map("api_unit")
  creditCost      Decimal      @map("credit_cost") @db.Decimal(7, 2)
  realCostPerUnit Decimal      @map("real_cost_per_unit") @db.Decimal(15, 10)
  createdAt       DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt       DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)

  @@unique([tier, apiProvider, apiOperation], map: "credit_api_pricing_unique")
  @@map("credit_api_pricing")
}

model CreditTransactionLog {
  id               BigInt          @id @default(autoincrement())
  orgId            String          @map("org_id") @db.Uuid
  type             TransactionType @map("type")
  creditUsageId    BigInt?         @map("credit_usage_id")
  creditPurchaseId Int?            @map("credit_purchase_id")
  creditAmount     Decimal         @map("credit_amount") @db.Decimal(10, 2)
  metadata         Json?           @db.JsonB
  createdAt        DateTime        @default(now()) @map("created_at") @db.Timestamptz(6)
  organization     Organization    @relation(fields: [orgId], references: [id], onDelete: Cascade, map: "organization_fk")
  creditUsage      CreditUsage?    @relation(fields: [creditUsageId], references: [id], map: "credit_usage_fk")
  creditPurchase   CreditPurchase? @relation(fields: [creditPurchaseId], references: [id], map: "credit_purchase_fk")

  @@map("credit_transaction_log")
}

model ScoringModel {
  id            String    @id(map: "scoring_model_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  name          String    @map("name") @db.VarChar(255)
  description   String?   @map("description") @db.Text
  listCompanyId String    @map("list_company_id") @db.Uuid
  createdAt     DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime? @map("updated_at") @db.Timestamptz(6)
  deletedAt     DateTime? @map("deleted_at") @db.Timestamptz(6)
  createdById   String    @map("created_by_id") @db.Uuid
  updatedById   String?   @map("updated_by_id") @db.Uuid

  listCompany  ListCompany      @relation(fields: [listCompanyId], references: [id], map: "list_company_fk")
  createdBy    User             @relation("createdBy", fields: [createdById], references: [id], map: "created_by_user_fk")
  updatedBy    User?            @relation("updatedBy", fields: [updatedById], references: [id], map: "updated_by_user_fk")
  ruleSets     ScoringRuleSet[]
  scoreResults ScoringResult[]

  @@map("scoring_model")
}

model ScoringRuleSet {
  id             String    @id(map: "scoring_rule_set_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  scoringModelId String    @map("scoring_model_id") @db.Uuid
  label          String    @map("label") @db.VarChar(255)
  weight         Int       @map("weight") @db.SmallInt
  defaultScore   Int       @default(0) @map("default_score") @db.SmallInt
  fieldDataId    String    @map("field_data_id") @db.Uuid
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime? @map("updated_at") @db.Timestamptz(6)

  scoringModel ScoringModel     @relation(fields: [scoringModelId], references: [id], map: "scoring_model_fk", onDelete: Cascade)
  fieldData    ScoringFieldData @relation(fields: [fieldDataId], references: [id], map: "scoring_field_data_fk", onDelete: Cascade)
  rules        ScoringRule[]

  @@map("scoring_rule_set")
}

model ScoringRule {
  id        String          @id(map: "scoring_rule_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  ruleSetId String          @map("rule_set_id") @db.Uuid
  operator  ScoringOperator @map("operator")
  value     Json            @map("value") @db.JsonB
  score     Int             @map("score") @db.SmallInt
  createdAt DateTime        @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime?       @map("updated_at") @db.Timestamptz(6)

  ruleSet ScoringRuleSet @relation(fields: [ruleSetId], references: [id], map: "scoring_rule_set_fk", onDelete: Cascade)

  @@map("scoring_rule")
}

model ScoringFieldData {
  id           String             @id(map: "scoring_field_data_pk") @default(dbgenerated("gen_uuid_v7_fn()")) @db.Uuid
  sourceType   ScoringFieldSource @map("source_type")
  fieldType    FieldDataType      @map("field_type")
  fieldPath    String             @map("field_path") @db.VarChar(255)
  enrichmentId String?            @map("enrichment_id") @db.Uuid
  createdAt    DateTime           @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime?          @map("updated_at") @db.Timestamptz(6)

  enrichment Enrichment?      @relation(fields: [enrichmentId], references: [id], map: "enrichment_fk")
  ruleSets   ScoringRuleSet[]

  @@map("scoring_field_data")
}

model ScoringResult {
  id             BigInt    @id(map: "scoring_result_pk") @default(autoincrement())
  entityId       String    @map("entity_id") @db.Uuid
  scoringModelId String    @map("scoring_model_id") @db.Uuid
  executionId    String    @map("execution_id") @db.Uuid
  totalScore     Decimal   @map("total_score") @db.Decimal(5, 2)
  details        Json      @map("details") @db.JsonB
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime? @map("updated_at") @db.Timestamptz(6)

  listCompanyIdea ListCompanyIdea @relation(fields: [entityId], references: [id], onDelete: Cascade, map: "list_company_idea_fk")
  scoringModel    ScoringModel    @relation(fields: [scoringModelId], references: [id], onDelete: Cascade, map: "scoring_model_fk")
  execution       Execution       @relation(fields: [executionId], references: [id], map: "execution_fk")

  @@unique([entityId, scoringModelId], name: "unique_entity_scoring_model")
  @@map("scoring_result")
}

model FirebaseDLQ {
  id       BigInt   @id(map: "firebase_dql_pk") @default(autoincrement())
  payload  Json     @map("payload") @db.JsonB
  failedAt DateTime @default(now()) @map("failed_at") @db.Timestamptz(6)

  @@map("firebase_dlq")
}

enum ListSourceStatus {
  completed
  inProgress
  failed

  @@map("list_source_status")
}

enum ListCompanyStatus {
  completed
  inProgress

  @@map("list_company_status")
}

enum ListSourceType {
  googleMaps
  aiAgent
  resource
  companyFilter
  oceanIo
  sourceScrub
  exa

  @@map("list_source_type")
}

enum PlayStatus {
  preview
  draft
  published

  @@map("play_status")
}

enum ValidProtocols {
  http  @map("http:")
  https @map("https:")

  @@map("valid_protocols")
}

enum VariableType {
  field
  pseudo
  enrichment

  @@map("variable_type")
}

enum EmailMessageStatus {
  raw
  templateerror
  pending
  inqueue
  processing
  sent
  error

  @@map("email_message_status")
}

enum ExecutionType {
  play
  playtask
  enrichment
  enrichmenttask
  system
  sequence
  sequencestep
  channel
  list
  scoring

  @@map("execution_type")
}

enum QueueTaskType {
  enrichment
  systememail
  channeloutput
  play
  listcompany
  scoring

  @@map("queue_type")
}

enum EnrichmentConfigKey {
  AiFieldProcessor
  WebsiteSummary
  LinkedinUrlFinder
  LinkedinPageContentFetcher
  AnyWebContentReader
  CompanyWebsiteReader
  ResearchAgent

  @@map("enrichment_config_key")
}

enum EnrichmentType {
  llm
  agent
  linkedinUrlFinder
  linkedinContentFetcher
  linkedinEmployeeCountFinder
  mixrankCompanyDetails

  @@map("enrichment_type")
}

enum EnrichmentDataType {
  basic
  rich

  @@map("enrichment_data_type")
}

enum EnrichmentProvider {
  acqwired
  linkedin
  mixrank

  @@map("enrichment_provider")
}

enum PlayChannel {
  email
  sms
  handwrittennote

  @@map("play_channel")
}

enum LlmProvider {
  openai
  gemini
  acqwired
  non_ai

  @@map("llm_provider")
}

enum ExecutionStatus {
  scheduled
  pending
  inqueue
  cancelled
  ready
  processing
  completed
  error

  @@map("execution_status")
}

enum EmailStatus {
  draft
  scheduled
  cancelled
  ready
  inqueue
  processing
  sent
  error

  @@map("email_status")
}

enum PromptStatus {
  success
  error

  @@map("prompt_status")
}

enum FieldDataType {
  string
  id
  int
  double
  email
  url
  phone
  date
  datetime
  time
  currency
  boolean
  reference
  picklist
  address
  textarea
  array
  object

  @@map("field_data_type")
}

enum OrgType {
  Private_Equity @map("Private Equity")

  @@map("organization_type")
}

enum CrmType {
  salesforce
  affinity

  @@map("crm_type")
}

enum DataSourceType {
  salesforce
  affinity
  acqwired

  @@map("data_source_type")
}

enum ContactPurpose {
  billing
  administration
  sales
  technical

  @@map("contact_purpose")
}

enum ContactType {
  phone
  email

  @@map("contact_type")
}

enum EmailType {
  initial
  followup
  nonai

  @@map("email_type")
}

enum IntegrationProvider {
  openai
  office365
  salesforce
  affinity

  @@map("integration_provider")
}

enum MembershipType {
  Launch_Partner_L1 @map("Launch Partner L1")
  Pilot_Partner_P1  @map("Pilot Partner P1")

  @@map("membership_type")
}

enum OrgStatus {
  active
  suspended
  expired
  deleted

  @@map("org_status")
}

enum EntityType {
  contact
  company
  idea
  list

  @@map("entity_type")
}

enum EnrichmentEntityType {
  contact
  contactWithoutCompany
  company
  companyWithoutContact

  @@map("enrichment_entity_type")
}

enum TaskType {
  enrichment
  email
  userapproval
  system
  channeloutput

  @@map("task_type")
}

enum ScrapeStatus {
  success
  error

  @@map("scrape_status")
}

enum TokenStatus {
  active
  invalidated

  @@map("token_status")
}

enum UserStatus {
  active
  deleted
  suspended

  @@map("user_status")
}

enum IntegrationType {
  llm
  email
  crm

  @@map("integration_type")
}

enum FieldMetadataType {
  default

  @@map("field_metadata_type")
}

enum StatsType {
  company
  contact
  play
  enrichment

  @@map("stats_type")
}

enum StatsKeyType {
  successCount
  runCount
  errorCount

  @@map("stats_key_type")
}

enum CrmFieldType {
  standard
  custom
  global
  list

  @@map("crm_field_type")
}

enum ActivityType {
  email
  text
  call
  chat
  f2f
  video
  other

  @@map("activity_type")
}

enum ActivityStatus {
  completed
  scheduled
  cancelled
  missed
  unknown
  error

  @@map("activity_status")
}

enum ActivitySource {
  manual
  crm
  acqwired

  @@map("activity_source")
}

enum CrmEntityType {
  task
  event
  interaction

  @@map("crm_entity_type")
}

enum OrgTier {
  tier_launch_partner
  tier_standard

  @@map("org_tier")
}

enum ApiProvider {
  openai
  mixrank

  @@map("api_provider")
}

enum ApiOperation {
  gpt_4o
  get_company
  gpt_4_1
  gpt_4_1_mini

  @@map("api_operation")
}

enum ApiUnit {
  token
  request

  @@map("api_unit")
}

enum UsedFor {
  enrichment
  play
  list

  @@map("used_for")
}

enum TransactionType {
  usage
  purchase
  refund
  expire

  @@map("transaction_type")
}

enum ScoringOperator {
  contain
  not_contain
  starts_with
  ends_with
  equals
  not_equals
  greater_than
  less_than
  between
  not_between

  @@map("scoring_operator")
}

enum ScoringFieldSource {
  mandatory
  enrichment

  @@map("scoring_field_source")
}
