import { But<PERSON>, Spinner } from '@heroui/react';
import React from 'react';

interface SubmitButtonProps {
  onPress?: () => void;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
  isBusy?: boolean;
  text?: string;
  type?: 'button' | 'submit' | 'reset';
}

export const SubmitButton: React.FC<SubmitButtonProps> = ({ onPress, size = 'sm', className = '', disabled = false, isBusy = false, text = 'Submit', type = 'button' }) => {
  return (
    <Button size={size} color="primary" variant="solid" radius="sm" onPress={onPress} isDisabled={disabled || isBusy} className={className} type={type}>
      <div className="relative">
        <span className={isBusy ? 'invisible' : ''}>{text}</span>
        {isBusy && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Spinner color="white" size="sm" />
          </div>
        )}
      </div>
    </Button>
  );
};
