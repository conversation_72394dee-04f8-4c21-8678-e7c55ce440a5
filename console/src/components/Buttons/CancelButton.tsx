import { Button } from '@heroui/react';
import React, { ReactNode } from 'react';

interface CancelButtonProps {
  onPress: () => void;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
  content?: ReactNode;
}

export const CancelButton: React.FC<CancelButtonProps> = ({ onPress, size = 'sm', className = '', disabled = false, content = 'Cancel' }) => {
  return (
    <Button size={size} color="default" variant="light" radius="sm" onPress={onPress} isDisabled={disabled} className={className}>
      {content}
    </Button>
  );
};
