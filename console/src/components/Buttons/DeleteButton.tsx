import { Button } from '@heroui/react';
import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash, faSpinner } from '@fortawesome/free-solid-svg-icons';

interface DeleteButtonProps {
  onPress: () => void;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
  isBusy?: boolean;
  text?: string;
  variant?: 'ghost' | 'solid';
}

export const DeleteButton: React.FC<DeleteButtonProps> = ({ onPress, size = 'sm', className = '', disabled = false, isBusy = false, text = 'Delete', variant = 'ghost' }) => {
  return (
    <Button size={size} color="danger" variant={variant} radius="sm" onPress={onPress} isDisabled={disabled || isBusy} className={className}>
      <div className="relative">
        <span className={isBusy ? 'invisible' : ''}>
          <FontAwesomeIcon icon={faTrash} className="mr-1" /> {text}
        </span>
        {isBusy && (
          <div className="absolute inset-0 flex items-center justify-center">
            <FontAwesomeIcon icon={faSpinner} spin className="mr-1" /> Processing...
          </div>
        )}
      </div>
    </Button>
  );
};
