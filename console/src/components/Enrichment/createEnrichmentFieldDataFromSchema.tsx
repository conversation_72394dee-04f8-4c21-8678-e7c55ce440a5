import { EnrichmentProvider } from '@prisma/client';

export const createEnrichmentFieldDataFromSchema = (
  schema: string | any,
  name: string,
  enrichmentId: string,
  parentPath: string = '$',
  provider?: EnrichmentProvider
): any => {
  const parsedSchema = typeof schema === 'string' ? (JSON.parse(schema)) : schema;
  const basePath = provider === EnrichmentProvider.mixrank ? parentPath :
    parentPath === '$' ? `${parentPath}.result` : parentPath;

  if (!parsedSchema || !parsedSchema.properties || Object.keys(parsedSchema.properties).length === 0) {
    return {
      enrichmentId,
      id: `field_${enrichmentId}_${basePath}`,
      name,
      path: basePath,
      type: 'field',
      tooltip: (
        <div className="text-left text-sm max-w-48 p-2">
          <div className="font-bold">{name}</div>
        </div>
      )
    };
  }

  const items = Object.entries(parsedSchema.properties).map(([key, value]) => {
    const propPath = `${basePath}.${key}`;
    if ((value as any).type === 'object' && (value as any).properties) {
      return createEnrichmentFieldDataFromSchema(value, key, enrichmentId, propPath, provider);
    }
    if ((value as any).type === 'array') {
      const arrayPath = `${propPath}[]`;
      if ((value as any).items?.properties) {
        return createEnrichmentFieldDataFromSchema((value as any).items, key, enrichmentId, arrayPath, provider);
      }
      return {
        enrichmentId,
        id: `field_${enrichmentId}_${arrayPath}`,
        name: key,
        path: arrayPath,
        type: 'field',
        tooltip: (
          <div className="text-left text-sm max-w-48 p-2">
            <div className="font-bold">{key}</div>
            <div className="text-gray-500">{(value as any).description || ''}</div>
          </div>
        )
      };
    }
    return {
      enrichmentId,
      id: `field_${enrichmentId}_${propPath}`,
      name: key,
      path: propPath,
      type: 'field',
      tooltip: (
        <div className="text-left text-sm max-w-48 p-2">
          <div className="font-bold">{key}</div>
          <div className="text-gray-500">{(value as any).description || ''}</div>
        </div>
      )
    };
  });

  if (items.length > 0) {
    return {
      items,
      key: enrichmentId,
      name,
      type: 'subgroup'
    };
  } else {
    return {
      enrichmentId,
      id: `field_${enrichmentId}_${basePath}`,
      name,
      path: basePath,
      type: 'field',
      tooltip: (
        <div className="text-left text-sm max-w-48 p-2">
          <div className="font-bold">{name}</div>
        </div>
      )
    };
  }
};
