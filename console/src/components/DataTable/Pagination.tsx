import { Button, ButtonGroup, Pagination } from '@heroui/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAngleDoubleLeft, faAngleDoubleRight } from '@fortawesome/free-solid-svg-icons';
import React from 'react';
import { ITablePagination } from '../../types/StoredTable';

export interface IDataTablePaginationArgs {
  pagination: ITablePagination;
  setPage: (page: number) => void;
}

export function DataTablePagination({ pagination, setPage }: IDataTablePaginationArgs) {
  return (
    <div className="__acqwired_datatable_pagination_container pt-[6px] pb-[18px] flex flex-row justify-center gap-10 text-sm items-center">
      <ButtonGroup radius="sm">
        <Button
          isIconOnly
          className="shadow disabled:text-gray-300 hover:bg-gray-300 text-gray-600 min-w-9 box-border h-9 w-9 bg-default-100 text-small active:bg-default-300"
          onPress={() => {
            setPage(1);
          }}
          isDisabled={pagination.page === 0}
        >
          <FontAwesomeIcon icon={faAngleDoubleLeft} />
        </Button>
        <Pagination radius="none" isCompact showShadow showControls page={pagination.page + 1} total={pagination.totalPages} onChange={setPage} />
        <Button
          isIconOnly
          className="shadow disabled:text-gray-300 hover:bg-gray-300 text-gray-600 min-w-9 box-border h-9 w-9 bg-default-100 text-small outline-none active:bg-default-300"
          onPress={() => {
            setPage(pagination.totalPages);
          }}
          isDisabled={pagination.page === pagination.totalPages - 1}
        >
          <FontAwesomeIcon icon={faAngleDoubleRight} />
        </Button>
      </ButtonGroup>

      <div className="text-sm">
        Page <strong>{pagination.page + 1}</strong> of <strong>{pagination.totalPages}</strong>
      </div>

      <select
        data-rerender={pagination.page + 1}
        className="px-5 py-2 rounded-md bg-white border-1"
        value={pagination.page + 1}
        onChange={(e) => {
          setPage(parseInt(e.target.value));
        }}
      >
        {Array.from({ length: pagination.totalPages }, (_, i) => (
          <option key={i + 1} value={i + 1}>
            {i + 1}
          </option>
        ))}
      </select>
    </div>
  );
}
