import React, { useContext, useEffect, useState } from 'react';
import { AuthContext } from '../../context';
import { Contact, EmailStatus } from '@prisma/client';
import DraftEmailComponent from './DraftEmailComponent';
import { Uuid } from '../../lib/CommonTypes';
import { NewContactModal } from '../NewContactModal';
import { useFirebase } from '../../context/FirebaseContext';
import { useAcqwired } from '../../context/AcqwiredContext';
import { DraftItem } from '../../types/Email';

interface TemplateData {
  id: string;
  name: string;
  type: string;
}

export interface PlayResponse {
  id: string;
  name: string;
  description: string;
  template: TemplateData;
  ai: boolean;
}

const DraftEmailPanel = () => {
  const { userData } = useContext(AuthContext);
  const acqwired = useAcqwired();
  const playStore = acqwired.store.play;
  const emailStore = acqwired.store.email;
  const integrationStore = acqwired.store.integration;

  const [selectedDraftIndex, setSelectedDraftIndex] = useState<number>(0);

  const [showNewContactModal, setShowNewContactModal] = useState(false);

  const updateListItem = <K extends keyof DraftItem>(id: K, value: DraftItem[K], index: number) => {
    const updatedItem = { [id]: value };
    emailStore.update(updatedItem, index);
  };

  useEffect(() => {
    if (!integrationStore.ready) {
      return;
    }
    const { message, status } = integrationStore.getOffice365Status();
    emailStore.get({ email: userData?.email, message, status });
  }, [integrationStore.integrations]);

  const firebaseCtx = useFirebase();
  useEffect(() => {
    if (firebaseCtx.readyToFetchDrafts) {
      firebaseCtx.setReadyToFetchDrafts(false);
      const { message, status } = integrationStore.getOffice365Status();
      emailStore.getUpdatedPanel({ email: userData?.email, message, status });
    }
  }, [firebaseCtx.readyToFetchDrafts]);


  return (
    <>
      {emailStore.items
        .filter((data) => ![EmailStatus.cancelled, EmailStatus.sent, EmailStatus.scheduled as string].includes(data.status))
        .map((data, index: number) => (
          <DraftEmailComponent
            data={data}
            key={index}
            modalKey={index}
            draftList={emailStore.items}
            playData={playStore.playsList}
            onNewContact={() => {
              setShowNewContactModal(true);
              setSelectedDraftIndex(index);
            }}
          />
        ))}


      {showNewContactModal && (
        <NewContactModal
          onClose={() => setShowNewContactModal(false)}
          onSuccess={({ email, contact, companyId }: { email: string; contact: Contact; companyId: Uuid }) => {
            updateListItem('to', email, selectedDraftIndex);
            updateListItem('contact', { id: contact.id, firstName: contact.firstName, lastName: contact.lastName, email: contact.email, entityMapping: [] }, selectedDraftIndex);
            updateListItem('companyId', companyId, selectedDraftIndex);
          }}
          isNew
        />
      )}
    </>
  );
};

export default DraftEmailPanel;
