import React, { useContext, useEffect } from 'react';
import DraftEmailPanel from './DraftEmailPanel';
import { AuthContext } from '../../context';
import { useAcqwired } from '../../context/AcqwiredContext';

const DraftMainComponent = ({ children }) => {
  const { userData } = useContext(AuthContext);
  const acqwired = useAcqwired();
  const emailStore = acqwired.store.email;

  useEffect(() => {
    if (userData?.email) {
      emailStore.setUserEmail(userData.email);
    }
  }, [userData]);

  return (
    <>
      {children}
      <DraftEmailPanel />
    </>
  );
};

export default DraftMainComponent;
