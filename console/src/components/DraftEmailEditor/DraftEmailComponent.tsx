import React, { useEffect, useState } from 'react';
import { AlertModal } from '..';
import EmailEditor from '../PrimeReactEditor/PrimeReactEditor';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faWindowMaximize, faWindowMinimize, faXmark, faAngleUp, faAngleDown, faCircleExclamation, faCircleCheck, faExclamationTriangle, faSpinner } from '@fortawesome/free-solid-svg-icons';
import { PrimaryEntity } from '../PrimaryEntity';
import { Tags } from '../Tags';
import Patterns from '../../utils/patterns';
import { ENTITY_TYPE, PrimaryEntityType } from '../../constants';
import SelectDropDown, { IOptions } from '../SelectDropDown';
import { Uuid } from '../../lib/CommonTypes';
import { EmailStatus, EntityType } from '@prisma/client';
import { debounce } from 'lodash';
import { IBasicContactData } from '../Drawer/types';
import { IPlay } from '../../lib/PlayTypes';
import { useAcqwired } from '../../context/AcqwiredContext';
import lodash from 'lodash';
import { DrawerVisibility } from '../../types/Drawer';
import { DraftItem } from '../../types/Email';
import { IntegrationStatus } from '../../types/Integration';
import { useExecution } from '../../context/ExecutionContext';
import { AlertModalType } from '../AlertModal';
import { IContactInfo } from '../../types/Contact';
import { Button } from '@heroui/react';

interface DraftEmailComponentProps {
  data: DraftItem;
  playData: IPlay[];
  modalKey: number;
  onNewContact: () => void;
  draftList: DraftItem[];
}

const DraftEmailComponent = ({ data, modalKey, onNewContact, playData, draftList }: DraftEmailComponentProps) => {
  const [isLoading, setLoading] = useState(false);
  const [rightPosition, setRightPosition] = useState(0);
  const [confirmRegenerateModal, confirmShowRegenerateModal] = useState<boolean>(false);
  const [deleteModal, setDeleteModal] = useState<boolean>(false);
  const acqwired = useAcqwired();
  const execution = useExecution();
  const emailStore = acqwired.store.email;
  const playStore = acqwired.store.play;
  const toast = acqwired.toast;

  const handleSendEmailToSelf = async (data: DraftItem) => {
    if (data.emailBody) {
      setLoading(true);
      return emailStore
        .selfEmail(data)
        .then((response) => {
          if (response.success) {
            toast.success('Email sent successfully.');
          }
          if (!response.message) {
            toast.error(response.message);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
    toast.error('Information is not valid. Please fill the information first.', { timeout: 0 });
  };

  const requiredFields = () => {
    const fields = {
      emailBody: 'Email Body',
      to: "Recipient ('to')",
      companyId: 'Company',
      contactId: 'Contact'
    };

    lodash.forEach(fields, (errorMessage, field) => {
      if (!data[field]) {
        toast.error(`${errorMessage} is required.`);
      }
    });
  };

  const sendNow = async (data: DraftItem) => {
    if (!data.emailBody || !data.to) {
      toast.error('Information is not valid. Please fill the information first.');
      return;
    }

    if (data.emailBody && data.to && data.companyId && data.contact) {
      setLoading(true);

      return emailStore
        .send(modalKey)
        .then((response) => {
          if (!response.success) {
            toast.error(response.message);
            return;
          }
          toast.success('Email scheduled successfully.');
          emailStore.delete(modalKey);
          if (acqwired.drawer.entityType === EntityType.company) {
            acqwired.store.company.reloadTable();
          } else {
            acqwired.store.contact.reloadTable();
          }
        })
        .catch(console.error)
        .finally(() => {
          setLoading(false);
        });
    } else {
      requiredFields();
    }
  };

  const updateOnChange = debounce(() => {
    return emailStore.saveOnChange(modalKey);
  }, 500);

  useEffect(() => {
    updateOnChange();
    return () => {
      updateOnChange.cancel();
    };
  }, [data.to, data.subject, data.emailBody]);

  const isEmailValid = (email: string): boolean => {
    return Patterns.Email.test(email);
  };

  const generateAiEmail = async () => {
    emailStore.generate(data, modalKey).then((response) => {
      if (!response.success) {
        onHandleFailure(response.message);
        return;
      }
      onHandleSuccess(data, modalKey);
    });
  };

  const onHandleSuccess = async (data: Partial<DraftItem>, index: number) => {
    execution
      .executePlay({ playId: (data.play as IPlay).id as Uuid, targetIds: [(data.contact as IContactInfo).id!], entityType: EntityType.contact, executionId: data.executionId })
      .then((result) => {
        if (!result.length) {
          toast.success('Unable to create play.');
          return;
        }

        const hasFailure = lodash.some(result, (data) => !data.success);

        if (hasFailure) {
          toast.error('Invalid contact or company.');
        } else {
          toast.success('Play execution started.');
        }

        result
          .filter((data) => data.success)
          .map((result) => {
            const updatedData: Partial<DraftItem> = {
              play: data.play,
              isAiEmailGenerated: true,
              status: EmailStatus.processing,
              executionId: result.playExecutionId,
              statusMessage: ''
            };
            emailStore.update(updatedData, index); // Pass only the updated fields and index
          });
      })
      .catch(() => {
        toast.error('Play execution failed.');
      });
  };

  const onHandleFailure = (message: string) => {
    toast.error(message);
  };

  const DraftEmailPanelHeader = ({ className, title, style, isMin, status, id }) => {
    return (
      <div
        onClick={() => {
          if (isMin) {
            emailStore.toggleExpand(modalKey);
          }
        }}
        style={style}
        className={`${className ?? className} flex items-center bg-acq-600 justify-between px-4 py-1.5 text-white text-base rounded-t-lg`}
      >
        {isMin ? (
          <div className={`absolute ${status === EmailStatus.error || (status === EmailStatus.draft && id) ? 'border rounded-xl bg-white' : ''} p-[2px] ml-[-22px] ${isMin ? 'mb-[45px]' : 'mt-[-25px]'}`}>
            {status === EmailStatus.error ? <FontAwesomeIcon icon={faCircleExclamation} size="xl" style={{ color: '#d0342c' }} /> : null}
            {status === EmailStatus.draft && id ? <FontAwesomeIcon icon={faCircleCheck} size="xl" style={{ color: '#00CA14' }} /> : null}
          </div>
        ) : null}
        <div className="font-semibold overflow-hidden whitespace-nowrap text-ellipsis">{title}</div>
        <div className="flex">
          {isMin ? (
            <>
              <Button
                onPress={() => {
                  emailStore.toggleExpand(modalKey);
                }}
                size="sm"
                variant="light"
                className="min-w-1 px-3"
              >
                <FontAwesomeIcon icon={faWindowMaximize} size="lg" color="white" />
              </Button>

              <Button
                onPress={() => {
                  setDeleteModal(true);
                }}
                size="sm"
                variant="light"
                className="min-w-1 px-3"
              >
                <FontAwesomeIcon icon={faXmark} size="lg" color="white" />
              </Button>
            </>
          ) : (
            <>
              <Button
                onPress={() => {
                  emailStore.toggleExpand(modalKey);
                }}
                size="sm"
                variant="light"
                className="min-w-1 px-3"
              >
                <FontAwesomeIcon icon={faWindowMinimize} size="lg" color="white" />
              </Button>
              <Button
                onPress={() => {
                  setDeleteModal(true);
                }}
                size="sm"
                variant="light"
                className="min-w-1 px-3"
              >
                <FontAwesomeIcon icon={faXmark} size="lg" color="white" />
              </Button>
            </>
          )}
        </div>
      </div>
    );
  };

  const generateEmailCheck = () => {
    if (!data.executionId) {
      return;
    }
    emailStore.verifyAiEmail(modalKey);
  };

  const checkEmail = debounce(() => {
    return generateEmailCheck();
  }, 500);

  useEffect(() => {
    checkEmail();
    return () => {
      checkEmail.cancel();
    };
  }, [data.contact]);

  const onConfirmDelete = async () => {
    emailStore.cancel(modalKey).then((response) => {
      if (response.success) {
        if (data.channelEmailId) {
          emailStore.updateStatus({ id: data.channelEmailId!, status: EmailStatus.cancelled }).then((response) => {
            if (!response) {
              toast.error('Failed to update email status.');
            }
          });
        }
        emailStore.delete(modalKey);
        setDeleteModal(false);
      } else {
        toast.error(response.message);
      }
    });
  };

  const EmailErrorBox = ({ message, status }) => {
    const hasError = status === EmailStatus.error;
    const hasWarning = status === IntegrationStatus.Warning;

    return (
      <div className="flex justify-center">
        <div className={`mt-2 flex items-center rounded-md w-[95%] pl-5 pr-5 justify-between p-1 ${hasError ? 'bg-red-700' : 'bg-amber-300'}`}>
          {hasError ? (
            <>
              <span className="text-sm font-semibold text-white leading-10">{message ? 'Error' : 'Send Error'}</span>
              <span className="flex items-center text-sm text-acqgray-100 pl-4 w-4/5">{message || 'Please check Office365 settings and try again'}</span>
              <FontAwesomeIcon icon={faExclamationTriangle} className="text-white" />
            </>
          ) : hasWarning ? (
            <>
              <span className="flex items-center text-sm font-semibold text-black leading-10">
                Warning
                <FontAwesomeIcon icon={faExclamationTriangle} className="text-black pl-2" />
              </span>
              <span className="flex items-center text-sm text-black pl-4 w-full">{message}</span>
            </>
          ) : null}
        </div>
      </div>
    );
  };

  useEffect(() => {
    let position = 0;

    for (let i = 0; i < modalKey; i++) {
      if (draftList[i].expand) {
        position += 600;
      } else {
        position += 250;
      }
      position += 10;
    }

    setRightPosition(position);
  }, [data.expand, modalKey, draftList]);

  return (
    <>
      {data.expand && (
        <div
          className={`z-40 fixed bottom-0 overflow-x-auto rounded-t-lg w-[600px] h-[610px] bg-white shadow-md shadow-gray-500 ml-2 ${!acqwired.drawer.entityId ? '' : acqwired.drawer.visibility === DrawerVisibility.Visible ? 'mr-[412px]' : acqwired.drawer.visibility === DrawerVisibility.Collapsed ? 'mr-[62px]' : ''}`}
          style={{ right: `${rightPosition}px` }}
        >
          <div className="fixed w-[600px] z-50">
            <DraftEmailPanelHeader style={{}} className="" title={data.title} isMin={false} status={data.status} id={data.channelEmailId} />
          </div>

          {data.status === EmailStatus.ready || data.status === EmailStatus.inqueue || data.status === EmailStatus.processing ? (
            <div className="absolute z-40 items-center justify-center bg-slate-200 bg-opacity-50 h-full w-[inherit] flex">
              <FontAwesomeIcon icon={faSpinner} size="xl" spin />
            </div>
          ) : null}
          <div className="mt-10 h-[510px] overflow-hidden overflow-y-auto	">
            <div className={`${data.showToFromCC ? '' : 'hidden'}`}>
              <div className="mt-4 flex">
                <div className="w-14 mr-2 text-right">
                  <p className="text-sm font-normal text-gray-900">From:</p>
                </div>
                <div className="w-1/3 pl-1">
                  <p className="text-sm text-gray-900">{data.from}</p>
                </div>
              </div>
              <div className="mt-2 flex items-center">
                <div className="w-14 mr-2 text-right">
                  <p className="text-sm font-normal text-gray-900">To:</p>
                </div>
                <PrimaryEntity
                  disabled={data.readOnly}
                  showModal={true}
                  entity={ENTITY_TYPE.COMPANY}
                  selectedEntity={data.contact as IBasicContactData}
                  onChange={(value) => {
                    if (value?.id === PrimaryEntityType.newContact) {
                      onNewContact();
                      return;
                    }
                    emailStore.updateEntity(modalKey, value);
                  }}
                  className="w-[525px]"
                />
              </div>
              <div className="mt-2 flex items-center">
                <div className="w-14 mr-2 text-right">
                  <p className="text-sm font-normal text-gray-900">Cc:</p>
                </div>
                <div className="w-[525px]">
                  <Tags
                    entity="email addresses"
                    tags={data.cc}
                    placeHolder="Type an email address"
                    onUpdate={(values) => emailStore.updateCC(values, modalKey)}
                    isTagValid={(value: string) => isEmailValid(value)}
                    onError={(hasError) => emailStore.setErrorState(hasError || data.status === EmailStatus.error, modalKey)}
                    disabled={data.readOnly}
                  />
                </div>
              </div>
            </div>

            <div className="mt-2 flex items-center">
              <div className="w-14 mr-2 text-right">
                <p className="text-sm font-normal text-gray-900">Subject:</p>
              </div>
              <div className="flex justify-between items-center w-[525px]">
                <input
                  disabled={data.readOnly}
                  name="Subject"
                  type="text"
                  required={true}
                  autoComplete="off"
                  value={data.subject}
                  onChange={(event) => {
                    emailStore.updateSubject(event.target.value, modalKey);
                  }}
                  placeholder="Subject"
                  className="w-full text-sm bg-white rounded-md border p-2 border-slate-200 focus:border-acq-600 text-acqgray-900"
                ></input>
              </div>
            </div>

            <div className="mt-2 flex items-center">
              <div className="w-14 mr-2 text-right">
                <p className="text-sm font-bold text-acq-500">Play:</p>
              </div>

              <div className="flex justify-between items-center w-[525px] gap-2">
                <div className="w-[350px]">
                  <SelectDropDown disabled={data.readOnly} label="Select Play" value={playData.find((a) => a.id === data.play?.id)} options={playData} onChange={(value: IOptions) => emailStore.updatePlay(value as IPlay, modalKey)} />
                </div>
                {data.isAiEmailGenerated ? (
                  <div className="">
                    <Button
                      variant="faded"
                      size="sm"
                      onPress={() => {
                        confirmShowRegenerateModal(true);
                      }}
                      isDisabled={!data.play}
                    >
                      Regenerate
                    </Button>
                  </div>
                ) : (
                  <div>
                    <Button variant="faded" size="sm" isDisabled={!data.play || !data.to || data.readOnly} onPress={generateAiEmail}>
                      Generate
                    </Button>
                  </div>
                )}

                <Button size="sm" variant="light" onPress={() => emailStore.setToFromCc(modalKey)}>
                  {data.showToFromCC ? <FontAwesomeIcon icon={faAngleUp} title="Hide Options" /> : <FontAwesomeIcon icon={faAngleDown} title="Show Options" />}
                </Button>
              </div>
            </div>

            {data.status === EmailStatus.error || data.status === IntegrationStatus.Warning ? (
              <>
                <div className="mt-[15px] mx-[12px]">
                  <hr />
                </div>
                <EmailErrorBox message={data.statusMessage} status={data.status} />
              </>
            ) : null}

            <div className="mt-4">
              <EmailEditor
                initialText={data.emailBody}
                onTextChange={(text) => emailStore.updateBody(text, modalKey)}
                editorClass={` ml-3 ${!data.showToFromCC ? `${data.status === EmailStatus.error || data.statusMessage ? 'h-[18.4rem]' : 'h-[22.9rem]'} w-[577px]` : 'h-[15rem] w-[577px]'}`}
                headerContainerClass={`h-[17.5rem]`}
              />
            </div>
          </div>

          <div className={`fixed bottom-0 w-[577px] ml-3 flex items-center justify-between pb-2.5	`}>
            <div className="">
              <Button
                size="sm"
                variant="bordered"
                onPress={() => {
                  setDeleteModal(true);
                }}
                isDisabled={data.readOnly}
              >
                Discard
              </Button>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="bordered" isDisabled={data.hasError || data.readOnly} onPress={() => handleSendEmailToSelf(data)}>
                {isLoading ? 'Please Wait.....' : 'Send Test Email To Myself'}
              </Button>
              <Button size="sm" variant="bordered" isDisabled={data.hasError || data.readOnly} onPress={() => sendNow(data)}>
                {isLoading ? 'Please Wait.....' : 'Send Now'}
              </Button>
            </div>
          </div>
        </div>
      )}
      {!data.expand && (
        <DraftEmailPanelHeader
          className={`z-40 cursor-pointer h-[40px] w-[250px] shadow-md items-center justify-between mt-auto fixed bottom-0 ${!acqwired.drawer.entityId ? '' : acqwired.drawer.visibility === DrawerVisibility.Visible ? 'mr-[412px]' : acqwired.drawer.visibility === DrawerVisibility.Collapsed ? 'mr-[62px]' : ''}`}
          style={{ right: `${rightPosition}px` }}
          title={data.title}
          isMin={true}
          status={data.status}
          id={data.channelEmailId}
        />
      )}

      <AlertModal
        confirmOnClick={() => {
          confirmShowRegenerateModal(false);
          void generateAiEmail();
        }}
        showModal={confirmRegenerateModal}
        setShowModal={confirmShowRegenerateModal}
        type={AlertModalType.REGENERATE}
        text="Regenerate"
        buttonText="Yes, Regenerate"
        secondText={`" ${playStore.playsList.find((a) => a.id === data?.play?.id)?.name} "`}
        cancelOnClick={() => {
          confirmShowRegenerateModal(!confirmRegenerateModal);
        }}
      />

      <AlertModal
        confirmOnClick={onConfirmDelete}
        showModal={deleteModal}
        setShowModal={setDeleteModal}
        type={AlertModalType.DELETE}
        text="Discard Email?"
        buttonText="Yes, Delete"
        cancelOnClick={() => {
          setDeleteModal(!deleteModal);
        }}
      />
    </>
  );
};

export default DraftEmailComponent;
