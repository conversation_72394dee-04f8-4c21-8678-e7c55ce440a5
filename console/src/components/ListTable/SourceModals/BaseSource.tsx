import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { useAcqwired } from '../../../context/AcqwiredContext';
import { SourceFooter } from '../../Lists/SourceList/SourceFooter';
import { ListSourceComponentProps } from '../../../types/List';
import { PreviewResultFooter } from '../../Lists/SourceList/PreviewResultFooter';

export interface BaseSourceParameters {
  name?: string;
  [key: string]: any;
}

export interface BaseSourceState {
  isBusy: boolean;
  error: string | null;
  previewData: any[] | null;
}

export interface BaseSourceChildProps<P> {
  createSource: (params: P) => Promise<void>;
  isBusy: boolean;
  handleSubmit: () => Promise<void>;
  onSave: () => Promise<boolean>;
  showFooter: boolean;
}

export interface BaseSourceProps<P extends BaseSourceParameters = BaseSourceParameters> extends Omit<ListSourceComponentProps, 'onSourceCreated'> {
  /** Render function for the source component's content */
  children: (props: BaseSourceChildProps<P>) => React.ReactNode;
  /** Optional validation function for source parameters */
  validateParameters?: (params: P) => string | null;
  /** Function to get the current source parameters */
  getSourceParameters: () => P;
  /** Optional form validation function */
  validateForm?: () => Promise<boolean>;
  /** Optional callback after source creation */
  onSourceCreated?: (data: { id: string; listId: string; createdListId?: string }) => Promise<void>;
  /** Whether to hide the footer */
  hideFooter?: boolean;
  /** If true, enables the preview feature */
  showPreviewFooter?: boolean;
  onPreviewResultClick?: () => Promise<void>;
  isPreviewResults?: boolean;
  isPreviewButtonDisabled?: boolean;
}

export const BaseSource = <P extends BaseSourceParameters = BaseSourceParameters>({
  children,
  validateParameters = () => null,
  getSourceParameters,
  validateForm = async () => true,
  onSourceCreated,
  hideFooter,
  showPreviewFooter = false,
  onPreviewResultClick = async () => {},
  isPreviewResults = false,
  isPreviewButtonDisabled = false,
  ...props
}: BaseSourceProps<P>) => {
  const acqwired = useAcqwired();
  const listStore = acqwired.store.list;
  const router = useRouter();
  const toast = acqwired.toast;
  const [state, setState] = useState<BaseSourceState>({
    isBusy: false,
    error: null,
    previewData: null
  });

  const createSource = async (parameters: P): Promise<void> => {
    const validationError = validateParameters(parameters);
    if (validationError) {
      toast.error(validationError);
      return;
    }

    setState((prev) => ({ ...prev, isBusy: true, error: null }));

    try {
      const data = await listStore.createSource({
        intentionSourceId: props.intentionSource?.id,
        source: {
          sourceType: props.intentionSource.sourceType,
          parameters
        },
        file: parameters.file,
        createList: true
      });

      if (!data?.id) {
        toast.error('Error creating source');
        return;
      }

      // Handle toast notifications based on source status and results
      // Check if we have sourceResult data to determine actual completion status
      if (data.sourceResult && data.sourceResult.totalProcessed > 0) {
        // We have processing results, show detailed feedback regardless of status
        if (data.sourceResult.success) {
          // Success toast with detailed numbers
          const { successCount, failureCount, skippedCount } = data.sourceResult;
          let message = `Successfully processed ${successCount} companies`;

          if (skippedCount > 0 || failureCount > 0) {
            const details: string[] = [];
            if (skippedCount > 0) details.push(`${skippedCount} duplicates skipped`);
            if (failureCount > 0) details.push(`${failureCount} failed`);
            message += ` (${details.join(', ')})`;
          }

          toast.success(message);
        } else {
          // Processing completed but with errors
          toast.error(`Source creation completed with errors: ${data.sourceResult?.failures?.[0]?.reason || 'Unknown error'}`);
        }
      } else if (data.status === 'failed') {
        // Failed toast
        const errorReason = data.sourceResult?.failures?.[0]?.reason || 'Unknown error';
        toast.error(`Source creation failed: ${errorReason}`);
      }

      if (onSourceCreated) {
        await onSourceCreated(data);
      }

      listStore.reloadSingleList();

      if (data.createdListId) {
        router.push(`/list-company/${data.createdListId}`);
      }

      props.onCancel();
    } catch (error) {
      toast.error('Error creating source');
      setState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : 'An unexpected error occurred'
      }));
    } finally {
      setState((prev) => ({ ...prev, isBusy: false }));
    }
  };

  const onSave = async (): Promise<boolean> => {
    const isValid = await validateForm();
    if (!isValid) {
      return false;
    }
    const parameters = getSourceParameters();
    await createSource(parameters);
    return true;
  };

  const handleSubmit = async () => {
    await onSave();
  };

  const childProps: BaseSourceChildProps<P> = {
    createSource,
    isBusy: state.isBusy,
    handleSubmit,
    onSave,
    showFooter: !hideFooter
  };

  return (
    <div>
      {children(childProps)}
      {!hideFooter && <SourceFooter isBusy={state.isBusy} onCancel={props.onCancel} onSave={handleSubmit} />}
      {showPreviewFooter && <PreviewResultFooter onCancel={props.onCancel} onSave={() => (isPreviewResults ? handleSubmit() : onPreviewResultClick())} isBusy={state.isBusy} isDisabled={isPreviewButtonDisabled} isPreviewResults={isPreviewResults} />}
    </div>
  );
};
