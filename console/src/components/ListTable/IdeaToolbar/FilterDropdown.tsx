import React, { useEffect, useMemo, useState, useRef } from 'react';
import {
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownSection,
  DropdownItem,
  Button,
  SharedSelection,
  Badge,
} from '@heroui/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faLightbulb, faTimes, faFilter } from '@fortawesome/free-solid-svg-icons';
import { AcqwiredIcon } from '../../AcqwiredIcon';
import { useAcqwired } from '../../../context/AcqwiredContext';
import { TableFilterConditions } from '../../../types/StoredTable';

type FilterKey = 'inShortlist' | 'recordStatus';

interface FilterOption {
  key: string;
  label: string;
  icon: React.ReactNode;
  filterKey: FilterKey;
}

export function IdeaTableFilterDropdown() {
  const acqwired = useAcqwired();
  const ideaStore = acqwired?.store?.idea;

  const [selectedKeys, setSelectedKeys] = useState<SharedSelection>(new Set());

  const prevShortlistRef = useRef<string[]>([]);
  const prevStatusRef = useRef<string[]>([]);

  const options: FilterOption[] = useMemo(
    () => [
      {
        key: 'true',
        label: 'Shortlisted',
        icon: <FontAwesomeIcon icon={faCheck} className="text-success" fixedWidth />,
        filterKey: 'inShortlist',
      },
      {
        key: 'false',
        label: 'Not Shortlisted',
        icon: <FontAwesomeIcon icon={faTimes} className="text-gray-400" fixedWidth />,
        filterKey: 'inShortlist',
      },
      {
        key: 'inAcqwired',
        label: 'In Acqwired',
        icon: <AcqwiredIcon icon="acqwiredLogo" size="sm" className="text-acq-500" />,
        filterKey: 'recordStatus',
      },
      {
        key: 'idea',
        label: 'Valid Ideas',
        icon: <FontAwesomeIcon icon={faLightbulb} className="text-orange-400" fixedWidth />,
        filterKey: 'recordStatus',
      },
      {
        key: 'invalid',
        label: 'Invalid Ideas',
        icon: <FontAwesomeIcon icon={faTimes} className="text-danger" fixedWidth />,
        filterKey: 'recordStatus',
      },
    ],
    []
  );

  useEffect(() => {
    const shortlist = Array.from(selectedKeys).filter((key) => key === 'true' || key === 'false') as string[];
    const status = Array.from(selectedKeys).filter((key) =>
      ['idea', 'inAcqwired', 'invalid'].includes(key as string)
    ) as string [];

    const shortlistChanged =
      shortlist.length !== prevShortlistRef.current.length ||
      shortlist.some((v, i) => v !== prevShortlistRef.current[i]);
    const statusChanged =
      status.length !== prevStatusRef.current.length ||
      status.some((v, i) => v !== prevStatusRef.current[i]);

    if (shortlistChanged) {
      ideaStore?.addIdeaTableFilter({
        key: 'inShortlist',
        condition: TableFilterConditions.In,
        data: shortlist,
        unique: true,
      });
      prevShortlistRef.current = shortlist;
    }

    if (statusChanged) {
      ideaStore?.addIdeaTableFilter({
        key: 'recordStatus',
        condition: TableFilterConditions.In,
        data: status,
        unique: true,
      });
      prevStatusRef.current = status;
    }
  }, [selectedKeys, ideaStore]);

  useEffect(() => {
    setSelectedKeys(new Set());
  }, [ideaStore?.resetFilters]);

  const selectedCount = selectedKeys === 'all' ? options.length : selectedKeys.size;

  return (
    <Badge size="sm" variant="solid" color="primary" className="absolute" aria-label={`${selectedCount} filters selected`} content={selectedCount} isInvisible={!(selectedCount > 0)} placement="top-right">
      <Dropdown placement="bottom-start" radius="sm">
        <DropdownTrigger>
          <Button variant="flat" size="sm" className="flex items-center justify-center bg-white text-gray-700 min-w-8 p-0 hover:bg-gray-200" aria-label={`${selectedCount} filters selected`}>
            <FontAwesomeIcon icon={faFilter} size="xl" fixedWidth />
          </Button>
        </DropdownTrigger>

        <DropdownMenu
          aria-label="Combined Filter"
          variant="bordered"
          closeOnSelect={false}
          selectionMode="multiple"
          selectedKeys={selectedKeys}
          onSelectionChange={setSelectedKeys}
        >
          <DropdownSection title="Shortlist">
            {options
              .filter((o) => o.filterKey === 'inShortlist')
              .map((option) => (
                <DropdownItem variant="faded" key={option.key} startContent={option.icon}>
                  {option.label}
                </DropdownItem>
              ))}
          </DropdownSection>
          <DropdownSection title="Status">
            {options
              .filter((o) => o.filterKey === 'recordStatus')
              .map((option) => (
                <DropdownItem variant="faded" key={option.key} startContent={option.icon}>
                  {option.label}
                </DropdownItem>
              ))}
          </DropdownSection>
        </DropdownMenu>
      </Dropdown>
    </Badge>
  );
}

