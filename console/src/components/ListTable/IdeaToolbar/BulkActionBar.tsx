import { useMemo } from 'react';
import { useAcqwired } from '../../../context/AcqwiredContext';

interface SelectionBarProps {
  isResult?: boolean;
}

export const BulkActionBar = ({ isResult = false }: SelectionBarProps) => {

  const acqwired = useAcqwired();
  const ideaStore = acqwired.store.idea;
  const listStore = acqwired.store.list;
  const showBulkActionBar = ideaStore.showBulkActionBar;
  const pagination = ideaStore.ideaTable.pagination;
  const totalPages = pagination.totalPages;
  const totalRecords = pagination.totalRecords;

  const validIdeas = useMemo(() => {
    return ideaStore.retrieveFilteredSelectedIdeaIds(listStore.selectedSources).length;
  }, [ideaStore.integratedIdeas, ideaStore.selectedIdeas, listStore.selectedSources, ideaStore.allSelected]);

  return <>
    {!isResult && totalPages > 1 && showBulkActionBar ? (
      <div className="flex flex-row justify-center">
        <div className="min-w-8 bg-acqgray-50 rounded-lg gap-1 py-1 px-2 flex flex-row items-center justify-center text-base">
          {ideaStore.allSelected ? (
            <>
              <b>{totalRecords}</b>companies are selected.
              <span className="text-acq-500 cursor-pointer" onClick={() => { ideaStore.clearSelected(); ideaStore.hideBulkActionBar(); }}>
                Clear selection
              </span>
            </>
          ) : (
            <>
              <span><b>{validIdeas}</b> companies are selected.</span>
              <span data-hover="false" className="text-acq-500 cursor-pointer" onClick={() => ideaStore.selectAllAcrossPages()}>
                Select all {totalRecords} companies in list. {/* listStore.selectedList?.name */}
              </span>
            </>
          )}
        </div>
      </div>
    ) : null}
  </>;
};
