import { faSpinner } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React from "react";

export const AccountFilterResult = ({
  isLoadingFilterResults,
  filteredObjects,
}) => {
  return (
    <div className="py-1">
      <div className="mb-2 font-bold text-lg flex gap-2 items-center">
        Sample Matching Results{" "}
        {isLoadingFilterResults && (
          <FontAwesomeIcon
            icon={faSpinner}
            className="fa-spinner fa-spin fa-md"
          />
        )}
      </div>
      <div className="mb-4 text-sm text-acqgray-600">
        Sample results of the filters you created to the left
      </div>
      <table className="text-sm w-full h-12 text-left">
        <thead className="bg-slate-50 p-3 max-w-0 min-w-[1rem] w-2 cursor-pointer whitespace-nowrap overflow-hidden text-ellipsis">
          <tr>
            <th className="p-3">Company Name</th>
            <th className="p-3">Company URL</th>
          </tr>
        </thead>
        <tbody>
          {filteredObjects?.slice(0, 5).map((object, i) => {
            return (
              <tr key={i} className="cursor-pointer hover-bg-slate-50 border-b border-gray-200">
                <td scope="row" className="p-3">
                  <span className="break-words">{`${object?.Name}`}</span>
                </td>
                <td className="p-3">
                  <span className="break-words">{object?.Website}</span>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};
