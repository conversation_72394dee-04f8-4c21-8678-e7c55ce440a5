import { useEffect, useState } from "react";
import CrmFilterModal from "./CrmFilterModal";
import { CrmFilterTable } from "./CrmFilterTable";
import { Dropdown } from "..";
import { ENTITY_TYPE } from "../../constants";

export const FILTER_TYPE = {
  ACCOUNT: 'ACCOUNT',
  CONTACT: 'CONTACT'
};

export const CrmFilters = ({ crmServiceInstance }) => {
  const [fields, setFields] = useState(null);
  const [filters, setFilters] = useState(null);
  const [openModalType, setOpenModalType] = useState(null);
  const [selectedFilters, setSelectedFilters] = useState(null);
  const FILTER_SERVICE = {
    [FILTER_TYPE.ACCOUNT]: {
      crmServiceInstance: crmServiceInstance,
      entityType: ENTITY_TYPE.COMPANY,
    },
    [FILTER_TYPE.CONTACT]: {
      crmServiceInstance: crmServiceInstance,
      entityType: ENTITY_TYPE.CONTACT,
    }
  };

  const loadFilterData = () => {
    [FILTER_TYPE.ACCOUNT, FILTER_TYPE.CONTACT].forEach(key => {
      const filterService = FILTER_SERVICE[key];
      filterService.crmServiceInstance.getEntityFields(filterService.entityType).then((res) =>
        setFields(prev => ({ ...prev, [key]: res?.filter((item) => item).sort((a, b) => a?.label?.localeCompare(b?.label)) })),
      );
      filterService.crmServiceInstance.getFilters(filterService.entityType).then((res) => setFilters(prev => ({ ...prev, [key]: res })));
    });
  };

  const onCloseModal = () => {
    setOpenModalType(null);
    setSelectedFilters(null);
    loadFilterData();
  };

  useEffect(() => {
    loadFilterData();
  }, []);

  return (
    <>
      {openModalType && (
        <CrmFilterModal
          openModal={!!openModalType}
          filterType={openModalType}
          setOpenModal={onCloseModal}
          fieldList={fields[openModalType]}
          filter={selectedFilters}
          service={FILTER_SERVICE[openModalType]}
        />
      )}

      <div className="flex justify-between text-lg mb-4 ">
        <div className="text-2xl py-4">CRM Selective Sync</div>

        <Dropdown title="Add Filter"
          buttonClassName="h-10"
          options={[
            {
              title: `Account Filter`,
              onClick: () => {
                setOpenModalType(FILTER_TYPE.ACCOUNT);
              }
            },
            {
              title: "Contact Filter",
              onClick: () => {
                setOpenModalType(FILTER_TYPE.CONTACT);
              }
            }
          ]} />
      </div>
      <CrmFilterTable
        loadFilterData={loadFilterData}
        filterType={FILTER_TYPE.ACCOUNT}
        filters={filters}
        setSelectedFilters={setSelectedFilters}
        setOpenModalType={setOpenModalType}
        crmServiceInstance={crmServiceInstance} />
      <CrmFilterTable
        loadFilterData={loadFilterData}
        filterType={FILTER_TYPE.CONTACT}
        filters={filters}
        setSelectedFilters={setSelectedFilters}
        setOpenModalType={setOpenModalType}
        crmServiceInstance={crmServiceInstance} />
    </>
  );
};
