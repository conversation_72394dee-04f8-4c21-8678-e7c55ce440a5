import React, { useEffect, useState, useMemo } from 'react';
import { SelectBox } from '..';
import { <PERSON><PERSON>, <PERSON>dalContent, <PERSON><PERSON>Footer, ModalHeader } from '../Modal';
import { faArrowRight, faMagnifyingGlass, faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICrmDefaultFields, ICrmFieldMapping, ICrmFields, ICrmListMapping } from '../../types/Crm';
import { CrmService } from '../../services';
import { ENTITY_TYPE } from '../../constants';
import EntityService from '../../services/Entity';
import { IEntityField } from '../../types/EntityField';
import { capitalizeWords, formatKey } from '../../lib/utils';
import { CrmFieldType } from '@prisma/client';
import { useAcqwired } from '../../context/AcqwiredContext';
import { CancelButton } from '../Buttons/CancelButton';
import { SubmitButton } from '../Buttons/SubmitButton';

interface ICrmMapFieldsModalProps {
  openModal: boolean;
  setOpenModal: (open: boolean) => void;
  mappings: ICrmFieldMapping[];
  crmServiceInstance: CrmService;
  entityType: ENTITY_TYPE;
  onMappingChanged: () => void;
}

interface IFieldSource {
  uniqueId: string;
  sourceType?: CrmFieldType;
  label: string;
  sourceId?: string;
}

const CrmMapFieldsModal: React.FC<ICrmMapFieldsModalProps> = ({ openModal, setOpenModal, mappings = [], crmServiceInstance, entityType, onMappingChanged }) => {
  const acqwired = useAcqwired();
  const toast = acqwired.toast;

  const [fieldList, setFieldList] = useState<ICrmFields[]>([]);
  const [fieldInfos, setFieldInfos] = useState<IEntityField[]>([]);
  const [filteredListFields, setFilteredListFields] = useState<ICrmFields[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');
  const [data, setData] = useState<ICrmFieldMapping[]>([]);
  const [defaultFields, setDefaultFields] = useState<ICrmDefaultFields[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [source, setSource] = useState('all');
  const [fieldSource, setFieldSource] = useState<IFieldSource[]>([]);

  useEffect(() => {
    const prepareFieldSource = (fields: ICrmFields[], mappedLists: ICrmListMapping[]) => {
      const sources = new Set(fields.map((field) => field.source));
      const filteredLists = mappedLists.filter((list) => list.entityType === entityType);
      const listSources: IFieldSource[] = filteredLists.map((list) => {
        return {
          uniqueId: list.id,
          sourceType: CrmFieldType.list,
          label: `List - ${list.name}`,
          sourceId: list.id
        };
      });

      const fieldSources: IFieldSource[] = Array.from(sources).map((source) => {
        return {
          uniqueId: source,
          sourceType: source,
          label: capitalizeWords(source)
        };
      });
      const allSource: IFieldSource = {
        uniqueId: 'all',
        label: 'All'
      };

      setFieldSource([allSource, ...fieldSources, ...listSources]);
    };

    const fetchData = async () => {
      try {
        setLoading(true);
        const [fields, entityFields, defaultFields, mappedLists] = await Promise.all([crmServiceInstance.getEntityFields(entityType), EntityService.getEntityFields(), crmServiceInstance.getDefaultFields(entityType), crmServiceInstance.getMappedLists()]);

        setFieldList(fields);
        const entityFieldInfos = entityFields?.filter((field) => field.entity === entityType) || [];
        setFieldInfos(entityFieldInfos);
        setDefaultFields(defaultFields);
        prepareFieldSource(fields, mappedLists);

        const defaultData: ICrmFieldMapping[] = defaultFields
          .map((defaultField) => {
            const crmField = fields.find((item) => item.uniqueId === defaultField.uniqueId);
            if (!crmField) return null;
            return {
              uniqueId: crmField.uniqueId,
              crmLabel: crmField.label,
              label: defaultField.label,
              invalid: !isValid(defaultField.key, crmField.uniqueId),
              type: defaultField.type,
              locked: defaultField.locked ?? false,
              source: crmField.source,
              sourceId: crmField.sourceId,
              key: defaultField.key,
              entityType,
              isMandatory: defaultField.isMandatory ?? false
            };
          })
          .filter(Boolean) as ICrmFieldMapping[];

        const mappingsData: ICrmFieldMapping[] = mappings
          .map((mapping) => {
            const crmField = fields.find((item) => item.uniqueId === mapping.uniqueId);
            if (!crmField) return null;
            return {
              uniqueId: crmField.uniqueId,
              crmLabel: crmField.label,
              label: mapping.label,
              invalid: !isValid(mapping.key, crmField.uniqueId),
              type: mapping.type,
              locked: mapping.locked ?? false,
              source: crmField.source,
              sourceId: crmField.sourceId,
              key: mapping.key,
              entityType,
              isMandatory: mapping.isMandatory ?? false,
              schemaFieldId: mapping.schemaFieldId,
              id: mapping.id
            };
          })
          .filter(Boolean) as ICrmFieldMapping[];

        setData(mappingsData.length > 0 ? mappingsData : defaultData);
      } catch (_error) {
        toast.error('Failed to load fields', { timeout: 0 });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [entityType, mappings]);

  useEffect(() => {
    filterFields();
  }, [fieldList, searchValue, source]);

  const filterFields = () => {
    if (source === 'all') {
      setFilteredListFields(fieldList.filter((field) => field.label.toLowerCase().includes(searchValue.toLowerCase())));
    } else {
      const sourceItem = fieldSource.find((item) => item.uniqueId === source);
      if (sourceItem && sourceItem.sourceType === CrmFieldType.list && sourceItem.sourceId) {
        setFilteredListFields(fieldList.filter((field) => field.label.toLowerCase().includes(searchValue.toLowerCase()) && field.source === sourceItem?.sourceType && field.sourceId === sourceItem.sourceId));
      } else {
        setFilteredListFields(fieldList.filter((field) => field.label.toLowerCase().includes(searchValue.toLowerCase()) && field.source === sourceItem?.sourceType));
      }
    }
  };

  const handleDeleteRow = (uniqueId: string) => {
    setData((prevData) => prevData.filter((field) => field.uniqueId !== uniqueId));
  };

  const handleCancel = () => {
    setOpenModal(false);
  };

  const save = async () => {
    try {
      const result = await crmServiceInstance.saveEntityMapping(data, entityType);
      if (result) {
        onMappingChanged();
        await acqwired.store.field.reload();
        setOpenModal(false);
      } else {
        toast.error('Unable to save the mapping.', { timeout: 0 });
      }
    } catch {
      toast.error('An error occurred while saving the mapping.', { timeout: 0 });
    }
  };

  const handleCheckboxChange = (crmField: ICrmFields) => {
    setData((prevData) => {
      if (prevData.some((item) => item.uniqueId === crmField.uniqueId)) {
        return prevData.filter((item) => item.uniqueId !== crmField.uniqueId);
      }
      const formattedKey = formatKey(crmField.label);
      return [
        ...prevData,
        {
          uniqueId: crmField.uniqueId,
          crmLabel: crmField.label,
          label: crmField.label,
          validationResult: isValid(formattedKey, crmField.uniqueId),
          type: crmField.type,
          locked: false,
          source: crmField.source,
          sourceId: crmField.sourceId,
          key: formattedKey,
          entityType: entityType === ENTITY_TYPE.CONTACT ? 'contact' : 'company',
          isMandatory: false
        }
      ];
    });
  };

  const onLabelChanged = (value: string, index: number) => {
    setData((prevData) => {
      const newData = [...prevData];
      newData[index].label = value;
      if (!newData[index].locked) {
        const formattedValue = formatKey(value);
        newData[index].key = formattedValue;
        newData[index].validationResult = isValid(formattedValue, newData[index].uniqueId);
      }
      return newData;
    });
  };

  const onFieldIdChanged = (value: string, index: number) => {
    if (value && !/^[a-zA-Z0-9_]+$/.test(value)) {
      return;
    }
    setData((prevData) => {
      const newData = [...prevData];
      newData[index].key = value;
      newData[index].validationResult = isValid(value, newData[index].uniqueId);
      return newData;
    });
  };

  const isValid = (value: string, uniqueId: string) => {
    if (!value) {
      return { invalid: true, error: 'Field ID is required' };
    }
    const isUnique = !fieldInfos.some((item) => item.key === value) && !data.some((item) => item.key === value && item.uniqueId !== uniqueId);
    if (!isUnique) {
      return { invalid: true, error: 'Field ID must be unique' };
    }
    const alphaNumericRegex = /^[_a-z][a-z0-9_]*$/;
    if (!alphaNumericRegex.test(value)) {
      return { invalid: true, error: 'Field Id can start with an underscore (_) or a lowercase letter, but cannot start with a number. It can only contain lowercase letters, numbers, and underscores (_).' };
    }
    return { invalid: false };
  };

  const isAddMappingDisabled = useMemo(() => {
    return data.some((item) => item.validationResult?.invalid || !item.uniqueId || !item.key) || loading;
  }, [data, loading]);

  const isDefaultField = (uniqueId: string) => {
    return defaultFields.some((field) => field.uniqueId === uniqueId);
  };

  const isKeyNotEditableField = (field: ICrmFieldMapping) => {
    return !!(field?.id || field.locked);
  };

  const handleSourceChange = (sourceItem) => {
    if (!sourceItem?.uniqueId) {
      return;
    }
    setSource(sourceItem.uniqueId);
  };

  return (
    <Modal esc={true} showModal={openModal} setShowModal={setOpenModal}>
      <ModalHeader className="text-2xl" subTitle="Select which records and fields to synchronize between your CRM and Acqwired" closeButton={!!handleCancel} setShowModal={handleCancel}>
        Bulk {entityType === ENTITY_TYPE.CONTACT ? 'Contact' : 'Account'} Field Mapping:
      </ModalHeader>
      <ModalContent className="salesforce-map">
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <div>Loading...</div>
          </div>
        ) : (
          <div className="grid grid-cols-4">
            <div className="border-r col-span-1 mt-2">
              <div className="mr-5 mt-1">
                <label className="block font-bold ml-1 text-sm mt-1">Field Source</label>
                <SelectBox
                  labelField="label"
                  valueField="uniqueId"
                  options={fieldSource}
                  value={source}
                  containerClasses={'!w-full'}
                  buttonProps={{ className: 'bg-transparent w-full grow-1' }}
                  onChange={handleSourceChange}
                  className="mt-2 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md h-8"
                />
              </div>
              <label className="block font-bold ml-1 text-sm mt-1">Available Fields:</label>
              <div className="mr-5 mt-3">
                <div className="pr-3 mb-3 flex items-center bg-white rounded-md border border-acqgray-200 px-3 w-full">
                  <FontAwesomeIcon icon={faMagnifyingGlass} className="h-5 w-5 text-gray-500 inline-block text-sm" />
                  <input className="w-full bg-white rounded-md py-2 px-3 outline-none text-sm" placeholder="Search Fields" onChange={(e) => setSearchValue(e.target.value)} />
                </div>
                <div className="overflow-auto max-h-72">
                  {filteredListFields
                    .sort((a, b) => a.label.localeCompare(b.label))
                    .map((field) => {
                      const isChecked = data.some((item) => item.uniqueId === field.uniqueId);
                      const isDisabled = isChecked;
                      return (
                        <div className="py-1 mr-3" key={field.uniqueId}>
                          <div className="flex justify-between">
                            <span className="ml-2 flex items-center">
                              <input id={`field_id_${field.uniqueId}`} className="mr-2" type="checkbox" checked={isChecked} onChange={() => handleCheckboxChange(field)} disabled={isDisabled} />
                              <label className="text-sm" htmlFor={`field_id_${field.uniqueId}`}>
                                {field.label}
                              </label>
                            </span>
                            <span className="text-xs text-acqgray-500">{field.type}</span>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            </div>
            <div className="col-span-3 max-h-96 overflow-auto">
              <table className="text-sm w-full h-12 text-center">
                <thead className="p-3 max-w-0 min-w-[1rem] w-2 whitespace-nowrap overflow-hidden text-ellipsis">
                  <tr>
                    <th className="p-3">CRM Account Field</th>
                    <th className="p-3"></th>
                    <th className="p-3">Acqwired Company Field Label</th>
                    <th className="p-3">Field ID</th>
                    <th className="p-3">Data Type</th>
                    <th className="p-3"></th>
                  </tr>
                </thead>
                <tbody className="border-t">
                  {data.map((selectedField, index) => (
                    <tr key={index}>
                      <td scope="row" className="px-3 pt-2 text-left align-middle">
                        <div className="overflow-hidden text-ellipsis whitespace-nowrap">
                          <span>{selectedField.crmLabel}</span>
                          <br />
                          {selectedField.source !== CrmFieldType.standard ? <span className="text-xs text-acqgray-500">{selectedField.uniqueId}</span> : null}
                        </div>
                      </td>
                      <td className="px-1 pt-2">
                        <span className="cursor-pointer" title="Fetch Only">
                          <FontAwesomeIcon className="mt-2" icon={faArrowRight} />
                        </span>
                      </td>
                      <td className="px-3 pt-2">
                        <input onChange={(e) => onLabelChanged(e.target.value, index)} placeholder="Enter Text" className="input-new" value={selectedField.label} />
                      </td>
                      <td className="pt-2">
                        <input
                          disabled={isKeyNotEditableField(selectedField)}
                          onChange={(e) => onFieldIdChanged(e.target.value, index)}
                          placeholder="Enter Text"
                          title={selectedField.validationResult?.invalid ? selectedField.validationResult?.error : ''}
                          className={`input-new ${isKeyNotEditableField(selectedField) ? 'input-new-disabled' : ''} ${selectedField.validationResult?.invalid ? 'input-error' : ''}`}
                          value={selectedField.key}
                        />
                      </td>
                      <td className="px-3 pt-2">
                        <input disabled value={selectedField.type} className="input-new-disabled" />
                      </td>
                      <td>{!isDefaultField(selectedField.uniqueId) && <FontAwesomeIcon onClick={() => handleDeleteRow(selectedField.uniqueId)} className="mt-5 cursor-pointer" icon={faTrash} />}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </ModalContent>
      <ModalFooter className="text-right">
        <CancelButton onPress={handleCancel} />
        <SubmitButton isBusy={false} disabled={isAddMappingDisabled} onPress={save} text={`${mappings.length ? 'Update' : 'Add'} Mappings`} />
      </ModalFooter>
    </Modal>
  );
};

export default CrmMapFieldsModal;
