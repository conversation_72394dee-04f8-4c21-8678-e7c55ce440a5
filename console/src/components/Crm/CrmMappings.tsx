import React, { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faAdd, faArrowRight, faPencil } from "@fortawesome/free-solid-svg-icons";
import CrmMapFieldsModal from "./CrmMapFieldsModal";
import Button from "../Button";
import { ICrmFieldMapping } from "../../types/Crm";
import { capitalizeWords } from "../../lib/utils";
import { CrmService } from "../../services";
import { ENTITY_TYPE } from "../../constants";

interface CrmMappingsProps {
  crmServiceInstance: CrmService;
  entityType: ENTITY_TYPE;
  onMappingChanged: () => void;
}

export const CrmMappings: React.FC<CrmMappingsProps> = ({
  crmServiceInstance,
  entityType,
  onMappingChanged
}) => {
  const [showModal, setShowModal] = useState<boolean>(false);
  const [mappings, setMappings] = useState<ICrmFieldMapping[]>([]);

  useEffect(() => {
    crmServiceInstance.getEntityMappings(entityType).then((res) => {
      setMappings(res || []);
    });
  }, [showModal, entityType]);

  return (
    <>
      {showModal && (
        <CrmMapFieldsModal
          openModal={showModal}
          setOpenModal={setShowModal}
          mappings={mappings}
          crmServiceInstance={crmServiceInstance}
          entityType={entityType}
          onMappingChanged={onMappingChanged}
        />
      )}
      <div className="font-bold justify-between flex text-lg mb-4">
        <span>{entityType === ENTITY_TYPE.CONTACT ? "Contact" : "Account"}: Fields</span>
        <Button
          className="button-active flex gap-2 items-center"
          onClick={() => setShowModal(true)}
        >
          <FontAwesomeIcon icon={mappings?.length > 0 ? faPencil : faAdd} />
          {mappings?.length > 0 ? "Edit" : "Add"}{" "}
          {entityType === ENTITY_TYPE.CONTACT ? "Contact" : "Account"} Mapping
        </Button>
      </div>

      <table className="w-full text-sm text-left mb-8 table-fixed">
        <thead className="bg-slate-50 p-3 max-w-0 min-w-[1rem] w-2 whitespace-nowrap overflow-hidden text-ellipsis">
          <tr>
            <th className="p-3">CRM Field</th>
            <th className="p-3">Type</th>
            <th className="p-3">Sync Direction</th>
            <th className="p-3">Acqwired Field</th>
            <th className="p-3">Field ID</th>
            <th className="p-3">Data Type</th>
          </tr>
        </thead>
        <tbody>
          {mappings?.map((mapping) => (
            <tr key={mapping.id} className="hover:bg-slate-50 border-b border-gray-200">
              <td scope="row" className="p-3">
                <span className="break-words">
                  {mapping.label}
                </span>
              </td>
              <td className="p-3">
                <span className="break-words">{capitalizeWords(mapping.source)}</span>
              </td>
              <td className="p-3">
                <FontAwesomeIcon icon={faArrowRight} />
              </td>
              <td className="p-3">
                <span className="break-words">{mapping.label}</span>
              </td>
              <td className="p-3">
                <span className="break-words">{mapping.key}</span>
              </td>
              <td className="p-3">
                <span className="break-words">{mapping.type}</span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {!mappings?.length && (
        <div className="text-center text-sm">
          No fields map. Please add your first fields by clicking <span className="text-acq-500 cursor-pointer" onClick={() => setShowModal(true)}>+ Add {entityType === ENTITY_TYPE.COMPANY ? "Company" : "Contact"} Fields</span>
        </div>
      )}
    </>
  );
};
