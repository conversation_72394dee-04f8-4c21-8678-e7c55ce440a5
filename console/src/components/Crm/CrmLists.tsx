import { useState, useEffect } from 'react';
import { Button } from '../../components';
import CrmListTable from './CrmListTable';
import CrmListModal from './CrmListModal';
import { ICrmListMapping } from '../../types/Crm';
import { CrmService } from '../../services';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAdd } from '@fortawesome/free-solid-svg-icons';

interface ICrmListsProps {
  crmServiceInstance: CrmService;
  refreshKey: number;
}

export const CrmLists = ({ crmServiceInstance, refreshKey }: ICrmListsProps) => {
  const [lists, setLists] = useState<ICrmListMapping[]>([]);
  const [openModal, setOpenModal] = useState(false);
  const [selectedList, setSelectedList] = useState<ICrmListMapping | null>(null);

  const loadListData = async () => {
    return crmServiceInstance
      .getMappedLists()
      .then((set) => setLists(set))
      .catch((err) => console.error('Failed to load lists:', err));
  };

  const onCloseModal = (state: boolean) => {
    if (state) {
      setOpenModal(true);
    } else {
      setOpenModal(false);
      setSelectedList(null);
      loadListData();
    }
  };

  useEffect(() => {
    loadListData();
  }, [refreshKey]);

  return (
    <>
      {openModal && <CrmListModal openModal={openModal} setOpenModal={onCloseModal} selectedList={selectedList} crmServiceInstance={crmServiceInstance} />}

      <div className="flex justify-between text-lg mb-4">
        <div className="text-2xl py-4">CRM Lists Management</div>

        <Button
          className="button-active flex gap-1 items-center h-8 font-bold"
          onClick={() => {
            setSelectedList(null); // Reset selected list for new entry
            setOpenModal(true);
          }}
        >
          <FontAwesomeIcon icon={faAdd} />
          Add List Mapping
        </Button>
      </div>
      <CrmListTable lists={lists} setSelectedList={setSelectedList} setOpenModal={onCloseModal} crmServiceInstance={crmServiceInstance} />
    </>
  );
};

export default CrmLists;
