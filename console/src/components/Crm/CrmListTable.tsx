import { faPencil, faTrash, faCheck } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICrmListMapping } from '../../types/Crm';
import { capitalizeWords } from '../../lib/utils';
import { Modal, ModalFooter } from '../Modal';
import { useState } from 'react';
import { CrmService } from '../../services';
import { useAcqwired } from '../../context/AcqwiredContext';
import { CancelButton } from '../Buttons/CancelButton';
import { DeleteButton } from '../Buttons/DeleteButton';

interface CrmListTableProps {
  lists: ICrmListMapping[];
  setSelectedList: (list: ICrmListMapping | null) => void;
  setOpenModal: (open: boolean) => void;
  crmServiceInstance: CrmService;
}

const CrmListTable = ({ lists, setSelectedList, setOpenModal, crmServiceInstance }: CrmListTableProps) => {
  const acqwired = useAcqwired();
  const toast = acqwired.toast;

  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState<boolean>(false);
  const [isDeletionBusy, setIsDeletionBusy] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] = useState<ICrmListMapping | null>(null);
  const handleDelete = async (list: ICrmListMapping) => {
    setIsDeletionBusy(true);
    try {
      if (list.hasMapping) {
        toast.error('Cannot delete this list because its fields are mapped');
        return;
      }

      const result = await crmServiceInstance.deleteCrmList(list.id);
      if (!result) {
        toast.error('Failed to delete list');
      } else {
        // Reload the list data after deletion
        setIsConfirmModalOpen(false);
        setOpenModal(false);
        // You may want to reload the list data here
      }
    } catch (_error) {
      toast.error('Failed to delete list');
    } finally {
      setIsDeletionBusy(false);
    }
  };

  return (
    <>
      <Modal esc={true} showModal={isConfirmModalOpen} setShowModal={setIsConfirmModalOpen}>
        <div className="p-4 px-12 text-center text-acq-500">
          <p className="mb-4">
            <FontAwesomeIcon icon={faTrash} size="2xl" />
          </p>
          <p className="text-lg font-extrabold">Delete</p>
          <p className="mb-2 text-lg font-extrabold">{selectedItem?.name} CRM List?</p>
        </div>
        <ModalFooter className="text-center">
          <CancelButton onPress={() => setIsConfirmModalOpen(false)} />
          <DeleteButton onPress={() => selectedItem && handleDelete(selectedItem)} isBusy={isDeletionBusy} text="Yes, Delete" variant="solid" />
        </ModalFooter>
      </Modal>
      <table className="w-full text-sm text-left mb-8 table-fixed">
        <thead className="bg-slate-50 p-3 max-w-0 min-w-[1rem] w-2 whitespace-nowrap overflow-hidden text-ellipsis">
          <tr>
            <th className="p-3">List Name</th>
            <th className="p-3">Type</th>
            <th className="p-3">Fetch Related</th>
            <th className="p-3">Actions</th>
          </tr>
        </thead>
        <tbody>
          {lists.map((list) => (
            <tr key={list.id} className="hover:bg-slate-50 border-b border-gray-200">
              <td className="p-3">{list.name}</td>
              <td className="p-3">{capitalizeWords(list.entityType)}</td>
              <td className="p-3">{list.fetchRelated && <FontAwesomeIcon icon={faCheck} />}</td>
              <td className="p-3">
                <div className="flex gap-5">
                  <FontAwesomeIcon
                    icon={faPencil}
                    onClick={() => {
                      setSelectedList(list);
                      setOpenModal(true);
                    }}
                    className="cursor-pointer"
                    title="Edit this list"
                  />
                  <FontAwesomeIcon
                    icon={faTrash}
                    onClick={() => {
                      if (!list.hasMapping) {
                        setSelectedItem(list);
                        setIsConfirmModalOpen(true);
                      }
                    }}
                    className={list.hasMapping ? 'cursor-not-allowed opacity-30' : 'cursor-pointer'}
                    title={list.hasMapping ? 'Cannot delete this list because it fields are mapped' : 'Delete this list'}
                  />
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {!lists?.length && (
        <div className="text-center text-sm">
          No lists map. Please add your first list by clicking{' '}
          <span className="text-acq-500 cursor-pointer" onClick={() => setOpenModal(true)}>
            + Add List{' '}
          </span>
        </div>
      )}
    </>
  );
};

export default CrmListTable;
