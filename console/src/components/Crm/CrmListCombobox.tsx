import React, { useEffect, useState } from 'react';
import { Combobox } from '@headlessui/react';
import { ICrmListEntity } from '../../types/Crm';
import { capitalizeWords } from '../../lib/utils';

interface CrmListComboboxProps {
  options: ICrmListEntity[];
  selectedValue: ICrmListEntity | null;
  onChange: (value: ICrmListEntity | null) => void;
  className: string;
  disabled?: boolean;
}

const CrmListCombobox: React.FC<CrmListComboboxProps> = ({ className, onChange, options, selectedValue, disabled = false }) => {
  const [filteredValues, setFilteredValues] = useState<ICrmListEntity[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');

  useEffect(() => {
    const handleFiltering = () => {
      // Filter options based on the search query
      const newFilteredValues = options.filter((option: ICrmListEntity) => option.name.toLowerCase().includes(searchQuery.toLowerCase()));

      setFilteredValues(newFilteredValues);

      // Clear selected value if it is not in the new filtered values
      if (selectedValue && !newFilteredValues.some((option) => option.id === selectedValue.id)) {
        handleSelect(null);
      }
    };
    handleFiltering();
  }, [searchQuery, options]);

  const handleSelect = (value: ICrmListEntity | null) => {
    setSearchQuery(value?.name || '');
    onChange(value);
  };

  return (
    <div className={`${className} relative`}>
      <Combobox
        value={selectedValue}
        onChange={(value: ICrmListEntity) => {
          handleSelect(value);
        }}
        disabled={disabled}
      >
        <div className={`flex items-center bg-white w-full rounded-md border ${!disabled ? 'focus-within:border-acq-600' : ''}`}>
          <Combobox.Button as="div" className="flex w-full justify-between items-center">
            <Combobox.Input placeholder={'Type or select from available lists'} className="combobox-input p-2" autoComplete="off" displayValue={(item: ICrmListEntity) => item?.name || ''} onChange={(event) => setSearchQuery(event.target.value)} />
          </Combobox.Button>
        </div>
        <div className="relative">
          <Combobox.Options className="combobox-options">
            <div className="grid">
              <Combobox.Label className="p-3 text-sm font-bold">Available Lists</Combobox.Label>
              {filteredValues.length === 0 ? <Combobox.Label className="p-3 pl-6">No Available Lists</Combobox.Label> : null}
            </div>
            {filteredValues.map((value) => (
              <Combobox.Option key={value.id} value={value} className={({ active }) => `py-2 cursor-pointer px-4 select-none ${active ? 'bg-acq-50' : ''}`}>
                {({ selected }) => (
                  <div className={`flex justify-between items-center text-sm block truncate ${selected ? 'font-medium' : 'font-normal'}`}>
                    <span>{value.name}</span>
                    <span className="text-gray-500 ml-2">{capitalizeWords(value.entityType)}</span>
                  </div>
                )}
              </Combobox.Option>
            ))}
          </Combobox.Options>
        </div>
      </Combobox>
    </div>
  );
};

export default CrmListCombobox;
