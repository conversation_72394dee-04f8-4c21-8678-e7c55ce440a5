import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON>Box, Switch } from '../../components';
import { ICrmListEntity, ICrmListMapping } from '../../types/Crm';
import { CrmService } from '../../services';
import { ENTITY_TYPE } from '../../constants';
import CrmListCombobox from './CrmListCombobox';
import { useAcqwired } from '../../context/AcqwiredContext';
import { CancelButton } from '../Buttons/CancelButton';
import { SubmitButton } from '../Buttons/SubmitButton';

interface CrmListModalProps {
  openModal: boolean;
  setOpenModal: (open: boolean) => void;
  selectedList: ICrmListMapping | null;
  crmServiceInstance: CrmService;
}

const CrmListModal = ({ openModal, setOpenModal, selectedList, crmServiceInstance }: CrmListModalProps) => {
  const acqwired = useAcqwired();
  const toast = acqwired.toast;

  const [selectedListEntity, setSelectedListEntity] = useState<ICrmListEntity | null>(null);
  const [type, setType] = useState('All');
  const [fetchRelated, setFetchRelated] = useState(false);
  const [availableLists, setAvailableLists] = useState<ICrmListEntity[]>([]);
  const [filteredLists, setFilteredLists] = useState<ICrmListEntity[]>([]);
  const isEditMode = Boolean(selectedList);

  useEffect(() => {
    if (selectedList) {
      const crmList: ICrmListEntity = selectedList as ICrmListEntity;
      setSelectedListEntity(crmList || null);
      setType(selectedList.entityType || 'All');
      setFetchRelated(selectedList.fetchRelated || false);
    }

    const initializeLists = async () => {
      const lists = await loadAvailableLists();
      filterLists(type, lists);
    };

    initializeLists();
  }, [selectedList]);

  const loadAvailableLists = async () => {
    try {
      const allLists = await crmServiceInstance.getCrmLists();
      const mappedLists = await crmServiceInstance.getMappedLists();
      const mappedListIds = new Set(mappedLists.map((mappedList) => mappedList.id));

      const nonMappedLists = allLists.filter((list) => !mappedListIds.has(list.id) || list.id === selectedList?.id);

      setAvailableLists(nonMappedLists);
      return nonMappedLists; // Return the lists to use immediately
    } catch (err) {
      console.error('Failed to load lists:', err);
      return [];
    }
  };

  const filterLists = (entityType: string, lists: ICrmListEntity[]) => {
    if (entityType === 'All') {
      setFilteredLists(lists);
    } else {
      setFilteredLists(lists.filter((list) => list.entityType === entityType));
    }
  };

  const handleEntityTypeChange = (event) => {
    const value = event.value;
    setType(value);
    filterLists(value, availableLists);
  };

  const handleListChange = (value: ICrmListEntity | null) => {
    if (value) {
      setSelectedListEntity(value);
    } else {
      setSelectedListEntity(null);
    }
  };

  const handleCancel = () => {
    setOpenModal(false);
  };

  const saveList = async () => {
    if (selectedListEntity) {
      const listData: ICrmListMapping = {
        id: selectedListEntity.id,
        name: selectedListEntity.name,
        entityType: selectedListEntity.entityType,
        ownerId: selectedListEntity.ownerId,
        fetchRelated
      };

      return crmServiceInstance
        .saveCrmList(listData)
        .then((result) => {
          if (result) {
            setOpenModal(false);
          } else {
            toast.error('Failed to save list mapping');
          }
        })
        .catch(() => toast.error('Failed to save list mapping'));
    }
  };

  const ENTITY_TYPE_OPTIONS = [
    { label: 'All', value: 'All' },
    { label: 'Company', value: ENTITY_TYPE.COMPANY },
    { label: 'Contact', value: ENTITY_TYPE.CONTACT }
  ];

  return (
    <Modal esc={true} showModal={openModal} setShowModal={setOpenModal}>
      <ModalHeader className="text-2xl" subTitle="Select the entity type and list to map" closeButton={!!handleCancel} setShowModal={handleCancel}>
        {isEditMode ? 'Edit List Mapping' : 'Add List Mapping'}
      </ModalHeader>
      <ModalContent className="crm-list-map">
        <div className="space-y-4">
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700">Entity Type</label>
            <SelectBox
              labelField="label"
              valueField="value"
              options={ENTITY_TYPE_OPTIONS}
              value={type}
              containerClasses={'!w-full'}
              buttonProps={{ className: 'bg-transparent w-full grow-1', disabled: isEditMode }}
              onChange={handleEntityTypeChange}
              className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md h-8"
              disabled={isEditMode}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Select List</label>
            <CrmListCombobox options={filteredLists} selectedValue={selectedListEntity} onChange={handleListChange} className="w-full" disabled={isEditMode} />
          </div>
          <div className="flex items-center mt-2">
            <label className="block text-sm font-medium text-gray-700 mr-4">Fetch Related</label>
            <Switch enabled={fetchRelated} setEnabled={setFetchRelated} />
          </div>
        </div>
      </ModalContent>
      <ModalFooter className="text-right">
        <CancelButton onPress={handleCancel} />
        <SubmitButton isBusy={false} disabled={selectedListEntity === null} onPress={() => saveList()} text={(isEditMode ? 'Update ' : 'Add ') + 'List'} />
      </ModalFooter>
    </Modal>
  );
};

export default CrmListModal;
