import React, { useState, useEffect, ChangeEvent } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalContent, SimpleInput } from './../../components';
import { CrmType } from '@prisma/client';
import { CrmService } from '../../services';
import { useAcqwired } from '../../context/AcqwiredContext';
import { CancelButton } from '../Buttons/CancelButton';
import { SubmitButton } from '../Buttons/SubmitButton';

interface APIKeyModalProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onSave: (crmType: CrmType) => void;
  crmType: CrmType;
}

const APIKeyModal: React.FC<APIKeyModalProps> = ({ isOpen, setIsOpen, onSave, crmType }) => {
  const acqwired = useAcqwired();
  const toast = acqwired.toast;

  const [apiKey, setApiKey] = useState<string>('');
  const [isValid, setIsValid] = useState<boolean>(true);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);

  useEffect(() => {
    if (isOpen) {
      setIsValid(true);
      loadApiKey();
    }
  }, [isOpen]);

  const loadApiKey = async () => {
    try {
      const crmServiceInstance = new CrmService(crmType);
      const storedApiKey = await crmServiceInstance.getApiKey();
      setApiKey(storedApiKey || '');
    } catch (_error) {
      toast.error('Error loading API key');
    }
  };

  const handleSave = async () => {
    try {
      if (apiKey.length === 0) {
        setIsValid(false);
        return;
      }
      setIsProcessing(true);
      const crmServiceInstance = new CrmService(crmType);
      const isValidKey = await crmServiceInstance.submitApiKey(apiKey);
      if (isValidKey) {
        onSave(crmType as CrmType);
        setIsOpen(false);
      } else {
        setIsValid(false);
      }
    } catch (_error) {
      setIsValid(false);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setIsValid(true);
    setApiKey(e.target.value);
  };

  return (
    <Modal showModal={isOpen} setShowModal={setIsOpen} esc={true}>
      <ModalHeader className="py-3 px-2 !mb-0 text-xl font-normal" setShowModal={() => setIsOpen(false)} closeButton={false} subTitle="To establish a connection to CRM, set the API Key.">
        API Key
      </ModalHeader>
      <ModalContent className="w-[400px]">
        <div className="mb-5">
          <SimpleInput
            onKeyUp={(event) => {
              if (event.key === 'Enter' && isValid) {
                handleSave();
              }
            }}
            onChange={handleChange}
            name="apikey"
            type="text"
            value={apiKey}
            error={isValid ? '' : 'Please type a valid API key'}
          />
        </div>
      </ModalContent>
      <ModalFooter className="text-right">
        <CancelButton disabled={isProcessing} onPress={() => setIsOpen(false)} />
        <SubmitButton isBusy={isProcessing} onPress={handleSave} text="Save" />
      </ModalFooter>
    </Modal>
  );
};

export default APIKeyModal;
