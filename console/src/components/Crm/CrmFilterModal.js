import { faPlus, faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import Button from '../Button';
import DynamicSelectbox from '../DynamicSelectbox';
import { <PERSON><PERSON>, ModalContent, ModalFooter, ModalHeader } from '../Modal';
import SelectBox from '../SelectBox';
import { ContactFilterResult } from './ContactFilterResult';
import { AccountFilterResult } from './AccountFilterResult';
import { FILTER_TYPE } from './CrmFilters';

export const FILTER_OPTIONS = [
  { label: 'None', value: 'none' },
  { label: 'Equals', value: 'equals' },
  { label: 'Not equal to', value: 'not-equal' },
  { label: 'Less than', value: 'less-than' },
  { label: 'Greater than', value: 'greater-than' },
  { label: 'Less or equal', value: 'less-or-equal' },
  { label: 'Greater or equal', value: 'greater-or-equal' },
  { label: 'Contains', value: 'contains' },
  { label: 'Does not contain', value: 'not-contains' }
];

const SalesforceFilterModal = ({ openModal, setOpenModal, fieldList, filter, filterType, service }) => {
  const [isLoadingFilterResults, setIsLoadingFilterResults] = useState(false);
  const [filteredObjects, setFilteredObjects] = useState([]);
  const [fetchRelated, setFetchRelated] = useState(!!filter?.fetchRelated);
  const [rows, setRows] = useState([{ field: '', condition: '', input: '' }]);
  const rowRef = useRef(rows);
  const handleCancel = () => {
    setOpenModal(false);
  };

  const addRow = () => {
    setRows([...rows, { field: '', condition: '', input: '' }]);
  };

  const deleteRow = (index) => {
    const updatedRows = [...rows];
    updatedRows.splice(index, 1);
    setRows(updatedRows);
  };

  const isFilterValid = (filter) => {
    return filter.input !== '' && filter.condition !== '' && filter.condition?.value !== 'none' && filter.field !== '';
  };

  const convertRowsToQuery = (rows) => {
    return rows.filter(isFilterValid).map((row) => ({
      field: row.field,
      condition: row.condition.value,
      input: row.input
    }));
  };

  const queryObjectsCallback = useCallback(() => {
    const query = convertRowsToQuery(rowRef.current);

    if (query.length === 0) return;
    setIsLoadingFilterResults(true);
    service.crmServiceInstance
      .getEntities(query, service.entityType)
      .then((res) => {
        setFilteredObjects(res ?? []);
      })
      .catch(console.error)
      .finally(() => {
        setIsLoadingFilterResults(false);
      });
  }, []);

  const queryObjectsDelayed = useCallback(_.debounce(queryObjectsCallback, 500), []);

  const handleDynamicSelectChange = (index, value) => {
    rows[index].field = value;
    setRows([...rows]);
  };

  const handleSelectChange = (index, value) => {
    rows[index].condition = value;
    setRows([...rows]);
  };

  const handleInputChange = (index, value) => {
    rows[index].input = value;
    setRows([...rows]);
  };
  const save = () => {
    service.crmServiceInstance.saveCrmFilter({ id: filter?.id, parameters: convertRowsToQuery(rows), fetchRelated }, service.entityType).then(() => {
      setOpenModal(false);
    });
  };
  useEffect(() => {
    rowRef.current = rows;
    queryObjectsDelayed();
  }, [rows]);

  useEffect(() => {
    if (filter?.parameters?.length > 0)
      setRows(
        filter?.parameters?.map((item) => ({
          field: item.field,
          condition: FILTER_OPTIONS.find((option) => option.value === item.condition),
          input: item.input
        }))
      );
  }, [filter]);

  const isAddFilterDisabled = () => {
    return rows?.some((filter) => !isFilterValid(filter));
  };

  return (
    <Modal esc={true} showModal={openModal} setShowModal={setOpenModal}>
      <ModalHeader className="text-2xl border-b" subTitle={'Select which records and fields to synchronize between your CRM and Acqwired'} closeButton={handleCancel} setShowModal={handleCancel}>
        CRM Integration Setup: Salesforce
      </ModalHeader>
      <ModalContent>
        <div className="salesforce-filter">
          <div className="overflow-y-auto col-span-1">
            <div className="mb-2 font-bold text-lg">Filter CRM {filterType === FILTER_TYPE.CONTACT ? 'Contacts' : 'Accounts'}</div>
            <div className="mb-4 text-sm text-acqgray-600">You can add filters to narrow down the set of {filterType === FILTER_TYPE.CONTACT ? 'Contacts' : 'Accounts'} you will sync to Acqwired</div>
            {rows.map((row, index) => (
              <div key={index} className="flex gap-5 py-1">
                <div>
                  <DynamicSelectbox className={'w-20 md:w-32 xl:w-44'} labelField="label" valueField="key" options={fieldList ?? []} value={row.field} onChange={(value) => handleDynamicSelectChange(index, value)} />
                </div>
                <div>
                  <SelectBox labelField="label" valueField="value" options={FILTER_OPTIONS} value={row.condition.value} onChange={(value) => handleSelectChange(index, value)} />
                </div>
                <div className="w-28 lg:w-40 mt-1 ">
                  <input placeholder="Enter a key" value={row.input} onChange={(e) => handleInputChange(index, e.target.value)} className="bg-white rounded-md w-full py-2 px-3 outline-none border border-acqgray-200 text-sm" />
                </div>
                <div className="text-acqgray-900 flex items-center">
                  <div className="" onClick={() => deleteRow(index)}>
                    <FontAwesomeIcon className={'pl-3  cursor-pointer'} icon={faTrash} />
                  </div>
                </div>
              </div>
            ))}

            <div className="flex mb-6 pt-3 justify-between  items-center">
              <div className="flex gap-3 text-sm">
                <input id="fetch-related" onChange={(e) => setFetchRelated(e.target.checked)} defaultChecked={filter ? filter?.fetchRelated : true} type="checkbox"></input>
                <label htmlFor="fetch-related">Fetch Related {filterType === FILTER_TYPE.CONTACT ? 'Accounts' : 'Contacts'}</label>
              </div>
              <FontAwesomeIcon onClick={addRow} className="cursor-pointer" icon={faPlus} />
            </div>
          </div>
          {filterType === FILTER_TYPE.CONTACT ? <ContactFilterResult isLoadingFilterResults={isLoadingFilterResults} filteredObjects={filteredObjects} /> : <AccountFilterResult isLoadingFilterResults={isLoadingFilterResults} filteredObjects={filteredObjects} />}
        </div>
      </ModalContent>
      <ModalFooter className="text-right">
        <Button className="button-passive" onClick={() => setOpenModal(false)}>
          Cancel
        </Button>
        <Button disabled={isAddFilterDisabled()} className={'button-active  disabled:opacity-50'} onClick={() => save()}>
          {filter?.parameters?.length ? 'Update' : 'Add'} Filters
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export default SalesforceFilterModal;
