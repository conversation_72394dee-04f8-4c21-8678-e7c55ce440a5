import { faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Modal, ModalFooter } from '../Modal';
import { CancelButton } from '../Buttons/CancelButton';
import { DeleteButton } from '../Buttons/DeleteButton';

export const DeleteCrmFilterModal = ({ showModal, hideModal, filter, onDeleted, crmServiceInstance }) => {
  const onDeleteFilter = () => {
    crmServiceInstance.deleteFilter(filter?.id).then(onDeleted);
  };

  return (
    <Modal esc={true} showModal={showModal} setShowModal={hideModal}>
      <div className="p-4 px-12 text-center text-acq-500">
        <p className="mb-4">
          <FontAwesomeIcon icon={faTrash} size="2xl" />
        </p>
        <p className="mb-2 text-lg font-extrabold">Delete filter?</p>
      </div>
      <ModalFooter className="text-center">
        <CancelButton onPress={() => hideModal()} />
        <DeleteButton onPress={onDeleteFilter} isBusy={false} text="Yes, Delete" />
      </ModalFooter>
    </Modal>
  );
};
