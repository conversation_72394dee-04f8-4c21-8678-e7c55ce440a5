import { faC<PERSON><PERSON>, fa<PERSON><PERSON>cil, faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useState } from 'react';
import { DeleteCrmFilterModal } from './DeleteCrmFilterModal';
import { FILTER_TYPE } from './CrmFilters';

export const CrmFilterTable = ({ filters, setSelectedFilters, setOpenModalType, filterType, loadFilterData, crmServiceInstance }) => {
  const [deletedFilter, setDeletedFilter] = useState(null);

  const formatFilterParameters = (filter) => {
    switch (filter.condition) {
      case 'equals':
        return `${filter.field} = ${filter.input}`;
      case 'not-equal':
        return `${filter.field} !== ${filter.input}`;
      case 'less-than':
        return `${filter.field} < ${filter.input}`;
      case 'less-or-equal':
        return `${filter.field} <= ${filter.input}`;
      case 'greater-or-equal':
        return `${filter.field} >= ${filter.input}`;
      case 'greater-than':
        return `${filter.field} > ${filter.input}`;
      case 'contains':
        return `${filter.field} contains ${filter.input}`;
      case 'not-contains':
        return `${filter.field} not contains ${filter.input}`;
    }
  };

  const renderFilterRow = (filterType, filter) => {
    if (!filter) return null;
    return (
      <tr key={filter.field} className="hover:bg-slate-50 border-b border-gray-200">
        <td className="p-3">
          <span className="break-words">{filter?.parameters?.map(formatFilterParameters).join(', ')}</span>
        </td>
        <td className="p-3">{filter.fetchRelated && <FontAwesomeIcon icon={faCheck} />}</td>
        <td className="p-3">
          <div className="flex gap-5">
            <FontAwesomeIcon
              icon={faPencil}
              onClick={() => {
                setSelectedFilters(filter);
                setOpenModalType(filterType);
              }}
            />
            <FontAwesomeIcon
              icon={faTrash}
              onClick={() => {
                setDeletedFilter(filter);
              }}
            />
          </div>
        </td>
      </tr>
    );
  };
  return (
    <>
      {deletedFilter && (
        <DeleteCrmFilterModal
          showModal={true}
          hideModal={() => {
            setDeletedFilter(false);
          }}
          onDeleted={() => {
            setDeletedFilter(null);
            loadFilterData();
          }}
          filter={deletedFilter}
          crmServiceInstance={crmServiceInstance}
        />
      )}
      <div className="font-bold justify-between flex text-lg mb-4 ">
        <span>{filterType === FILTER_TYPE.CONTACT ? 'Contact' : 'Account'}: Sync Filters</span>
      </div>
      <table className="w-full text-sm text-left mb-4 table-fixed">
        <thead className="bg-slate-50 p-3 max-w-0 min-w-[1rem] w-2 whitespace-nowrap overflow-hidden text-ellipsis">
          <tr>
            <th className="p-3">Parameters</th>
            <th className="p-3">Fetch Related</th>
            <th className="p-3">Actions</th>
          </tr>
        </thead>
        <tbody>{filters?.[filterType]?.map((filter) => renderFilterRow(filterType, filter))}</tbody>
      </table>
      {!filters?.[filterType]?.length && (
        <div className="flex items-center flex-col text-sm">
          <div>No data filters means all records will be synced w/ Acqwired (totally ok BTW)</div>
          <div>
            {' '}
            Add filters if you want to down-select by clicking{' '}
            <span onClick={() => setOpenModalType(filterType)} className="text-acq-500 cursor-pointer">
              {' '}
              Add Filter
            </span>
          </div>
        </div>
      )}
    </>
  );
};
