import React from 'react';

interface AutoCompleteWrapperProps {
  children: React.ReactElement;
}

export const AutoCompleteWrapper: React.FC<AutoCompleteWrapperProps> = ({ children }) => {
  const customScrollToItem = (container: HTMLDivElement, item: HTMLDivElement) => {
    if (container && item) {
      item.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
    }
  };

  return React.cloneElement(children, { scrollToItem: customScrollToItem });
};
