@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-roboto font-normal text-gray-700;
  }

  :focus-visible {
    outline-style: none;
    outline-width: 0;
  }

  select {
    @apply bg-acqgray-100 rounded-sm;
  }

  select:disabled {
    @apply bg-acqgray-50;
    opacity: 0.6;
  }

  .welcome-breadcrumb {
    margin: auto;
    width: 75%;
    min-width: 500px;
    max-width: 1000px;
  }

  input[type='checkbox'] {
    background-color: #ffffff;
    background-image: none;
  }

  input:checked[type='checkbox'] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
  }

  button,
  .button {
    @apply font-montserrat cursor-pointer;
  }

  .table-filter-button {
    @apply font-roboto cursor-pointer;
  }

  .z-5 {
    z-index: 5;
  }

  .popover-arrow,
  .popover-arrow::before {
    @apply border border-gray-300;
    position: absolute;
    width: 16px;
    height: 16px;
    background: inherit;
    z-index: -1;
  }

  .popover-arrow {
    visibility: hidden;
  }

  .popover-arrow::before {
    visibility: visible;
    content: '';
    transform: rotate(45deg);
  }

  .popover[data-popper-placement^='top'] > .popover-arrow {
    bottom: -8px;
  }

  .popover[data-popper-placement^='bottom'] > .popover-arrow {
    top: -8px;
  }

  .popover[data-popper-placement^='left'] > .popover-arrow {
    right: -8px;
  }

  .popover[data-popper-placement^='right'] > .popover-arrow {
    left: -8px;
  }

  .icon-sm {
    font-size: 10px;
  }

  .icon-base {
    font-size: 12px;
  }

  .icon-lg {
    font-size: 15px;
  }

  .icon-xl {
    font-size: 18px;
  }

  .icon-2xl {
    font-size: 24px;
  }

  .icon-3xl {
    font-size: 32px;
  }

  .icon-4xl {
    font-size: 40px;
  }

  .icon-5xl {
    font-size: 50px;
  }
}

@layer components {
  .button-passive {
    @apply cursor-default text-xs px-3 py-2 whitespace-nowrap rounded text-white bg-acqgray-400 leading-tight hover:bg-acqgray-500;
  }

  .button-blue {
    @apply cursor-default font-bold text-sm px-1 py-2 whitespace-nowrap rounded text-acq-500 leading-tight hover:bg-acqgray-50;
  }

  .button-active {
    @apply button text-xs px-3 whitespace-nowrap py-2 rounded text-white bg-acq-500 hover:bg-acq-600 active:bg-acq-600 transition duration-150 ease-in-out disabled:cursor-not-allowed disabled:opacity-50;
  }

  .button-white {
    @apply button text-xs px-3 py-2 whitespace-nowrap;
  }

  .button-danger {
    @apply button text-sm px-3 py-2 rounded text-white bg-red-400 hover:bg-red-600 active:bg-red-600 transition duration-150 ease-in-out;
  }

  .sticky-footer {
    @apply border-t border-slate-300 bg-white bottom-0 right-0 left-0 fixed ml-48 h-16 flex ease-in-out duration-200;
  }

  .modal-container {
    @apply fixed left-0 top-0 w-full h-full z-50 bg-gray-900/60 justify-center items-center;
  }

  .modal-content {
    @apply h-auto rounded-lg bg-white inline-block p-4 pb-2;
  }

  .salesforce-map {
    @apply h-96 border-t w-[560px] md:w-[750px] lg:w-[950px] xl:w-[1050px];
  }

  .crm-list-map {
    @apply h-96 border-t w-[560px];
  }

  .salesforce-filter {
    @apply grid grid-cols-2 h-96 max-h-96 gap-5 lg:gap-10 w-[650px] md:w-[800px] lg:w-[900px] xl:w-[1000px];
  }

  .table-row-icon-button {
    @apply text-sm hover:text-acq-500 active:text-acq-500 transition duration-150 ease-in-out;
  }

  .select {
    @apply bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5;
  }

  .err-msg {
    @apply w-2/3 mx-auto my-4 bg-red-500 text-white text-center rounded;
  }

  .ai-attribute-button-open {
    @apply border border-gray-300 rounded p-4 shadow-lg;
  }

  .ai-attribute-button {
    @apply border border-gray-300 rounded p-4;
  }

  .chip-light {
    @apply inline-block bg-acq-50 rounded-sm px-2 py-1 text-acq-500;
  }

  .chip-dark {
    @apply inline-block bg-acq-500 rounded-sm px-2 py-1 text-white;
  }

  .demo-chip-dark {
    @apply bg-acq-500 rounded-none px-3 rounded-l text-white;
  }

  .demo-chip-text {
    @apply bg-acq-600 px-3 py-1 rounded-r inline-block text-white;
  }

  .tags-error {
    @apply px-2.5 pb-1.5 pt-2 w-full text-sm bg-transparent rounded-md border border-red-700 text-red-700;
  }

  .grid-button {
    @apply w-8 h-8 rounded items-center hover:bg-acqgray-100 text-center;
  }

  .tags {
    @apply px-2 py-2 w-full text-sm bg-transparent rounded-md border focus-within:border-acq-600 border-slate-200 text-acqgray-900;
  }

  .tags-input {
    @apply min-w-52 max-w-screen-sm focus:outline-none focus:ring-0 appearance-none text-slate-900;
  }

  .tags-input-label {
    @apply absolute bg-white text-sm px-2 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] left-1 text-slate-500;
  }

  .attribute-list-item {
    @apply p-5 bg-acq-50 border border-acq-50 rounded-lg;
  }

  .attribute-list-item.edit {
    @apply bg-white border-acq-600;
  }

  .attribute-list-item .title {
    @apply font-semibold whitespace-nowrap overflow-hidden text-ellipsis text-xl text-acq-500;
  }

  .attribute-list-item > p {
    @apply pt-2 mb-2 text-acqgray-700 text-sm;
  }

  .attribute-list-item .icon {
    @apply text-acq-500 mr-2 pointer-events-none;
  }

  .demo {
    @apply bg-acq-50 mb-5 h-[300px] w-full;
  }

  .progressbar {
    @apply w-[210px] h-3 border border-acqgray-700;
  }

  .progress {
    @apply h-full bg-acqgray-700;
  }

  .list-button {
    @apply border border-acqgray-600 rounded-md bg-white w-full sm:text-sm py-2 px-3 outline-none cursor-default text-left;
  }

  .listbox-button {
    @apply button-sup border relative w-full cursor-default rounded-md border-slate-200 bg-white py-2 pl-3 pr-10 text-left focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm;
  }

  .combobox-options {
    @apply max-h-40 overflow-y-auto absolute mt-1 bg-white w-full shadow border border-acqgray-50 z-50;
  }

  .combobox-option-float {
    @apply max-h-40 overflow-y-auto mt-1 bg-white w-full shadow border border-acqgray-50 z-50;
  }

  .combobox-input {
    @apply w-full text-sm bg-transparent;
  }

  .combobox-content {
    @apply flex items-center bg-white rounded-md border border-acqgray-200 px-2 w-60 py-1 justify-between;
  }

  .combobox-content-disabled {
    @apply flex items-center bg-acqgray-200 rounded-md border border-acqgray-200 px-2 w-60 py-1 justify-between;
  }

  .existing-entity-input {
    @apply bg-acqgray-100 cursor-default block px-2.5 pb-2 pt-2 w-full text-sm rounded border;
  }

  .input-new {
    @apply border border-acqgray-600 rounded-md bg-white w-full sm:text-sm py-2 px-3 outline-none text-left mt-1;
  }

  .input-new-disabled {
    @apply truncate rounded-md bg-acqgray-200 w-full text-acqgray-500 py-2 px-3 outline-none text-left mt-1;
  }

  .input-error {
    @apply border border-red-500 rounded-md bg-white w-full sm:text-sm py-2 px-3 outline-none text-left mt-1;
  }

  .table-row-icon {
    @apply w-full bg-cover bg-center bg-no-repeat;
  }

  .table-cell {
    @apply p-3 h-14 truncate text-ellipsis max-w-[145px] overflow-hidden;
  }

  .table-row {
    @apply border-b hover:bg-acqgray-50 border-gray-200;
  }

  .table-row-selected {
    @apply border-b bg-acq-100 cursor-pointer;
  }

  .table-row-link {
    @apply text-sm hover:underline cursor-pointer;
  }

  .main-content {
    @apply basis-full h-full grow overflow-y-auto;
  }

  .drawer {
    @apply basis-[400px] grow-0 shrink-0 bg-white h-screen flex flex-col overflow-y-hidden border-l;

    .drawer-header {
      @apply flex flex-row pl-2 pt-6 pb-6;

      .drawer-icon {
        @apply basis-12 grow-0 shrink-0 p-0 text-center text-acq-500;
      }

      .drawer-title {
        @apply grow;

        .title {
          @apply font-bold h-6 truncate text-ellipsis w-72;
        }

        .subtitle {
          @apply text-acqgray-400 text-sm h-4 truncate text-ellipsis w-72;
        }
      }

      .drawer-close {
        @apply basis-[50px] grow-0 shrink-0 text-center leading-5 text-acq-500;
      }
    }

    .drawer-body {
      @apply text-sm grow flex flex-col;
      height: calc(100vh - 90px);
      overflow-y: auto;

      &:hover {
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
      }

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #eee;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #bbb;
        border-radius: 4px;
      }

      .field-content-container {
        .field-content-action-buttons {
          display: none;
        }

        &:hover {
          .field-content-action-buttons {
            display: flex;
            flex-direction: row;
          }
        }
      }

      .details-tab {
        @apply pt-4 pb-12 h-[calc(100vh-88px-54px)] overflow-y-auto;
      }

      .enrichment-tab {
        @apply px-6 pt-4 pb-12 h-[calc(100vh-88px-54px)] overflow-y-auto;

        hr {
          @apply my-2;
        }

        .plays {
          .title {
            @apply flex cursor-pointer pb-2 items-center;

            &:hover {
              button {
                @apply text-acq-500;
              }
            }
          }

          .play {
            @apply py-2 cursor-pointer flex flex-row items-center;

            .name {
              @apply font-bold mb-0.5;
            }

            .description {
              @apply text-acqgray-500;
            }

            &:hover {
              @apply text-acq-500;
            }
          }

          .play-disabled {
            @apply text-acqgray-500 cursor-default;

            &:hover {
              @apply bg-white text-acqgray-500;
            }
          }
        }
      }

      .activities-tab {
        @apply px-2 pt-2 pb-12 overflow-y-auto;

        hr {
          @apply my-4;
        }

        .no-activities {
          @apply text-acqgray-500 text-center;

          div {
            @apply pt-8;

            div {
              @apply pt-1 text-acqgray-600;
            }
          }
        }

        .scheduled-email {
          @apply bg-yellow-50 border-yellow-500 border rounded-lg p-2;

          button {
            @apply button-active text-xs font-bold mt-4;
          }
        }

        .email-error {
          @apply bg-red-600 text-white border rounded-lg p-2;

          .error-msg {
            @apply flex items-center border-b border-red-400 pb-3 mb-3;

            .title {
              @apply font-bold;
            }

            .error {
              @apply text-xs text-acqgray-100;
            }
          }

          button {
            @apply text-xs font-bold mt-4 mb-2;
          }
        }

        .email-content {
          .subject {
            @apply text-sm font-bold pt-1 pb-2;
          }

          .body {
            @apply text-sm;

            &.collapsed {
              @apply h-4 overflow-y-hidden truncate text-ellipsis;
            }
          }
        }
      }
    }
  }

  .drawer-toolbar {
    @apply basis-[50px] grow-0 shrink-0 bg-white flex flex-col pt-3 border-l;

    .drawer-toolbar-action {
      @apply w-full text-center py-3;
    }
  }

  .button-sup {
    @apply font-roboto;
  }

  .acqwired-dropdown {
    @apply z-50 min-w-52 py-2 mt-1 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none;
  }

  .dropdown-button {
    @apply inline-flex w-full h-full justify-center items-center rounded-md bg-gray-200 text-sm text-gray-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75;
  }

  s {
    text-decoration-color: black;
    text-decoration-style: double;
    text-decoration-thickness: 1px;
    text-decoration-line: line-through;
  }
  .source-list {
    @apply font-roboto;

    .source-list-header {
      @apply mb-2 relative;
    }

    .source-list-content {
      @apply font-roboto divide-y divide-gray-200 max-h-[500px] overflow-y-auto min-w-[300px];
    }

    .source-list-group {
      @apply py-2 w-full;
    }

    .source-list-group-title {
      @apply font-medium text-gray-700 px-1 mb-2;
      font-size: 12px !important;
    }

    .source-list-item {
      @apply font-roboto flex items-center w-full text-left px-3 py-1 rounded-md hover:bg-gray-50 transition-colors;
      font-size: 12px !important;
    }

    .source-list-item-icon {
      @apply flex-shrink-0 mr-3;
    }

    .source-list-item-content {
      @apply flex flex-col;
    }

    .source-list-item-title {
      @apply font-roboto font-medium;
      font-size: 12px !important;
    }

    .source-list-item-description {
      @apply font-roboto text-gray-500;
      font-size: 12px !important;
    }

    .source-list-empty {
      @apply py-4 text-center w-full text-gray-500;
      font-size: 12px !important;
    }

    /* Force exact font on all text elements */
    p,
    span,
    div,
    button {
      font-size: 12px !important;
    }
  }
}

.rc-slider-track {
  background-color: #6c63ff !important;
}

.rc-slider-dot-active {
  border-color: #a7a1ff !important;
}

.rc-slider-handle,
.rc-slider-handle:focus,
.rc-slider-handle:hover {
  border-color: #a7a1ff !important;
  background-color: #6c63ff;
}

.rc-slider-handle:active {
  box-shadow: 0 0 5px #a7a1ff !important;
}

.show-on-hover:hover {
  text-overflow: clip;
  white-space: normal;
  word-break: break-all;
}

.table-cell-processing:after {
  display: inline-block;
  animation: dotty steps(1, end) 1s infinite;
  content: '';
}

.json-schema-editor {
  font-family: 'Fira Code', monospace;
  height: 200px;
  overflow-y: auto;
  padding: 10px;
  margin-top: 10px;
  white-space: pre-wrap;
  tab-size: 2;
}

@keyframes dotty {
  0% {
    content: '';
  }
  25% {
    content: '.';
  }
  50% {
    content: '..';
  }
  75% {
    content: '...';
  }
}

.table-cell-preprocessing {
  color: transparent;
  text-shadow: 0 0 2px rgb(156 163 175);
}

.rta__nomatch {
  @apply flex flex-col text-center w-full px-2 text-acqgray-500;
  min-height: 250px;
  overflow-y: hidden;

  .nomatch-icon {
    @apply mb-4;
  }

  .nomatch-text {
    @apply text-base;
  }
}

.field-content {
  position: relative;

  .field-actions {
    position: absolute;
    right: 0;
    cursor: pointer;
    display: none;
    /*@apply flex flex-row gap-5 items-center;*/
  }

  &:hover .field-actions {
    @apply flex flex-row gap-5 items-center;
  }
}

.__acqwired_datatable_container {
  @apply text-sm w-full overflow-x-auto p-0 grow;
  /*height: calc(100vh - 100px);*/

  & > div {
    @apply h-full p-0 m-0;
  }

  &:hover {
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
  }

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #eee;
    border-radius: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #bbb;
    border-radius: 6px;
  }

  table {
    @apply w-full border-spacing-0 border-separate rounded-none;

    thead {
      tr {
        th {
          @apply sticky top-0 bg-white border-1 border-l-0 p-2 whitespace-nowrap overflow-ellipsis cursor-default text-left;
          z-index: 7;

          &.table-separator {
            @apply text-slate-300 h-[50px];
          }

          &[data-sortable='true'] {
            @apply cursor-pointer;
          }

          &[data-celltype='field'] {
            @apply min-w-[200px];

            .field-column-settings-container {
              @apply cursor-default absolute z-50 w-[300px] bg-white border-1 shadow-3xl rounded-t-none rounded-lg font-normal;
              top: 36px;

              .auto-hidden-info {
                opacity: 0;
                transition: opacity 0.5s ease-in;
              }

              .auto-hidden-info.show {
                opacity: 1;
                animation: fadeOut 1s ease-out forwards;
              }

              @keyframes fadeOut {
                0%,
                90% {
                  opacity: 1;
                }
                100% {
                  opacity: 0;
                }
              }

              .primary-enrichment-selector-items {
                .action-buttons > span {
                  opacity: 0;
                }

                &:hover {
                  .action-buttons > span {
                    opacity: 100;
                  }
                }
              }
            }

            .field-column-executor-container {
              @apply cursor-default absolute z-50 w-[300px] bg-white border-1 shadow-3xl rounded-t-none rounded-lg font-normal;
              top: 35px;

              .auto-hidden-info {
                opacity: 0;
                transition: opacity 0.5s ease-in;
              }

              .auto-hidden-info.show {
                opacity: 1;
                animation: fadeOut 1s ease-out forwards;
              }

              @keyframes fadeOut {
                0%,
                90% {
                  opacity: 1;
                }
                100% {
                  opacity: 0;
                }
              }

              .primary-enrichment-selector-items {
                & > .action-buttons > span {
                  display: none;
                }

                &:hover {
                  & > .action-buttons > span {
                    display: block;
                  }
                }
              }
            }
          }

          &[data-celltype='enrichment'] {
            @apply min-w-[200px];

            .enrichment-column-settings-container {
              @apply cursor-default absolute z-50 w-[300px] bg-white border-1 shadow-3xl rounded-t-none rounded-lg font-normal overflow-hidden;
              top: 36px;

              .auto-hidden-info {
                opacity: 0;
                transition: opacity 0.5s ease-in;
              }

              .auto-hidden-info.show {
                opacity: 1;
                animation: fadeOut 1s ease-out forwards;
              }

              @keyframes fadeOut {
                0%,
                90% {
                  opacity: 1;
                }

                100% {
                  opacity: 0;
                }
              }
            }

            .enrichment-executor-container {
              @apply cursor-default absolute z-50 w-[300px] bg-white border-1 shadow-3xl rounded-t-none rounded-lg font-normal;
              top: 35px;

              .auto-hidden-info {
                opacity: 0;
                transition: opacity 0.5s ease-in;
              }

              .auto-hidden-info.show {
                opacity: 1;
                animation: fadeOut 1s ease-out forwards;
              }

              @keyframes fadeOut {
                0%,
                90% {
                  opacity: 1;
                }
                100% {
                  opacity: 0;
                }
              }
            }
          }

          &:first-child {
            @apply rounded-none border-l-1 text-center w-12 px-1 py-1;
            left: 0;
            z-index: 6;

            & > label {
              @apply !m-0;

              & > span {
                @apply !m-0;
              }
            }
          }

          &:nth-child(2) {
            left: 46px;
            z-index: 6;
          }

          &:nth-last-child(2) {
            @apply sticky border-r-0 w-[200px];
          }

        }

        &:nth-child(2) {
          display: none;
        }
      }
    }

    tbody {
      tr {
        &[data-softselected='true'] {
          td {
            @apply bg-acq-100;

            &:nth-child(1) {
              @apply !bg-acq-100;
            }

            &:nth-child(2) {
              @apply !bg-acq-100 font-bold;
            }

          }
        }

        &[data-softselected='false'][data-selected='true'] {
          td {
            @apply !bg-acq-50;
          }
        }

        &[data-softselected='false']:hover {
          td {
            @apply !bg-acqgray-50;
          }
        }

        td {
          @apply z-0 border-1 border-t-0 border-l-0 p-2;

          &.currently-editing {
            z-index: 100;
          }

          &[data-celltype='field'] .cell-status-container,
          &[data-celltype='enrichment'] .cell-status-container {
            @apply absolute flex flex-col;
            height: 100%;

            &.status-none {
              display: none;
            }

            &.status-preprocessing,
            &.status-processing {
              top: 0;
              left: 0;
              width: 8px;
              background-color: #faedcb;
            }

            &.status-completed {
              top: 0;
              left: 0;
              width: 8px;
              background-color: #c9e4de;
            }

            &.status-error {
              @apply flex flex-row items-center pl-2 truncate text-xs;
              bottom: 0;
              height: 14px;
              width: 100%;
              color: #ff686b;
            }
          }

          &[data-celltype='field'] {
            @apply relative p-0 w-[200px];

            &[data-mode='edit'] {
              @apply relative;

              .editable-field-container {
                @apply absolute cursor-default left-0 top-0 w-[300px] flex flex-col bg-white border-1 rounded-t-none rounded-b-lg shadow-3xl-r z-50;

                textarea {
                  &::-webkit-scrollbar {
                    width: 6px;
                    height: 6px;
                  }

                  &::-webkit-scrollbar {
                    width: 6px;
                    height: 6px;
                  }

                  &::-webkit-scrollbar-track {
                    background: #eee;
                    border-radius: 6px;
                  }

                  &::-webkit-scrollbar-thumb {
                    background: #bbb;
                    border-radius: 6px;
                  }
                }
              }
            }

            .inline-field-hover-buttongroup {
              opacity: 0;
            }

            &:hover .inline-field-hover-buttongroup {
              opacity: 100;
            }
          }

          &[data-celltype='enrichment'] {
            @apply relative p-0 w-[200px];

            .inline-field-hover-buttongroup {
              opacity: 0;
            }

            &:hover .inline-field-hover-buttongroup {
              opacity: 100;
            }
          }

          &:first-child {
            @apply sticky bg-white border-l-1 text-center p-0 px-1 m-0 w-12 cursor-pointer;
            left: 0;
            z-index: 5;

            & > label {
              @apply !m-0;

              & > span {
                @apply !m-0;
              }
            }
          }

          &:nth-child(2) {
            @apply sticky bg-white;
            left: 46px;
            z-index: 5;
          }

          &:nth-last-child(2) {
            @apply border-r-0;
          }

          &:nth-last-child(3) {
            @apply border-b-0;
          }
        }

        &:last-child {
          td:nth-last-child(3) {
            @apply border-b-1;
          }
        }
      }
    }
  }
}

.__list_datatable_container {
  @apply text-sm w-full h-full overflow-y-auto p-0 grow;

  & > div {
    @apply h-full p-0 m-0;
  }

  &:hover {
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
  }

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #eee;
    border-radius: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #bbb;
    border-radius: 6px;
  }

  table {
    @apply w-full border-spacing-0 border-separate rounded-none;

    thead {
      tr {
        th {
          @apply bg-gray-50 cursor-default text-left sticky top-0;
          z-index: 3;

          &[data-sortable='true'] {
            @apply cursor-pointer;
          }

          &:last-child {
            @apply w-[60px];
            right: 0;
          }
        }
      }
    }

    tbody {
      tr {
        td {
          @apply border-b-1;
        }
      }
    }
  }
}

.__idea_datatable_container {
  @apply text-sm w-full overflow-x-auto p-0 grow;

  & > div {
    @apply h-full p-0 m-0;
  }

  &:hover {
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
  }

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #eee;
    border-radius: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #bbb;
    border-radius: 6px;
  }

  table {
    @apply w-full border-spacing-0 border-separate rounded-none;

    thead {
      tr {
        &:first-child {
          @apply h-[20px];

          th {
            @apply sticky top-0;
          }
        }
        &:last-child {
          @apply h-[40px];

          th {
            @apply sticky top-[20px];
          }
        }

        th {
          @apply cursor-default border-r-1 border-b-1 border-t-1;
          z-index: 3;

          &.new-column {
            @apply w-[150px] whitespace-nowrap p-0;
          }

          &:first-child {
            @apply rounded-none border-l-1 text-center w-12 px-1 py-1;
            left: 0;
            z-index: 6;

            & > label {
              @apply !m-0;

              & > span {
                @apply !m-0;
              }
            }
          }

          &:nth-child(2) {
            border-right: 3px solid #ddd;
            left: 46px;
            z-index: 6;
          }

          &:not([data-type='enrichment']) {
            @apply text-left bg-white;
          }

          &[data-type='enrichment'] {
            @apply text-center min-w-[50px] w-[50px] top-[20px] border-0;
            z-index: 2;
          }

          &[data-type='enrichmentColumn'] {
            @apply top-[20px] border-0;
            z-index: 2;
          }

          &[data-type='maindata'] {
            @apply bg-gray-50;
          }

          &[data-type='checkbox'] {
            @apply bg-gray-50;
          }

          &[data-sortable='true'] {
            @apply cursor-pointer;
          }

          &:nth-last-child(2) {
            @apply border-r-0;
          }

          &.enrichment-header {
            @apply text-center border-0 rounded-t-lg;
          }

          &.blank-header {
            @apply border-0;
          }
        }
      }
    }

    tbody {
      tr {
        @apply bg-white;

        &:hover {
          @apply bg-gray-50;

          td {
            @apply bg-gray-50;
          }

          td:first-child,
          td:nth-child(2) {
            @apply bg-gray-50;
          }
        }


        td {
          @apply border-b-1 bg-white border-r-1 relative;
          z-index: 1;

          &:first-child {
            @apply sticky border-l-1 text-center p-0 px-1 m-0 w-12 cursor-pointer;
            left: 0;
            z-index: 5;

            & > label {
              @apply !m-0;

              & > span {
                @apply !m-0;
              }
            }
          }

          &:nth-child(2) {
            @apply sticky bg-white w-[248px];
            border-right: 3px solid #ddd;
            left: 46px;
            z-index: 5;
          }

          &:nth-last-child(2) {
            @apply border-r-0;
          }

        }
      }
    }
  }
}

.__acqwired_datatable_pagination_container {
  @apply m-0;
}

.company-card-container {
  @apply flex flex-row items-center py-0.5 px-1.5 gap-2 w-full w-[248px];
}

.ql-toolbar {
  background-color: #b4bbc74a;
  border-color: transparent !important;
  padding: 8px;
  font-size: 12px !important;
}

.item-hover-class {
  @apply m-0 p-2 bg-white hover:bg-acq-100 !important;
}

.radioWrapper {
  @apply flex border-gray-500 !important;
}

.selectedRadioWrapper {
  @apply flex border-acq-500 !important;
}

.optimistic-break-wrap {
  overflow-wrap: anywhere; /* Prefer breaking at spaces first */
  word-break: break-word; /* Break long words only when necessary */
  white-space: normal; /* Allow wrapping */
}

.fancy-scrollbar {
  &:hover {
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
  }

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #eee;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #bbb;
    border-radius: 4px;
  }
}

/* Container for the entire JSON viewer */
.enrichment-json-viewer {
  width: 380px;
  height: 400px;
  display: flex;
  flex-direction: column;
  border-radius: 6px;
}

.enrichment-json-viewer .w-json-view-container {
  font-size: inherit !important;
  font-family: inherit !important;
  color: inherit !important;
  background: transparent !important;
  line-height: 1.5rem !important;
}

/* The header at the top (title, search, icons) */
.enrichment-json-viewer__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #eee;
  margin-bottom: 0.25rem;
}

/* Left side of the header: icon + title */
.enrichment-json-viewer__title-area {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Right side of the header (run button + search) */
.enrichment-json-viewer__actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* The scrollable area that holds the JSON tree */
.enrichment-json-viewer__content {
  flex: 1;
  overflow: auto;
  padding: 0.5rem 1rem;
}

/* A row in the JSON tree */
.enrichment-json-viewer__row {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}

/* The container for row actions (copy/column).
   We'll switch between hidden/visible on hover. */
.enrichment-json-viewer__row-actions {
  margin-left: auto;
  display: flex;
  gap: 0.5rem;
  align-items: center;
  visibility: hidden; /* default is hidden; show on hover */
}

/* Force the row-actions to become visible when the user hovers. */
.enrichment-json-viewer__row:hover .enrichment-json-viewer__row-actions {
  visibility: visible;
}

.tooltip-content {
  white-space: normal;
  max-width: 250px; /* Adjust as needed */
  word-wrap: break-word;
}

.office365-color {
  color: #dc3e15;
}

.affinity-color {
  color: #2665f1;
}

.main-body-container {
  @apply flex flex-row w-screen h-screen;

  .navigation-container {
    @apply h-screen flex flex-col gap-2 border-r-2 relative;

    .expander {
      @apply absolute min-w-6 p-0 w-8 h-8;
    }

    &[data-expanded='true'] {
      @apply w-[182px] min-w-[182px];

      .expander {
        @apply bottom-4 right-4;
      }

      .item {
        @apply flex items-center h-8 rounded-md mx-2 text-base;

        &[data-type='group-title'] {
          @apply text-gray-500 h-4 text-xs m-0 ml-5 cursor-default;
        }

        &[data-type='separator'] {
          @apply text-gray-500 h-5 mx-3 my-0;
        }

        &[data-type='item']:hover {
          @apply bg-gray-100 cursor-pointer;
        }

        &[data-active='true'] {
          @apply bg-gray-100 cursor-pointer text-acq-600;
        }
      }
    }

    &[data-expanded='false'] {
      @apply w-[52px];

      .expander {
        @apply bottom-4 left-2;
      }

      .item {
        @apply flex items-center h-8 rounded-md mx-2 text-base;

        &[data-type='group-title'] {
          @apply text-gray-500 h-4 text-xs m-0 ml-5 cursor-default;
        }

        &[data-type='separator'] {
          @apply text-gray-500 h-5 mx-3 my-0;
        }

        &[data-type='item']:hover {
          @apply bg-gray-100 cursor-pointer;
        }

        &[data-active='true'] {
          @apply bg-gray-100 cursor-pointer text-acq-600;
        }
      }
    }
  }

  .main-content-container {
    @apply grow overflow-auto fancy-scrollbar overflow-x-hidden;
  }
}

.overflow-wrap-anywhere {
  overflow-wrap: anywhere;
}

/* Popover Font */
[role='dialog'] *,
[data-slot='base'] * {
  font-family: Roboto, sans-serif !important;
}

.border-s-foreground {
  border-inline-start-color: transparent
}

.border-x-half-spacing-6 {
  border-left-width: 0;
  border-right-width: calc(0.5rem);
}

.no-scale-dropdown {
  transform: none !important;
}
