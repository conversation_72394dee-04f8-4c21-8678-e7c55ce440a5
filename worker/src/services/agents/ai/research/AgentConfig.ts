import { Chat<PERSON>penAI } from '@langchain/openai';
import { BatchGoogleSearchTool, BatchScrapeTool, StructuredOutputTool } from 'agents/tools';
import { getSystemPrompt, getUserPrompt } from './Prompt';
import { container } from '@acqwired';
import { JSONSchemaObject } from 'types/JsonSchema';

export class AgentConfig {
  public static createLLM({ apiKey, model }: { apiKey: string; model: string }) {
    return new ChatOpenAI({
      apiKey,
      model,
      temperature: 0
    });
  }

  public static createTools({ apiKey, model, schema }: { apiKey: string; model: string; schema: JSONSchemaObject }) {
    const scrapeTool = container.resolve<BatchScrapeTool>('BatchScrapeTool', {
      config: {
        scrapeService: container.resolve('ScrapeService'),
        chunkSize: 50000,
        llm: new ChatOpenAI({
          model,
          temperature: 0.15,
          apiKey
        })
      }
    });

    const googleSearchBatchTool = container.resolve<BatchGoogleSearchTool>('BatchGoogleSearchTool', {});
    const structuredOutputTool = new StructuredOutputTool(schema);
    return [googleSearchBatchTool, scrapeTool, structuredOutputTool];
  }

  public static getPrompt({ input, schema }: { input: string; schema: string }) {
    return {
      system: getSystemPrompt({ input, schema }),
      user: getUserPrompt({ input, schema })
    };
  }
}
