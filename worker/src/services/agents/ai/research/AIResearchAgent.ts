import { createReactAgent } from '@langchain/langgraph/prebuilt';
import { AgentConfig } from './AgentConfig';
import { AgentResponse } from 'utils/AgentResponse';
import { logger } from '@acqwired';
import { JSONSchemaObject } from 'types/JsonSchema';
import { AIResearchAgentExecutionResult, IAgentServiceArgs } from 'types/Agent';
import { DEFAULT_SCHEMA } from 'agents/tools/StructuredOutput';
import { ExecutionService, EntityService, TemplateVariablesService } from '@services';
import { UnprocessableContentError } from '@errors';
import { IPreTemplateExtraction } from 'types/ChannelOutput';
import { EntityType } from '@prisma/client';
import { IBasicExecution } from 'types/Execution';
import { ICompanySelectResult, IIdeaSelectResult, IContactSelectResult } from 'types/Entity';
import Handlebars from 'handlebars';

export class AIResearchAgent {
  private readonly llm: ReturnType<typeof AgentConfig.createLLM>;
  private readonly openAiApiKey: string;
  private readonly model: string;
  private readonly entityService: EntityService;
  private readonly templateVariablesService: TemplateVariablesService;
  private readonly executionService: ExecutionService;

  constructor({ executionService, entityService, templateVariablesService, openAiApiKey, model }: IAgentServiceArgs) {
    this.openAiApiKey = openAiApiKey;
    this.model = model;
    this.entityService = entityService;
    this.templateVariablesService = templateVariablesService;
    this.executionService = executionService;
    this.llm = AgentConfig.createLLM({ apiKey: this.openAiApiKey, model });
  }

  public async createAgent(schema: JSONSchemaObject): Promise<ReturnType<typeof createReactAgent>> {
    const transformedSchema = this.transformSchema(schema);
    logger.debug({ msg: 'Research Agent Schema', transformedSchema });
    return createReactAgent({
      llm: this.llm,
      tools: AgentConfig.createTools({ apiKey: this.openAiApiKey, model: this.model, schema: transformedSchema }),
      name: 'research_agent'
    });
  }

  private transformSchema(schema: JSONSchemaObject): JSONSchemaObject {
    const transformed = { ...schema };

    if (transformed.properties) {
      Object.entries(transformed.properties).forEach(([key, prop]) => {
        if (prop.type === 'object' && 'properties' in prop && prop.properties) {
          transformed.properties[key] = {
            ...prop,
            properties: this.transformSchema({ type: 'object', properties: prop.properties }).properties
          };
        }
        // Transform array with enum to string
        else if (prop.type === 'array' && 'enum' in prop) {
          transformed.properties[key] = {
            description: prop.description,
            type: 'string'
          };
        }
      });
    }

    return transformed;
  }

  public async process({ userInput, inputSchema }: { userInput: string; inputSchema: JSONSchemaObject }): Promise<AIResearchAgentExecutionResult> {
    const userInputWithEntities = await this.populatePromptTemplate(userInput);
    return this.execute({ query: userInputWithEntities, inputSchema });
  }

  public async execute({ query, inputSchema }: { query: string; inputSchema: JSONSchemaObject }): Promise<AIResearchAgentExecutionResult> {
    const schema = (inputSchema as JSONSchemaObject) || DEFAULT_SCHEMA;
    const researchAgent = await this.createAgent(schema);

    const initialState = this.createInitialState({ query, schema });

    logger.debug({ msg: 'Research Agent Execution', initialState });
    const result = await researchAgent.invoke(initialState, { recursionLimit: 15 });
    logger.debug({ msg: 'Research Agent Result', result });

    const response = AgentResponse.extract<typeof schema>(result);
    logger.debug({ msg: 'Research Agent Result', response });

    return {
      answer: JSON.stringify(response.content),
      tokenUsage: response.totalTokens || 0
    };
  }

  private createInitialState({ query, schema }: { query: string; schema: JSONSchemaObject }) {
    const prompts = AgentConfig.getPrompt({ input: query, schema: JSON.stringify(schema) });
    logger.debug({ msg: 'Research Agent Prompt', prompts });

    return {
      messages: [
        {
          role: 'system',
          content: prompts.system
        },
        {
          role: 'user',
          content: prompts.user
        }
      ]
    };
  }

  private async populatePromptTemplate(prompt: string) {
    const bodyVariables: (string | null)[] = this.templateVariablesService.extractVariables(prompt);
    const entityIds = await this.getEntityIds();

    if (!entityIds) {
      throw new UnprocessableContentError('Invalid entityType in entityService.getEntity', { entityIds });
    }

    const { companyId, contactId, ideaId, entityType } = entityIds;

    const templateEngine = Handlebars.compile(prompt);
    const templateData = await this.templateVariablesService.createTemplateData({ entityType, companyId, contactId, ideaId, variables: bodyVariables.filter((v): v is string => v !== null) });
    return templateEngine(templateData);
  }

  private async getEntityIds(): Promise<IPreTemplateExtraction | null> {
    const { entityType } = await this.executionService.getExecution<IBasicExecution>();

    switch (entityType) {
      case EntityType.company:
        return this.entityService.getEntity<ICompanySelectResult>().then(({ id: companyId, ...others }) => {
          const contactId = others.entityMapping ? others.entityMapping.contact.id : null;
          return { companyId, contactId, ideaId: null, entityType };
        });
      case EntityType.contact:
        return this.entityService.getEntity<IContactSelectResult>().then(({ id: contactId, ...others }) => {
          const companyId = others.entityMapping ? others.entityMapping.company.id : null;
          return { contactId, companyId, ideaId: null, entityType };
        });
      case EntityType.idea:
        return this.entityService.getEntity<IIdeaSelectResult>().then(({ id: ideaId }) => {
          return { contactId: null, companyId: null, ideaId, entityType };
        });
      case EntityType.list:
        return { contactId: null, companyId: null, ideaId: null, entityType };
      default:
        return null;
    }
  }
}
