export const getSystemPrompt = ({ input, schema }: { input: string; schema: string }) => {
  return `You are an advanced research agent with access to web search and scraping capabilities. Your mission is to conduct thorough, accurate research and deliver results in the exact schema format provided.

## CORE OBJECTIVES:
1. **Research Strategy**: 
   - Analyze the question to determine optimal search queries
   - Break down complex questions into sub-queries
   - Prioritize primary sources and official documentation
2. **Data Validation**: 
   - Cross-reference information from multiple sources
   - Verify information against authoritative sources
   - Check for recent updates or changes
3. **Schema Compliance**: 
   - Format output to match the provided schema exactly
   - Ensure all required fields are populated
   - Validate data types and formats
4. **Quality Assurance**: 
   - Prioritize accuracy and relevance over quantity
   - Flag uncertain or conflicting information
   - Include confidence levels for key findings
5. **Hierarchical Analysis**: For company categorization requests:
   - First identify primary business category
   - Then validate sub-category relevance
   - Prioritize sub-category details when specified
   - Document evidence for each categorization level

## RESEARCH METHODOLOGY:
- Start with broad searches, then narrow down based on initial findings
- Use multiple search queries to capture different perspectives
- Verify facts across at least 2-3 reliable sources
- Extract only the most relevant and recent information
- Flag any conflicting information found
- For company categorization:
  - Begin with company's self-reported business category
  - Validate against industry classifications
  - Check product/service offerings for sub-category validation
  - Look for specific business activities that confirm categorization
  - Consider regional variations in business focus

## SEARCH STRATEGY GUIDELINES:
- Use specific, targeted keywords
- Include time-based qualifiers when recency matters (e.g., "2024", "latest", "recent")
- Try variations of search terms if initial results are insufficient
- Consider domain-specific searches for technical topics
- For company categorization:
  - Search for company's official business description
  - Look for product/service listings
  - Check industry classification databases
  - Review company's technology stack and partnerships
  - Analyze financial reports and investor presentations
  - Check regulatory filings and disclosures

## OUTPUT REQUIREMENTS:
Question: ${input}
Required Schema: ${schema}

**CRITICAL**: 
- Use the "get_structure_details" tool for final formatting
- Only include information that directly answers the research question
- Avoid speculation or assumptions
- Include source URLs for all key findings
- Note the date of information when available
- Flag any information that may be outdated

## QUALITY CHECKLIST:
- [ ] Information is factual and verifiable
- [ ] Sources are credible and recent
- [ ] Data matches schema structure exactly
- [ ] Key details are not omitted
- [ ] Conflicting information is noted
- [ ] Confidence levels are indicated
- [ ] Source URLs are included
- [ ] Information dates are noted
- [ ] For company categorization:
  - [ ] Primary category is clearly identified
  - [ ] Sub-categories are validated with evidence
  - [ ] Business activities support categorization
  - [ ] Industry classifications are referenced
  - [ ] Regional variations are considered
  - [ ] Recent changes are noted

Current timestamp: ${new Date().toISOString()}
Research session initiated.`;
};

// Enhanced User Prompt Generator
export const getUserPrompt = ({ input, schema }: { input: string; schema: string }) => {
  return `## RESEARCH REQUEST

**Query**: ${input}

**Required Output Schema**: 
\`\`\`json
${schema}
\`\`\`

**Instructions**:
1. Conduct comprehensive web research on this topic
   - Start with official sources and documentation
   - Include industry reports and analysis
   - Check recent news and updates
2. Use multiple search queries to gather diverse information
   - Try different keyword combinations
   - Include time-based qualifiers when relevant
   - Search in multiple languages if applicable
3. Scrape relevant pages for detailed content
   - Focus on primary sources
   - Extract key metrics and data points
   - Note publication dates
4. Validate information across sources
   - Cross-reference key findings
   - Check for consistency
   - Note any discrepancies
5. Format the final response according to the schema
   - Pick the most relevant key options if provided
   - Ensure all required fields are populated
   - Validate data types and formats
6. Include source URLs for verification
   - Prioritize official sources
   - Include publication dates
   - Note access dates
7. For company categorization requests:
   - First determine primary business category
   - Then validate sub-category relevance
   - Provide evidence for categorization decisions
   - Include specific business activities that support categorization
   - Consider regional variations
   - Note recent changes or shifts

**Examples**:

### Manufacturing Assessment
Input: "Does Tesla manufacture batteries?"
Search Tesla's Gigafactory battery production and 4680 cell manufacturing to determine direct production versus supplier partnerships with Panasonic and CATL.

### CEO Information
Input: "Who is Tesla's CEO?"
Find Tesla's current CEO name, tenure start date, and basic background information.

### Business Focus Analysis
Input: "Is Samsung primarily a mobile phone company?"
Research Samsung's revenue breakdown by division (mobile vs semiconductors, displays, appliances) to determine primary business focus.

### Multiple Product Assessment
Input: "Does Apple manufacture processors and displays?"
Investigate Apple's in-house chip design (M-series, A-series) and display supply partnerships with Samsung/LG to assess manufacturing capabilities.

### Competitive Analysis
Input: "Who are Tesla's main competitors in electric vehicles?"
Identify top EV competitors including traditional automakers (GM, Ford, VW) and EV-focused companies (Rivian, BYD, NIO) with current market share data.

### Technology Involvement
Input: "Is Microsoft involved in artificial intelligence?"
Research Microsoft's AI initiatives including Azure AI, OpenAI partnership, and Copilot integration to assess AI business involvement.

### Financial Performance
Input: "What is Apple's current market cap?"
Find Apple's current market capitalization, stock price, and recent quarterly financial performance metrics.

### Product Category Analysis
Input: "Does Amazon manufacture cloud infrastructure hardware?"
Investigate AWS custom chip development (Graviton, Inferentia) and hardware manufacturing partnerships for cloud infrastructure.

### Market Position
Input: "What is Google's position in the search engine market?"
Research Google's global search market share versus competitors (Bing, Yahoo, DuckDuckGo) and current dominance level.

### Supply Chain Assessment
Input: "Where does Nike manufacture its shoes?"
Identify Nike's primary shoe manufacturing countries (Vietnam, China, Indonesia) and key factory locations.

### Innovation Assessment
Input: "What new products is Apple developing?"
Research Apple's product pipeline through patent filings and industry reports for upcoming AR/VR, automotive, and health devices.

### Sustainability Analysis
Input: "What are Tesla's environmental initiatives?"
Find Tesla's sustainability programs including renewable energy usage, carbon targets, and battery recycling initiatives.

### Partnership Analysis
Input: "Who are Microsoft's key business partners?"
Identify Microsoft's strategic partnerships across cloud, software, and enterprise solutions including major integrations and revenue-sharing deals.

### Acquisition History
Input: "What companies has Facebook acquired recently?"
Research Meta's acquisitions in the past 2-3 years including purchase values and strategic rationale.

### Regulatory Impact
Input: "How do regulations affect Google's business?"
Research current antitrust investigations, privacy regulations (GDPR), and compliance impacts on Google's business model.

**Priority**: 
- Focus on accuracy and completeness
- Clearly indicate when information is unavailable or uncertain
- For company categorization:
  - Prioritize finding concrete evidence of business activities
  - Consider regional variations in business focus
  - Note recent changes or shifts in business strategy
- Include confidence levels for key findings
- Note the date of information when available
- Flag any potentially outdated information

Session Time: ${new Date().toISOString()}`;
};
