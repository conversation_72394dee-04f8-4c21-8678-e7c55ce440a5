import { ProcessorBase } from './ProcessorBase';
import { ExecutionService, ScoringModelService, EntityService, NotificationService } from '@services';
import { EntityType } from '@prisma/client';
import { UnprocessableContentError } from '@errors';
import { logger } from '@acqwired';
import { IBasicExecution } from 'types/Execution';

interface IScoringProcessorArgs {
  name: string;
  executionService: ExecutionService;
  scoringModelService: ScoringModelService;
  entityService: EntityService;
  notificationService: NotificationService;
}

export class ScoringProcessor extends ProcessorBase {
  protected readonly executionService: ExecutionService;
  protected readonly scoringModelService: ScoringModelService;
  protected readonly entityService: EntityService;
  protected readonly notificationService: NotificationService;

  constructor({ name, executionService, scoringModelService, entityService, notificationService }: IScoringProcessorArgs) {
    super({ name });
    this.executionService = executionService;
    this.scoringModelService = scoringModelService;
    this.entityService = entityService;
    this.notificationService = notificationService;
  }

  async process(): Promise<boolean> {
    const execution = await this.executionService.getExecution<IBasicExecution>();
    if (!execution) {
      throw new UnprocessableContentError('Scoring task missing execution');
    }

    const ideaId = execution.entityId;
    if (!ideaId) {
      throw new UnprocessableContentError('Scoring task missing ideaId');
    }

    const listId = await this.scoringModelService.getListIdByScoreModelId(execution.typeId);
    if (!listId) {
      throw new UnprocessableContentError('Scoring task missing listId');
    }

    // Check if the idea is in the list
    if (execution.entityType === EntityType.idea) {
      const isIdeaInList = await this.entityService.isIdeaInList({ ideaId: execution.entityId, listCompanyId: listId });
      if (!isIdeaInList) {
        throw new UnprocessableContentError('Idea not found in list', { execution });
      }
    }

    const scoreResult = await this.scoringModelService.processScoringExecution();
    if (!scoreResult) {
      throw new Error('Scoring process failed');
    }

    // Send notification of successful scoring
    if (this.notificationService) {
      await this.notificationService.updateScoringExecutionStatus({
        orgId: execution.orgId,
        status: 'completed',
        listId,
        entityId: ideaId,
        scoringModelId: execution.typeId,
        data: {
          totalScore: scoreResult.totalScore,
          details: scoreResult.details,
          error: null
        }
      });
    }

    logger.info({ msg: 'Scoring execution processed', executionId: execution.id });
    return true;
  }

  async setProcessCompleted(): Promise<boolean> {
    const execution = await this.executionService.getExecution<IBasicExecution>();
    if (!execution) return false;

    await this.executionService.setExecutionAsCompleted();
    return true;
  }
}
