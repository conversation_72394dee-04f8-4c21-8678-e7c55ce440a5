import { AIResearchAgent } from '@services';
import { ICreateAgentServiceFactoryArgs } from 'types/Agent';

const createAIResearchAgentServiceFactory = () => {
  return ({ executionService, enrichmentService, notificationService, entityService, templateVariablesService, openAiApiKey, model, schema, updater }: ICreateAgentServiceFactoryArgs): AIResearchAgent => {
    return new AIResearchAgent({ executionService, enrichmentService, notificationService, entityService, templateVariablesService, openAiApiKey, model, schema, updater });
  };
};

export { createAIResearchAgentServiceFactory };
