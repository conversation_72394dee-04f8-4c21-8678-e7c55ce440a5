#!/bin/sh
#
# check if there is any build error

check_build() {
    service=$1
    echo "Checking code quality for $service..."
    cd "./$service" || { echo "Unable to change directory to $service"; exit 1; }
    npm run build > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "Aborting commit. Unable to compile $service service!" >&2
        exit 1
    fi
    cd ..
}

check_lint() {
    service=$1
    echo "Checking code formatting for $service..."
    cd "./$service" || { echo "Unable to change directory to $service"; exit 1; }
    npm run lint > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "Lint error detected! Run 'cd $service && npm run lint' to see errors" >&2
        exit 1
    fi
    cd ..
}

check_lint "console"
check_lint "admin"
check_lint "dataapi"
check_lint "worker"

check_build "console"
check_build "admin"
check_build "frontend"
check_build "dataapi"
check_build "worker"
check_build "scheduler"
